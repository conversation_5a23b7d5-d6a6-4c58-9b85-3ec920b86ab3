-- 应急救援中心总览模块数据库表结构
-- 创建时间：2025-01-20

-- 1. 入井领导表
CREATE TABLE `tb_mk_emergency_leader` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `leader_code` varchar(50) NOT NULL COMMENT '领导编码',
    `leader_name` varchar(100) NOT NULL COMMENT '领导姓名',
    `position` varchar(100) DEFAULT NULL COMMENT '职位',
    `department` varchar(100) DEFAULT NULL COMMENT '部门',
    `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
    `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
    `is_underground` tinyint(1) DEFAULT 0 COMMENT '是否下井：0-未下井，1-已下井',
    `entry_time` datetime DEFAULT NULL COMMENT '入井时间',
    `exit_time` datetime DEFAULT NULL COMMENT '出井时间',
    `current_location` varchar(200) DEFAULT NULL COMMENT '当前位置',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_leader_code` (`leader_code`),
    KEY `idx_org_code` (`org_code`),
    KEY `idx_is_underground` (`is_underground`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='应急救援入井领导信息表';

-- 2. 下井人员统计表
CREATE TABLE `tb_mk_emergency_personnel_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '统计编码',
    `stats_date` date NOT NULL COMMENT '统计日期',
    `category_type` varchar(20) NOT NULL COMMENT '分类类型：TEAM-区队类型，AREA-区域类型',
    `category_name` varchar(100) NOT NULL COMMENT '分类名称：搬家公司、机电队、运转队、综掘队、车辆等',
    `personnel_count` int DEFAULT 0 COMMENT '人员数量',
    `total_count` int DEFAULT 0 COMMENT '总人数',
    `percentage` decimal(5,2) DEFAULT 0.00 COMMENT '占比百分比',
    `color_code` varchar(20) DEFAULT NULL COMMENT '显示颜色代码',
    `sort_order` int DEFAULT 0 COMMENT '排序顺序',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_category` (`category_type`, `category_name`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='应急救援下井人员统计表';

-- 3. 生产安全总结表
CREATE TABLE `tb_mk_emergency_safety_summary` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `summary_code` varchar(50) NOT NULL COMMENT '总结编码',
    `summary_date` date NOT NULL COMMENT '总结日期',
    `summary_title` varchar(200) DEFAULT NULL COMMENT '总结标题',
    `summary_content` text COMMENT '总结内容',
    `has_content` tinyint(1) DEFAULT 0 COMMENT '是否有内容：0-无内容，1-有内容',
    `warning_level` tinyint(1) DEFAULT 0 COMMENT '警告级别：0-正常，1-提醒，2-警告，3-严重',
    `warning_message` varchar(500) DEFAULT NULL COMMENT '警告信息',
    `responsible_person` varchar(100) DEFAULT NULL COMMENT '负责人',
    `department` varchar(100) DEFAULT NULL COMMENT '负责部门',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_summary_code` (`summary_code`),
    KEY `idx_summary_date` (`summary_date`),
    KEY `idx_org_code` (`org_code`),
    KEY `idx_warning_level` (`warning_level`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='应急救援生产安全总结表';

-- 4. 矿井工作面信息表
CREATE TABLE `tb_mk_emergency_workface` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `workface_code` varchar(50) NOT NULL COMMENT '工作面编码',
    `workface_name` varchar(100) NOT NULL COMMENT '工作面名称',
    `workface_type` varchar(50) DEFAULT NULL COMMENT '工作面类型：采空区、回采工作面等',
    `area_level` int DEFAULT NULL COMMENT '区域层级',
    `coordinate_x` decimal(10,2) DEFAULT NULL COMMENT 'X坐标',
    `coordinate_y` decimal(10,2) DEFAULT NULL COMMENT 'Y坐标',
    `coordinate_z` decimal(10,2) DEFAULT NULL COMMENT 'Z坐标（高度）',
    `width` decimal(8,2) DEFAULT NULL COMMENT '宽度',
    `length` decimal(8,2) DEFAULT NULL COMMENT '长度',
    `height` decimal(8,2) DEFAULT NULL COMMENT '高度',
    `parent_id` bigint DEFAULT 0 COMMENT '父级ID',
    `level_path` varchar(500) DEFAULT NULL COMMENT '层级路径',
    `is_active` tinyint(1) DEFAULT 1 COMMENT '是否活跃：0-非活跃，1-活跃',
    `safety_status` tinyint(1) DEFAULT 1 COMMENT '安全状态：0-危险，1-正常，2-警告',
    `description` text COMMENT '描述信息',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_workface_code` (`workface_code`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_org_code` (`org_code`),
    KEY `idx_safety_status` (`safety_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='应急救援矿井工作面信息表';

-- 5. 应急救援项目管理表（统一管理导航和列表项目）
CREATE TABLE `tb_mk_emergency_item` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `item_code` varchar(50) NOT NULL COMMENT '项目编码',
    `item_name` varchar(100) NOT NULL COMMENT '项目名称',
    `item_type` varchar(50) NOT NULL COMMENT '项目类型：NAV-导航模块，PERSON-人员，VEHICLE-车辆，SAFETY-安全监测，ENVIRONMENT-环境监测，WAREHOUSE-应急仓库',
    `parent_code` varchar(50) DEFAULT NULL COMMENT '父级项目编码，列表项目关联到导航项目',
    `display_order` int DEFAULT 0 COMMENT '显示顺序',
    `item_status` tinyint(1) DEFAULT 1 COMMENT '项目状态：0-异常，1-正常，2-警告',
    `item_value` varchar(100) DEFAULT NULL COMMENT '项目值',
    `item_unit` varchar(20) DEFAULT NULL COMMENT '项目单位',
    `threshold_min` decimal(10,2) DEFAULT NULL COMMENT '最小阈值',
    `threshold_max` decimal(10,2) DEFAULT NULL COMMENT '最大阈值',
    `nav_icon` varchar(100) DEFAULT NULL COMMENT '导航图标（导航类型使用）',
    `nav_url` varchar(255) DEFAULT NULL COMMENT '导航链接（导航类型使用）',
    `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认选中（导航类型使用）：0-否，1-是',
    `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    `is_visible` tinyint(1) DEFAULT 1 COMMENT '是否可见：0-不可见，1-可见',
    `action_url` varchar(255) DEFAULT NULL COMMENT '操作链接',
    `icon_class` varchar(100) DEFAULT NULL COMMENT '图标样式类',
    `color_code` varchar(20) DEFAULT NULL COMMENT '颜色代码',
    `location_info` varchar(200) DEFAULT NULL COMMENT '位置信息',
    `coordinate_x` decimal(10,2) DEFAULT NULL COMMENT 'X坐标',
    `coordinate_y` decimal(10,2) DEFAULT NULL COMMENT 'Y坐标',
    `coordinate_z` decimal(10,2) DEFAULT NULL COMMENT 'Z坐标',
    `permission_code` varchar(100) DEFAULT NULL COMMENT '权限编码',
    `description` varchar(500) DEFAULT NULL COMMENT '描述信息',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_item_code` (`item_code`),
    KEY `idx_item_type` (`item_type`),
    KEY `idx_parent_code` (`parent_code`),
    KEY `idx_display_order` (`display_order`),
    KEY `idx_org_code` (`org_code`),
    KEY `idx_item_status` (`item_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='应急救援项目管理表';

-- 插入示例数据

-- 入井领导示例数据
INSERT INTO `tb_mk_emergency_leader` (`leader_code`, `leader_name`, `position`, `department`, `phone`, `is_underground`, `org_code`) VALUES
('LEADER001', '张总经理', '总经理', '管理层', '13812345678', 1, 'LJTKYJGS'),
('LEADER002', '李副总', '副总经理', '管理层', '13987654321', 0, 'LJTKYJGS'),
('LEADER003', '王主任', '安全主任', '安全部', '13765432109', 1, 'LJTKYJGS');

-- 下井人员统计示例数据
-- 区队类型统计（TEAM）
INSERT INTO `tb_mk_emergency_personnel_stats` (`stats_code`, `stats_date`, `category_type`, `category_name`, `personnel_count`, `total_count`, `percentage`, `color_code`, `sort_order`, `org_code`) VALUES
('STATS001', '2025-01-20', 'TEAM', '搬家公司', 25, 193, 12.95, '#FF8C00', 1, 'LJTKYJGS'),
('STATS002', '2025-01-20', 'TEAM', '机电队', 21, 193, 10.88, '#FF8C00', 2, 'LJTKYJGS'),
('STATS003', '2025-01-20', 'TEAM', '运转队', 17, 193, 8.81, '#1E90FF', 3, 'LJTKYJGS'),
('STATS004', '2025-01-20', 'TEAM', '综掘队', 17, 193, 8.81, '#32CD32', 4, 'LJTKYJGS'),
('STATS005', '2025-01-20', 'TEAM', '车辆', 12, 193, 6.22, '#32CD32', 5, 'LJTKYJGS');

-- 区域类型统计（AREA）
INSERT INTO `tb_mk_emergency_personnel_stats` (`stats_code`, `stats_date`, `category_type`, `category_name`, `personnel_count`, `total_count`, `percentage`, `color_code`, `sort_order`, `org_code`) VALUES
('STATS006', '2025-01-20', 'AREA', '1301工作面', 35, 193, 18.13, '#FF6B6B', 1, 'LJTKYJGS'),
('STATS007', '2025-01-20', 'AREA', '1302工作面', 28, 193, 14.51, '#4ECDC4', 2, 'LJTKYJGS'),
('STATS008', '2025-01-20', 'AREA', '1303工作面', 22, 193, 11.40, '#45B7D1', 3, 'LJTKYJGS'),
('STATS009', '2025-01-20', 'AREA', '主运输巷', 18, 193, 9.33, '#96CEB4', 4, 'LJTKYJGS'),
('STATS010', '2025-01-20', 'AREA', '回风巷', 15, 193, 7.77, '#FECA57', 5, 'LJTKYJGS'),
('STATS011', '2025-01-20', 'AREA', '进风巷', 12, 193, 6.22, '#FF9FF3', 6, 'LJTKYJGS'),
('STATS012', '2025-01-20', 'AREA', '调度室', 8, 193, 4.15, '#54A0FF', 7, 'LJTKYJGS'),
('STATS013', '2025-01-20', 'AREA', '变电所', 6, 193, 3.11, '#5F27CD', 8, 'LJTKYJGS'),
('STATS014', '2025-01-20', 'AREA', '水泵房', 5, 193, 2.59, '#00D2D3', 9, 'LJTKYJGS'),
('STATS015', '2025-01-20', 'AREA', '其他区域', 44, 193, 22.80, '#C7ECEE', 10, 'LJTKYJGS');

-- 生产安全总结示例数据
INSERT INTO `tb_mk_emergency_safety_summary` (`summary_code`, `summary_date`, `summary_title`, `summary_content`, `has_content`, `warning_level`, `warning_message`, `responsible_person`, `department`, `org_code`) VALUES
('SUMMARY001', '2025-05-24', '日常安全检查总结', NULL, 0, 1, '没有填写内容', '张安全', '安全部', 'LJTKYJGS'),
('SUMMARY002', '2025-01-20', '应急演练总结', '今日进行了火灾应急演练，演练过程中发现以下问题：1.人员疏散速度较慢；2.应急设备检查不到位；3.通讯联络存在延迟。建议加强日常培训，完善应急预案。', 1, 2, '发现多项问题需要整改', '李主管', '安全部', 'LJTKYJGS'),
('SUMMARY003', '2025-01-19', '设备安全检查总结', '对矿井主要设备进行了全面检查，检查结果如下：1.瓦斯监测系统运行正常；2.通风设备状态良好；3.排水系统需要维护；4.供电系统稳定。总体安全状况良好。', 1, 0, '设备运行正常', '王工程师', '机电队', 'LJTKYJGS'),
('SUMMARY004', '2025-01-18', '人员安全培训总结', '组织了新入职人员安全培训，培训内容包括：1.矿井安全基础知识；2.应急逃生技能；3.设备操作规范；4.事故案例分析。共培训32人，考核合格率100%。', 1, 0, '培训效果良好', '刘培训师', '人力资源部', 'LJTKYJGS'),
('SUMMARY005', '2025-01-17', '环境监测总结', '对矿井环境进行了监测，监测结果：1.瓦斯浓度0.3%，在安全范围内；2.粉尘浓度略高，需加强通风；3.噪音水平正常；4.温度湿度适宜。建议加强粉尘治理。', 1, 1, '粉尘浓度需要关注', '陈监测员', '环保部', 'LJTKYJGS'),
('SUMMARY006', '2025-01-16', '事故隐患排查总结', '进行了全面的事故隐患排查，发现隐患3处：1.1302工作面支护不到位；2.主运输巷照明不足；3.排水沟堵塞。已制定整改计划，预计3日内完成整改。', 1, 3, '发现重要安全隐患', '赵检查员', '安全部', 'LJTKYJGS'),
('SUMMARY007', '2025-01-15', '应急物资检查总结', '对应急物资进行了盘点检查：1.防毒面具120个，完好率95%；2.应急照明100套，完好率98%；3.救援绳索50根，完好率100%；4.医疗用品充足。总体储备充足。', 1, 0, '应急物资储备充足', '孙管理员', '物资部', 'LJTKYJGS'),
('SUMMARY008', '2025-01-14', '通讯系统检查总结', '对矿井通讯系统进行了全面检查：1.井下电话系统正常；2.应急广播系统正常；3.视频监控系统正常；4.人员定位系统正常。通讯保障能力良好。', 1, 0, '通讯系统运行正常', '周技术员', '信息部', 'LJTKYJGS');

-- 矿井工作面示例数据
INSERT INTO `tb_mk_emergency_workface` (`workface_code`, `workface_name`, `workface_type`, `area_level`, `coordinate_x`, `coordinate_y`, `coordinate_z`, `org_code`) VALUES
('WF1302', '1302工作面采空区', '采空区', 1, 100.00, 200.00, -300.00, 'LJTKYJGS'),
('WF1303', '1303工作面采空区', '采空区', 1, 120.00, 220.00, -300.00, 'LJTKYJGS'),
('WF1304', '1304工作面采空区', '采空区', 1, 140.00, 240.00, -300.00, 'LJTKYJGS'),
('WF1305', '1305工作面采空区', '采空区', 1, 160.00, 260.00, -300.00, 'LJTKYJGS'),
('WF1307', '1307工作面采空区', '采空区', 1, 200.00, 300.00, -300.00, 'LJTKYJGS'),
('WF1308', '1308工作面采空区', '采空区', 1, 220.00, 320.00, -300.00, 'LJTKYJGS'),
('WF1309', '1309工作面采空区', '采空区', 1, 240.00, 340.00, -300.00, 'LJTKYJGS'),
('WF1310', '1310工作面采空区', '采空区', 1, 260.00, 360.00, -300.00, 'LJTKYJGS'),
('WF1311', '1311工作面采空区', '采空区', 1, 280.00, 380.00, -300.00, 'LJTKYJGS');

-- 应急救援项目示例数据
-- 1. 导航模块数据（NAV类型）
INSERT INTO `tb_mk_emergency_item` (`item_code`, `item_name`, `item_type`, `display_order`, `nav_icon`, `is_default`, `org_code`) VALUES
('NAV001', '人员', 'NAV', 1, 'icon-person', 0, 'LJTKYJGS'),
('NAV002', '车辆', 'NAV', 2, 'icon-vehicle', 0, 'LJTKYJGS'),
('NAV003', '安全监测', 'NAV', 3, 'icon-safety', 1, 'LJTKYJGS'),
('NAV004', '环境监测', 'NAV', 4, 'icon-environment', 0, 'LJTKYJGS'),
('NAV005', '应急仓库', 'NAV', 5, 'icon-warehouse', 0, 'LJTKYJGS');

-- 2. 人员列表项目（PERSON类型）
INSERT INTO `tb_mk_emergency_item` (`item_code`, `item_name`, `item_type`, `parent_code`, `display_order`, `item_status`, `location_info`, `org_code`) VALUES
('PERSON001', '张三', 'PERSON', 'NAV001', 1, 1, '1301工作面', 'LJTKYJGS'),
('PERSON002', '李四', 'PERSON', 'NAV001', 2, 1, '1302工作面', 'LJTKYJGS'),
('PERSON003', '王五', 'PERSON', 'NAV001', 3, 1, '调度室', 'LJTKYJGS');

-- 3. 车辆列表项目（VEHICLE类型）
INSERT INTO `tb_mk_emergency_item` (`item_code`, `item_name`, `item_type`, `parent_code`, `display_order`, `item_status`, `location_info`, `org_code`) VALUES
('VEHICLE001', '运输车001', 'VEHICLE', 'NAV002', 1, 1, '主运输巷', 'LJTKYJGS'),
('VEHICLE002', '救护车002', 'VEHICLE', 'NAV002', 2, 1, '地面停车场', 'LJTKYJGS');

-- 4. 安全监测列表项目（SAFETY类型，对应截图中的FB编号）
INSERT INTO `tb_mk_emergency_item` (`item_code`, `item_name`, `item_type`, `parent_code`, `display_order`, `item_status`, `location_info`, `org_code`) VALUES
('FB005', 'FB005监测点', 'SAFETY', 'NAV003', 1, 1, '1301工作面', 'LJTKYJGS'),
('FB026', 'FB026监测点', 'SAFETY', 'NAV003', 2, 1, '1302工作面', 'LJTKYJGS'),
('FB013', 'FB013监测点', 'SAFETY', 'NAV003', 3, 1, '1303工作面', 'LJTKYJGS'),
('FB027', 'FB027监测点', 'SAFETY', 'NAV003', 4, 1, '1304工作面', 'LJTKYJGS'),
('FB012', 'FB012监测点', 'SAFETY', 'NAV003', 5, 1, '1305工作面', 'LJTKYJGS'),
('FB003', 'FB003监测点', 'SAFETY', 'NAV003', 6, 1, '1307工作面', 'LJTKYJGS'),
('FB014', 'FB014监测点', 'SAFETY', 'NAV003', 7, 1, '1308工作面', 'LJTKYJGS'),
('FB008', 'FB008监测点', 'SAFETY', 'NAV003', 8, 1, '1309工作面', 'LJTKYJGS');

-- 5. 环境监测列表项目（ENVIRONMENT类型）
INSERT INTO `tb_mk_emergency_item` (`item_code`, `item_name`, `item_type`, `parent_code`, `display_order`, `item_status`, `location_info`, `org_code`) VALUES
('ENV001', '温度传感器001', 'ENVIRONMENT', 'NAV004', 1, 1, '1301工作面', 'LJTKYJGS'),
('ENV002', '湿度传感器002', 'ENVIRONMENT', 'NAV004', 2, 1, '1302工作面', 'LJTKYJGS'),
('ENV003', '风速传感器003', 'ENVIRONMENT', 'NAV004', 3, 1, '主通风巷', 'LJTKYJGS');

-- 6. 应急仓库列表项目（WAREHOUSE类型）
INSERT INTO `tb_mk_emergency_item` (`item_code`, `item_name`, `item_type`, `parent_code`, `display_order`, `item_status`, `location_info`, `org_code`) VALUES
('STORE001', '防毒面具', 'WAREHOUSE', 'NAV005', 1, 1, 'A区仓库', 'LJTKYJGS'),
('STORE002', '应急照明', 'WAREHOUSE', 'NAV005', 2, 1, 'B区仓库', 'LJTKYJGS'),
('STORE003', '救援绳索', 'WAREHOUSE', 'NAV005', 3, 1, 'C区仓库', 'LJTKYJGS');

-- 创建索引
CREATE INDEX idx_leader_entry_time ON tb_mk_emergency_leader(entry_time);
CREATE INDEX idx_stats_category ON tb_mk_emergency_personnel_stats(category_type, category_name);
CREATE INDEX idx_workface_coordinates ON tb_mk_emergency_workface(coordinate_x, coordinate_y, coordinate_z);
CREATE INDEX idx_item_type_parent ON tb_mk_emergency_item(item_type, parent_code);
CREATE INDEX idx_item_type_order ON tb_mk_emergency_item(item_type, display_order); 