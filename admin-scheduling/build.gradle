plugins {
    id 'java'
    id 'war'
}
group = 'com.yhd'
version = '0.0.1-SNAPSHOT'

dependencies {
    implementation group: 'com.squareup.okhttp3', name: 'okhttp'
    implementation group: 'commons-codec', name: 'commons-codec'
    implementation group: 'com.fasterxml.jackson.core', name: 'jackson-annotations'
    implementation group: 'com.baomidou', name: 'mybatis-plus-spring-boot3-starter'
    implementation group: 'commons-codec', name: 'commons-codec'
    implementation group: 'org.apache.commons', name: 'commons-lang3'
    implementation group: 'org.apache.commons', name: 'commons-text'
    implementation group: 'org.apache.commons', name: 'commons-pool2'
    implementation group: 'org.mapstruct', name: 'mapstruct'
    implementation 'com.opencsv:opencsv'
    implementation 'org.redisson:redisson'
    implementation 'com.baomidou:dynamic-datasource-spring-boot3-starter'
    implementation 'com.github.gavlyukovskiy:p6spy-spring-boot-starter'

    //SpringBoot
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation "org.springframework.boot:spring-boot-starter-oauth2-resource-server"

//    implementation 'cn.hutool:hutool-all'
    implementation 'commons-io:commons-io'
    implementation 'commons-codec:commons-codec'
    implementation 'org.apache.commons:commons-collections4'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'com.xuxueli:xxl-job-core'

    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.cloud:spring-cloud-loadbalancer'

    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor group: 'org.mapstruct', name: 'mapstruct-processor'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    providedRuntime 'org.springframework.boot:spring-boot-starter-tomcat'
    runtimeOnly 'com.mysql:mysql-connector-j'
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding'

    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok-mapstruct-binding'


}

