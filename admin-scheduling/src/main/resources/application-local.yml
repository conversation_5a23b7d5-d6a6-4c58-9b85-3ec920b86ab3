server:
    port: 9005
spring:
    datasource:
        dynamic:
            primary: mysql
            strict: false
            datasource:
                mysql:
                    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
                    url: ************************************************************************************************************
                    username: lsj_admin
                    password: Yhd@123456
                    hikari:
                        minimum-idle: 5
                        # 空闲连接存活最大时间，默认600000（10分钟）
                        idle-timeout: 180000
                        # 连接池最大连接数，默认是10
                        maximum-pool-size: 10
                        # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
                        is-auto-commit: true
                        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
                        max-lifetime: 30000
                        # 数据库连接超时时间,默认30秒，即30000
                        connection-timeout: 30000
                        connection-test-query: SELECT 1
            p6spy: true
    data:
        redis:
            host: **********
            port: 6379
            timeout: 60000
            password:
            lettuce:
                pool:
                    max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
                    max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
                    max-idle: 10      # 连接池中的最大空闲连接
                    min-idle: 5       # 连接池中的最小空闲连接
            database: 18
    application:
        name: admin-scheduling
    cache:
        redis:
            key-prefix: LSJ_XMC_SCHEDULING
    cloud:
        openfeign:
            okhttp:
                enabled: true

bms:
    url: http://localhost:9001/api

xxl:
    job:
        admin:
            addresses: http://**********:8061/xxl-job-admin
        accessToken:
        executor:
            appname: xxl-job-executor-lsj
            address:
            ip: **********
            port: 10001
            logpath: ../logs/jobhandler
            logretentiondays: 30

admin-api-url: http://localhost:9001/api/open

logging:
    level:
        web: trace
    file:
        path: ./logs/admin-scheduling
management:
    info:
        env:
            enabled: true
    endpoints:
        web:
            exposure:
                include:
                    - info
                    - health
decorator:
    datasource:
        p6spy:
            # 日志格式
            log-format: "\ntime:%(executionTime) || sql:%(sql)\n"
