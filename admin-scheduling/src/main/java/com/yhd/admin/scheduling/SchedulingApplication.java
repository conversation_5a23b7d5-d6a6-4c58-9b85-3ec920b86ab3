package com.yhd.admin.scheduling;

import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 */
@EnableTransactionManagement
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@SpringBootApplication
@ServletComponentScan
@EnableConfigurationProperties
@EnableFeignClients
public class SchedulingApplication {

    public static void main(String[] args) {
        SpringApplication bms = new SpringApplication(SchedulingApplication.class);
        bms.setBannerMode(Banner.Mode.OFF);
        bms.run(args);
    }
}
