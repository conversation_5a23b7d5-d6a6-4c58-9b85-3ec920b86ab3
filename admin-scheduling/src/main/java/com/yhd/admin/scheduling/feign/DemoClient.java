package com.yhd.admin.scheduling.feign;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.yhd.admin.scheduling.config.FeignClientConfig;

@FeignClient(name = "admin-api", url = "${admin-api-url}", configuration = FeignClientConfig.class)
public interface DemoClient {
    @PostMapping(value = "/feign/test", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    String test(@RequestBody Map<String, Object> map);
}
