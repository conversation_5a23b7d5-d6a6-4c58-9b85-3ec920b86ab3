package com.yhd.admin.scheduling.jobhandler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/** 值班管理数据 调度任务 Bean */
@Component
public class MesDutyXxlJob {
  private static Logger logger = LoggerFactory.getLogger(MesDutyXxlJob.class);

  // 明日润滑提醒
  private final String initLsjLubricationNotice = "/open/dutyAuto/initLsjLubricationNotice";
  // 润滑计划到期
  private final String initLsjLubricationExpire = "/open/dutyAuto/initLsjLubricationExpire";

  @Resource
  private RestTemplate restTemplate;

  @Value("${bms.url}")
  private String bmsUrl;

  @XxlJob("initLsjLubricationNotice")
  @Transactional(rollbackFor = Exception.class)
  public void initLsjLubricationNotice() throws Exception {
    StringBuffer httpurl = new StringBuffer(bmsUrl).append(initLsjLubricationNotice);

    HttpHeaders headers = new HttpHeaders();
    headers.add("Content-Type", "application/json");
    HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(null, headers);

    ResponseEntity<Object> responseEntity =
        restTemplate.postForEntity(httpurl.toString(), httpEntity, Object.class);

    XxlJobHelper.log(responseEntity.getBody().toString());
    XxlJobHelper.handleSuccess(responseEntity.getBody().toString());
  }

  @XxlJob("initLsjLubricationExpire")
  @Transactional(rollbackFor = Exception.class)
  public void initLsjLubricationExpire() throws Exception {
    StringBuffer httpurl = new StringBuffer(bmsUrl).append(initLsjLubricationExpire);

    HttpHeaders headers = new HttpHeaders();
    headers.add("Content-Type", "application/json");
    HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(null, headers);

    ResponseEntity<Object> responseEntity =
        restTemplate.postForEntity(httpurl.toString(), httpEntity, Object.class);

    XxlJobHelper.log(responseEntity.getBody().toString());
    XxlJobHelper.handleSuccess(responseEntity.getBody().toString());
  }
}
