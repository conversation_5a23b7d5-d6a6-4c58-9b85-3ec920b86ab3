-- 综合决策大屏模块数据库表结构
-- 创建时间：2025-08-01
-- 
-- 注意：实体类命名规范
-- 数据库表名：tb_mk_xxx_xxx (带tb前缀)
-- Java实体类名：MkXxxXxx (去掉tb前缀，驼峰命名)
-- 
-- 示例：
-- 表名：tb_mk_production_stats
-- 实体类：MkProductionStats
--

-- 1. 生产统计表
CREATE TABLE `tb_mk_production_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '统计编码',
    `stats_date` date NOT NULL COMMENT '统计日期',
    `daily_production` decimal(10,2) DEFAULT NULL COMMENT '日产量(吨)',
    `daily_sales` decimal(10,2) DEFAULT NULL COMMENT '日销量(吨)',
    `monthly_production` decimal(10,2) DEFAULT NULL COMMENT '月累计产量(万吨)',
    `monthly_sales` decimal(10,2) DEFAULT NULL COMMENT '月累计销量(万吨)',
    `yearly_production` decimal(10,2) DEFAULT NULL COMMENT '年累计产量(万吨)',
    `yearly_sales` decimal(10,2) DEFAULT NULL COMMENT '年累计销量(万吨)',
    `safety_days` int DEFAULT NULL COMMENT '安全生产天数',
    `design_capacity` decimal(10,2) DEFAULT NULL COMMENT '设计产能(万吨)',
    `main_coal_seam` varchar(100) DEFAULT NULL COMMENT '主采煤层',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='综合决策生产统计表';

-- 2. 传感器统计表
CREATE TABLE `tb_mk_sensor_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '统计编码',
    `stats_date` date NOT NULL COMMENT '统计日期',
    `co_sensor_count` int DEFAULT NULL COMMENT 'CO传感器数量',
    `methane_sensor_count` int DEFAULT NULL COMMENT '甲烷传感器数量',
    `total_sensor_count` int DEFAULT NULL COMMENT '接入传感器总数',
    `trend_data` json DEFAULT NULL COMMENT '趋势数据',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='综合决策传感器统计表';

-- 3. 风险管控统计表
CREATE TABLE `tb_mk_risk_control_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '统计编码',
    `stats_date` date NOT NULL COMMENT '统计日期',
    `major_risk_count` int DEFAULT NULL COMMENT '重大风险数量',
    `significant_risk_count` int DEFAULT NULL COMMENT '较大风险数量',
    `general_risk_count` int DEFAULT NULL COMMENT '一般风险数量',
    `low_risk_count` int DEFAULT NULL COMMENT '低风险数量',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='综合决策风险管控统计表';

-- 4. 隐患管控统计表
CREATE TABLE `tb_mk_hazard_control_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '统计编码',
    `stats_date` date NOT NULL COMMENT '统计日期',
    `general_hazard_count` int DEFAULT NULL COMMENT '一般隐患数量',
    `major_hazard_count` int DEFAULT NULL COMMENT '重大隐患数量',
    `trend_data` json DEFAULT NULL COMMENT '近7日趋势数据',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='综合决策隐患管控统计表';

-- 5. 人员统计表
CREATE TABLE `tb_mk_personnel_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '统计编码',
    `stats_date` date NOT NULL COMMENT '统计日期',
    `total_employee_count` int DEFAULT NULL COMMENT '员工总人数',
    `underground_leader_count` int DEFAULT NULL COMMENT '井下领导人数',
    `underground_worker_count` int DEFAULT NULL COMMENT '井下人数',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='综合决策人员统计表';

-- 5. 智能工作面统计表
CREATE TABLE `tb_mk_intelligent_workface_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '统计编码',
    `stats_date` date NOT NULL COMMENT '统计日期',
    `memory_cutting_rate` decimal(5,2) DEFAULT NULL COMMENT '记忆截割率(%)',
    `startup_rate` decimal(5,2) DEFAULT NULL COMMENT '开机率(%)',
    `auto_following_rate` decimal(5,2) DEFAULT NULL COMMENT '自动跟机率(%)',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='综合决策智能工作面统计表';

-- 6. 水文监测统计表
CREATE TABLE `tb_mk_hydrology_monitor_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '统计编码',
    `stats_date` date NOT NULL COMMENT '统计日期',
    `mining_area_pump_flow` decimal(8,2) DEFAULT NULL COMMENT '采区水泵瞬时流量',
    `central_pump_room_flow` decimal(8,2) DEFAULT NULL COMMENT '中央水泵房实时流量',
    `coal_auxiliary_tunnel_flow` decimal(8,2) DEFAULT NULL COMMENT '二煤集辅二联巷瞬时流量',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='综合决策水文监测统计表';

-- 7. 设备统计表
CREATE TABLE `tb_mk_equipment_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '统计编码',
    `stats_date` date NOT NULL COMMENT '统计日期',
    `online_equipment_count` int DEFAULT NULL COMMENT '设备开机数量',
    `total_equipment_count` int DEFAULT NULL COMMENT '设备总数量',
    `startup_rate` decimal(5,2) DEFAULT NULL COMMENT '开机率(%)',
    `fault_equipment_count` int DEFAULT NULL COMMENT '故障设备数量',
    `fault_rate` decimal(5,2) DEFAULT NULL COMMENT '故障率(%)',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='综合决策设备统计表';

-- 8. 能耗统计表
CREATE TABLE `tb_mk_energy_consumption_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '统计编码',
    `stats_date` date NOT NULL COMMENT '统计日期',
    `equipment_type` varchar(100) DEFAULT NULL COMMENT '设备类型',
    `equipment_name` varchar(200) DEFAULT NULL COMMENT '设备名称',
    `consumption_value` decimal(10,2) DEFAULT NULL COMMENT '能耗值',
    `consumption_percentage` decimal(5,2) DEFAULT NULL COMMENT '能耗占比(%)',
    `color_code` varchar(20) DEFAULT NULL COMMENT '颜色代码',
    `sort_order` int DEFAULT 0 COMMENT '排序',
    `org_code` varchar(50) DEFAULT NULL COMMENT '组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`),
    KEY `idx_equipment_type` (`equipment_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='综合决策能耗统计表';

-- 插入示例数据

-- 生产统计示例数据
INSERT INTO `tb_mk_production_stats` (`stats_code`, `stats_date`, `daily_production`, `daily_sales`, `monthly_production`, `monthly_sales`, `yearly_production`, `yearly_sales`, `safety_days`, `design_capacity`, `main_coal_seam`, `org_code`) VALUES
('PROD001', '2025-08-01', 5616.10, 3903.88, 19.73, 18.95, 109.11, 108.33, 1632, 300.00, '2#煤、3#煤', 'LJTKYJGS');

-- 传感器统计示例数据
INSERT INTO `tb_mk_sensor_stats` (`stats_code`, `stats_date`, `co_sensor_count`, `methane_sensor_count`, `total_sensor_count`, `org_code`) VALUES
('SENSOR001', '2025-08-01', 22, 25, 175, 'LJTKYJGS');

-- 风险管控统计示例数据
INSERT INTO `tb_mk_risk_control_stats` (`stats_code`, `stats_date`, `major_risk_count`, `significant_risk_count`, `general_risk_count`, `low_risk_count`, `org_code`) VALUES
('RISK001', '2025-08-01', 15, 0, 0, 0, 'LJTKYJGS');

-- 隐患管控统计示例数据
INSERT INTO `tb_mk_hazard_control_stats` (`stats_code`, `stats_date`, `general_hazard_count`, `major_hazard_count`, `org_code`) VALUES
('HAZARD001', '2025-08-01', 15, 0, 'LJTKYJGS');

-- 人员统计示例数据
INSERT INTO `tb_mk_personnel_stats` (`stats_code`, `stats_date`, `total_employee_count`, `underground_leader_count`, `underground_worker_count`, `org_code`) VALUES
('PERSONNEL001', '2025-08-01', 498, 2, 193, 'LJTKYJGS');

-- 智能工作面统计示例数据
INSERT INTO `tb_mk_intelligent_workface_stats` (`stats_code`, `stats_date`, `memory_cutting_rate`, `startup_rate`, `auto_following_rate`, `org_code`) VALUES
('INTEL001', '2025-08-01', 84.00, 55.00, 84.00, 'LJTKYJGS');

-- 水文监测统计示例数据
INSERT INTO `tb_mk_hydrology_monitor_stats` (`stats_code`, `stats_date`, `mining_area_pump_flow`, `central_pump_room_flow`, `coal_auxiliary_tunnel_flow`, `org_code`) VALUES
('HYDRO001', '2025-08-01', 30.31, 0.03, 36.65, 'LJTKYJGS');

-- 设备统计示例数据
INSERT INTO `tb_mk_equipment_stats` (`stats_code`, `stats_date`, `online_equipment_count`, `total_equipment_count`, `startup_rate`, `fault_equipment_count`, `fault_rate`, `org_code`) VALUES
('EQUIP001', '2025-08-01', 89, 120, 74.00, 0, 0.00, 'LJTKYJGS');

-- 能耗统计示例数据
INSERT INTO `tb_mk_energy_consumption_stats` (`stats_code`, `stats_date`, `equipment_type`, `equipment_name`, `consumption_value`, `consumption_percentage`, `color_code`, `sort_order`, `org_code`) VALUES
('ENERGY001', '2025-08-01', '主要动力系统', '主要动力系统', 1100.00, 11.00, '#FF6B6B', 1, 'LJTKYJGS'),
('ENERGY002', '2025-08-01', '主排水系统', '主排水系统', 700.00, 7.00, '#4ECDC4', 2, 'LJTKYJGS'),
('ENERGY003', '2025-08-01', '通风系统', '通风系统', 1000.00, 10.00, '#45B7D1', 3, 'LJTKYJGS'),
('ENERGY004', '2025-08-01', '水泵监控系统', '水泵监控系统', 4500.00, 45.00, '#96CEB4', 4, 'LJTKYJGS'),
('ENERGY005', '2025-08-01', '其他监控系统', '其他监控系统', 1200.00, 12.00, '#FECA57', 5, 'LJTKYJGS'),
('ENERGY006', '2025-08-01', '应急井管监控系统', '应急井管监控系统', 500.00, 5.00, '#FF9FF3', 6, 'LJTKYJGS'),
('ENERGY007', '2025-08-01', '井下通信监控系统', '井下通信监控系统', 0.00, 0.00, '#54A0FF', 7, 'LJTKYJGS');

-- 创建索引
CREATE INDEX idx_production_date ON tb_mk_production_stats(stats_date);
CREATE INDEX idx_sensor_date ON tb_mk_sensor_stats(stats_date);
CREATE INDEX idx_risk_date ON tb_mk_risk_control_stats(stats_date);
CREATE INDEX idx_hazard_date ON tb_mk_hazard_control_stats(stats_date);
CREATE INDEX idx_intel_date ON tb_mk_intelligent_workface_stats(stats_date);
CREATE INDEX idx_hydro_date ON tb_mk_hydrology_monitor_stats(stats_date);
CREATE INDEX idx_equip_date ON tb_mk_equipment_stats(stats_date);
CREATE INDEX idx_energy_date ON tb_mk_energy_consumption_stats(stats_date); 