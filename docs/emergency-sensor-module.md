# 应急救援监测信息表(传感器表)模块

## 概述

应急救援监测信息表模块是煤矿应急救援系统的核心组件，用于管理煤矿井下各类传感器的监测数据，支持层级化管理和实时监控。

## 功能特性

### 1. 层级化管理
- **矿井级别**：煤矿总监控中心
- **采区级别**：各采区监控节点
- **工作面级别**：具体工作面监控
- **监测点级别**：实际传感器设备

### 2. 传感器类型支持
- 氧气传感器
- 二氧化碳传感器
- 甲烷传感器
- 温度传感器
- 湿度传感器
- 其他气体传感器

### 3. 实时监控功能
- 传感器状态监控（正常/异常/离线）
- 阈值告警检测
- 数据实时更新
- 异常传感器快速定位

### 4. 统计分析
- 各状态传感器数量统计
- 各类型传感器分布统计
- 监控概览数据
- 正常率计算

## 技术架构

### 技术栈
- **框架**：Spring Boot + MyBatis-Plus
- **数据库**：MySQL 8.0+
- **对象映射**：MapStruct
- **日志**：SLF4J + Logback

### 代码结构
```
admin-api/src/main/java/com/yhd/admin/api/domain/emergency/
├── entity/
│   └── MkEmergencySensor.java              # 实体类
├── dto/
│   └── MkEmergencySensorDTO.java           # 数据传输对象
├── vo/
│   └── MkEmergencySensorVO.java            # 视图对象
├── query/
│   └── MkEmergencySensorParam.java         # 查询参数
└── convert/
    └── MkEmergencySensorConvert.java       # 对象转换器

admin-api/src/main/java/com/yhd/admin/api/dao/emergency/
└── MkEmergencySensorDao.java               # 数据访问层

admin-api/src/main/java/com/yhd/admin/api/service/emergency/
├── MkEmergencySensorService.java           # 服务接口
└── impl/
    └── MkEmergencySensorServiceImpl.java   # 服务实现

admin-api/src/main/java/com/yhd/admin/api/controller/emergency/
└── MkEmergencySensorController.java        # 控制器
```

## 数据库设计

### 表结构
```sql
CREATE TABLE `tb_mk_emergency_sensor` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sensor_code` varchar(50) NOT NULL COMMENT '传感器编码',
  `sensor_name` varchar(200) NOT NULL COMMENT '传感器名称',
  `sensor_type` varchar(50) NOT NULL COMMENT '传感器类型',
  `location` varchar(200) NOT NULL COMMENT '安装位置',
  `work_face` varchar(100) DEFAULT NULL COMMENT '关联工作面',
  `tunnel` varchar(100) DEFAULT NULL COMMENT '关联巷道',
  `parent_id` int DEFAULT NULL COMMENT '父级传感器ID',
  `level_type` varchar(50) DEFAULT NULL COMMENT '层级类型',
  `level_code` varchar(100) DEFAULT NULL COMMENT '层级编码',
  `level_name` varchar(200) DEFAULT NULL COMMENT '层级名称',
  `level_path` varchar(500) DEFAULT NULL COMMENT '层级路径',
  `sort_order` int DEFAULT 0 COMMENT '同级排序号',
  `is_leaf` tinyint(1) DEFAULT 1 COMMENT '是否叶子节点',
  `status` int NOT NULL DEFAULT 1 COMMENT '传感器状态',
  `org_code` varchar(255) NOT NULL COMMENT '组织编码',
  `current_value` decimal(10,2) DEFAULT NULL COMMENT '当前值',
  `threshold_min` decimal(10,2) DEFAULT NULL COMMENT '最小阈值',
  `threshold_max` decimal(10,2) DEFAULT NULL COMMENT '最大阈值',
  `unit` varchar(20) DEFAULT NULL COMMENT '测量单位',
  `description` text COMMENT '传感器描述',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sensor_code` (`sensor_code`),
  KEY `idx_sensor_type` (`sensor_type`),
  KEY `idx_org_code` (`org_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level_type` (`level_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应急救援监测信息表';
```

### 层级关系设计
- 使用 `parent_id` 建立父子关系
- 使用 `level_path` 存储完整层级路径
- 使用 `is_leaf` 标识叶子节点（实际传感器）
- 使用 `sort_order` 控制同级排序

## API接口

### 基础CRUD操作
- `POST /emergency/sensor/pagingQuery` - 分页查询
- `POST /emergency/sensor/add` - 新增传感器
- `POST /emergency/sensor/modify` - 修改传感器
- `GET /emergency/sensor/getCurrentDetail` - 获取详情
- `POST /emergency/sensor/removeBatch` - 批量删除

### 树形结构操作
- `GET /emergency/sensor/getSensorTree` - 获取传感器树
- `GET /emergency/sensor/getByLevelType` - 按层级类型查询

### 业务查询操作
- `GET /emergency/sensor/getBySensorCode` - 按编码查询
- `GET /emergency/sensor/getAbnormalSensors` - 查询异常传感器
- `GET /emergency/sensor/getOutOfRangeSensors` - 查询超阈值传感器
- `GET /emergency/sensor/getByWorkFace` - 按工作面查询
- `GET /emergency/sensor/getByTunnel` - 按巷道查询
- `GET /emergency/sensor/getBySensorType` - 按类型查询

### 状态管理操作
- `POST /emergency/sensor/updateStatus` - 更新状态
- `POST /emergency/sensor/batchUpdateStatus` - 批量更新状态
- `POST /emergency/sensor/updateCurrentValue` - 更新当前值
- `POST /emergency/sensor/batchUpdateCurrentValue` - 批量更新当前值

### 统计分析操作
- `GET /emergency/sensor/countByStatus` - 按状态统计
- `GET /emergency/sensor/countBySensorType` - 按类型统计
- `GET /emergency/sensor/getMonitorOverview` - 监控概览

### 工具操作
- `GET /emergency/sensor/checkSensorCodeUnique` - 校验编码唯一性

## 使用示例

### 1. 查询传感器树形结构
```java
@Resource
private MkEmergencySensorService sensorService;

// 获取完整树形结构
List<MkEmergencySensorDTO> tree = sensorService.getSensorTree("ORG001", null);

// 获取指定节点的子树
List<MkEmergencySensorDTO> subTree = sensorService.getSensorTree("ORG001", 1);
```

### 2. 查询异常传感器
```java
// 查询所有异常传感器
List<MkEmergencySensorDTO> abnormalSensors = sensorService.getAbnormalSensors("ORG001");

// 查询超出阈值的传感器
List<MkEmergencySensorDTO> outOfRangeSensors = sensorService.getOutOfRangeSensors("ORG001");
```

### 3. 更新传感器数据
```java
// 更新单个传感器当前值
Boolean result = sensorService.updateCurrentValue("SENSOR_001", new BigDecimal("25.5"));

// 批量更新传感器数据
Map<String, BigDecimal> dataMap = new HashMap<>();
dataMap.put("SENSOR_001", new BigDecimal("25.5"));
dataMap.put("SENSOR_002", new BigDecimal("0.3"));
Boolean batchResult = sensorService.batchUpdateCurrentValue(dataMap);
```

### 4. 获取统计数据
```java
// 获取监控概览
Map<String, Object> overview = sensorService.getMonitorOverview("ORG001");

// 获取各状态统计
Map<String, Long> statusCount = sensorService.countByStatus("ORG001");

// 获取各类型统计
Map<String, Long> typeCount = sensorService.countBySensorType("ORG001");
```

## 注意事项

1. **传感器编码唯一性**：系统会自动校验传感器编码的唯一性
2. **层级关系维护**：删除节点时会检查是否存在子节点
3. **阈值告警**：更新传感器当前值时会自动判断是否超出阈值并更新状态
4. **事务管理**：所有写操作都使用事务保证数据一致性
5. **MyBatis-Plus**：使用MyBatis-Plus的QueryWrapper进行复杂查询，避免手写SQL

## 扩展建议

1. **缓存优化**：对频繁查询的树形结构数据添加Redis缓存
2. **实时推送**：集成WebSocket实现传感器数据实时推送
3. **历史数据**：添加传感器历史数据表记录数值变化
4. **告警规则**：扩展更复杂的告警规则配置
5. **数据可视化**：集成图表组件展示传感器数据趋势
