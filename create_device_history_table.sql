-- 创建设备历史数据表
CREATE TABLE `tb_mk_emergency_device_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '设备编码',
  `device_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '设备名称',
  `current_value` decimal(10,2) NOT NULL COMMENT '实时值',
  `unit` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '测量单位',
  `status` int NOT NULL COMMENT '设备状态：1-正常，2-异常，3-离线',
  `org_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '组织编码',
  `record_time` datetime NOT NULL COMMENT '记录时间',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_device_code` (`device_code`) USING BTREE COMMENT '设备编码索引',
  KEY `idx_record_time` (`record_time`) USING BTREE COMMENT '记录时间索引',
  KEY `idx_org_code` (`org_code`) USING BTREE COMMENT '组织编码索引',
  KEY `idx_device_code_time` (`device_code`, `record_time`) USING BTREE COMMENT '设备编码和时间联合索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='应急救援设备历史数据表';

-- 插入示例历史数据
INSERT INTO `tb_mk_emergency_device_history` 
(`device_code`, `device_name`, `current_value`, `unit`, `status`, `org_code`, `record_time`) 
VALUES
-- DEV001 排风机数据（转速 RPM）
('DEV001', '矿井排风机001', 1200.50, 'RPM', 1, 'LJTKYJGS', '2024-01-20 08:00:00'),
('DEV001', '矿井排风机001', 1205.20, 'RPM', 1, 'LJTKYJGS', '2024-01-20 08:30:00'),
('DEV001', '矿井排风机001', 1198.80, 'RPM', 1, 'LJTKYJGS', '2024-01-20 09:00:00'),
('DEV001', '矿井排风机001', 1210.30, 'RPM', 1, 'LJTKYJGS', '2024-01-20 09:30:00'),
('DEV001', '矿井排风机001', 1195.60, 'RPM', 1, 'LJTKYJGS', '2024-01-20 10:00:00'),
('DEV001', '矿井排风机001', 1208.90, 'RPM', 1, 'LJTKYJGS', '2024-01-20 10:30:00'),
('DEV001', '矿井排风机001', 1202.40, 'RPM', 1, 'LJTKYJGS', '2024-01-20 11:00:00'),
('DEV001', '矿井排风机001', 1215.70, 'RPM', 1, 'LJTKYJGS', '2024-01-20 11:30:00'),

-- DEV003 应急照明系统数据（电压 V）
('DEV003', '应急照明系统', 220.5, 'V', 1, 'LJTKYJGS', '2024-01-20 08:00:00'),
('DEV003', '应急照明系统', 219.8, 'V', 1, 'LJTKYJGS', '2024-01-20 08:30:00'),
('DEV003', '应急照明系统', 221.2, 'V', 1, 'LJTKYJGS', '2024-01-20 09:00:00'),
('DEV003', '应急照明系统', 220.9, 'V', 1, 'LJTKYJGS', '2024-01-20 09:30:00'),
('DEV003', '应急照明系统', 218.5, 'V', 2, 'LJTKYJGS', '2024-01-20 10:00:00'),
('DEV003', '应急照明系统', 217.3, 'V', 2, 'LJTKYJGS', '2024-01-20 10:30:00'),
('DEV003', '应急照明系统', 220.1, 'V', 1, 'LJTKYJGS', '2024-01-20 11:00:00'),
('DEV003', '应急照明系统', 221.8, 'V', 1, 'LJTKYJGS', '2024-01-20 11:30:00'); 