-- 创建应急救援周边信息表
CREATE TABLE `tb_mk_emergency_surrounding` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `item_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目编码',
  `item_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目名称',
  `item_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目类型：人员、分站、视频广播、电话安监',
  `location` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '位置信息',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-异常，3-离线',
  `org_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '组织编码',
  `coordinate_x` decimal(10,2) DEFAULT NULL COMMENT 'X坐标',
  `coordinate_y` decimal(10,2) DEFAULT NULL COMMENT 'Y坐标',
  `coordinate_z` decimal(10,2) DEFAULT NULL COMMENT 'Z坐标',
  `contact_person` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联系电话',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '描述信息',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_item_code` (`item_code`) USING BTREE COMMENT '项目编码唯一索引',
  KEY `idx_item_type` (`item_type`) USING BTREE COMMENT '项目类型索引',
  KEY `idx_org_code` (`org_code`) USING BTREE COMMENT '组织编码索引',
  KEY `idx_location` (`location`) USING BTREE COMMENT '位置索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='应急救援周边信息表';

-- 插入示例数据
INSERT INTO `tb_mk_emergency_surrounding` 
(`item_code`, `item_name`, `item_type`, `location`, `status`, `org_code`, `coordinate_x`, `coordinate_y`, `coordinate_z`, `contact_person`, `contact_phone`, `description`) 
VALUES
('PERSON001', '张三', '人员', '1301工作面', 1, 'LJTKYJGS', 120.50, 36.20, 100.00, '张三', '13812345678', '1301工作面班长'),
('PERSON002', '李四', '人员', '1305工作面', 1, 'LJTKYJGS', 121.20, 36.25, 105.00, '李四', '13987654321', '1305工作面安全员'),
('STATION001', '1号分站', '分站', '2#辅运大巷', 1, 'LJTKYJGS', 119.80, 36.15, 95.00, '王五', '13765432198', '主要负责通风监测'),
('STATION002', '2号分站', '分站', '3#输送大巷', 2, 'LJTKYJGS', 122.10, 36.30, 110.00, '赵六', '13654321987', '负责瓦斯监测，目前设备异常'),
('VIDEO001', '视频监控点A', '视频广播', '1301工作面入口', 1, 'LJTKYJGS', 120.30, 36.18, 98.00, '技术部', '13543219876', '24小时视频监控'),
('VIDEO002', '视频监控点B', '视频广播', '主斜井口', 1, 'LJTKYJGS', 118.90, 36.12, 92.00, '技术部', '13543219876', '井口安全监控'),
('PHONE001', '应急电话001', '电话安监', '调度室', 1, 'LJTKYJGS', 120.00, 36.22, 102.00, '调度员', '13432198765', '24小时应急调度电话'),
('PHONE002', '应急电话002', '电话安监', '安全办公室', 1, 'LJTKYJGS', 120.15, 36.24, 103.00, '安全员', '13321987654', '安全事故报告专线'); 