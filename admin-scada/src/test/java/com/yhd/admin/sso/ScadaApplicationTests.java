package com.yhd.admin.sso;

import static org.springframework.boot.test.context.SpringBootTest.WebEnvironment.RANDOM_PORT;

import org.eclipse.milo.opcua.sdk.client.OpcUaClient;
import org.eclipse.milo.opcua.sdk.client.api.identity.AnonymousProvider;
import org.eclipse.milo.opcua.stack.core.UaException;
import org.eclipse.milo.opcua.stack.core.types.builtin.LocalizedText;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.yhd.admin.scada.ScadaApplication;

import lombok.extern.slf4j.Slf4j;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = RANDOM_PORT, classes = ScadaApplication.class)
@Slf4j
@ActiveProfiles("prd")
@AutoConfigureMockMvc
public class ScadaApplicationTests {

    @Test
    void testDo() throws UaException {
        if (log.isInfoEnabled()) {
            log.info("opc.tcp://***********:49320 OpcUaHelper Start");
        }
        OpcUaClient opcUaClient =
            OpcUaClient.create("opc.tcp://***********:49320", endpoints -> endpoints.stream().findFirst(),
                configBuilder -> configBuilder.setApplicationName(LocalizedText.english("eclipse milo opc-ua client"))
                    .setApplicationUri("urn:eclipse:milo:examples:client").setIdentityProvider(new AnonymousProvider())
                    .build());
        log.info("opc.tcp://***********:49320 OpcUaHelper End,{}", opcUaClient.getConfig());
    }

}
