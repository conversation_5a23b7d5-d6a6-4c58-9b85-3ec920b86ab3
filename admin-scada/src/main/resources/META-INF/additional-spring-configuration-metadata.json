{"properties": [{"name": "oss.server.endpoint", "type": "java.lang.String", "description": "Description for oss.server.endpoint."}, {"name": "oss.server.access_key", "type": "java.lang.String", "description": "Description for oss.server.access_key."}, {"name": "oss.server.secret_key", "type": "java.lang.String", "description": "Description for oss.server.secret_key."}, {"name": "oss.server.port", "type": "java.lang.Integer", "description": "Description for oss.server.port."}]}