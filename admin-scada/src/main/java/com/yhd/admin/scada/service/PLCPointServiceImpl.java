package com.yhd.admin.scada.service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.scada.dao.PLCPointDao;
import com.yhd.admin.scada.domain.Enum.RedisKeyEnum;
import com.yhd.admin.scada.domain.convert.PLCPointConvert;
import com.yhd.admin.scada.domain.dto.PLCPointDTO;
import com.yhd.admin.scada.domain.entity.MesPlcPoint;

/**
 * <AUTHOR>
 * @version 1.0 @Description TODO
 * @createTime 2021/7/22 15:06
 */
@Service
public class PLCPointServiceImpl extends ServiceImpl<PLCPointDao, MesPlcPoint> implements PLCPointService {

    private final PLCPointConvert convert;

    private final PLCKafkaService kafkaService;

    private final PLCWSService wsService;

    private final PLCFilterService filterService;

    private final RedisTemplate<String, PLCPointDTO> redisTemplate;

    public PLCPointServiceImpl(PLCPointConvert convert, PLCKafkaService kafkaService, PLCWSService wsService,
        PLCFilterService filterService, RedisTemplate<String, PLCPointDTO> redisTemplate) {
        this.convert = convert;
        this.kafkaService = kafkaService;
        this.wsService = wsService;
        this.filterService = filterService;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public List<PLCPointDTO> queryList(String connect, List<String> sysList) {
        LambdaQueryChainWrapper<MesPlcPoint> chainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        chainWrapper.eq(MesPlcPoint::getEnable, Boolean.TRUE).eq(MesPlcPoint::getConnect, connect)
            .in(!CollectionUtils.isEmpty(sysList), MesPlcPoint::getSys, sysList).orderByAsc(MesPlcPoint::getPriority);
        List<PLCPointDTO> pointDTOS = chainWrapper.list().stream().map(convert::toDTO).collect(Collectors.toList());

        Map<String, PLCPointDTO> redisMap = new ConcurrentHashMap<>(pointDTOS.size());

        pointDTOS.forEach(o -> {
            o.setKafkaTopics(kafkaService.queryList(o.getId()));
            o.setWsTopics(wsService.queryList(o.getId()));
            o.setFilters(filterService.queryList(o.getId()));
            redisMap.put(String.format(RedisKeyEnum.PLC.getKey(), o.getKwpAddress()), o);
        });
        redisTemplate.opsForValue().multiSet(redisMap);
        return pointDTOS;
    }
}
