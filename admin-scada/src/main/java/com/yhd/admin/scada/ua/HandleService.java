package com.yhd.admin.scada.ua;

import org.eclipse.milo.opcua.sdk.client.subscriptions.ManagedDataItem;
import org.eclipse.milo.opcua.stack.core.types.builtin.DataValue;

import com.yhd.admin.scada.domain.dto.PLCPointDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/6/2 10:58
 */

@FunctionalInterface
public interface HandleService {
    void handleMonitored(ManagedDataItem item, DataValue value, PLCPointDTO point);
}
