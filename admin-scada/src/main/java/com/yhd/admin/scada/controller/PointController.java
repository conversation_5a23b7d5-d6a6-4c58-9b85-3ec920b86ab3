package com.yhd.admin.scada.controller;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhd.admin.scada.boot.PiDaiChenCoalWashOPCUARunner;
import com.yhd.admin.scada.domain.param.PlcCtrlParam;
import org.eclipse.milo.opcua.sdk.client.OpcUaClient;
import org.eclipse.milo.opcua.stack.core.types.builtin.*;
import org.eclipse.milo.opcua.stack.core.types.builtin.unsigned.UInteger;
import org.eclipse.milo.opcua.stack.core.types.enumerated.TimestampsToReturn;
import org.eclipse.milo.opcua.stack.core.types.structured.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yhd.admin.common.domain.RespJson;

/**
 * OPC UA 读取实时点位数据
 *
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/6/5 11:14
 */

@RestController
@RequestMapping("/point/rt")
public class PointController {

//    private final OpcUaClient opcUaClient;
//    private final PiDaiChenCoalWashOPCUARunner piDaiChenCoalWashOPCUARunner;
//
//    public PointController(
//        @Qualifier("piDaiChenOpcUaClient")OpcUaClient opcUaClient, PiDaiChenCoalWashOPCUARunner piDaiChenCoalWashOPCUARunner) {
//        this.opcUaClient = opcUaClient;
//        this.piDaiChenCoalWashOPCUARunner = piDaiChenCoalWashOPCUARunner;
//    }
//
//
//    @PostMapping(
//        value = "/restart",
//        consumes = MediaType.APPLICATION_JSON_VALUE,
//        produces = MediaType.APPLICATION_JSON_VALUE)
//    public RespJson<Boolean> readNodeValues() {
//        try {
//            opcUaClient.getSubscriptionManager().clearSubscriptions();
//            piDaiChenCoalWashOPCUARunner.run();
//            return RespJson.success(true);
//        } catch (Exception e) {
//            return RespJson.failure(e.toString());
//        }
//    }
//
//    @PostMapping(value = "/read")
//    public RespJson<?> read(@RequestBody PlcCtrlParam param) {
//        String point = param.getPoint();
//        // 使用 String.split() 将逗号分隔的字符串转换为数组
//        String[] array = point.split(",");
//        // 将数组转换为 List
//        List<String> list = new ArrayList<>(Arrays.asList(array));
//
//        JSONArray resultJson = new JSONArray();
//        try {
//            for (String item : list) {
//                List<HistoryReadValueId> historyReadValueIdList = new ArrayList<>();
//                NodeId nodeId = NodeId.parse(item);
//                ZoneId zone = ZoneId.systemDefault();
//                // 循环查询机制（最多重试12次），查询半天的时间
//                ZonedDateTime[] timeRange = calculateTimeRange(zone);
//                int retryCount = 0;
//                boolean dataFound = false;
//                while (retryCount < 12 && !dataFound) {
//                    //历史查询参数 时间范围
//                    HistoryReadDetails details = new ReadRawModifiedDetails(false, new DateTime(timeRange[0].toInstant()), new DateTime(timeRange[1].toInstant()), UInteger.valueOf(0), true);
//                    //点位地址
//                    HistoryReadValueId historyReadValueId = new HistoryReadValueId(nodeId,null,QualifiedName.NULL_VALUE,ByteString.NULL_VALUE);
//                    historyReadValueIdList.add(historyReadValueId);
//                    //查询历史数据
//                    HistoryReadResult[] readResult = opcUaClient.historyRead(details, TimestampsToReturn.Both, true, historyReadValueIdList).get().getResults();
//
//                    for (HistoryReadResult result : readResult) {
//                        //返回结果成功，处理数据
//                        if (result.getStatusCode().isGood()) {
//                            //解码
//                            HistoryData data = (HistoryData) result.getHistoryData().decode(opcUaClient.getStaticSerializationContext());
//                            DataValue[] dataValues = data.getDataValues();
//                            if (dataValues.length > 0) {
//                                //有数据，查询结束，不循环
//                                dataFound = true;
//                                //初始化最新时间和对应值
//                                DataValue latestDataValue = null;
//                                for (DataValue dataValue : dataValues) {
//                                    if (latestDataValue == null || dataValue.getSourceTime().getJavaTime() > latestDataValue.getSourceTime().getJavaTime()) {
//                                        latestDataValue = dataValue;
//                                    }
//                                }
//                                //返回name，value，timestamp
//                                if (latestDataValue != null) {
//                                    JSONObject points = new JSONObject();
//                                    points.put("name", item);
//                                    points.put("value", latestDataValue.getValue().getValue());
//                                    points.put("timestamp", latestDataValue.getSourceTime());
//                                    resultJson.add(points);
//                                }
//                            }
//                        } else {
//                            // 向前推移1小时窗口
//                            timeRange[1] = timeRange[0];
//                            timeRange[0] = timeRange[0].minusHours(1);
//                            retryCount++;
//                        }
//                    }
//                }
//            }
//        } catch (Exception e) {
//            return RespJson.failure(e.toString());
//        }
//        return RespJson.success(resultJson);
//    }
//
//    private ZonedDateTime[] calculateTimeRange(ZoneId zone) {
//        ZonedDateTime now = ZonedDateTime.now(zone);
//        boolean isMorning = now.getHour() < 12;
//
//        // 设置查询结束时间
//        ZonedDateTime endTime = isMorning ? now.withHour(0).withMinute(0).withSecond(0).minusDays(1) : now.withHour(12).withMinute(0).withSecond(0);
//
//        // 开始时间 = 结束时间 - 1小时
//        ZonedDateTime startTime = endTime.minusHours(1);
//
//        return new ZonedDateTime[]{startTime, endTime};
//    }
}
