package com.yhd.admin.scada.boot;

import org.eclipse.milo.opcua.sdk.client.OpcUaClient;
import org.eclipse.milo.opcua.sdk.client.api.subscriptions.UaSubscription;
import org.eclipse.milo.opcua.sdk.client.api.subscriptions.UaSubscriptionManager;
import org.eclipse.milo.opcua.stack.core.types.builtin.StatusCode;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

//
/// ***
// * 重新订阅
// */
//
@Slf4j
public class PiDaiChenOpcUaResubscribeRunner implements CommandLineRunner {

    @Qualifier("piDaiChenOpcUaClient")
    @Resource
    OpcUaClient opcUaHelper;
    //
    @Resource
    PiDaiChenCoalWashOPCUARunner coalWashOPCUARunner;

    /**
     * Callback used to run the bean.
     *
     * @param args incoming main method arguments
     */
    @Override
    public void run(String... args) {
        opcUaHelper.getSubscriptionManager().addSubscriptionListener(new UaSubscriptionManager.SubscriptionListener() {
            @Override
            public void onSubscriptionTransferFailed(UaSubscription subscription, StatusCode statusCode) {
                if (log.isWarnEnabled()) {
                    log.warn(">>> 皮带称重新订阅：{},{}", subscription.getSubscriptionId(), statusCode.toString());
                }
                try {
                    opcUaHelper.getSubscriptionManager().clearSubscriptions();
                    coalWashOPCUARunner.run();
                } catch (Exception e) {
                    if (log.isErrorEnabled()) {
                        log.error("PiDaiChenCoalWashOPCUARunner 重新订阅失败");
                    }
                }
            }
        });
    }
}
