package com.yhd.admin.scada.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.scada.dao.PLCWSDao;
import com.yhd.admin.scada.domain.convert.PLCPointConvert;
import com.yhd.admin.scada.domain.dto.PLCWsDTO;
import com.yhd.admin.scada.domain.entity.MesPlcWs;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/7/27 14:36
 */

@Service
public class PLCWSServiceImpl extends ServiceImpl<PLCWSDao, MesPlcWs> implements PLCWSService {

    private final PLCPointConvert convert;

    public PLCWSServiceImpl(PLCPointConvert convert) {
        this.convert = convert;
    }

    /**
     * 根据PLCID 查询对应的filter
     *
     * @param PLCId
     * @return
     */
    @Override
    public List<PLCWsDTO> queryList(String PLCId) {
        LambdaQueryChainWrapper<MesPlcWs> chainWrapper =
            new LambdaQueryChainWrapper<>(baseMapper).eq(MesPlcWs::getPlcId, PLCId);

        return chainWrapper.list().stream().map(convert::toDTO).collect(Collectors.toList());
    }

}
