package com.yhd.admin.scada.domain.dto;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName SysDicItemDTO.java @Description 字典表项
 * @createTime 2020年05月20日 14:15:00
 */
@Data
@ToString
public class DicItemDTO implements Serializable, Cloneable {
    private Long id;
    /** 字典表主键 */
    private Long dicId;
    /** 字典分类 */
    private String category;
    /** 编码 */
    private String code;
    /** 编码值 */
    private String val;
    /** 状态 */
    private Boolean status;
    /** 排序 */
    private Long orderNum;

    /** 创建人 */
    private String createdBy;
    /** 创建时间 */
    private LocalDateTime createdTime;
    /** 更新人 */
    private String updatedBy;
    /** 更新时间 */
    private LocalDateTime updatedTime;
}
