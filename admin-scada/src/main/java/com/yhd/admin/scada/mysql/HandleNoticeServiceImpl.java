package com.yhd.admin.scada.mysql;

import org.eclipse.milo.opcua.sdk.client.subscriptions.ManagedDataItem;
import org.eclipse.milo.opcua.stack.core.types.builtin.DataValue;
import org.springframework.stereotype.Component;

import com.yhd.admin.scada.domain.dto.PLCPointDTO;
import com.yhd.admin.scada.service.MesNoticeService;
import com.yhd.admin.scada.ua.HandleService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0 @Description TODO
 * @createTime 2023/1/5 17:06
 */
@Component
@Slf4j
public class HandleNoticeServiceImpl implements HandleService {

    private MesNoticeService mesNoticeService;

    public HandleNoticeServiceImpl(MesNoticeService mesNoticeService) {
        // this.redisTemplate = redisTemplate;
        this.mesNoticeService = mesNoticeService;
    }

    @Override
    public void handleMonitored(ManagedDataItem item, DataValue value, PLCPointDTO point) {

    }
}
