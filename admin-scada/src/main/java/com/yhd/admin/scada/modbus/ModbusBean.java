package com.yhd.admin.scada.modbus;

import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/8/12 10:25
 */

@Configuration
public class ModbusBean {

    // @Bean
    // public ModbusMaster modbusMaster(ModbusCfg cfg) throws ModbusInitException {
    // IpParameters parameters = new IpParameters();
    // parameters.setHost(cfg.getIp());
    // parameters.setPort(cfg.getPort());
    // ModbusMaster modbusMaster = new ModbusFactory().createTcpMaster(parameters, true);
    // modbusMaster.init();
    // return modbusMaster;
    // }
}
