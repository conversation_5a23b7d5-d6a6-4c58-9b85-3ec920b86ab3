package com.yhd.admin.scada.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/6/2 15:41
 */

@Component
@ConfigurationProperties(value = "iotdb")
@Data
public class IotDBCfg {

    private String driver;
    private String url;
    private String ip;
    private Integer port;
    private String username;
    private String password;
}
