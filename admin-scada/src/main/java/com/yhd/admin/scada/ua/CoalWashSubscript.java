package com.yhd.admin.scada.ua;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.milo.opcua.sdk.client.OpcUaClient;
import org.eclipse.milo.opcua.sdk.client.subscriptions.ManagedDataItem;
import org.eclipse.milo.opcua.sdk.client.subscriptions.ManagedSubscription;
import org.eclipse.milo.opcua.stack.core.types.builtin.DataValue;
import org.eclipse.milo.opcua.stack.core.types.structured.MonitoredItemCreateRequest;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.yhd.admin.scada.domain.Enum.RedisKeyEnum;
import com.yhd.admin.scada.domain.dto.PLCPointDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0 @Description TODO
 * @createTime 2021/6/2 10:32
 */
@Service
@Slf4j
public class CoalWashSubscript {

    private final RedisTemplate<String, PLCPointDTO> redisTemplate;

    private final OpcUaClient coalWashGOpcUaClient;
    private List<HandleService> callBackBean = Collections.synchronizedList(new ArrayList<>(10));
    private List<MonitoredItemCreateRequest> lastMonitoredItemList = null;
    private boolean isConnecting = false;

    public CoalWashSubscript(@Qualifier("coalWashOpcUaClient") OpcUaClient coalWashOpcUaClient,
        ApplicationContext applicationContext, RedisTemplate redisTemplate) {
        this.coalWashGOpcUaClient = coalWashOpcUaClient;

        Map<String, HandleService> handleBean = applicationContext.getBeansOfType(HandleService.class);
        callBackBean.addAll(handleBean.values());
        this.redisTemplate = redisTemplate;
    }

    @Async
    public void onSubPoint(List<MonitoredItemCreateRequest> monitoredItemList) {
        if (coalWashGOpcUaClient == null) {
            log.error("OPC UA客户端对象为空，无法建立连接");
            return;
        }

        // 保存监控项列表，用于重试
        this.lastMonitoredItemList = monitoredItemList;

        if (isConnecting) {
            log.error("已有连接正在进行中，请等待...");
            return;
        }

        isConnecting = true;
        try {
            // 尝试连接
            log.error("正在尝试连接到OPC UA服务器...");
            coalWashGOpcUaClient.connect().get();
            log.error("OPC UA服务器连接成功");

            // 创建订阅
            ManagedSubscription subscription = ManagedSubscription.create(coalWashGOpcUaClient, 5000);
            log.error("成功创建订阅");

            // 创建数据项
            List<ManagedDataItem> items = subscription.createDataItems(
                monitoredItemList.stream().map(o -> o.getItemToMonitor().getNodeId()).collect(Collectors.toList()));
            log.error("成功创建数据项: {}/{}", items.size(), monitoredItemList.size());

            // 添加数据变更监听器
            subscription.addDataChangeListener((dataItems, values) -> {
                for (int i = 0; i < dataItems.size(); i++) {
                    if (log.isDebugEnabled()) {
                        log.debug("subscription value received: item={}, value={}", dataItems.get(i).getNodeId(),
                            values.get(i).getValue());
                    }
                    this.onSubscriptionValue(dataItems.get(i), values.get(i));
                }
            });
        } catch (Exception e) {
            log.error("OPC UA连接或订阅失败: {}", e.getMessage(), e);
            // 添加重试逻辑
            scheduleReconnect();
        } finally {
            isConnecting = false;
        }
    }

    /**
     * 安排重新连接
     */
    private void scheduleReconnect() {
        if (lastMonitoredItemList == null) {
            log.error("没有可用的监控项列表，无法重连");
            return;
        }

        new Thread(() -> {
            log.error("计划5秒后重新连接...");
            try {
                Thread.sleep(5000);
                log.error("开始重新连接");
                onSubPoint(lastMonitoredItemList);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("重连线程被中断");
            }
        }).start();
    }

    private void onSubscriptionValue(ManagedDataItem item, DataValue value) {
        PLCPointDTO pointDTO = redisTemplate.opsForValue()
            .get(String.format(RedisKeyEnum.PLC.getKey(), item.getReadValueId().getNodeId().getIdentifier()));
        if (log.isDebugEnabled()) {
            log.debug("{},{}", item.getReadValueId().getNodeId().getIdentifier(), value.getValue().getDataType());
        }
        if (value.getStatusCode().isGood()) {
            callBackBean.forEach(method -> method.handleMonitored(item, value, pointDTO));
        }
    }
}
