package com.yhd.admin.scada.boot;

import static org.eclipse.milo.opcua.stack.core.types.builtin.unsigned.Unsigned.uint;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.eclipse.milo.opcua.stack.core.AttributeId;
import org.eclipse.milo.opcua.stack.core.types.builtin.NodeId;
import org.eclipse.milo.opcua.stack.core.types.builtin.QualifiedName;
import org.eclipse.milo.opcua.stack.core.types.enumerated.MonitoringMode;
import org.eclipse.milo.opcua.stack.core.types.structured.MonitoredItemCreateRequest;
import org.eclipse.milo.opcua.stack.core.types.structured.MonitoringParameters;
import org.eclipse.milo.opcua.stack.core.types.structured.ReadValueId;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import com.yhd.admin.common.domain.enums.ConnectEnum;
import com.yhd.admin.scada.domain.dto.PLCPointDTO;
import com.yhd.admin.scada.service.PLCPointService;
import com.yhd.admin.scada.ua.PiDaiChenCoalWashSubscript;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0 @Description TODO
 * @createTime 2021/6/2 11:09
 */
@Component
@Slf4j
public class PiDaiChenCoalWashOPCUARunner implements CommandLineRunner {

    private final PiDaiChenCoalWashSubscript coalWashSubscript;
    private final PLCPointService pointService;

    private final List<String> sysList;

    public PiDaiChenCoalWashOPCUARunner(PiDaiChenCoalWashSubscript coalWashSubscript, PLCPointService pointService,
        List<String> sysList) {
        this.coalWashSubscript = coalWashSubscript;
        this.pointService = pointService;
        this.sysList = sysList;
    }

    /**
     * Callback used to run the bean.
     *
     * @param args incoming main method arguments
     * @throws Exception on error
     */
    @Override
    public void run(String... args) throws Exception {
        log.error(">>>>>>>>>>>>>>>>>>> 皮带称采集启动,{}", sysList);
        try {
            // 先配置KEEPSERVER地址和采集点位，不然会报错
            List<PLCPointDTO> pointDTOS = pointService.queryList(ConnectEnum.OPC_UA.getType(), sysList);
            log.error(">>>>>>>>>>>>>>>>>>> 皮带称采集点位:{}", pointDTOS.size());

            // 过滤出有效的OPC UA节点地址（以ns=开头）
            List<PLCPointDTO> validPoints = pointDTOS.stream().filter(point -> {
                String address = point.getKwpAddress();
                boolean isValid = address != null && address.startsWith("ns=");
                return isValid;
            }).collect(Collectors.toList());

            log.error(">>>>>>>>>>>>>>>>>>> 有效的皮带称采集点位:{}/{}", validPoints.size(), pointDTOS.size());

            List<MonitoredItemCreateRequest> monitoredItemList = new ArrayList<>(validPoints.size());
            for (int i = 0; i < validPoints.size(); i++) {
                PLCPointDTO item = validPoints.get(i);
                log.error("item >>>>>>>>>>>> " + item.toString());
                try {
                    ReadValueId readValueId = new ReadValueId(NodeId.parse(item.getKwpAddress()),
                        AttributeId.Value.uid(), null, QualifiedName.NULL_VALUE);
                    MonitoringParameters parameters = new MonitoringParameters(uint(i), 5000.0, // sampling interval
                        null, // filter, null means use default
                        uint(10), // queue size
                        true // discard oldest
                    );
                    MonitoredItemCreateRequest request =
                        new MonitoredItemCreateRequest(readValueId, MonitoringMode.Reporting, parameters);
                    monitoredItemList.add(request);
                } catch (Exception e) {
                    log.error(">>>>>>>>>>>>>>>>>>> 创建监控项失败，节点地址: {}, 错误: {}", item.getKwpAddress(), e.getMessage());
                }
            }

            log.error(">>>>>>>>>>>>>>>>>>> 成功创建监控项数量: {}", monitoredItemList.size());

            if (!monitoredItemList.isEmpty()) {
                // 即使连接失败也不会抛出异常，程序可以继续运行
                coalWashSubscript.onSubPoint(monitoredItemList);
                log.error(">>>>>>>>>>>>>>>>>>> 皮带称点位订阅已启动！");
            } else {
                log.error(">>>>>>>>>>>>>>>>>>> 没有有效的监控项，皮带称点位订阅未启动");
            }
        } catch (Exception e) {
            log.error(">>>>>>>>>>>>>>>>>>> 皮带称数据采集启动失败: {}", e.getMessage(), e);

        }
    }
}
