package com.yhd.admin.scada.service;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.scada.dao.PLCCtrlDao;
import com.yhd.admin.scada.domain.convert.PLCCtrlConvert;
import com.yhd.admin.scada.domain.entity.MesPlcCtrl;
import com.yhd.admin.scada.domain.param.PlcCtrlParam;

@Service
public class PLCCtrlServiceImpl extends ServiceImpl<PLCCtrlDao, MesPlcCtrl> implements PLCCtrlService {
    private final PLCCtrlConvert ctrlConvert;

    public PLCCtrlServiceImpl(PLCCtrlConvert ctrlConvert) {
        this.ctrlConvert = ctrlConvert;
    }

    @Override
    public void add(PlcCtrlParam param) {
        baseMapper.insert(ctrlConvert.toEntity(param));
    }
}
