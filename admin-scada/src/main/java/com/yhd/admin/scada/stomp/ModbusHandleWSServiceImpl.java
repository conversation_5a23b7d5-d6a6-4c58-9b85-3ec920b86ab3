package com.yhd.admin.scada.stomp;

import org.apache.commons.lang3.StringUtils;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.util.CollectionUtils;

import com.yhd.admin.scada.domain.dto.PLCPointDTO;
import com.yhd.admin.scada.modbus.ModbusConvert;
import com.yhd.admin.scada.modbus.ModbusHandleService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/6/10 09:46
 */

@Slf4j
public class ModbusHandleWSServiceImpl implements ModbusHandleService {

    private final SimpMessagingTemplate messagingTemplate;

    private final ModbusConvert convert;

    public ModbusHandleWSServiceImpl(SimpMessagingTemplate messagingTemplate, ModbusConvert convert) {
        this.messagingTemplate = messagingTemplate;
        this.convert = convert;
    }

    @Override
    public void handleValue(Float value, PLCPointDTO point) {

        try {
            if (point.getWebsocket() && !CollectionUtils.isEmpty(point.getWsTopics())) {

                point.getWsTopics().stream().filter(o -> StringUtils.isNotBlank(o.getTopic()))
                    .forEach(o -> messagingTemplate.convertAndSend(o.getTopic(), convert.transformToWS(value, point)));

            }
        } catch (Exception e) {
            log.error(">>>> {},{},{}", e.getMessage(), value, point);
        }
    }
}
