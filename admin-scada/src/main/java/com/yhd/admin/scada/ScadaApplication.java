package com.yhd.admin.scada;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 */
@EnableTransactionManagement
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@SpringBootApplication
@EnableAsync
@MapperScan("com.yhd.admin.scada.**.dao")
@EnableConfigurationProperties
public class ScadaApplication {

    public static void main(String[] args) {
        SpringApplication bms = new SpringApplication(ScadaApplication.class);
        bms.setBannerMode(Banner.Mode.OFF);
        bms.run(args);
    }
}
