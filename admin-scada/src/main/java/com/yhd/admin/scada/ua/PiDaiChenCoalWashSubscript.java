package com.yhd.admin.scada.ua;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.eclipse.milo.opcua.sdk.client.OpcUaClient;
import org.eclipse.milo.opcua.sdk.client.subscriptions.ManagedDataItem;
import org.eclipse.milo.opcua.sdk.client.subscriptions.ManagedSubscription;
import org.eclipse.milo.opcua.stack.core.types.builtin.DataValue;
import org.eclipse.milo.opcua.stack.core.types.structured.MonitoredItemCreateRequest;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.yhd.admin.scada.domain.Enum.RedisKeyEnum;
import com.yhd.admin.scada.domain.dto.PLCPointDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0 @Description TODO
 * @createTime 2021/6/2 10:32
 */
@Service
@Slf4j
public class PiDaiChenCoalWashSubscript {

    private final RedisTemplate<String, PLCPointDTO> redisTemplate;

    private final OpcUaClient coalPiDaizhenOpcUaClient;
    private List<HandleService> callBackBean = Collections.synchronizedList(new ArrayList<>(10));
    private List<MonitoredItemCreateRequest> lastMonitoredItemList = null;
    private boolean isConnecting = false;

    public PiDaiChenCoalWashSubscript(@Qualifier("piDaiChenOpcUaClient") OpcUaClient coalPiDaizhenOpcUaClient,
        ApplicationContext applicationContext, RedisTemplate redisTemplate) {
        this.coalPiDaizhenOpcUaClient = coalPiDaizhenOpcUaClient;

        Map<String, HandleService> handleBean = applicationContext.getBeansOfType(HandleService.class);
        callBackBean.addAll(handleBean.values());
        this.redisTemplate = redisTemplate;
    }

    @Async
    public void onSubPoint(List<MonitoredItemCreateRequest> monitoredItemList) {
        // synchronous connect
        if (coalPiDaizhenOpcUaClient == null) {
            log.error(">>> OPC UA客户端对象为null，无法建立连接");
            return;
        } else {
            log.error(">>> OPC UA客户端对象: {}", coalPiDaizhenOpcUaClient.toString());
        }

        // 保存监控项列表，用于重试
        this.lastMonitoredItemList = monitoredItemList;

        if (isConnecting) {
            log.error(">>> 已有连接正在进行中，请等待...");
            return;
        }

        isConnecting = true;
        log.error(">>> 开始连接皮带称OPC UA服务器...");
        log.error(">>> 监控项数量: {}", monitoredItemList.size());

        try {
            // 修改端点URL - 创建新的EndpointDescription对象
            String endpointUrl = coalPiDaizhenOpcUaClient.getConfig().getEndpoint().getEndpointUrl();
            String serverHost = endpointUrl.split("://")[1].split(":")[0];

            log.error(">>> 原始端点URL: {}", endpointUrl);
            log.error(">>> 服务器主机名: {}", serverHost);
            // 尝试连接前检查网络可达性
            try {
                String host = endpointUrl.replace("opc.tcp://", "").split(":")[0];
                int port = Integer.parseInt(endpointUrl.split(":")[2].split("/")[0]);
                log.error(">>> 检查网络连接 {}:{}", host, port);

                // 可以添加网络连接测试代码
            } catch (Exception e) {
                log.error(">>> 网络检查失败: {}", e.getMessage());
            }
            // 同步连接
            log.error(">>> 尝试连接到服务器...");
            coalPiDaizhenOpcUaClient.connect().get();
            log.error(">>> 皮带称OPC UA服务器连接成功");
            // 创建订阅
            ManagedSubscription subscription = ManagedSubscription.create(coalPiDaizhenOpcUaClient, 5000);
            log.error(">>> 成功创建订阅");

            // 创建数据项
            List<ManagedDataItem> items = subscription.createDataItems(
                monitoredItemList.stream().map(o -> o.getItemToMonitor().getNodeId()).collect(Collectors.toList()));
            log.error(">>> 成功创建数据项: {}/{}", items.size(), monitoredItemList.size());

            // 添加数据变更监听器
            subscription.addDataChangeListener((dataItems, values) -> {
                log.error(">>> 收到数据变更: {} 项", dataItems.size());
                for (int i = 0; i < dataItems.size(); i++) {
                    this.onSubscriptionValue(dataItems.get(i), values.get(i));
                }
            });

        } catch (Exception e) {
            log.error(">>> 皮带称OPC UA连接失败: {}", e.getMessage());
            log.error(">>> 异常类型: {}", e.getClass().getName());
            if (e.getCause() != null) {
                log.error(">>> 根本原因: {}", e.getCause().getMessage());
                log.error(">>> 根本原因类型: {}", e.getCause().getClass().getName());
            }
            // 添加重试逻辑
            scheduleReconnect();
        } finally {
            isConnecting = false;
        }
    }

    /**
     * 安排重新连接
     */
    private void scheduleReconnect() {
        if (lastMonitoredItemList == null) {
            log.error(">>> 没有可用的监控项列表，无法重连");
            return;
        }

        new Thread(() -> {
            log.error(">>> 计划5秒后重新连接...");
            try {
                Thread.sleep(5000);
                log.error(">>> 开始重新连接");
                onSubPoint(lastMonitoredItemList);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error(">>> 重连线程被中断");
            }
        }).start();
    }

    private void onSubscriptionValue(ManagedDataItem item, DataValue value) {
        PLCPointDTO pointDTO = redisTemplate.opsForValue()
            .get(String.format(RedisKeyEnum.PLC.getKey(), item.getReadValueId().getNodeId().toParseableString()));
        if (log.isDebugEnabled()) {
            log.debug("{},{}", item.getReadValueId().getNodeId().getIdentifier(), value.getValue().getDataType());
        }
        if (value.getStatusCode().isGood()) {
            callBackBean.forEach(method -> method.handleMonitored(item, value, pointDTO));
        }
    }
}
