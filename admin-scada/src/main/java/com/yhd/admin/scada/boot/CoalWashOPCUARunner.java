package com.yhd.admin.scada.boot;

import static org.eclipse.milo.opcua.stack.core.types.builtin.unsigned.Unsigned.uint;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.milo.opcua.stack.core.AttributeId;
import org.eclipse.milo.opcua.stack.core.types.builtin.NodeId;
import org.eclipse.milo.opcua.stack.core.types.builtin.QualifiedName;
import org.eclipse.milo.opcua.stack.core.types.enumerated.MonitoringMode;
import org.eclipse.milo.opcua.stack.core.types.structured.MonitoredItemCreateRequest;
import org.eclipse.milo.opcua.stack.core.types.structured.MonitoringParameters;
import org.eclipse.milo.opcua.stack.core.types.structured.ReadValueId;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import com.yhd.admin.common.domain.enums.ConnectEnum;
import com.yhd.admin.scada.domain.dto.PLCPointDTO;
import com.yhd.admin.scada.service.PLCPointService;
import com.yhd.admin.scada.ua.CoalWashSubscript;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0 @Description TODO
 * @createTime 2021/6/2 11:09
 */
@Component
@Slf4j
public class CoalWashOPCUARunner implements CommandLineRunner {

    private final CoalWashSubscript coalWashSubscript;
    private final PLCPointService pointService;

    private final List<String> sysList;

    public CoalWashOPCUARunner(CoalWashSubscript coalWashSubscript, PLCPointService pointService,
        List<String> sysList) {
        this.coalWashSubscript = coalWashSubscript;
        this.pointService = pointService;
        this.sysList = sysList;
    }

    /**
     * Callback used to run the bean.
     *
     * @param args incoming main method arguments
     * @throws Exception on error
     */
    @Override
    public void run(String... args) throws Exception {
        log.error(">>>>>>>>>>>>>>>>>>> 主洗数据采集启动,{}", sysList);
        try {
            // 先配置KEEPSERVER地址和采集点位，不然会报错
            List<PLCPointDTO> pointDTOS = pointService.queryList(ConnectEnum.OPC_UA.getType(), sysList);
            log.error(">>>>>>>>>>>>>>>>>>> 主洗采集点位:{}", pointDTOS.size());
            List<MonitoredItemCreateRequest> monitoredItemList = new ArrayList<>(pointDTOS.size());
            for (int i = 0; i < pointDTOS.size(); i++) {
                PLCPointDTO item = pointDTOS.get(i);
                log.error("item >>>>>>>>>>>> " + item.toString());
                ReadValueId readValueId = new ReadValueId(new NodeId(2, item.getKwpAddress()), AttributeId.Value.uid(),
                    null, QualifiedName.NULL_VALUE);
                MonitoringParameters parameters = new MonitoringParameters(uint(i), 5000.0, // sampling interval
                    null, // filter, null means use default
                    uint(10), // queue size
                    true // discard oldest
                );
                MonitoredItemCreateRequest request =
                    new MonitoredItemCreateRequest(readValueId, MonitoringMode.Reporting, parameters);
                monitoredItemList.add(request);
            }

            // 即使连接失败也不会抛出异常，程序可以继续运行
            coalWashSubscript.onSubPoint(monitoredItemList);
            log.error(">>>>>>>>>>>>>>>>>>>  主洗采集点位订阅已启动！");
        } catch (Exception e) {
            log.error("主洗数据采集启动失败: {}", e.getMessage(), e);
            // 即使出现异常，应用程序也会继续运行
        }
    }
}
