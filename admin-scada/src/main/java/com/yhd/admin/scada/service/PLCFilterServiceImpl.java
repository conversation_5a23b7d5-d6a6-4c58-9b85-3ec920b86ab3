package com.yhd.admin.scada.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.scada.dao.PLCFilterDao;
import com.yhd.admin.scada.domain.convert.PLCPointConvert;
import com.yhd.admin.scada.domain.dto.PLCFilterDTO;
import com.yhd.admin.scada.domain.entity.MesPlcFilter;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/7/27 14:36
 */

@Service
public class PLCFilterServiceImpl extends ServiceImpl<PLCFilterDao, MesPlcFilter> implements PLCFilterService {

    private final PLCPointConvert convert;

    public PLCFilterServiceImpl(PLCPointConvert convert) {
        this.convert = convert;
    }

    /**
     * 根据PLCID 查询对应的filter
     *
     * @param PLCId
     * @return
     */
    @Override
    public List<PLCFilterDTO> queryList(String PLCId) {
        LambdaQueryChainWrapper<MesPlcFilter> chainWrapper =
            new LambdaQueryChainWrapper<>(baseMapper).eq(MesPlcFilter::getPlcId, PLCId);

        return chainWrapper.list().stream().map(convert::toDTO).collect(Collectors.toList());
    }

}
