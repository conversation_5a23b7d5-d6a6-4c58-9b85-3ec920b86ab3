package com.yhd.admin.scada.config;


import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yhd.admin.common.domain.RespJson;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AuthExceptionEntryPoint.java
 * @Description TODO
 * @createTime 2020年03月21日 17:57:00
 */
@Slf4j
@Component
public class ResExceptionEntryPoint implements AuthenticationEntryPoint {

    @Autowired
    private ObjectMapper jacksonObjectMapper;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws ServletException {
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        try {
            jacksonObjectMapper.writeValue(response.getOutputStream(),
                RespJson.failure(authException.getMessage()));
            response.getOutputStream().flush();
        } catch (Exception e) {
            log.error("Token 异常", e);
            throw new ServletException();
        }
    }

}
