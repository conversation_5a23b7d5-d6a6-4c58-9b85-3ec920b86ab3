package com.yhd.admin.scada.domain.vo;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/7/30 10:44
 */

@Data
@ToString
@AllArgsConstructor
public class PointValueVO implements Serializable, Cloneable {

    private String identifier;

    private Object value;

    private Long serverTime;

}
