package com.yhd.admin.scada.ua;

import org.eclipse.milo.opcua.sdk.client.OpcUaClient;
import org.eclipse.milo.opcua.sdk.client.api.identity.AnonymousProvider;
import org.eclipse.milo.opcua.stack.core.security.SecurityPolicy;
import org.eclipse.milo.opcua.stack.core.types.builtin.LocalizedText;
import org.eclipse.milo.opcua.stack.core.types.builtin.unsigned.UInteger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.yhd.admin.scada.config.PiDaiChenOpcUaCfg;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0 @Description TODO
 * @createTime 2021/6/1 16:46
 */
@Configuration
@Slf4j
public class PiDaiChenOpcUaBean {

    private final PiDaiChenOpcUaCfg opcuaCfg;

    public PiDaiChenOpcUaBean(PiDaiChenOpcUaCfg opcuaCfg) {
        this.opcuaCfg = opcuaCfg;
    }

    @Bean("piDaiChenOpcUaClient")
    public OpcUaClient piDaiChenOpcUaClient() {
        log.error(">>> 开始创建皮带称OPC UA客户端，地址：{}", opcuaCfg.getEndPointUrl());
        OpcUaClient client = null;
        try {
            // 打印配置信息
            log.error(">>> OPC UA配置参数：超时时间={}ms", 10000);

            client = OpcUaClient.create(opcuaCfg.getEndPointUrl(), endpoints -> {
                // 打印发现的端点信息
                log.error(">>> 发现端点数量: {}", endpoints.size());
                endpoints
                    .forEach(e -> log.error(">>> 可用端点: {}, 安全模式: {}", e.getEndpointUrl(), e.getSecurityPolicyUri()));
                // 根据安全策略选择端点
                return endpoints.stream().filter(e -> e.getSecurityPolicyUri().equals(SecurityPolicy.None.getUri())) // 例如选择无安全策略的端点
                    .findFirst();
            }, configBuilder -> configBuilder.setApplicationName(LocalizedText.english("eclipse milo opc-ua client"))
                .setApplicationUri("urn:eclipse:milo:examples:client").setIdentityProvider(new AnonymousProvider())
                .setRequestTimeout(UInteger.valueOf(10000)).build());
            log.error(">>> 皮带称OPC UA客户端创建成功: {}", client);
        } catch (Exception e) {
            log.error(">>> 皮带称OPC UA客户端创建失败: {}", e.getMessage(), e);
            // 创建一个备用客户端，允许程序继续运行
            try {
                // 使用默认配置创建一个客户端，不会立即连接
                client = OpcUaClient.create(
                    opcuaCfg.getEndPointUrl(),
                    endpoints -> endpoints.stream().findFirst(),
                    configBuilder -> configBuilder
                        .setApplicationName(LocalizedText.english("eclipse milo opc-ua client"))
                        .setApplicationUri("urn:eclipse:milo:examples:client")
                        .setIdentityProvider(new AnonymousProvider())
                        .setRequestTimeout(UInteger.valueOf(10000))
                        .build()
                );
                log.error(">>> 已创建皮带称备用OPC UA客户端，但可能无法正常工作");
            } catch (Exception ex) {
                log.error(">>> 皮带称备用OPC UA客户端创建也失败: {}", ex.getMessage());
            }
        }

        return client;
    }
}
