package com.yhd.admin.scada.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.scada.domain.dto.PLCWsDTO;
import com.yhd.admin.scada.domain.entity.MesPlcWs;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/7/27 13:52
 */

public interface PLCWSService extends IService<MesPlcWs> {

    /**
     * 根据PLCID 查询对应的kafka Topic
     *
     * @param PLCId
     * @return
     */
    List<PLCWsDTO> queryList(String PLCId);

}
