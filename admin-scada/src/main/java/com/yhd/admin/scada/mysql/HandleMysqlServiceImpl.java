package com.yhd.admin.scada.mysql;

import com.yhd.admin.scada.ua.HandleService;
import org.eclipse.milo.opcua.sdk.client.subscriptions.ManagedDataItem;
import org.eclipse.milo.opcua.stack.core.types.builtin.DataValue;
import org.springframework.stereotype.Component;

import com.yhd.admin.scada.dao.AlarmDao;
import com.yhd.admin.scada.domain.dto.PLCPointDTO;
import com.yhd.admin.scada.domain.entity.MesAlarm;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0 @Description TODO
 * @createTime 2021/8/12 15:36
 */
@Component
@Slf4j
public class HandleMysqlServiceImpl implements HandleService {
    private AlarmDao alarmDao;

    public HandleMysqlServiceImpl(AlarmDao alarmDao) {
        this.alarmDao = alarmDao;
    }

    @Override
    public void handleMonitored(ManagedDataItem item, DataValue value, PLCPointDTO point) {
        MesAlarm alarm = new MesAlarm();
        if (point.getMysql() && "T4".equals(point.getTypes()) && Boolean.TRUE.equals(value.getValue().getValue())) {
            alarm.setAlarmName(point.getPointName());
            alarm.setDeviceNo(point.getDeviceNo());
            alarm.setDeviceName(point.getDeviceName());
            alarm.setHappendTime(value.getServerTime().getJavaTime());
            alarm.setAlarmPoint(item.getReadValueId().getNodeId().getIdentifier().toString());
            alarm.setSource("PLC");
            alarm.setStatus("0");
            alarmDao.insert(alarm);
        }
    }
}
