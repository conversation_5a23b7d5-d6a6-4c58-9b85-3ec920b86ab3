package com.yhd.admin.scada.domain.convert;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.yhd.admin.scada.domain.dto.PLCFilterDTO;
import com.yhd.admin.scada.domain.dto.PLCKafkaDTO;
import com.yhd.admin.scada.domain.dto.PLCPointDTO;
import com.yhd.admin.scada.domain.dto.PLCWsDTO;
import com.yhd.admin.scada.domain.entity.MesPlcFilter;
import com.yhd.admin.scada.domain.entity.MesPlcKafka;
import com.yhd.admin.scada.domain.entity.MesPlcPoint;
import com.yhd.admin.scada.domain.entity.MesPlcWs;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/7/23 10:02
 */

@Mapper(componentModel = "spring")
public interface PLCPointConvert {

    @Mapping(ignore = true, target = "wsTopics")
    @Mapping(ignore = true, target = "kafkaTopics")
    @Mapping(ignore = true, target = "filters")
    PLCPointDTO toDTO(MesPlcPoint plc);

    PLCWsDTO toDTO(MesPlcWs ws);

    PLCKafkaDTO toDTO(MesPlcKafka kafka);

    PLCFilterDTO toDTO(MesPlcFilter filter);

}
