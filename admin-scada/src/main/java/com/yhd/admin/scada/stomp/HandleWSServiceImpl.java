package com.yhd.admin.scada.stomp;

import com.yhd.admin.scada.ua.HandleService;
import com.yhd.admin.scada.ua.OpcUaConvert;
import org.codehaus.jackson.map.ObjectMapper;
import org.eclipse.milo.opcua.sdk.client.subscriptions.ManagedDataItem;
import org.eclipse.milo.opcua.stack.core.types.builtin.DataValue;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.yhd.admin.scada.domain.dto.PLCPointDTO;
import com.yhd.admin.scada.websocket.WsServerEndpoint;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0 @Description TODO
 * @createTime 2021/6/10 09:46
 */
@Service
@Async
@Slf4j
public class HandleWSServiceImpl implements HandleService {

    private WsServerEndpoint serverEndpoint;

    private final OpcUaConvert convert;

    private ObjectMapper objectMapper;

    public HandleWSServiceImpl(OpcUaConvert convert, WsServerEndpoint serverEndpoint) {
        this.serverEndpoint = serverEndpoint;
        this.convert = convert;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public void handleMonitored(ManagedDataItem item, DataValue value, PLCPointDTO point) {

        try {
//            if (point.getWebsocket()) {
//                serverEndpoint.sendMessage(objectMapper.writeValueAsString(convert.transformToWS(item, value, point)));
//            }
        } catch (Exception e) {
            log.error(">>>> {},{},{},{}", e.getMessage(), item, value, point);
        }
    }
}
