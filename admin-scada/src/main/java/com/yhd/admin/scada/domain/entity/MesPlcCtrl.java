package com.yhd.admin.scada.domain.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;

@Data
public class MesPlcCtrl {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /** 控制描述 */
    private String intro;
    /** 控制点位 */
    private String point;
    /** 下发来源系统 */
    private String source;
    /** 下发值 */
    private String val;
    /** 是否自动执行;预留 */
    private String auto;
    /** 操作人 */
    private String operator;
    /** 创建人 */
    private String createdBy;
    /** 创建时间 */
    private LocalDateTime createdTime;
    /** 更新人 */
    private String updatedBy;
    /** 更新时间 */
    private LocalDateTime updatedTime;
}
