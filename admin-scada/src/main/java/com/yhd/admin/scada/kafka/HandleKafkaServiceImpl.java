package com.yhd.admin.scada.kafka;

import com.yhd.admin.scada.ua.HandleService;
import com.yhd.admin.scada.ua.OpcUaConvert;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.milo.opcua.sdk.client.subscriptions.ManagedDataItem;
import org.eclipse.milo.opcua.stack.core.types.builtin.DataValue;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.yhd.admin.scada.domain.dto.PLCPointDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/6/9 10:37
 */

@Component
@Async
@Slf4j
public class HandleKafkaServiceImpl implements HandleService {

    private final KafkaTemplate<Object, Object> kafkaTemplate;

    private final OpcUaConvert opcUaConvert;

    public HandleKafkaServiceImpl(KafkaTemplate<Object, Object> kafkaTemplate, OpcUaConvert opcUaConvert) {
        this.kafkaTemplate = kafkaTemplate;
        this.opcUaConvert = opcUaConvert;
    }

    @Override
    public void handleMonitored(ManagedDataItem item, DataValue value, PLCPointDTO point) {
        try {
//            if (Boolean.TRUE.equals(point.getKafka()) && !CollectionUtils.isEmpty(point.getKafkaTopics())) {
//                point.getKafkaTopics().stream().filter(o -> StringUtils.isNotBlank(o.getTopic()))
//                    .forEach(o -> kafkaTemplate.send(o.getTopic(), opcUaConvert.transformToKafka(item, value, point)));
//
//            }
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(">>>> {},{},{},{}", e.getMessage(), item, value, point);
            }
        }
    }
}
