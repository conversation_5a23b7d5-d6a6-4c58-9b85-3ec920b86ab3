package com.yhd.admin.scada.domain.entity;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description PLC 点表
 * @createTime 2021/6/5 16:38
 */

@Data
public class MesPlc {

    private String id;
    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 点位地址
     */
    private String pointAddress;
    /**
     * 点位名称
     */
    private String pointName;
    /**
     * 所属系统
     */
    private String sys;

    /**
     * 链接方式
     */
    private String connect;
    /**
     * KEEPSERVER地址
     */
    private String kwpAddress;
    /**
     * 订阅
     */
    private Boolean onsubscription;
    /**
     * 启用
     */
    private Boolean enable;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 是否推送kafka
     */
    private Boolean kafka;
    /**
     * 是否推送websocket
     */
    private Boolean websocket;
    /**
     * 是否入库
     */
    private Boolean db;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
