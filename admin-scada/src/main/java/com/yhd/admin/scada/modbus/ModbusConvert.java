package com.yhd.admin.scada.modbus;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

import org.springframework.stereotype.Component;

import com.yhd.admin.scada.domain.dto.KafkaMessageDTO;
import com.yhd.admin.scada.domain.dto.PLCPointDTO;
import com.yhd.admin.scada.domain.dto.WSMessageDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/6/9 09:29
 */

@Component
public class ModbusConvert {

    public KafkaMessageDTO transformToKafka(Float value, PLCPointDTO point) {
        KafkaMessageDTO message = new KafkaMessageDTO();
        message.setValue(value);
        message.setTime(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        message.setAddress(point.getPointAddress());
        return message;
    }

    public WSMessageDTO transformToWS(Float value, PLCPointDTO point) {
        WSMessageDTO message = new WSMessageDTO();
        message.setValue(value);
        message.setTime(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        message.setAddress(point.getPointAddress());
        return message;
    }

}
