package com.yhd.admin.scada.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.scada.domain.dto.PLCKafkaDTO;
import com.yhd.admin.scada.domain.entity.MesPlcKafka;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/7/27 13:52
 */

public interface PLCKafkaService extends IService<MesPlcKafka> {

    /**
     * 根据PLCID 查询对应的kafka Topic
     *
     * @param PLCId
     * @return
     */
    List<PLCKafkaDTO> queryList(String PLCId);

    /**
     * 批量新增 kafka
     *
     * @param kafka
     */

}
