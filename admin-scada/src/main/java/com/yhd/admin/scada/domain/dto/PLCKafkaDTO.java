package com.yhd.admin.scada.domain.dto;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/7/25 16:14
 */

@lombok.Data
@lombok.ToString
public class PLCKafkaDTO implements java.io.Serializable, Cloneable {

    private static final long serialVersionUID = 2538859870224158236L;
    private String id;
    /**
     * PLC主键
     */
    private String plcId;
    /**
     * 队列
     */
    private String topic;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private java.time.LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private java.time.LocalDateTime updatedTime;
}
