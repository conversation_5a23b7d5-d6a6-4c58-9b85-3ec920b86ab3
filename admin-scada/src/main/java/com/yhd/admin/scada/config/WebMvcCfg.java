package com.yhd.admin.scada.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName WebMvcCfg.java
 * @Description TODO 跨域设置
 * @createTime 2020年03月27日 11:14:00
 */
@Configuration
public class WebMvcCfg implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**").allowedOrigins("*").allowedMethods("*").allowedHeaders("*").allowCredentials(true)
            .maxAge(3600);
    }

}
