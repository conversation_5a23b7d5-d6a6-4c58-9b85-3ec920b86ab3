package com.yhd.admin.scada.kafka;

import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.yhd.admin.scada.domain.dto.PLCPointDTO;
import com.yhd.admin.scada.modbus.ModbusConvert;
import com.yhd.admin.scada.modbus.ModbusHandleService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/8/12 11:04
 */
@Component
@Slf4j
@Async
public class ModbusKafkaHandleServiceImpl implements ModbusHandleService {
    private final KafkaTemplate<Object, Object> kafkaTemplate;

    private final ModbusConvert modbusConvert;

    public ModbusKafkaHandleServiceImpl(KafkaTemplate<Object, Object> kafkaTemplate, ModbusConvert modbusConvert) {
        this.kafkaTemplate = kafkaTemplate;
        this.modbusConvert = modbusConvert;
    }

    @Override
    public void handleValue(Float value, PLCPointDTO point) {
        try {
            if (Boolean.TRUE.equals(point.getKafka()) && !CollectionUtils.isEmpty(point.getKafkaTopics())) {
                point.getKafkaTopics().stream().filter(o -> StringUtils.isNotBlank(o.getTopic()))
                    .forEach(o -> kafkaTemplate.send(o.getTopic(), modbusConvert.transformToKafka(value, point)));

            }
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(">>>> {},{},{}", e.getMessage(), value, point);
            }
        }
    }
}
