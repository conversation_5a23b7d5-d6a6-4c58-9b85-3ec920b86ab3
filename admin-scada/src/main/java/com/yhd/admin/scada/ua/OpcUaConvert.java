package com.yhd.admin.scada.ua;

import com.yhd.admin.scada.domain.dto.KafkaMessageDTO;
import com.yhd.admin.scada.domain.dto.PLCPointDTO;
import com.yhd.admin.scada.domain.dto.WSMessageDTO;
import org.eclipse.milo.opcua.sdk.client.subscriptions.ManagedDataItem;
import org.eclipse.milo.opcua.stack.core.types.builtin.DataValue;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @createTime 2021/6/9 09:29
 */

@Component
public class OpcUaConvert {

    public OpcUaConvert() {
    }

    public KafkaMessageDTO transformToKafka(ManagedDataItem item, DataValue value, PLCPointDTO point) {
        if (value.getStatusCode().isGood()) {
            KafkaMessageDTO message = new KafkaMessageDTO();
            message.setValue(value.getValue().getValue());
            message.setTime(value.getServerTime().getJavaTime());
            message.setAddress(point.getPointAddress());
            return message;
        }
        return null;
    }

    public WSMessageDTO transformToWS(ManagedDataItem item, DataValue value, PLCPointDTO point) {
        if (value.getStatusCode().isGood()) {
            WSMessageDTO message = new WSMessageDTO();
            message.setValue(value.getValue().getValue());
            message.setTime(value.getServerTime().getJavaTime());
            message.setAddress(point.getPointAddress());
            return message;
        }
        return null;
    }


}
