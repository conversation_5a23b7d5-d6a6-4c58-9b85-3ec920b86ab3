FROM gradle:jdk17-jammy AS builder
WORKDIR /workspace/app
COPY . .

RUN gradle -x test clean admin-scada:bootJar
RUN java -Djarmode=layertools -jar admin-scada/build/libs/*.jar extract --destination admin-scada/build/libs

FROM openjdk:17-buster
VOLUME /tmp
ARG EXTRACTED=/workspace/app/admin-scada/build/libs
COPY --from=builder  ${EXTRACTED}/dependencies/ ./
COPY --from=builder ${EXTRACTED}/spring-boot-loader/ ./
COPY --from=builder ${EXTRACTED}/snapshot-dependencies/ ./
COPY --from=builder ${EXTRACTED}/application/ ./
# 添加时区环境变量，亚洲，上海
ENV TimeZone=Asia/Shanghai
# 使用软连接，并且将时区配置覆盖/etc/timezone
RUN ln -snf /usr/share/zoneinfo/$TimeZone /etc/localtime && echo $TimeZone > /etc/timezone
ARG JAVA_OPTS
ARG JAVA_TOOL_OPTIONS

ENV JAVA_OPTS="$JAVA_OPTS --add-opens=java.base/java.lang=ALL-UNNAMED"
ENV JAVA_TOOL_OPTIONS="$JAVA_TOOL_OPTIONS -Dfile.encoding=UTF-8 -agentlib:jdwp=transport=dt_socket,address=*:6666,server=y,suspend=n"

#ENTRYPOINT ["sh", "-c","java","${JAVA_OPTS}","-jar","/app.jar ${0} ${@}"]
ENTRYPOINT ["java","org.springframework.boot.loader.launch.JarLauncher"]
