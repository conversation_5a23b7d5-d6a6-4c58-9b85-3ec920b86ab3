plugins {
    id 'java'
    id 'war'
}
group = 'com.yhd'
version = '0.0.1-SNAPSHOT'

dependencies {
    implementation project(':admin-common')

    implementation group: 'commons-codec', name: 'commons-codec'
    implementation group: 'com.fasterxml.jackson.core', name: 'jackson-annotations'
    implementation group: 'commons-codec', name: 'commons-codec'
    implementation group: 'org.apache.commons', name: 'commons-lang3'
    implementation group: 'org.apache.commons', name: 'commons-text'
    implementation group: 'org.apache.commons', name: 'commons-pool2'
    implementation group: 'org.mapstruct', name: 'mapstruct'
    implementation 'com.opencsv:opencsv'
    implementation 'org.redisson:redisson'
// https://mvnrepository.com/artifact/com.baomidou/dynamic-datasource-spring-boot3-starter
    implementation 'com.baomidou:dynamic-datasource-spring-boot3-starter'
    implementation 'com.github.gavlyukovskiy:p6spy-spring-boot-starter'

    //SpringBoot
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation "org.springframework.boot:spring-boot-starter-oauth2-resource-server"
    implementation 'org.springframework.kafka:spring-kafka'
    implementation 'org.springframework.boot:spring-boot-starter-websocket'

    implementation 'com.baomidou:mybatis-plus-spring-boot3-starter'
    implementation 'com.baomidou:mybatis-plus-jsqlparser'

    implementation 'com.mysql:mysql-connector-j'
    implementation 'org.postgresql:postgresql'
//    implementation 'cn.hutool:hutool-all'
    implementation 'commons-io:commons-io'
    implementation 'commons-codec:commons-codec'
    implementation 'org.apache.commons:commons-collections4'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'

    implementation 'com.google.guava:guava:32.0.0-jre'
    implementation('org.eclipse.milo:sdk-server:0.6.8') {
        exclude group: 'org.bouncycastle', module: 'bcprov-jdk15on:1.69'
    }
    implementation 'org.eclipse.milo:sdk-client:0.6.8'
    implementation 'org.eclipse.milo:stack-client:0.6.8'
    implementation 'org.eclipse.milo:stack-server:0.6.8'
    implementation 'com.alibaba:fastjson:2.0.0'
    implementation 'javax.servlet:javax.servlet-api:4.0.1'


    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor group: 'org.mapstruct', name: 'mapstruct-processor'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    runtimeOnly 'com.mysql:mysql-connector-j'
    providedRuntime 'org.springframework.boot:spring-boot-starter-tomcat'

    implementation 'org.codehaus.jackson:jackson-mapper-asl:1.9.13'

    //Test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding'

    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok-mapstruct-binding'


}

