{"modules": [{"name": "SSO", "chnname": "单点登录模块", "entities": [{"title": "oauth_approvals", "chnname": "", "fields": [{"name": "userId", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "clientId", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "scope", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "STATUS", "type": "VARCHAR_10", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "expiresAt", "type": "TIMESTAMP", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "lastModifiedAt", "type": "TIMESTAMP", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}]}, {"title": "oauth_client_details", "chnname": "", "fields": [{"name": "client_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": true, "notNull": true, "autoIncrement": false, "defaultValue": ""}, {"name": "resource_ids", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "client_secret", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "scope", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authorized_grant_types", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "web_server_redirect_uri", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authorities", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "access_token_validity", "type": "BigInt", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "refresh_token_validity", "type": "BigInt", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "additional_information", "type": "VARCHAR_4096", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "autoapprove", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}]}, {"title": "oauth_client_token", "chnname": "", "fields": [{"name": "token_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "token", "type": "VARBINARY", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authentication_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": true, "notNull": true, "autoIncrement": false, "defaultValue": ""}, {"name": "user_name", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "client_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}]}, {"title": "oauth_code", "chnname": "", "fields": [{"name": "CODE", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authentication", "type": "VARBINARY", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}]}, {"title": "oauth_refresh_token", "chnname": "", "fields": [{"name": "token_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "token", "type": "VARBINARY", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authentication", "type": "VARBINARY", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}]}, {"title": "oauth_access_token", "chnname": "", "fields": [{"name": "token_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "token", "type": "VARBINARY", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authentication_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": true, "notNull": true, "autoIncrement": false, "defaultValue": ""}, {"name": "user_name", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "client_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authentication", "type": "VARBINARY", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "refresh_token", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}]}], "graphCanvas": {"nodes": [], "edges": []}, "associations": []}, {"name": "SYS", "chnname": "系统配置", "entities": [{"title": "tb_sys_user_account", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "userName", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "登录名称", "pk": true, "notNull": true}, {"name": "password", "type": "MiddleString", "remark": "", "chnname": "密码", "notNull": true}, {"name": "is_account_non_expired", "type": "YesNo", "remark": "", "chnname": "账户未过期"}, {"name": "is_account_non_locked", "type": "YesNo", "remark": "", "chnname": "账户未锁定"}, {"name": "is_credentials_non_expired", "type": "YesNo", "remark": "", "chnname": "密码未过期"}, {"name": "is_enable", "type": "YesNo", "remark": "", "chnname": "账户状态"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "登录账户表"}, {"title": "tb_sys_user_role", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "account_id", "type": "BigInt", "remark": "", "chnname": "账户ID"}, {"name": "role_id", "type": "BigInt", "remark": "", "chnname": "角色"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "账户角色"}, {"title": "tb_sys_role", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "role_name", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "角色名称"}, {"name": "role_code", "type": "DefaultString", "remark": "", "chnname": "角色编码"}, {"name": "is_enable", "type": "YesNo", "remark": "", "chnname": "角色状态"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "角色表"}, {"title": "tb_sys_user", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "account_name", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "关联账户"}, {"name": "name", "type": "ShortString", "remark": "", "chnname": "姓名"}, {"name": "org_id", "type": "BigInt", "remark": "", "chnname": "部门"}, {"name": "avatar", "type": "MiddleString", "remark": "", "chnname": "头像"}, {"name": "email", "type": "ShortString", "remark": "", "chnname": "邮件"}, {"name": "signature", "type": "MiddleString", "remark": "", "chnname": "签名"}, {"name": "title", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "头衔"}, {"name": "address", "type": "DefaultString", "remark": "", "chnname": "地址"}, {"name": "phone", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "手机电话"}, {"name": "tags", "type": "DefaultString", "remark": "", "chnname": "标签"}, {"name": "is_enable", "type": "YesNo", "remark": "0禁用，1启用", "chnname": "状态"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "用户信息表"}, {"title": "tb_sys_menu", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "parent_id", "type": "BigInt", "remark": "", "chnname": "父主键", "notNull": true}, {"name": "client_id", "type": "DefaultString", "remark": "", "chnname": "客户端ID"}, {"name": "`key`", "type": "DefaultString", "remark": "", "chnname": "菜单标识", "notNull": true}, {"name": "path", "type": "DefaultString", "remark": "", "chnname": "路径", "notNull": true}, {"name": "name", "type": "DefaultString", "remark": "", "chnname": "菜单名称", "notNull": true}, {"name": "locale", "type": "DefaultString", "remark": "", "chnname": "国际化"}, {"name": "icon", "type": "DefaultString", "remark": "", "chnname": "图标"}, {"name": "hide_in_menu", "type": "YesNo", "remark": "0:显示1:隐藏", "chnname": "隐藏菜单", "defaultValue": "0"}, {"name": "hide_children_in_menu", "type": "YesNo", "remark": "0:显示1:隐藏", "chnname": "隐藏子菜单", "defaultValue": "0"}, {"name": "authority", "type": "DefaultString", "remark": "", "chnname": "权限"}, {"name": "type", "type": "Dict", "remark": "0：目录   1：菜单   2：按钮", "chnname": "类型"}, {"name": "order_num", "type": "Integer", "remark": "", "chnname": "排序"}, {"name": "level", "type": "Integer", "remark": "", "chnname": "层级"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "菜单表"}, {"title": "tb_sys_role_menu", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "role_id", "type": "BigInt", "remark": "", "chnname": "角色ID"}, {"name": "menu_id", "type": "DefaultString", "remark": "", "chnname": "菜单ID"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "角色菜单名称"}, {"title": "tb_sys_parameter", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "param_key", "type": "DefaultString", "remark": "", "chnname": "参数KEY", "pk": false, "notNull": false}, {"name": "param_val", "type": "DefaultString", "remark": "", "chnname": "参数值"}, {"name": "is_enable", "type": "YesNo", "remark": "0否1是", "chnname": "状态"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [{"name": "INX_PARAM_KEY", "isUnique": true, "fields": ["param_key"]}], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "系统参数表", "remark": "控制系统常用的变量。"}, {"title": "tb_sys_dic", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "name", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "字典名称"}, {"name": "category", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "字典类别"}, {"name": "status", "type": "YesNo", "remark": "", "chnname": "状态"}, {"name": "order_num", "type": "DefaultString", "remark": "", "chnname": "排序"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "字典表", "remark": "字典表"}, {"title": "t_sys_dic_item", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "dic_id", "type": "BigInt", "remark": "", "chnname": "字典表主键"}, {"name": "category", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "字典分类"}, {"name": "code", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "编码"}, {"name": "val", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "编码值"}, {"name": "status", "type": "YesNo", "remark": "", "chnname": "状态"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "字典项表", "remark": "字典项表"}, {"title": "tb_sys_org", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "org_name", "type": "ShortString", "remark": "", "chnname": "组织名称"}, {"name": "org_code", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "组织编码"}, {"name": "parent_id", "type": "BigInt", "remark": "", "chnname": "父级ID"}, {"name": "parent_name", "type": "ShortString", "remark": "", "chnname": "父级名称"}, {"name": "status", "type": "YesNo", "remark": "", "chnname": "状态"}, {"name": "sort_num", "type": "BigInt", "remark": "", "chnname": "排序"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "组织管理", "remark": "部门组织管理"}, {"title": "tb_sys_client", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "client_id", "type": "DefaultString", "remark": "", "chnname": "模块名称"}, {"name": "resource_id", "type": "DefaultString", "remark": "", "chnname": "资源名称"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "客户端资源模块", "remark": "客户端，模块，用户区分不同的终端。"}, {"title": "tb_sys_ops_log", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "url", "type": "LongString", "remark": "", "chnname": "URL"}, {"name": "method", "type": "ShortString", "remark": "", "chnname": "方法名称"}, {"name": "params", "type": "LongString", "remark": "", "chnname": "参数"}, {"name": "client_ip", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "客户端IP"}, {"name": "client_module", "type": "ShortString", "remark": "", "chnname": "来源模块"}, {"name": "ops_name", "type": "ShortString", "remark": "", "chnname": "操作人"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "用户操作日志"}, {"title": "tb_sys_template_table_column", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "table_id", "type": "DefaultString", "remark": "", "chnname": "表主键"}, {"name": "column_group_id", "type": "DefaultString", "remark": "", "chnname": "父级Column_ID"}, {"name": "index", "type": "Integer", "remark": "", "chnname": "顺序"}, {"name": "title", "type": "DefaultString", "remark": "", "chnname": "标题"}, {"name": "data_index", "type": "DefaultString", "remark": "支持一个数字，[a,b] 会转化为 obj.a.b", "chnname": "与实体映射的key"}, {"name": "key", "type": "DefaultString", "remark": "", "chnname": "确定这个列的唯一值"}, {"name": "align", "type": "DefaultString", "remark": "left | right | center", "chnname": "设置列的对齐方式", "defaultValue": "left"}, {"name": "col_size", "type": "Integer", "remark": "", "chnname": "每个表单占据的格子大小"}, {"name": "tooltip", "type": "DefaultString", "remark": "", "chnname": "展示一个 icon，hover 是展示一些提示信息"}, {"name": "initial_value", "type": "ShortString", "remark": "", "chnname": "搜索表单的默认值"}, {"name": "ellipsis", "type": "YesNo", "remark": "", "chnname": "是否缩略"}, {"name": "copyable", "type": "YesNo", "remark": "", "chnname": "是否拷贝"}, {"name": "search", "type": "YesNo", "remark": "", "chnname": "在查询表单中隐藏"}, {"name": "hide_in_table", "type": "YesNo", "remark": "", "chnname": "在 table 中隐藏"}, {"name": "hide_in_form", "type": "YesNo", "remark": "", "chnname": "在新建表单中删除"}, {"name": "hide_in_setting", "type": "YesNo", "remark": "", "chnname": "不在配置工具中显示"}, {"name": "filters", "type": "YesNo", "remark": "", "chnname": "表头的筛选菜单项"}, {"name": "on_filter", "type": "YesNo", "remark": "", "chnname": "可编辑表格是否可编辑"}, {"name": "editable", "type": "DefaultString", "remark": "", "chnname": "可编辑表格是否可编辑"}, {"name": "value_enum", "type": "MiddleString", "remark": "", "chnname": "映射值的类型"}, {"name": "hide_in_descriptions", "type": "YesNo", "remark": "", "chnname": "隐藏在 descriptions"}, {"name": "form_item_props", "type": "MiddleString", "remark": "", "chnname": "自定义的 formItemProps render"}, {"name": "field_props", "type": "MiddleString", "remark": "", "chnname": "自定义的 fieldProps render"}, {"name": "render_form_item", "type": "DefaultString", "remark": "", "chnname": "自定义编辑模式"}, {"name": "render", "type": "DefaultString", "remark": "", "chnname": "自定义渲染"}, {"name": "value_type", "type": "DefaultString", "remark": "", "chnname": "选择如何渲染相应的模式"}, {"name": "render_text", "type": "DefaultString", "remark": "", "chnname": "自定义 render"}, {"name": "fixed", "type": "DefaultString", "remark": "", "chnname": "IE 下无效）列是否固定，可选 true (等效于 left) left right"}, {"name": "width", "type": "DefaultString", "remark": "", "chnname": "列宽度"}, {"name": "column_sort", "type": "Integer", "remark": "", "chnname": "列顺序"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "表格列", "remark": "列描述数据对象"}, {"title": "tb_sys_template_table", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "table_name", "type": "DefaultString", "remark": "", "chnname": ""}, {"name": "type", "type": "DefaultString", "remark": "", "chnname": "pro-table 类型"}, {"name": "title", "type": "DefaultString", "remark": "", "chnname": "表格标题"}, {"name": "column_empty_text", "type": "DefaultString", "remark": "", "chnname": "空值时的显示，不设置时显示 -， false 可以关闭此功能"}, {"name": "bordered", "type": "YesNo", "remark": "", "chnname": "是否展示外边框和列边框"}, {"name": "loading", "type": "YesNo", "remark": "", "chnname": "页面是否加载中"}, {"name": "pagination", "type": "DefaultString", "remark": "", "chnname": "分页器，参考配置项或 pagination 文档，设为 false 时不展示和进行分页"}, {"name": "row_key", "type": "DefaultString", "remark": "", "chnname": "表格行 key 的取值，可以是字符串或一个函数"}, {"name": "show_header", "type": "YesNo", "remark": "", "chnname": "是否显示表头"}, {"name": "show_sorter_tooltip", "type": "YesNo", "remark": "", "chnname": "表头是否显示下一次排序的 tooltip 提示。当参数类型为对象时，将被设置为 Tooltip 的属性"}, {"name": "size", "type": "DefaultString", "remark": "default | middle | small", "chnname": "表格大小", "defaultValue": ""}, {"name": "sort_directions", "type": "DefaultString", "remark": "", "chnname": "支持的排序方式，取值为 ascend descend"}, {"name": "sticky", "type": "DefaultString", "remark": "boolean | {offsetHeader?: number, offsetScroll?: number, getContainer?: () => HTMLElement}", "chnname": "设置粘性头部和滚动条"}, {"name": "summary", "type": "DefaultString", "remark": "", "chnname": "总结栏"}, {"name": "table_layout", "type": "DefaultString", "remark": "", "chnname": "表格元素的 table-layout 属性，设为 fixed 表示内容不会影响列的布局"}, {"name": "request", "type": "DefaultString", "remark": "", "chnname": "一个获得 dataSource 的方法"}, {"name": "tool_bar_render", "type": "DefaultString", "remark": "", "chnname": "渲染操作栏"}, {"name": "table_alert_render", "type": "DefaultString", "remark": "", "chnname": "自定义 table 的 alert"}, {"name": "table_alert_option_render", "type": "DefaultString", "remark": "", "chnname": "自定义 table 的 alert 的操作"}, {"name": "row_selection", "type": "DefaultString", "remark": "", "chnname": "表格行是否可选择"}, {"name": "editable", "type": "DefaultString", "remark": "", "chnname": "可编辑表格的相关配置"}, {"name": "cardBordered", "type": "DefaultString", "remark": "", "chnname": "able 和 Search 外围 Card 组件的边框"}, {"name": "on_change", "type": "DefaultString", "remark": "", "chnname": "分页、排序、筛选变化时触发"}, {"name": "on_header_row", "type": "DefaultString", "remark": "", "chnname": "设置头部行属性"}, {"name": "on_row", "type": "DefaultString", "remark": "", "chnname": "设置行属性"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "动态表格模板"}], "graphCanvas": {"edges": [], "nodes": []}, "associations": []}], "dataTypeDomains": {"datatype": [{"name": "默认字串", "code": "DefaultString", "apply": {"JAVA": {"type": "String"}, "MYSQL": {"type": "VARCHAR(32)"}, "ORACLE": {"type": "NVARCHAR2(32)"}, "SQLServer": {"type": "NVARCHAR(32)"}, "PostgreSQL": {"type": "VARCHAR(32)"}}}, {"name": "标识号", "code": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "apply": {"JAVA": {"type": "String"}, "ORACLE": {"type": "VARCHAR2(32)"}, "MYSQL": {"type": "VARCHAR(32)"}, "SQLServer": {"type": "VARCHAR(32)"}, "PostgreSQL": {"type": "VARCHAR(32)"}}}, {"name": "标识号-长", "code": "<PERSON><PERSON><PERSON>", "apply": {"MYSQL": {"type": "VARCHAR(64)"}, "ORACLE": {"type": "VARCHAR2(64)"}, "JAVA": {"type": "String"}, "SQLServer": {"type": "VARCHAR(64)"}, "PostgreSQL": {"type": "VARCHAR(64)"}}}, {"name": "名称", "code": "Name", "apply": {"JAVA": {"type": "String"}, "MYSQL": {"type": "VARCHAR(128)"}, "ORACLE": {"type": "NVARCHAR2(128)"}, "SQLServer": {"type": "NVARCHAR(128)"}, "PostgreSQL": {"type": "VARCHAR(128)"}}}, {"name": "备注说明", "code": "Intro", "apply": {"JAVA": {"type": "String"}, "MYSQL": {"type": "VARCHAR(512)"}, "ORACLE": {"type": "NVARCHAR2(512)"}, "SQLServer": {"type": "NVARCHAR(512)"}, "PostgreSQL": {"type": "VARCHAR(512)"}}}, {"name": "字串-短", "code": "ShortString", "apply": {"JAVA": {"type": "String"}, "MYSQL": {"type": "VARCHAR(128)"}, "ORACLE": {"type": "NVARCHAR2(128)"}, "SQLServer": {"type": "NVARCHAR(128)"}, "PostgreSQL": {"type": "VARCHAR(128)"}}}, {"name": "字串-中", "code": "MiddleString", "apply": {"JAVA": {"type": "String"}, "MYSQL": {"type": "VARCHAR(1024)"}, "ORACLE": {"type": "NVARCHAR2(1024)"}, "SQLServer": {"type": "NVARCHAR(1024)"}, "PostgreSQL": {"type": "VARCHAR(1024)"}}}, {"name": "字串-长", "code": "LongString", "apply": {"JAVA": {"type": "String"}, "ORACLE": {"type": "NVARCHAR2(3072)"}, "MYSQL": {"type": "VARCHAR(3072)"}, "SQLServer": {"type": "NVARCHAR(3072)"}, "PostgreSQL": {"type": "VARCHAR(3072)"}}}, {"name": "大文本", "code": "LongText", "apply": {"JAVA": {"type": "String"}, "MYSQL": {"type": "TEXT"}, "ORACLE": {"type": "CLOB"}, "SQLServer": {"type": "NTEXT"}, "PostgreSQL": {"type": "TEXT"}}}, {"name": "小数", "code": "Double", "apply": {"JAVA": {"type": "Double"}, "MYSQL": {"type": "DECIMAL(32,10)"}, "ORACLE": {"type": "NUMBER(32,10)"}, "SQLServer": {"type": "DECIMAL(32,10)"}, "PostgreSQL": {"type": "DECIMAL(32,10)"}}}, {"name": "比例", "code": "<PERSON><PERSON>", "apply": {"MYSQL": {"type": "DECIMAL(4,2)"}, "JAVA": {"type": "Double"}, "ORACLE": {"type": "NUMBER(4,2)"}, "SQLServer": {"type": "DECIMAL(4,2)"}, "PostgreSQL": {"type": "DECIMAL(4,2)"}}}, {"name": "整数", "code": "Integer", "apply": {"JAVA": {"type": "Integer"}, "MYSQL": {"type": "INT"}, "ORACLE": {"type": "INT"}, "SQLServer": {"type": "INT"}, "PostgreSQL": {"type": "INT"}}}, {"name": "大整数", "code": "BigInt", "apply": {"MYSQL": {"type": "BIGINT"}, "JAVA": {"type": "<PERSON>"}, "ORACLE": {"type": "NUMBER"}, "SQLServer": {"type": "BIGINT"}, "PostgreSQL": {"type": "BIGINT"}}}, {"name": "金额", "code": "Money", "apply": {"JAVA": {"type": "Double"}, "MYSQL": {"type": "DECIMAL(32,8)"}, "ORACLE": {"type": "NUMBER(32,8)"}, "SQLServer": {"type": "DECIMAL(32,8)"}, "PostgreSQL": {"type": "DECIMAL(32,8)"}}}, {"name": "是否", "code": "YesNo", "apply": {"JAVA": {"type": "Boolean"}, "MYSQL": {"type": "TINYINT(1)"}, "ORACLE": {"type": "VARCHAR2(1)"}, "SQLServer": {"type": "VARCHAR(1)"}, "PostgreSQL": {"type": "VARCHAR(1)"}}}, {"name": "数据字典", "code": "Dict", "apply": {"JAVA": {"type": "String"}, "MYSQL": {"type": "VARCHAR(32)"}, "ORACLE": {"type": "VARCHAR2(32)"}, "SQLServer": {"type": "VARCHAR(32)"}, "PostgreSQL": {"type": "VARCHAR(32)"}}}, {"name": "日期", "code": "Date", "apply": {"JAVA": {"type": "LocalDate"}, "MYSQL": {"type": "DATE"}, "ORACLE": {"type": "DATE"}, "SQLServer": {"type": "DATE"}, "PostgreSQL": {"type": "DATE"}}}, {"name": "日期时间", "code": "DateTime", "apply": {"JAVA": {"type": "LocalDateTime"}, "MYSQL": {"type": "DATETIME"}, "ORACLE": {"type": "DATE"}, "SQLServer": {"type": "DATE"}, "PostgreSQL": {"type": "DATE"}}}, {"name": "单字符", "code": "Char", "apply": {"MYSQL": {"type": "CHAR(1)"}, "ORACLE": {"type": "CHAR(1)"}, "JAVA": {"type": "String"}, "SQLServer": {"type": "CHAR(1)"}, "PostgreSQL": {"type": "CHAR(1)"}}}, {"name": "INT_10", "code": "INT_10", "apply": {"MYSQL": {"type": "INT(10)"}}}, {"name": "TIMESTAMP", "code": "TIMESTAMP", "apply": {"MYSQL": {"type": "TIMESTAMP"}}}, {"name": "VARBINARY", "code": "VARBINARY", "apply": {"MYSQL": {"type": "VARBINARY(6535)"}}}, {"name": "VARCHAR_10", "code": "VARCHAR_10", "apply": {"MYSQL": {"type": "VARCHAR(10)"}}}, {"name": "VARCHAR_256", "code": "VARCHAR_256", "apply": {"MYSQL": {"type": "VARCHAR(256)"}}}, {"name": "VARCHAR_4096", "code": "VARCHAR_4096", "apply": {"MYSQL": {"type": "VARCHAR(4096)"}}}], "database": [{"code": "MYSQL", "template": "DROP TABLE {{=it.entity.title}};\n$blankline\nCREATE TABLE {{=it.entity.title}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}} {{= field.pk ? 'NOT NULL' : '' }} COMMENT '{{=it.func.join(field.chnname,field.remark,';')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n) COMMENT = '{{=it.func.join(it.entity.chnname,it.entity.remark,';') }}'", "fileShow": true, "defaultDatabase": true, "createTableTemplate": "CREATE TABLE {{=it.entity.title}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'AUTO_INCREMENT' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }} COMMENT '{{=it.func.join(field.chnname,field.remark,' ')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n) COMMENT = '{{=it.func.join(it.entity.chnname,it.entity.remark,' ') }}';{{=it.separator}}\n$blankline\n", "deleteTableTemplate": "DROP TABLE {{=it.entity.title}};{{=it.separator}}/*SkipError*/", "rebuildTableTemplate": "create table PDMAN_UP_{{=it.oldEntity.title}}\nas select * from {{=it.oldEntity.title}};{{=it.separator}}\n$blankline\n\ndrop table {{=it.oldEntity.title}};{{=it.separator}}\n$blankline\n\nCREATE TABLE {{=it.newEntity.title}}(\n{{ pkList = [] ; }}\n{{~it.newEntity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}} {{= field.notNull ? 'NOT NULL' : '' }} COMMENT '{{=it.func.join(field.chnname,field.remark,';')}}' {{= index < it.newEntity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n) COMMENT = '{{=it.func.join(it.newEntity.chnname,it.newEntity.remark,';') }}';{{=it.separator}}\n$blankline\n\n{{ sameCols = it.func.intersect(it.newEntity.fields,it.oldEntity.fields) ;}}\ninsert into {{=it.newEntity.title}}(\n{{~sameCols:field:index}}\n    {{=field.name}}{{? index<sameCols.length-1}},{{?}}\n{{~}}\n) \nselect \n{{~sameCols:field:index}}\n    {{=field.name}}{{? index<sameCols.length-1}},{{?}}\n{{~}}\nfrom PDMAN_UP_{{=it.oldEntity.title}};{{=it.separator}}\n$blankline\n\ndrop table PDMAN_UP_{{=it.oldEntity.title}};{{=it.separator}}\n$blankline\n{{~it.newEntity.indexs:index}}\nALTER TABLE {{=it.newEntity.title}} ADD {{? index.isUnique}}UNIQUE{{??}}INDEX{{?}} {{=index.name}}({{=it.func.join(...index.fields,',')}});{{=it.separator}}\n{{~}}", "createFieldTemplate": "ALTER TABLE {{=it.entity.title}} ADD COLUMN {{=it.field.name}} {{=it.field.type}} {{? it.field.notNull}}NOT NULL{{?}} {{? it.field.defaultValue}}DEFAULT {{? null==it.field.defaultValue}}NULL{{??}}'{{=it.field.defaultValue}}'{{?}}{{?}} {{? it.field.autoIncrement}}AUTO_INCREMENT{{?}} {{? it.field.pk}}PRIMARY KEY{{?}} {{? it.field.chnname}}COMMENT '{{=it.field.chnname}}'{{?}} {{? it.field.addAfter}}AFTER {{=it.field.addAfter}}{{?}};{{=it.separator}}", "updateFieldTemplate": "ALTER TABLE {{=it.entity.title}} MODIFY COLUMN {{=it.field.name}} {{=it.field.type}} {{? it.field.notNull}}NOT NULL{{?}} {{? it.field.defaultValue}}DEFAULT {{? null==it.field.defaultValue}}NULL{{??}}'{{=it.field.defaultValue}}'{{?}}{{?}} {{? it.field.autoIncrement}}AUTO_INCREMENT{{?}} {{? it.field.chnname}}COMMENT '{{=it.field.chnname}}'{{?}};{{=it.separator}}", "deleteFieldTemplate": "ALTER TABLE {{=it.entity.title}} DROP {{=it.field.name}};{{=it.separator}}", "deleteIndexTemplate": "ALTER TABLE {{=it.entity.title}} DROP INDEX {{=it.index.name}};{{=it.separator}}", "createIndexTemplate": "ALTER TABLE {{=it.entity.title}} ADD {{? it.index.isUnique}}UNIQUE{{??}}INDEX{{?}} {{=it.index.name}}({{=it.func.join(...it.index.fields,',')}});{{=it.separator}}", "updateTableComment": "ALTER TABLE {{=it.entity.title}} COMMENT '{{=it.entity.chnname}}';{{=it.separator}}"}, {"code": "ORACLE", "template": "DROP TABLE {{=it.entity.title}};{{=it.separator}}\n$blankline\nCREATE TABLE {{=it.entity.title}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}}  {{= field.pk ? 'NOT NULL' : '' }} {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);{{=it.separator}}\n$blankline\nCOMMENT ON TABLE {{=it.entity.title}} IS '{{=it.func.join(it.entity.chnname,it.entity.remark,';') }}';{{=it.separator}}\n{{~it.entity.fields:field:index}}\nCOMMENT ON COLUMN {{=it.entity.title}}.{{=field.name}} IS '{{=it.func.join(field.chnname,field.remark,';')}}';{{=it.separator}}\n{{~}}", "createTableTemplate": "CREATE TABLE {{=it.entity.title}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}}{{? field.defaultValue}} DEFAULT {{=field.defaultValue}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);{{=it.separator}}\n$blankline\n{{? it.entity.chnname || it.entity.remark}}COMMENT ON TABLE {{=it.entity.title}} IS {{? it.entity.remark}}'{{=it.entity.remark}}'{{??}}'{{=it.entity.chnname}}'{{?}};{{=it.separator}}{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.chnname || field.remark}}COMMENT ON COLUMN {{=it.entity.title}}.{{=field.name}} IS {{? field.remark}}'{{=field.remark}}'{{??}}'{{=field.chnname}}'{{?}};{{=it.separator}}{{?}}\n{{~}}", "deleteTableTemplate": "DROP TABLE {{=it.entity.title}};{{=it.separator}}/*SkipError*/\r\n$blankline", "rebuildTableTemplate": "CREATE TABLE PDMAN_UP_{{=it.oldEntity.title}}\nAS SELECT * FROM {{=it.oldEntity.title}};{{=it.separator}}\n$blankline\nDROP TABLE {{=it.oldEntity.title}};{{=it.separator}}\n$blankline\nCREATE TABLE {{=it.newEntity.title}}(\n{{ pkList = [] ; }}{{~it.newEntity.fields:field:index}}{{? field.pk }}{{ pkList.push(field.name) }}{{?}}    {{=field.name}} {{=field.type}}{{? field.defaultValue}} DEFAULT {{=field.defaultValue}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= index < it.newEntity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}{{? pkList.length >0 }}    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}}){{?}}\n);{{=it.separator}}\n$blankline\n{{? it.newEntity.chnname || it.newEntity.remark}}COMMENT ON TABLE {{=it.newEntity.title}} IS {{? it.newEntity.remark}}'{{=it.entity.remark}}'{{??}}'{{=it.newEntity.chnname}}'{{?}};{{?}}{{=it.separator}}\n{{~it.newEntity.fields:field:index}}\n{{? field.chnname || field.remark}}COMMENT ON COLUMN {{=it.newEntity.title}}.{{=field.name}} IS {{? field.remark}}'{{=field.remark}}'{{??}}'{{=field.chnname}}'{{?}};{{?}}{{=it.separator}}\n{{~}}\n{{ sameCols = it.func.intersect(it.newEntity.fields,it.oldEntity.fields) ;}}\n$blankline\nINSERT INTO {{=it.newEntity.title}}(\n{{~sameCols:field:index}}   {{=field.name}}{{? index<sameCols.length-1}},{{?}}\n{{~}}) \nSELECT\n{{~sameCols:field:index}}   {{=field.name}}{{? index<sameCols.length-1}},{{?}}\n{{~}}FROM PDMAN_UP_{{=it.oldEntity.title}};{{=it.separator}}\n\nDROP TABLE PDMAN_UP_{{=it.oldEntity.title}};{{=it.separator}}\n{{~it.newEntity.indexs:index}}\nCREATE{{? index.isUnique}} UNIQUE{{?}} INDEX {{=index.name}} ON {{=it.newEntity.title}}({{=it.func.join(index.fields,',')}});{{=it.separator}}\n{{~}}", "createFieldTemplate": "ALTER TABLE {{=it.entity.title}} ADD({{=it.field.name}} {{=it.field.type}}{{? it.field.defaultValue}} DEFAULT {{=it.field.defaultValue}}{{?}}{{? it.field.notNull}} NOT NULL{{?}});{{=it.separator}}\r\n{{? it.field.chnname || it.field.remark}}COMMENT ON COLUMN {{=it.entity.title}}.{{=it.field.name}} IS {{? it.field.remark}}'{{=it.field.remark}}'{{??}}'{{=it.field.chnname}}'{{?}};{{=it.separator}}{{?}}\r\n$blankline", "updateFieldTemplate": "ALTER TABLE {{=it.entity.title}} MODIFY({{=it.field.name}} {{=it.field.type}}{{? it.field.defaultValue}} DEFAULT {{=it.field.defaultValue}}{{?}}{{? it.field.notNull}} NOT NULL{{?}});{{=it.separator}}\r\n{{? it.field.chnname || it.field.remark}}COMMENT ON COLUMN {{=it.entity.title}}.{{=it.field.name}} IS {{? it.field.remark}}'{{=it.field.remark}}'{{??}}'{{=it.field.chnname}}'{{?}};{{=it.separator}}{{=it.separator}}{{?}}\r\n$blankline", "deleteFieldTemplate": "ALTER TABLE {{=it.entity.title}} DROP({{=it.field.name}});{{=it.separator}}\r\n$blankline", "deleteIndexTemplate": "DROP INDEX {{=it.entity.title}}.{{=it.index.name}};{{=it.separator}}\r\n$blankline", "createIndexTemplate": "CREATE{{? it.index.isUnique}} UNIQUE{{?}} INDEX {{=it.index.name}} ON {{=it.entity.title}}({{=it.func.join(it.index.fields,',')}});{{=it.separator}}\r\n$blankline", "updateTableComment": "{{? it.entity.chnname || it.entity.remark}}COMMENT ON TABLE {{=it.entity.title}} IS {{? it.entity.remark}}'{{=it.entity.remark}}'{{??}}'{{=it.entity.chnname}}'{{?}};{{=it.separator}}{{?}}\r\n$blankline"}, {"code": "SQLServer", "createTableTemplate": "CREATE TABLE {{=it.entity.title}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}} {{= field.notNull ? 'NOT NULL' : '' }} {{= field.autoIncrement ? 'IDENTITY(1,1)' : '' }} {{= field.defaultValue ? it.func.join('DEFAULT',field.defaultValue,' ') : '' }}  {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    CONSTRAINT PK_{{=it.entity.title}} PRIMARY KEY CLUSTERED ({{~pkList:pkName:i}}{{= pkName }} ASC {{= i<pkList.length-1 ? ',' : '' }}{{~}}) ON [PRIMARY] \n{{?}}\n) ;{{=it.separator}}\n\n$blankline\nEXECUTE sp_addextendedproperty N'MS_Description', '{{= it.entity.chnname || it.entity.remark}}', N'user', N'dbo', N'table', N'{{=it.entity.title}}', NULL, NULL;{{=it.separator}}\n{{~it.entity.fields:field:index}}\n{{? field.chnname || field.remark}}EXECUTE sp_addextendedproperty N'MS_Description', {{? field.remark}}'{{=field.remark}}'{{??}}'{{=field.chnname}}'{{?}}, N'user', N'dbo', N'table', N'{{=it.entity.title}}', N'column', N'{{=field.name}}';{{=it.separator}}{{?}}\n{{~}}\n", "deleteTableTemplate": "", "rebuildTableTemplate": "", "createFieldTemplate": "", "updateFieldTemplate": "", "deleteFieldTemplate": "", "deleteIndexTemplate": "", "createIndexTemplate": "", "updateTableComment": ""}, {"code": "PostgreSQL", "template": "DROP TABLE {{=it.entity.title}};\n$blankline\nCREATE TABLE {{=it.entity.title}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}} {{= field.pk ? 'NOT NULL' : '' }} COMMENT '{{=it.func.join(field.chnname,field.remark,';')}}' {{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n) COMMENT = '{{=it.func.join(it.entity.chnname,it.entity.remark,';') }}'", "createTableTemplate": "CREATE TABLE {{=it.entity.title}}(\n{{ pkList = [] ; }}\n{{~it.entity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}}{{? field.defaultValue}} DEFAULT {{=field.defaultValue}}{{?}}{{= field.notNull ? ' NOT NULL' : '' }}{{= index < it.entity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n);{{=it.separator}}\n$blankline\n{{? it.entity.chnname || it.entity.remark}}COMMENT ON TABLE {{=it.entity.title}} IS {{? it.entity.remark}}'{{=it.entity.remark}}'{{??}}'{{=it.entity.chnname}}'{{?}};{{=it.separator}}{{?}}\n{{~it.entity.fields:field:index}}\n{{? field.chnname || field.remark}}COMMENT ON COLUMN {{=it.entity.title}}.{{=field.name}} IS {{? field.remark}}'{{=field.remark}}'{{??}}'{{=field.chnname}}'{{?}};{{=it.separator}}{{?}}\n{{~}}", "deleteTableTemplate": "DROP TABLE {{=it.entity.title}};{{=it.separator}}/*SkipError*/", "rebuildTableTemplate": "create table PDMAN_UP_{{=it.oldEntity.title}}\nas select * from {{=it.oldEntity.title}};{{=it.separator}}\n$blankline\n\ndrop table {{=it.oldEntity.title}};{{=it.separator}}\n$blankline\n\nCREATE TABLE {{=it.newEntity.title}}(\n{{ pkList = [] ; }}\n{{~it.newEntity.fields:field:index}}\n    {{? field.pk }}{{ pkList.push(field.name) }}{{?}}\n    {{=field.name}} {{=field.type}} {{= field.notNull ? 'NOT NULL' : '' }} COMMENT '{{=it.func.join(field.chnname,field.remark,';')}}' {{= index < it.newEntity.fields.length-1 ? ',' : ( pkList.length>0 ? ',' :'' ) }}\n{{~}}\n{{? pkList.length >0 }}\n    PRIMARY KEY ({{~pkList:pkName:i}}{{= pkName }}{{= i<pkList.length-1 ? ',' : '' }}{{~}})\n{{?}}\n) COMMENT = '{{=it.func.join(it.newEntity.chnname,it.newEntity.remark,';') }}';{{=it.separator}}\n$blankline\n\n{{ sameCols = it.func.intersect(it.newEntity.fields,it.oldEntity.fields) ;}}\ninsert into {{=it.newEntity.title}}(\n{{~sameCols:field:index}}\n    {{=field.name}}{{? index<sameCols.length-1}},{{?}}\n{{~}}\n) \nselect \n{{~sameCols:field:index}}\n    {{=field.name}}{{? index<sameCols.length-1}},{{?}}\n{{~}}\nfrom PDMAN_UP_{{=it.oldEntity.title}};{{=it.separator}}\n$blankline\n\ndrop table PDMAN_UP_{{=it.oldEntity.title}};{{=it.separator}}", "createFieldTemplate": "ALTER TABLE {{=it.entity.title}} ADD COLUMN {{=it.field.name}} {{=it.field.type}} {{? it.field.notNull}}NOT NULL{{?}} {{? it.field.defaultValue}}DEFAULT {{? null==it.field.defaultValue}}NULL{{??}}'{{=it.field.defaultValue}}'{{?}}{{?}} {{? it.field.autoIncrement}}AUTO_INCREMENT{{?}} {{? it.field.pk}}PRIMARY KEY{{?}} {{? it.field.chnname}}COMMENT '{{=it.field.chnname}}'{{?}} {{? it.field.addAfter}}AFTER {{=it.field.addAfter}}{{?}};{{=it.separator}}", "updateFieldTemplate": "ALTER TABLE {{=it.entity.title}} MODIFY COLUMN {{=it.field.name}} {{=it.field.type}} {{? it.field.notNull}}NOT NULL{{?}} {{? it.field.defaultValue}}DEFAULT {{? null==it.field.defaultValue}}NULL{{??}}'{{=it.field.defaultValue}}'{{?}}{{?}} {{? it.field.autoIncrement}}AUTO_INCREMENT{{?}} {{? it.field.chnname}}COMMENT '{{=it.field.chnname}}'{{?}};{{=it.separator}}", "deleteFieldTemplate": "ALTER TABLE {{=it.entity.title}} DROP {{=it.field.name}};{{=it.separator}}", "deleteIndexTemplate": "ALTER TABLE {{=it.entity.title}} DROP INDEX {{=it.index.name}};{{=it.separator}}", "createIndexTemplate": "ALTER TABLE {{=it.entity.title}} ADD {{? it.index.isUnique}}UNIQUE{{??}}INDEX{{?}} {{=it.index.name}}({{=it.func.join(...it.index.fields,',')}});{{=it.separator}}", "updateTableComment": "ALTER TABLE {{=it.entity.title}} COMMENT '{{=it.entity.chnname}}';{{=it.separator}}"}, {"code": "JAVA", "template": "package group.rober.pdman.{{=it.module.name}}.entity;\n$blankline\nimport javax.persistence.*;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n$blankline\n\n/** {{=it.entity.chnname}} */\n@Table(name=\"{{=it.entity.title}}\")\npublic class {{=it.func.camel(it.entity.title,true) }} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.chnname,field.remark,';')}} */\n    {{? field.pk }}\n    @Id\n    @GeneratedValue\n    {{?}}\n    private {{=field.type}} {{=it.func.camel(field.name,false)}} ;\n{{~}}\n$blankline\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.chnname,field.remark,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.name,true)}}(){\n        return this.{{=it.func.camel(field.name,false)}};\n    }\n    /** {{=it.func.join(field.chnname,field.remark,';')}} */\n    public void set{{=it.func.camel(field.name,true)}}({{=field.type}} {{= it.func.camel(field.name,false) }}){\n        this.{{=it.func.camel(field.name,false)}} = {{= it.func.camel(field.name,false) }};\n    }\n{{~}}\n}", "createTableTemplate": "package group.rober.pdman.{{=it.module.name}}.entity;\n$blankline\nimport javax.persistence.*;\nimport java.io.Serializable;\nimport java.util.Date;\n$blankline\n$blankline\n@Table(name=\"{{=it.entity.title}}\")\npublic class {{=it.func.camel(it.entity.title,true) }} implements Serializable,Cloneable{\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.chnname,field.remark,';')}} */\n    {{? field.pk }}\n    @Id\n    @GeneratedValue\n    {{?}}\n    private {{=field.type}} {{=it.func.camel(field.name,false)}} ;\n{{~}}\n$blankline\n{{~it.entity.fields:field:index}}\n    /** {{=it.func.join(field.chnname,field.remark,';')}} */\n    public {{=field.type}} get{{=it.func.camel(field.name,true)}}(){\n        return this.{{=it.func.camel(field.name,false)}};\n    }\n    /** {{=it.func.join(field.chnname,field.remark,';')}} */\n    public void set{{=it.func.camel(field.name,true)}}({{=field.type}} {{= it.func.camel(field.name,false) }}){\n        this.{{=it.func.camel(field.name,false)}} = {{= it.func.camel(field.name,false) }};\n    }\n{{~}}\n}", "deleteTableTemplate": "", "rebuildTableTemplate": "", "createFieldTemplate": "", "updateFieldTemplate": "", "deleteFieldTemplate": "", "deleteIndexTemplate": "", "createIndexTemplate": "", "updateTableComment": ""}]}, "profile": {"defaultFields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "defaultFieldsType": "2", "javaConfig": {"JAVA_HOME": ""}, "sqlConfig": ";", "dbs": [{"name": "192.168.1.148[admin]", "defaultDB": false, "properties": {"driver_class_name": "com.mysql.jdbc.Driver", "url": "*************************************************************************************************************", "password": "2z_Jhm*u", "username": "root"}}, {"name": "dev.aliyun.com[admin]", "defaultDB": true, "properties": {"driver_class_name": "com.mysql.jdbc.Driver", "url": "**************************************************************************************************************", "password": "F@yX2y4!", "username": "root"}}], "wordTemplateConfig": ""}}