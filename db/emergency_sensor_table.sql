-- 应急救援监测信息表(传感器表)创建脚本
-- 创建时间: 2025-07-31
-- 作者: system

-- 删除表（如果存在）
DROP TABLE IF EXISTS `tb_mk_emergency_sensor`;

-- 创建监测信息表(传感器表)
CREATE TABLE `tb_mk_emergency_sensor` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sensor_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '传感器编码',
  `sensor_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '传感器名称',
  `sensor_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '传感器类型：氧气传感器、二氧化碳传感器、气体传感器、温度传感器等',
  `location` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '安装位置',
  `work_face` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '关联工作面',
  `tunnel` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '关联巷道',
  `status` int NOT NULL DEFAULT 1 COMMENT '传感器状态：1-正常，2-异常，3-离线',
  `org_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '组织编码',
  `current_value` decimal(10,2) DEFAULT NULL COMMENT '当前值',
  `threshold_min` decimal(10,2) DEFAULT NULL COMMENT '最小阈值',
  `threshold_max` decimal(10,2) DEFAULT NULL COMMENT '最大阈值',
  `unit` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '测量单位',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '传感器描述',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_sensor_code` (`sensor_code`) USING BTREE COMMENT '传感器编码唯一索引',
  KEY `idx_sensor_type` (`sensor_type`) USING BTREE COMMENT '传感器类型索引',
  KEY `idx_org_code` (`org_code`) USING BTREE COMMENT '组织编码索引',
  KEY `idx_location` (`location`) USING BTREE COMMENT '位置索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='应急救援监测信息表';

-- 插入示例数据（可选）
INSERT INTO `tb_mk_emergency_sensor` (
  `sensor_code`, 
  `sensor_name`, 
  `sensor_type`, 
  `location`, 
  `work_face`, 
  `tunnel`, 
  `status`, 
  `org_code`, 
  `current_value`, 
  `threshold_min`, 
  `threshold_max`, 
  `unit`, 
  `description`, 
  `remark`,
  `created_by`
) VALUES 
('SENSOR001', '氧气传感器01', '氧气传感器', '井下主巷道入口', '工作面A', '主巷道', 1, 'ORG001', 20.50, 18.00, 23.00, '%', '监测井下氧气浓度', '正常运行', 'admin'),
('SENSOR002', '二氧化碳传感器01', '二氧化碳传感器', '井下工作面', '工作面A', '工作面巷道', 1, 'ORG001', 0.30, 0.00, 0.50, '%', '监测井下二氧化碳浓度', '正常运行', 'admin'),
('SENSOR003', '温度传感器01', '温度传感器', '井下设备房', NULL, '设备巷道', 1, 'ORG001', 25.60, 10.00, 40.00, '℃', '监测井下温度', '正常运行', 'admin'),
('SENSOR004', '甲烷传感器01', '气体传感器', '井下采掘面', '工作面B', '采掘巷道', 1, 'ORG001', 0.15, 0.00, 1.00, '%', '监测井下甲烷浓度', '正常运行', 'admin'),
('SENSOR005', '湿度传感器01', '湿度传感器', '井下通风口', NULL, '通风巷道', 1, 'ORG001', 65.20, 30.00, 90.00, '%', '监测井下湿度', '正常运行', 'admin');

-- 创建完成提示
SELECT '应急救援监测信息表(tb_mk_emergency_sensor)创建完成' AS message;
