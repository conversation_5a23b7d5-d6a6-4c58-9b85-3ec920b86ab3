-- 应急救援监测信息表(传感器表)创建脚本
-- 创建时间: 2025-07-31
-- 作者: system

-- 删除表（如果存在）
DROP TABLE IF EXISTS `tb_mk_emergency_sensor`;

-- 创建监测信息表(传感器表)
CREATE TABLE `tb_mk_emergency_sensor` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sensor_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '传感器编码',
  `sensor_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '传感器名称',
  `sensor_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '传感器类型：氧气传感器、二氧化碳传感器、气体传感器、温度传感器等',
  `location` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '安装位置',
  `work_face` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '关联工作面',
  `tunnel` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '关联巷道',
  `parent_id` int DEFAULT NULL COMMENT '父级传感器ID，用于构建层级关系',
  `level_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '层级类型：mine-矿井，area-采区，workface-工作面，tunnel-巷道，point-监测点',
  `level_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '层级编码，如：1302、1308等工作面编码',
  `level_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '层级名称，如：1302工作面采区',
  `level_path` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '层级路径，如：/mine/area_1302/workface_1302',
  `sort_order` int DEFAULT 0 COMMENT '同级排序号',
  `is_leaf` tinyint(1) DEFAULT 1 COMMENT '是否叶子节点：0-否，1-是',
  `status` int NOT NULL DEFAULT 1 COMMENT '传感器状态：1-正常，2-异常，3-离线',
  `org_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '组织编码',
  `current_value` decimal(10,2) DEFAULT NULL COMMENT '当前值',
  `threshold_min` decimal(10,2) DEFAULT NULL COMMENT '最小阈值',
  `threshold_max` decimal(10,2) DEFAULT NULL COMMENT '最大阈值',
  `unit` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '测量单位',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '传感器描述',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_sensor_code` (`sensor_code`) USING BTREE COMMENT '传感器编码唯一索引',
  KEY `idx_sensor_type` (`sensor_type`) USING BTREE COMMENT '传感器类型索引',
  KEY `idx_org_code` (`org_code`) USING BTREE COMMENT '组织编码索引',
  KEY `idx_location` (`location`) USING BTREE COMMENT '位置索引',
  KEY `idx_parent_id` (`parent_id`) USING BTREE COMMENT '父级ID索引',
  KEY `idx_level_type` (`level_type`) USING BTREE COMMENT '层级类型索引',
  KEY `idx_level_code` (`level_code`) USING BTREE COMMENT '层级编码索引',
  KEY `idx_level_path` (`level_path`) USING BTREE COMMENT '层级路径索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='应急救援监测信息表';

-- 插入示例数据（可选）
-- 先插入层级结构数据（矿井、采区、工作面等）
INSERT INTO `tb_mk_emergency_sensor` (
  `sensor_code`,
  `sensor_name`,
  `sensor_type`,
  `location`,
  `parent_id`,
  `level_type`,
  `level_code`,
  `level_name`,
  `level_path`,
  `sort_order`,
  `is_leaf`,
  `status`,
  `org_code`,
  `description`,
  `remark`,
  `created_by`
) VALUES
-- 矿井根节点
('MINE_ROOT', '煤矿总监控', '监控中心', '地面监控中心', NULL, 'mine', 'MINE001', '煤矿总监控中心', '/mine', 1, 0, 1, 'ORG001', '煤矿总体监控节点', '根节点', 'admin'),

-- 1302采区
('AREA_1302', '1302采区监控', '采区监控', '1302采区', 1, 'area', '1302', '1302采区', '/mine/area_1302', 1, 0, 1, 'ORG001', '1302采区监控节点', '采区级监控', 'admin'),

-- 1302工作面
('WORKFACE_1302', '1302工作面监控', '工作面监控', '1302工作面', 2, 'workface', '1302', '1302工作面', '/mine/area_1302/workface_1302', 1, 0, 1, 'ORG001', '1302工作面监控节点', '工作面级监控', 'admin'),

-- 1308采区
('AREA_1308', '1308采区监控', '采区监控', '1308采区', 1, 'area', '1308', '1308采区', '/mine/area_1308', 2, 0, 1, 'ORG001', '1308采区监控节点', '采区级监控', 'admin'),

-- 1308工作面
('WORKFACE_1308', '1308工作面监控', '工作面监控', '1308工作面', 4, 'workface', '1308', '1308工作面', '/mine/area_1308/workface_1308', 1, 0, 1, 'ORG001', '1308工作面监控节点', '工作面级监控', 'admin');

-- 插入具体传感器数据
INSERT INTO `tb_mk_emergency_sensor` (
  `sensor_code`,
  `sensor_name`,
  `sensor_type`,
  `location`,
  `work_face`,
  `tunnel`,
  `parent_id`,
  `level_type`,
  `level_code`,
  `level_name`,
  `level_path`,
  `sort_order`,
  `is_leaf`,
  `status`,
  `org_code`,
  `current_value`,
  `threshold_min`,
  `threshold_max`,
  `unit`,
  `description`,
  `remark`,
  `created_by`
) VALUES
-- 1302工作面传感器
('SENSOR_1302_O2_001', '1302工作面氧气传感器01', '氧气传感器', '1302工作面进风口', '1302工作面', '1302进风巷', 3, 'point', '1302_O2_001', '1302工作面氧气监测点01', '/mine/area_1302/workface_1302/point_o2_001', 1, 1, 1, 'ORG001', 20.50, 18.00, 23.00, '%', '监测1302工作面氧气浓度', '正常运行', 'admin'),

('SENSOR_1302_CO2_001', '1302工作面二氧化碳传感器01', '二氧化碳传感器', '1302工作面回风口', '1302工作面', '1302回风巷', 3, 'point', '1302_CO2_001', '1302工作面二氧化碳监测点01', '/mine/area_1302/workface_1302/point_co2_001', 2, 1, 1, 'ORG001', 0.30, 0.00, 0.50, '%', '监测1302工作面二氧化碳浓度', '正常运行', 'admin'),

('SENSOR_1302_CH4_001', '1302工作面甲烷传感器01', '气体传感器', '1302工作面采煤机附近', '1302工作面', '1302工作面', 3, 'point', '1302_CH4_001', '1302工作面甲烷监测点01', '/mine/area_1302/workface_1302/point_ch4_001', 3, 1, 2, 'ORG001', 0.85, 0.00, 1.00, '%', '监测1302工作面甲烷浓度', '浓度偏高，需关注', 'admin'),

('SENSOR_1302_TEMP_001', '1302工作面温度传感器01', '温度传感器', '1302工作面中部', '1302工作面', '1302工作面', 3, 'point', '1302_TEMP_001', '1302工作面温度监测点01', '/mine/area_1302/workface_1302/point_temp_001', 4, 1, 1, 'ORG001', 28.50, 10.00, 40.00, '℃', '监测1302工作面温度', '正常运行', 'admin'),

-- 1308工作面传感器
('SENSOR_1308_O2_001', '1308工作面氧气传感器01', '氧气传感器', '1308工作面进风口', '1308工作面', '1308进风巷', 5, 'point', '1308_O2_001', '1308工作面氧气监测点01', '/mine/area_1308/workface_1308/point_o2_001', 1, 1, 1, 'ORG001', 19.80, 18.00, 23.00, '%', '监测1308工作面氧气浓度', '正常运行', 'admin'),

('SENSOR_1308_CO2_001', '1308工作面二氧化碳传感器01', '二氧化碳传感器', '1308工作面回风口', '1308工作面', '1308回风巷', 5, 'point', '1308_CO2_001', '1308工作面二氧化碳监测点01', '/mine/area_1308/workface_1308/point_co2_001', 2, 1, 1, 'ORG001', 0.25, 0.00, 0.50, '%', '监测1308工作面二氧化碳浓度', '正常运行', 'admin'),

('SENSOR_1308_CH4_001', '1308工作面甲烷传感器01', '气体传感器', '1308工作面掘进头', '1308工作面', '1308掘进巷', 5, 'point', '1308_CH4_001', '1308工作面甲烷监测点01', '/mine/area_1308/workface_1308/point_ch4_001', 3, 1, 1, 'ORG001', 0.12, 0.00, 1.00, '%', '监测1308工作面甲烷浓度', '正常运行', 'admin'),

('SENSOR_1308_HUMID_001', '1308工作面湿度传感器01', '湿度传感器', '1308工作面通风口', '1308工作面', '1308通风巷', 5, 'point', '1308_HUMID_001', '1308工作面湿度监测点01', '/mine/area_1308/workface_1308/point_humid_001', 4, 1, 1, 'ORG001', 72.30, 30.00, 90.00, '%', '监测1308工作面湿度', '正常运行', 'admin'),

-- 主巷道传感器（直接挂在矿井根节点下）
('SENSOR_MAIN_TEMP_001', '主巷道温度传感器01', '温度传感器', '主巷道中段', NULL, '主巷道', 1, 'point', 'MAIN_TEMP_001', '主巷道温度监测点01', '/mine/point_main_temp_001', 1, 1, 1, 'ORG001', 22.80, 10.00, 40.00, '℃', '监测主巷道温度', '正常运行', 'admin'),

('SENSOR_MAIN_HUMID_001', '主巷道湿度传感器01', '湿度传感器', '主巷道通风处', NULL, '主巷道', 1, 'point', 'MAIN_HUMID_001', '主巷道湿度监测点01', '/mine/point_main_humid_001', 2, 1, 1, 'ORG001', 68.50, 30.00, 90.00, '%', '监测主巷道湿度', '正常运行', 'admin');

-- 创建完成提示
SELECT '应急救援监测信息表(tb_mk_emergency_sensor)创建完成' AS message;
