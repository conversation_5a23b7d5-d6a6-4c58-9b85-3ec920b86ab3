-- 应急预案分类表
DROP TABLE IF EXISTS `tb_mk_emergency_plan_category`;
CREATE TABLE `tb_mk_emergency_plan_category` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `category_code` varchar(64) NOT NULL COMMENT '分类编码',
    `category_name` varchar(128) NOT NULL COMMENT '分类名称',
    `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '父级ID，0表示根节点',
    `category_level` int(11) NOT NULL DEFAULT 1 COMMENT '分类层级',
    `category_path` varchar(512) DEFAULT NULL COMMENT '分类路径，如：1/2/3',
    `org_code` varchar(64) NOT NULL COMMENT '组织编码',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序号',
    `category_icon` varchar(128) DEFAULT NULL COMMENT '分类图标',
    `is_leaf` tinyint(1) DEFAULT 0 COMMENT '是否叶子节点：0-否，1-是',
    `status` int(11) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    `remark` varchar(512) DEFAULT NULL COMMENT '备注',
    `created_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` varchar(32) DEFAULT NULL COMMENT '更新人',
    `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_category_code` (`category_code`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应急预案分类表';

-- 应急救援数字预案表（修改现有表，添加分类关联）
DROP TABLE IF EXISTS `tb_mk_emergency_plan`;
CREATE TABLE `tb_mk_emergency_plan` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `plan_code` varchar(64) NOT NULL COMMENT '预案编码',
    `plan_name` varchar(256) NOT NULL COMMENT '预案名称',
    `category_id` int(11) NOT NULL COMMENT '预案分类ID',
    `plan_type` varchar(64) DEFAULT NULL COMMENT '预案类型',
    `plan_level` varchar(32) DEFAULT NULL COMMENT '预案级别',
    `applicable_scope` varchar(512) DEFAULT NULL COMMENT '适用范围',
    `plan_content` longtext COMMENT '预案内容',
    `flow_chart` longtext COMMENT '流程图数据',
    `org_code` varchar(64) NOT NULL COMMENT '组织编码',
    `plan_status` int(11) DEFAULT 1 COMMENT '预案状态：1-草稿，2-审核中，3-已发布，4-已废止',
    `version` varchar(32) DEFAULT '1.0' COMMENT '版本号',
    `effective_date` datetime DEFAULT NULL COMMENT '生效日期',
    `expiry_date` datetime DEFAULT NULL COMMENT '失效日期',
    `responsible_person` varchar(128) DEFAULT NULL COMMENT '责任人',
    `responsible_department` varchar(128) DEFAULT NULL COMMENT '责任部门',
    `emergency_contacts` varchar(512) DEFAULT NULL COMMENT '应急联系人',
    `required_resources` varchar(1024) DEFAULT NULL COMMENT '所需资源',
    `training_requirements` varchar(1024) DEFAULT NULL COMMENT '培训要求',
    `drill_frequency` varchar(64) DEFAULT NULL COMMENT '演练频次',
    `last_drill_date` datetime DEFAULT NULL COMMENT '最后演练日期',
    `next_drill_date` datetime DEFAULT NULL COMMENT '下次演练日期',
    `review_cycle` int(11) DEFAULT 12 COMMENT '评审周期（月）',
    `last_review_date` datetime DEFAULT NULL COMMENT '最后评审日期',
    `next_review_date` datetime DEFAULT NULL COMMENT '下次评审日期',
    `attachment_urls` varchar(1024) DEFAULT NULL COMMENT '附件地址',
    `keywords` varchar(256) DEFAULT NULL COMMENT '关键词',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序号',
    `remark` varchar(512) DEFAULT NULL COMMENT '备注',
    `created_by` varchar(32) DEFAULT NULL COMMENT '创建人',
    `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` varchar(32) DEFAULT NULL COMMENT '更新人',
    `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_plan_code` (`plan_code`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_org_code` (`org_code`),
    KEY `idx_plan_status` (`plan_status`),
    CONSTRAINT `fk_plan_category` FOREIGN KEY (`category_id`) REFERENCES `tb_mk_emergency_plan_category` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应急救援数字预案表';

-- 插入测试数据
-- 预案分类数据
INSERT INTO `tb_mk_emergency_plan_category` (`category_code`, `category_name`, `parent_id`, `category_level`, `category_path`, `org_code`, `sort_order`, `category_icon`, `is_leaf`, `status`) VALUES
('FIRE_PLAN', '井下火灾预案', 0, 1, '1', 'ORG001', 1, '🔥', 0, 1),
('FIRE_PLAN_SUB', '井下火灾事故应急救援预案', 1, 2, '1/2', 'ORG001', 1, '📄', 1, 1),
('FLOOD_PLAN', '山洪灾害预案', 0, 1, '3', 'ORG001', 2, '🌊', 1, 1),
('PLAN_MGMT', '预案管理', 0, 1, '4', 'ORG001', 3, '📍', 1, 1),
('SPACE_PLAN', '有限空间作业-进入有限空间预案', 0, 1, '5', 'ORG001', 4, '🏗️', 1, 1),
('HEALTH_PLAN', '作业场所职业卫生事件预案', 0, 1, '6', 'ORG001', 5, '🏢', 1, 1),
('EQUIPMENT_PLAN', '主要设备设施故障预案', 0, 1, '7', 'ORG001', 6, '🔧', 1, 1),
('FLOOD_DISASTER', '洪涝灾害预案', 0, 1, '8', 'ORG001', 7, '🌊', 1, 1),
('PUBLIC_HEALTH', '突发公共卫生事件', 0, 1, '9', 'ORG001', 8, '🌀', 1, 1);

-- 预案内容数据（井下火灾事故应急救援预案分类下的具体预案）
INSERT INTO `tb_mk_emergency_plan` (`plan_code`, `plan_name`, `category_id`, `plan_type`, `plan_level`, `org_code`, `plan_status`, `sort_order`) VALUES
('FIRE_MAIN_001', '火灾主预案', 2, '火灾应急', '一级', 'ORG001', 3, 1),
('LPG_LEAK_001', '液化石油气泄漏事故', 2, '火灾应急', '二级', 'ORG001', 1, 2),
('FLAMMABLE_001', '易燃易爆物品', 2, '火灾应急', '二级', 'ORG001', 1, 3),
('FUEL_LEAK_001', '液体内燃、石油及其产品', 2, '火灾应急', '二级', 'ORG001', 1, 4),
('FLAMMABLE_PUBLIC_001', '易燃公开', 2, '火灾应急', '二级', 'ORG001', 1, 5),
('EXPLOSION_PROOF_001', '防爆', 2, '火灾应急', '二级', 'ORG001', 1, 6),
('PUBLIC_FACILITY_001', '公用设施', 2, '火灾应急', '二级', 'ORG001', 1, 7),
('PUBLIC_EQUIPMENT_001', '公用设备', 2, '火灾应急', '二级', 'ORG001', 1, 8),
('ROAD_SUPERIOR_001', '公路上级', 2, '火灾应急', '二级', 'ORG001', 1, 9),
('ROAD_PATROL_001', '公路巡查', 2, '火灾应急', '二级', 'ORG001', 1, 10),
('ROAD_EMERGENCY_001', '公路应急预案', 2, '火灾应急', '二级', 'ORG001', 1, 11),
('EVACUATION_001', '人员疏散预案', 2, '火灾应急', '二级', 'ORG001', 1, 12),
('MEDICAL_001', '医疗救护预案', 2, '火灾应急', '二级', 'ORG001', 1, 13),
('INVESTIGATION_001', '事故调查预案', 2, '火灾应急', '二级', 'ORG001', 1, 14);
