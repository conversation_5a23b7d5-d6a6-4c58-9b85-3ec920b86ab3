-- 应急救援中心-矿井快照模块数据库脚本

-- 应急快照表
DROP TABLE IF EXISTS tb_mk_emergency_snapshot;
CREATE TABLE tb_mk_emergency_snapshot (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    snapshot_name VARCHAR(255) NOT NULL COMMENT '快照名称',
    snapshot_type VARCHAR(50) NOT NULL COMMENT '快照类型',
    mine_id BIGINT COMMENT '矿井ID',
    mine_name VARCHAR(255) COMMENT '矿井名称',
    snapshot_time DATETIME NOT NULL COMMENT '快照时间',
    set_time DATETIME NOT NULL COMMENT '设置时间',
    backtrack_time DATETIME COMMENT '回溯时间',
    snapshot_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '快照状态(ACTIVE:有效,INACTIVE:无效)',
    description TEXT COMMENT '快照描述',
    file_path VARCHAR(500) COMMENT '快照文件路径',
    file_size BIGINT COMMENT '文件大小(字节)',
    created_by BIGINT COMMENT '创建人ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人ID',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除(0:未删除,1:已删除)',
    PRIMARY KEY (id),
    INDEX idx_mine_id (mine_id),
    INDEX idx_snapshot_time (snapshot_time),
    INDEX idx_set_time (set_time),
    INDEX idx_backtrack_time (backtrack_time),
    INDEX idx_snapshot_type (snapshot_type),
    INDEX idx_created_time (created_time)
) COMMENT = '应急快照表';

-- 快照信息表
DROP TABLE IF EXISTS tb_mk_snapshot_info;
CREATE TABLE tb_mk_snapshot_info (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    snapshot_id BIGINT NOT NULL COMMENT '快照ID',
    list_type VARCHAR(20) NOT NULL COMMENT '列表类型(EVENT:事件列表,TIME:时间列表)',
    event_id VARCHAR(100) COMMENT '事件ID字段',
    event_name VARCHAR(255) COMMENT '事件名称字段',
    info_type VARCHAR(50) NOT NULL COMMENT '信息类型',
    info_category VARCHAR(100) COMMENT '信息分类',
    info_name VARCHAR(255) NOT NULL COMMENT '信息名称',
    info_value TEXT COMMENT '信息值',
    info_unit VARCHAR(50) COMMENT '信息单位',
    data_source VARCHAR(100) COMMENT '数据来源',
    collect_time DATETIME COMMENT '采集时间',
    location_info VARCHAR(255) COMMENT '位置信息',
    priority_level INT DEFAULT 1 COMMENT '优先级(1:低,2:中,3:高,4:紧急)',
    status VARCHAR(20) DEFAULT 'NORMAL' COMMENT '状态(NORMAL:正常,WARNING:警告,ALARM:报警)',
    threshold_min DECIMAL(10,2) COMMENT '阈值下限',
    threshold_max DECIMAL(10,2) COMMENT '阈值上限',
    remark TEXT COMMENT '备注',
    created_by BIGINT COMMENT '创建人ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人ID',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除(0:未删除,1:已删除)',
    PRIMARY KEY (id),
    INDEX idx_snapshot_id (snapshot_id),
    INDEX idx_list_type (list_type),
    INDEX idx_event_id (event_id),
    INDEX idx_info_type (info_type),
    INDEX idx_collect_time (collect_time),
    INDEX idx_priority_level (priority_level),
    INDEX idx_status (status),
    FOREIGN KEY (snapshot_id) REFERENCES tb_mk_emergency_snapshot(id) ON DELETE CASCADE
) COMMENT = '快照信息表';

-- 插入示例数据
INSERT INTO tb_mk_emergency_snapshot (snapshot_name, snapshot_type, mine_id, mine_name, snapshot_time, set_time, backtrack_time, description) VALUES
('矿井A应急快照_20250729_001', 'EMERGENCY', 1, '矿井A', '2025-07-29 10:00:00', '2025-07-29 09:55:00', '2025-07-29 09:30:00', '应急情况下的矿井快照'),
('矿井A日常快照_20250729_001', 'ROUTINE', 1, '矿井A', '2025-07-29 08:00:00', '2025-07-29 07:55:00', NULL, '日常监控快照');

-- 事件列表数据
INSERT INTO tb_mk_snapshot_info (snapshot_id, list_type, info_type, info_category, info_name, info_value, info_unit, data_source, collect_time, location_info, priority_level, status) VALUES
(1, 'EVENT', 'ENVIRONMENT', '环境监测', '瓦斯浓度超标', '0.8', '%', '瓦斯传感器', '2025-07-29 10:00:00', '工作面A区', 3, 'WARNING'),
(1, 'EVENT', 'PERSONNEL', '人员异常', '人员滞留井下', '3', '人', '人员定位系统', '2025-07-29 10:01:00', '工作面B区', 4, 'ALARM'),
(1, 'EVENT', 'EQUIPMENT', '设备故障', '通风机异常', '故障', '', '设备监控系统', '2025-07-29 10:02:00', '地面风机房', 4, 'ALARM'),
(2, 'EVENT', 'ENVIRONMENT', '环境监测', '温度正常', '24.2', '℃', '温度传感器', '2025-07-29 08:00:00', '工作面A区', 1, 'NORMAL');

-- 时间列表数据
INSERT INTO tb_mk_snapshot_info (snapshot_id, list_type, event_id, event_name, info_type, info_category, info_name, info_value, info_unit, data_source, collect_time, location_info, priority_level, status) VALUES
(1, 'TIME', 'YJ_20250224103043', '副井并下火灾事故', 'FIRE', '火灾事故', '副井并下火灾', '火灾报警', '', '火灾报警系统', '2025-02-24 10:30:43', '副井', 4, 'ALARM'),
(1, 'TIME', 'YJ_20250224105452', '副井并下火灾事故', 'FIRE', '火灾事故', '副井并下火灾', '火灾报警', '', '火灾报警系统', '2025-02-24 10:54:52', '副井', 4, 'ALARM'),
(1, 'TIME', 'YJ_20250224115112', '副井并下火灾事故', 'FIRE', '火灾事故', '副井并下火灾', '火灾报警', '', '火灾报警系统', '2025-02-24 11:51:12', '副井', 4, 'ALARM'),
(1, 'TIME', 'YJ_20250224144508', '2#运输人员并下火灾事故', 'FIRE', '火灾事故', '2#运输人员并下火灾', '火灾报警', '', '火灾报警系统', '2025-02-24 14:45:08', '2#运输井', 4, 'ALARM'),
(1, 'TIME', 'YJ_20250224144759', '车运人员并下火灾事故', 'FIRE', '火灾事故', '车运人员并下火灾', '火灾报警', '', '火灾报警系统', '2025-02-24 14:47:59', '车运井', 4, 'ALARM'),
(1, 'TIME', 'YJ_20250224145036', '122+102运输顺槽并下火灾事故', 'FIRE', '火灾事故', '122+102运输顺槽并下火灾', '火灾报警', '', '火灾报警系统', '2025-02-24 14:50:36', '122+102运输顺槽', 4, 'ALARM'),
(1, 'TIME', 'YJ_20250304134953', '122+102运输顺槽并下火灾事故', 'FIRE', '火灾事故', '122+102运输顺槽并下火灾', '火灾报警', '', '火灾报警系统', '2025-03-04 13:49:53', '122+102运输顺槽', 4, 'ALARM');
