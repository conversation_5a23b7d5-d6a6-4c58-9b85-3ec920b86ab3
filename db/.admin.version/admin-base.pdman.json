{"modules": [{"name": "SSO", "chnname": "单点登录模块", "entities": [{"title": "oauth_approvals", "chnname": "", "fields": [{"name": "userId", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "clientId", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "scope", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "STATUS", "type": "VARCHAR_10", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "expiresAt", "type": "TIMESTAMP", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "lastModifiedAt", "type": "TIMESTAMP", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}]}, {"title": "oauth_client_details", "chnname": "", "fields": [{"name": "client_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": true, "notNull": true, "autoIncrement": false, "defaultValue": ""}, {"name": "resource_ids", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "client_secret", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "scope", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authorized_grant_types", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "web_server_redirect_uri", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authorities", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "access_token_validity", "type": "BigInt", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "refresh_token_validity", "type": "BigInt", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "additional_information", "type": "VARCHAR_4096", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "autoapprove", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}]}, {"title": "oauth_client_token", "chnname": "", "fields": [{"name": "token_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "token", "type": "VARBINARY", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authentication_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": true, "notNull": true, "autoIncrement": false, "defaultValue": ""}, {"name": "user_name", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "client_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}]}, {"title": "oauth_code", "chnname": "", "fields": [{"name": "CODE", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authentication", "type": "VARBINARY", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}]}, {"title": "oauth_refresh_token", "chnname": "", "fields": [{"name": "token_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "token", "type": "VARBINARY", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authentication", "type": "VARBINARY", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}]}, {"title": "oauth_access_token", "chnname": "", "fields": [{"name": "token_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "token", "type": "VARBINARY", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authentication_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": true, "notNull": true, "autoIncrement": false, "defaultValue": ""}, {"name": "user_name", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "client_id", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "authentication", "type": "VARBINARY", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}, {"name": "refresh_token", "type": "VARCHAR_256", "chnname": "", "remark": "", "pk": false, "notNull": false, "autoIncrement": false, "defaultValue": ""}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}]}], "graphCanvas": {"nodes": [], "edges": []}, "associations": []}, {"name": "SYS", "chnname": "系统配置", "entities": [{"title": "tb_sys_user_account", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "userName", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "登录名称", "pk": true, "notNull": true}, {"name": "password", "type": "MiddleString", "remark": "", "chnname": "密码", "notNull": true}, {"name": "is_account_non_expired", "type": "YesNo", "remark": "", "chnname": "账户未过期"}, {"name": "is_account_non_locked", "type": "YesNo", "remark": "", "chnname": "账户未锁定"}, {"name": "is_credentials_non_expired", "type": "YesNo", "remark": "", "chnname": "密码未过期"}, {"name": "is_enable", "type": "YesNo", "remark": "", "chnname": "账户状态"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "登录账户表"}, {"title": "tb_sys_user_role", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "account_id", "type": "BigInt", "remark": "", "chnname": "账户ID"}, {"name": "role_id", "type": "BigInt", "remark": "", "chnname": "角色"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "账户角色"}, {"title": "tb_sys_role", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "role_name", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "角色名称"}, {"name": "role_code", "type": "DefaultString", "remark": "", "chnname": "角色编码"}, {"name": "is_enable", "type": "YesNo", "remark": "", "chnname": "角色状态"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "角色表"}, {"title": "tb_sys_user", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "account_name", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "关联账户"}, {"name": "name", "type": "ShortString", "remark": "", "chnname": "姓名"}, {"name": "avatar", "type": "MiddleString", "remark": "", "chnname": "头像"}, {"name": "email", "type": "ShortString", "remark": "", "chnname": "邮件"}, {"name": "signature", "type": "MiddleString", "remark": "", "chnname": "签名"}, {"name": "title", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "头衔"}, {"name": "address", "type": "DefaultString", "remark": "", "chnname": "地址"}, {"name": "phone", "type": "<PERSON><PERSON><PERSON>", "remark": "", "chnname": "手机电话"}, {"name": "tags", "type": "DefaultString", "remark": "", "chnname": "标签"}, {"name": "is_enable", "type": "YesNo", "remark": "0禁用，1启用", "chnname": "状态"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "用户信息表"}, {"title": "tb_sys_menu", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "parent_id", "type": "BigInt", "remark": "", "chnname": "父主键", "notNull": true}, {"name": "`key`", "type": "DefaultString", "remark": "", "chnname": "菜单标识", "notNull": true}, {"name": "path", "type": "DefaultString", "remark": "", "chnname": "路径", "notNull": true}, {"name": "name", "type": "DefaultString", "remark": "", "chnname": "菜单名称", "notNull": true}, {"name": "locale", "type": "DefaultString", "remark": "", "chnname": "国际化"}, {"name": "icon", "type": "DefaultString", "remark": "", "chnname": "图标"}, {"name": "hide_in_menu", "type": "YesNo", "remark": "0:显示1:隐藏", "chnname": "隐藏菜单", "defaultValue": "0"}, {"name": "hide_children_in_menu", "type": "YesNo", "remark": "0:显示1:隐藏", "chnname": "隐藏子菜单", "defaultValue": "0"}, {"name": "authority", "type": "DefaultString", "remark": "", "chnname": "权限"}, {"name": "type", "type": "Dict", "remark": "0：目录   1：菜单   2：按钮", "chnname": "类型"}, {"name": "order_num", "type": "Integer", "remark": "", "chnname": "排序"}, {"name": "level", "type": "Integer", "remark": "", "chnname": "层级"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "菜单表"}, {"title": "tb_sys_role_menu", "fields": [{"name": "id", "type": "BigInt", "remark": "", "chnname": "主键", "pk": true, "notNull": true, "autoIncrement": true}, {"name": "role_id", "type": "BigInt", "remark": "", "chnname": "角色ID"}, {"name": "menu_id", "type": "DefaultString", "remark": "", "chnname": "菜单ID"}, {"name": "created_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "创建人", "uiHint": "Text"}, {"name": "created_time", "type": "DateTime", "remark": "", "chnname": "创建时间", "defaultValue": "CURRENT_TIMESTAMP", "uiHint": "DatePicker"}, {"name": "updated_by", "type": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "remark": "", "chnname": "更新人", "uiHint": "Text"}, {"name": "updated_time", "type": "DateTime", "remark": "", "chnname": "更新时间", "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "uiHint": "DatePicker"}], "indexs": [], "headers": [{"fieldName": "ch<PERSON>me", "relationNoShow": false}, {"fieldName": "name", "relationNoShow": false}, {"fieldName": "type", "relationNoShow": false}, {"fieldName": "dataType", "relationNoShow": true}, {"fieldName": "remark", "relationNoShow": true}, {"fieldName": "pk", "relationNoShow": false}, {"fieldName": "notNull", "relationNoShow": true}, {"fieldName": "autoIncrement", "relationNoShow": true}, {"fieldName": "defaultValue", "relationNoShow": true}, {"fieldName": "relationNoShow", "relationNoShow": true}, {"fieldName": "uiHint", "relationNoShow": true}], "chnname": "角色菜单名称"}], "graphCanvas": {"edges": [], "nodes": []}, "associations": []}], "message": "用户表", "version": "v1.8", "date": "2020/4/2 16:43:27"}