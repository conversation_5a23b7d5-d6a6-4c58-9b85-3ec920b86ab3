-- 综合决策专业分析统计表
DROP TABLE IF EXISTS `tb_mk_professional_analysis_stats`;

CREATE TABLE `tb_mk_professional_analysis_stats` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stats_code` VARCHAR(50) NOT NULL COMMENT '统计编码',
    `stats_date` DATE NOT NULL COMMENT '统计日期',
    `professional_type` VARCHAR(100) NOT NULL COMMENT '专业类型',
    `hazard_count` INT DEFAULT 0 COMMENT '隐患数量',
    `hazard_list_data` TEXT COMMENT '隐患清单数据',
    `trend_data` TEXT COMMENT '趋势数据',
    `org_code` VARCHAR(50) NOT NULL COMMENT '组织机构编码',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(50) NOT NULL COMMENT '创建人',
    `update_by` VARCHAR(50) NOT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code_date_type` (`stats_code`, `stats_date`, `professional_type`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_professional_type` (`professional_type`),
    KEY `idx_org_code` (`org_code`),
    KEY `idx_status` (`status`),
    KEY `idx_hazard_count` (`hazard_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='综合决策专业分析统计表';

-- 插入示例数据
INSERT INTO `tb_mk_professional_analysis_stats` (
    `stats_code`, 
    `stats_date`, 
    `professional_type`, 
    `hazard_count`, 
    `hazard_list_data`, 
    `trend_data`, 
    `org_code`, 
    `status`, 
    `create_by`, 
    `update_by`
) VALUES 
-- ORG_001 当前日期的数据
('PROF_001', CURDATE(), '机电专业', 25, '{"hazards":[{"id":1,"name":"电机温度过高","level":"一般"},{"id":2,"name":"电缆老化","level":"重大"}]}', '{"trend":[15,18,22,25]}', 'ORG_001', 1, 'admin', 'admin'),
('PROF_002', CURDATE(), '采煤专业', 18, '{"hazards":[{"id":3,"name":"顶板冒落","level":"重大"},{"id":4,"name":"支架压力异常","level":"一般"}]}', '{"trend":[12,15,16,18]}', 'ORG_001', 1, 'admin', 'admin'),
('PROF_003', CURDATE(), '掘进专业', 15, '{"hazards":[{"id":5,"name":"巷道变形","level":"一般"},{"id":6,"name":"通风不良","level":"一般"}]}', '{"trend":[10,12,14,15]}', 'ORG_001', 1, 'admin', 'admin'),
('PROF_004', CURDATE(), '运输专业', 12, '{"hazards":[{"id":7,"name":"皮带跑偏","level":"一般"},{"id":8,"name":"减速器异响","level":"一般"}]}', '{"trend":[8,10,11,12]}', 'ORG_001', 1, 'admin', 'admin'),
('PROF_005', CURDATE(), '通风专业', 10, '{"hazards":[{"id":9,"name":"风量不足","level":"重大"},{"id":10,"name":"风门损坏","level":"一般"}]}', '{"trend":[6,8,9,10]}', 'ORG_001', 1, 'admin', 'admin'),
('PROF_006', CURDATE(), '地质专业', 8, '{"hazards":[{"id":11,"name":"地质构造复杂","level":"一般"},{"id":12,"name":"水文异常","level":"一般"}]}', '{"trend":[5,6,7,8]}', 'ORG_001', 1, 'admin', 'admin'),
('PROF_007', CURDATE(), '安全专业', 22, '{"hazards":[{"id":13,"name":"安全制度执行不力","level":"重大"},{"id":14,"name":"安全培训不到位","level":"一般"}]}', '{"trend":[18,19,21,22]}', 'ORG_001', 1, 'admin', 'admin'),

-- ORG_002 的数据
('PROF_008', CURDATE(), '机电专业', 20, '{"hazards":[{"id":15,"name":"变压器过载","level":"重大"},{"id":16,"name":"开关柜故障","level":"一般"}]}', '{"trend":[12,15,18,20]}', 'ORG_002', 1, 'admin', 'admin'),
('PROF_009', CURDATE(), '采煤专业', 16, '{"hazards":[{"id":17,"name":"工作面片帮","level":"一般"},{"id":18,"name":"采煤机故障","level":"一般"}]}', '{"trend":[10,12,14,16]}', 'ORG_002', 1, 'admin', 'admin'),
('PROF_010', CURDATE(), '掘进专业', 13, '{"hazards":[{"id":19,"name":"掘进机维护不当","level":"一般"},{"id":20,"name":"支护不及时","level":"重大"}]}', '{"trend":[8,10,12,13]}', 'ORG_002', 1, 'admin', 'admin'),
('PROF_011', CURDATE(), '运输专业', 9, '{"hazards":[{"id":21,"name":"胶带输送机故障","level":"一般"},{"id":22,"name":"装载点粉尘大","level":"一般"}]}', '{"trend":[6,7,8,9]}', 'ORG_002', 1, 'admin', 'admin'),
('PROF_012', CURDATE(), '通风专业', 14, '{"hazards":[{"id":23,"name":"局扇停风","level":"重大"},{"id":24,"name":"风筒破损","level":"一般"}]}', '{"trend":[9,11,13,14]}', 'ORG_002', 1, 'admin', 'admin'),

-- ORG_003 的数据
('PROF_013', CURDATE(), '机电专业', 15, '{"hazards":[{"id":25,"name":"电机振动异常","level":"一般"},{"id":26,"name":"电缆接头发热","level":"一般"}]}', '{"trend":[10,12,14,15]}', 'ORG_003', 1, 'admin', 'admin'),
('PROF_014', CURDATE(), '采煤专业', 11, '{"hazards":[{"id":27,"name":"液压支架漏液","level":"一般"},{"id":28,"name":"刮板输送机链条松动","level":"一般"}]}', '{"trend":[7,8,10,11]}', 'ORG_003', 1, 'admin', 'admin'),
('PROF_015', CURDATE(), '安全专业', 18, '{"hazards":[{"id":29,"name":"现场安全检查不到位","level":"重大"},{"id":30,"name":"违章作业","level":"一般"}]}', '{"trend":[12,14,16,18]}', 'ORG_003', 1, 'admin', 'admin'),

-- 历史数据（昨天）
('PROF_016', DATE_SUB(CURDATE(), INTERVAL 1 DAY), '机电专业', 22, '{"hazards":[{"id":31,"name":"电机温度异常","level":"一般"}]}', '{"trend":[12,15,18,22]}', 'ORG_001', 1, 'admin', 'admin'),
('PROF_017', DATE_SUB(CURDATE(), INTERVAL 1 DAY), '采煤专业', 16, '{"hazards":[{"id":32,"name":"工作面安全隐患","level":"重大"}]}', '{"trend":[10,12,14,16]}', 'ORG_001', 1, 'admin', 'admin'),

-- 禁用状态的数据
('PROF_018', CURDATE(), '测试专业', 0, '{}', '{}', 'ORG_004', 0, 'admin', 'admin');

-- 查询验证数据
SELECT 
    id,
    stats_code,
    stats_date,
    professional_type,
    hazard_count,
    hazard_list_data,
    trend_data,
    org_code,
    status,
    create_time
FROM tb_mk_professional_analysis_stats 
WHERE status = 1 AND stats_date = CURDATE()
ORDER BY hazard_count DESC;

-- 按组织机构和专业类型统计
SELECT 
    org_code,
    professional_type,
    hazard_count,
    stats_date
FROM tb_mk_professional_analysis_stats 
WHERE status = 1 
  AND stats_date = CURDATE()
ORDER BY org_code, hazard_count DESC;

-- 按专业类型汇总统计
SELECT 
    professional_type,
    COUNT(*) as record_count,
    SUM(hazard_count) as total_hazard_count,
    AVG(hazard_count) as avg_hazard_count
FROM tb_mk_professional_analysis_stats 
WHERE status = 1 
  AND stats_date = CURDATE()
GROUP BY professional_type
ORDER BY total_hazard_count DESC; 