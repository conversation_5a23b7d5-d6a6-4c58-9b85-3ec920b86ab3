version: "3.2"

services:
    webapp:
        image: 10.10.40.12/yhd/web-alpha-0.01:latest
        ports:
            - "9106:80"
        networks:
            - app_network
        deploy:
            mode: replicated
            replicas: 1
        depends_on:
            - passport
            - api

    passport:
        image: 10.10.40.12/yhd/passport-alpha-0.01:latest
        ports:
            - "9104:80"
        environment:
              - SERVER_PORT=80
              - SPRING_PROFILES_ACTIVE=PRD

        networks:
            - app_network
        deploy:
            mode: replicated
            replicas: 1
    api:
        image: 10.10.40.12/yhd/api-alpha-0.01:latest
        ports:
            - "9105:80"
        environment:
            - SERVER_PORT=80
            - SPRING_PROFILES_ACTIVE=PRD
        networks:
            - app_network
        deploy:
            mode: replicated
            replicas: 1
        depends_on:
            - passport
networks:
    app_network:
        driver: overlay
        attachable: true
