version: "3.2"

services:
  webapp:
    image: 10.10.40.12/yhd/admin-web:latest
    ports:
      - "9106:80"
    networks:
      - admin_network
    deploy:
        mode: replicated
        replicas: 1

  passport:
    image: 10.10.40.12/yhd/admin-passport:latest
    ports:
      - "1000:8080"
    networks:
      - admin_network
    deploy:
        mode: replicated
        replicas: 1
  api:
    image: 10.10.40.12/yhd/admin-api:latest
    ports:
      - "1001:8080"
    networks:
      - admin_network
    deploy:
        mode: replicated
        replicas: 1

networks:
  admin_network:
    driver: overlay
    attachable: true
