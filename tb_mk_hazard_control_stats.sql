-- 综合决策隐患管控统计表
DROP TABLE IF EXISTS `tb_mk_hazard_control_stats`;

CREATE TABLE `tb_mk_hazard_control_stats` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stats_code` VARCHAR(50) NOT NULL COMMENT '统计编码',
    `stats_date` DATE NOT NULL COMMENT '统计日期',
    `general_hazard_count` INT DEFAULT 0 COMMENT '一般隐患数量',
    `major_hazard_count` INT DEFAULT 0 COMMENT '重大隐患数量',
    `trend_data` TEXT COMMENT '近7日趋势数据',
    `org_code` VARCHAR(50) NOT NULL COMMENT '组织机构编码',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(50) NOT NULL COMMENT '创建人',
    `update_by` VARCHAR(50) NOT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code_date` (`stats_code`, `stats_date`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='综合决策隐患管控统计表';

-- 插入近7日示例数据
INSERT INTO `tb_mk_hazard_control_stats` (
    `stats_code`, 
    `stats_date`, 
    `general_hazard_count`, 
    `major_hazard_count`, 
    `org_code`, 
    `status`, 
    `create_by`, 
    `update_by`
) VALUES 
-- ORG_001 今天往前推7天的数据
('HAZ_001', DATE_SUB(CURDATE(), INTERVAL 6 DAY), 15, 0, 'ORG_001', 1, 'admin', 'admin'),
('HAZ_002', DATE_SUB(CURDATE(), INTERVAL 5 DAY), 18, 1, 'ORG_001', 1, 'admin', 'admin'),
('HAZ_003', DATE_SUB(CURDATE(), INTERVAL 4 DAY), 22, 0, 'ORG_001', 1, 'admin', 'admin'),
('HAZ_004', DATE_SUB(CURDATE(), INTERVAL 3 DAY), 25, 1, 'ORG_001', 1, 'admin', 'admin'),
('HAZ_005', DATE_SUB(CURDATE(), INTERVAL 2 DAY), 20, 0, 'ORG_001', 1, 'admin', 'admin'),
('HAZ_006', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 16, 0, 'ORG_001', 1, 'admin', 'admin'),
('HAZ_007', CURDATE(), 15, 0, 'ORG_001', 1, 'admin', 'admin'),
-- ORG_002 的数据
('HAZ_008', DATE_SUB(CURDATE(), INTERVAL 6 DAY), 12, 1, 'ORG_002', 1, 'admin', 'admin'),
('HAZ_009', DATE_SUB(CURDATE(), INTERVAL 5 DAY), 14, 0, 'ORG_002', 1, 'admin', 'admin'),
('HAZ_010', DATE_SUB(CURDATE(), INTERVAL 4 DAY), 16, 1, 'ORG_002', 1, 'admin', 'admin'),
('HAZ_011', DATE_SUB(CURDATE(), INTERVAL 3 DAY), 18, 0, 'ORG_002', 1, 'admin', 'admin'),
('HAZ_012', DATE_SUB(CURDATE(), INTERVAL 2 DAY), 15, 1, 'ORG_002', 1, 'admin', 'admin'),
('HAZ_013', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 13, 0, 'ORG_002', 1, 'admin', 'admin'),
('HAZ_014', CURDATE(), 11, 0, 'ORG_002', 1, 'admin', 'admin'),
-- ORG_003 的数据
('HAZ_015', DATE_SUB(CURDATE(), INTERVAL 6 DAY), 8, 0, 'ORG_003', 1, 'admin', 'admin'),
('HAZ_016', DATE_SUB(CURDATE(), INTERVAL 5 DAY), 10, 1, 'ORG_003', 1, 'admin', 'admin'),
('HAZ_017', DATE_SUB(CURDATE(), INTERVAL 4 DAY), 12, 0, 'ORG_003', 1, 'admin', 'admin'),
('HAZ_018', DATE_SUB(CURDATE(), INTERVAL 3 DAY), 14, 0, 'ORG_003', 1, 'admin', 'admin'),
('HAZ_019', DATE_SUB(CURDATE(), INTERVAL 2 DAY), 11, 1, 'ORG_003', 1, 'admin', 'admin'),
('HAZ_020', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 9, 0, 'ORG_003', 1, 'admin', 'admin'),
('HAZ_021', CURDATE(), 7, 0, 'ORG_003', 1, 'admin', 'admin');

-- 查询验证数据
SELECT 
    id,
    stats_code,
    stats_date,
    general_hazard_count,
    major_hazard_count,
    (general_hazard_count + major_hazard_count) as total_hazard_count,
    org_code,
    status,
    create_time
FROM tb_mk_hazard_control_stats 
WHERE stats_date >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
ORDER BY org_code, stats_date;

-- 按组织机构统计近7日趋势
SELECT 
    org_code,
    stats_date,
    general_hazard_count,
    major_hazard_count,
    (general_hazard_count + major_hazard_count) as total_hazard_count
FROM tb_mk_hazard_control_stats 
WHERE status = 1 
  AND stats_date >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
ORDER BY org_code, stats_date;