import org.springframework.boot.gradle.plugin.SpringBootPlugin


plugins {
    id 'org.springframework.boot' version '3.2.4'
    id "io.spring.dependency-management" version "1.1.6"
    id 'java'
    id 'idea'
    id 'maven-publish'
}

group 'com.yhd'
version '1.0-SNAPSHOT'

ext {
    set('springBootAdminVersion', "3.1.1")
    set('springCloudVersion', "2023.0.0")
}
allprojects {
    apply plugin: 'idea'
    apply plugin: 'java'
    apply plugin: 'maven-publish'

    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
    }
    repositories {
        mavenLocal()

        //aliyun
        maven { url "https://maven.aliyun.com/nexus/content/groups/public" }

//        mavenCentral()
//        spring
        maven { url 'https://repo.spring.io/milestone' }
        maven { url 'https://repo.spring.io/snapshot' }
//         mybatis plus snapshots
        maven { url 'https://oss.sonatype.org/content/repositories/snapshots/' }
        maven {
            allowInsecureProtocol = true
            name = 'release'
            credentials {
                username 'jiangzhenghao'
                password '6PQ8XxoFHpKD'
            }
            url = 'http://10.10.40.20:7071/repository/yehengda-release/'
        }
        maven {
            allowInsecureProtocol = true
            name = 'snapshot'
            credentials {
                username 'jiangzhenghao'
                password '6PQ8XxoFHpKD'
            }
            url = 'http://10.10.40.20:7071/repository/yehengda-snapshot/'
        }


    }
    publishing {
        repositories {
            maven {
                allowInsecureProtocol = true
                name = 'release'
                credentials {
                    username 'jiangzhenghao'
                    password '6PQ8XxoFHpKD'
                }
                url = 'http://10.10.40.20:7071/repository/yehengda-release/'
            }

            maven {
                allowInsecureProtocol = true
                name = 'snapshot'
                credentials {
                    username 'jiangzhenghao'
                    password '6PQ8XxoFHpKD'
                }
                url = 'http://10.10.40.20:7071/repository/yehengda-snapshot/'
            }
        }
    }

}

subprojects {

    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'org.springframework.boot'


    configurations {
        developmentOnly
        runtimeClasspath {
            extendsFrom developmentOnly
        }
        compileOnly {
            extendsFrom annotationProcessor
        }
    }

    task sourcesJar(type: Jar) {
        from sourceSets.main.allSource
    }

    tasks.named('test', Test) {
        useJUnitPlatform()
    }


    dependencyManagement {
        imports {
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
            mavenBom SpringBootPlugin.BOM_COORDINATES
            mavenBom "de.codecentric:spring-boot-admin-dependencies:${springBootAdminVersion}"
            mavenBom "com.baomidou:mybatis-plus-bom:3.5.9"
            dependencies {
                dependency group: 'com.github.houbb', name: 'pinyin', version: '0.3.1'
                dependency group: 'com.squareup.okhttp3', name: 'okhttp', version: '4.10.0'
                dependency group: 'commons-codec', name: 'commons-codec', version: '1.15'
                dependency group: 'commons-io', name: 'commons-io', version: '2.11.0'
                dependency group: 'io.minio', name: 'minio', version: '8.4.5'
                dependency group: 'com.alibaba', name: 'druid-spring-boot-starter', version: '1.2.13'
//                dependency group: 'com.baomidou', name: 'mybatis-plus-spring-boot3-starter', version: '3.5.9'
                dependency group: 'org.csource', name: 'fastdfs-client-java', version: '1.32-SNAPSHOT'
//                dependency 'cn.hutool:hutool-all:5.8.32'
                // https://mvnrepository.com/artifact/commons-io/commons-io
                dependency 'commons-io:commons-io:2.14.0'
                // https://mvnrepository.com/artifact/commons-codec/commons-codec
                dependency 'commons-codec:commons-codec:1.16.0'
                // https://mvnrepository.com/artifact/org.apache.commons/commons-collections4
                dependency 'org.apache.commons:commons-collections4:4.4'

                dependency 'com.yhd:alltool:1.0.0'

                //org.apache.commons
                dependency group: 'org.apache.commons', name: 'commons-lang3', version: '3.12.0'
                dependency group: 'org.apache.commons', name: 'commons-pool2', version: '2.11.1'
                dependency group: 'org.apache.commons', name: 'commons-text', version: '1.10.0'
                dependency 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
                dependencySet(group: 'org.mapstruct', version: '1.5.5.Final') {
                    entry 'mapstruct-processor'
                    entry 'mapstruct'
                }
                //Thymeleaf模板
                dependency 'org.thymeleaf:thymeleaf-spring5:3.1.1.RELEASE'
                dependency 'org.thymeleaf.extras:thymeleaf-extras-java8time:3.0.4.RELEASE'

                // ShardingSphere-JDBC
                dependency group: 'org.apache.shardingsphere', name: 'shardingsphere-jdbc-core', version: '5.4.0'

                dependency group: 'com.sun.xml.bind', name: 'jaxb-impl', version: '2.3.3'
                dependency "com.microsoft.sqlserver:mssql-jdbc:7.2.2.jre11"
                dependency 'com.baomidou:dynamic-datasource-spring-boot3-starter:4.3.1'
                dependency 'com.github.gavlyukovskiy:p6spy-spring-boot-starter:1.9.1'
                dependency 'com.opencsv:opencsv:5.9'
                dependency group: 'org.apache.poi', name: 'poi-ooxml', version: '5.2.3'
                dependency group: 'org.apache.poi', name: 'poi', version: '5.2.3'
                dependency 'org.redisson:redisson:3.35.0'
                dependency 'com.xuxueli:xxl-job-core:2.3.1'
                dependency group: 'org.flowable', name: 'flowable-spring-boot-starter', version: '7.0.1'
            }

        }
    }


}


