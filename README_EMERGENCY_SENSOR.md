# 应急救援监测信息表(传感器表) - 父子级关系说明

## 🏗️ 父子级关系定义

### 核心规则
- **0 表示根目录**：所有根节点的 `parent_id = 0`
- **父子关系**：子节点的 `parent_id` 指向父节点的 `id`
- **兼容性处理**：`parent_id = NULL` 也被视为根节点，但会统一转换为 `0`

### 层级结构示例

```
矿井根节点 (parent_id = 0)
├── 1302采区 (parent_id = 矿井ID)
│   └── 1302工作面 (parent_id = 1302采区ID)
│       ├── 氧气传感器 (parent_id = 1302工作面ID)
│       ├── 甲烷传感器 (parent_id = 1302工作面ID)
│       └── 温度传感器 (parent_id = 1302工作面ID)
├── 1308采区 (parent_id = 矿井ID)
│   └── 1308工作面 (parent_id = 1308采区ID)
│       ├── 氧气传感器 (parent_id = 1308工作面ID)
│       └── 湿度传感器 (parent_id = 1308工作面ID)
└── 主巷道传感器 (parent_id = 矿井ID)
```

## 📊 数据库设计

### 关键字段

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `id` | int | 主键ID | 1, 2, 3... |
| `parent_id` | int | 父级ID，**0表示根目录** | 0, 1, 2... |
| `level_type` | varchar | 层级类型 | mine, area, workface, point |
| `level_path` | varchar | 层级路径 | /mine/area_1302/workface_1302 |
| `is_leaf` | tinyint | 是否叶子节点 | 0=非叶子, 1=叶子 |

### 示例数据

```sql
-- 矿井根节点 (parent_id = 0)
INSERT INTO tb_mk_emergency_sensor (id, parent_id, sensor_name, level_type, is_leaf) 
VALUES (1, 0, '煤矿总监控', 'mine', 0);

-- 1302采区 (parent_id = 1)
INSERT INTO tb_mk_emergency_sensor (id, parent_id, sensor_name, level_type, is_leaf) 
VALUES (2, 1, '1302采区监控', 'area', 0);

-- 1302工作面 (parent_id = 2)
INSERT INTO tb_mk_emergency_sensor (id, parent_id, sensor_name, level_type, is_leaf) 
VALUES (3, 2, '1302工作面监控', 'workface', 0);

-- 氧气传感器 (parent_id = 3)
INSERT INTO tb_mk_emergency_sensor (id, parent_id, sensor_name, level_type, is_leaf) 
VALUES (4, 3, '1302工作面氧气传感器01', 'point', 1);
```

## 💻 代码实现

### 1. 查询根节点

```java
// 查询根节点 (parent_id = 0 或 parent_id IS NULL)
LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
wrapper.and(w -> w.eq(MkEmergencySensor::getParentId, 0)
              .or().isNull(MkEmergencySensor::getParentId));
```

### 2. 查询子节点

```java
// 查询指定父节点的子节点
LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
wrapper.eq(MkEmergencySensor::getParentId, parentId);
```

### 3. 构建树形结构

```java
@Override
public List<MkEmergencySensorDTO> getSensorTree(String orgCode, Integer parentId) {
    LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(StringUtils.hasText(orgCode), MkEmergencySensor::getOrgCode, orgCode);
    
    // 0表示根目录，null也当作根目录处理
    if (parentId == null || parentId == 0) {
        wrapper.and(w -> w.eq(MkEmergencySensor::getParentId, 0)
                      .or().isNull(MkEmergencySensor::getParentId));
    } else {
        wrapper.eq(MkEmergencySensor::getParentId, parentId);
    }
    
    // 递归构建子节点...
}
```

## 🔧 API 使用

### 获取根节点树形结构

```http
GET /emergency/sensor/getSensorTree?orgCode=ORG001
# parentId默认为0，查询根节点
```

### 获取指定节点的子树

```http
GET /emergency/sensor/getSensorTree?orgCode=ORG001&parentId=2
# 查询ID为2的节点的子树
```

## ⚠️ 注意事项

1. **根节点标识**：统一使用 `parent_id = 0` 表示根节点
2. **兼容性**：`parent_id = NULL` 会被自动转换为 `0`
3. **删除限制**：删除节点前会检查是否存在子节点
4. **路径构建**：系统会自动构建完整的层级路径
5. **排序**：同级节点按 `sort_order` 字段排序

## 🌟 优势

- **清晰的层级关系**：0作为根目录标识，便于理解和查询
- **灵活的扩展性**：可以根据实际需求添加更多层级
- **高效的查询**：通过索引优化父子关系查询性能
- **完整的路径信息**：level_path字段提供完整的层级路径
- **递归树形构建**：支持任意深度的树形结构构建
