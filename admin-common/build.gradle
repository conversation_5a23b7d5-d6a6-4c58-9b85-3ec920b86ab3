plugins {
    id 'java-library'
}

group 'com.yhd'
version '1.0-SNAPSHOT'

jar {
    enabled = true
}

dependencies {

    implementation group: 'com.squareup.okhttp3', name: 'okhttp'
    implementation group: 'com.github.houbb', name: 'pinyin'
    implementation group: 'commons-codec', name: 'commons-codec'
    implementation group: 'io.minio', name: 'minio'
    implementation group: 'com.fasterxml.jackson.core', name: 'jackson-annotations'
    implementation group: 'com.baomidou', name: 'mybatis-plus-spring-boot3-starter'
    implementation group: 'commons-codec', name: 'commons-codec'
    implementation group: 'org.apache.commons', name: 'commons-lang3'
    implementation group: 'org.apache.commons', name: 'commons-text'
    implementation group: 'org.apache.commons', name: 'commons-pool2'
    implementation group: 'org.mapstruct', name: 'mapstruct'
//    implementation 'cn.hutool:hutool-all'
    // https://mvnrepository.com/artifact/commons-io/commons-io
    implementation 'commons-io:commons-io'
    // https://mvnrepository.com/artifact/commons-codec/commons-codec
    implementation 'commons-codec:commons-codec'
    // https://mvnrepository.com/artifact/org.apache.commons/commons-collections4
    implementation 'org.apache.commons:commons-collections4'

    implementation group: 'org.apache.poi', name: 'poi-ooxml'
    implementation group: 'org.apache.poi', name: 'poi'

    //SpringBoot
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-web'

    annotationProcessor group: 'org.mapstruct', name: 'mapstruct-processor'

    //Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'

}
