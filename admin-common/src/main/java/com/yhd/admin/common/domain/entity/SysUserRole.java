package com.yhd.admin.common.domain.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> */
@EqualsAndHashCode(callSuper = false)
@Data
public class SysUserRole extends BaseEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 账户ID */
    private String uid;
    /** 角色 */
    private Long roleId;
}
