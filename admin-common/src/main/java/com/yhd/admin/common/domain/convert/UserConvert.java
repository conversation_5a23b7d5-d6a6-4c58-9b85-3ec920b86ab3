package com.yhd.admin.common.domain.convert;

import org.mapstruct.Mapper;

import com.yhd.admin.common.domain.dto.UserDTO;
import com.yhd.admin.common.domain.entity.SysUser;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UserAccountTransform.java
 * @Description UserAccount 转换
 * @createTime 2020年03月19日 21:05:00
 */

@Mapper(componentModel = "spring")
public interface UserConvert {

    /**
     * sysUserAccount 转换成 UserAccountDTO
     *
     * @param userAccount
     * @return
     */
    UserDTO toDTO(SysUser userAccount);
}
