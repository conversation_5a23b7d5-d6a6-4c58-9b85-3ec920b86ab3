package com.yhd.admin.common.domain.dto;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/** <AUTHOR> */
@Data
@EqualsAndHashCode(callSuper = false)
public class RoleDTO extends BaseDTO implements Cloneable, Serializable {
    private Long id;

    /** 角色名称 */
    private String roleName;
    /** 角色编码 */
    private String roleCode;
    /** 角色状态 */
    private Boolean isEnable;
}
