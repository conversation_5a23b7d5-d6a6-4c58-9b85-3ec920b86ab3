package com.yhd.admin.common.utils;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName RequestUtil.java @Description TODO request 工具类
 * @createTime 2020年04月02日 08:57:00
 */
public class RequestUtil {

    public static HttpServletRequest get() {

        return ((ServletRequestAttributes)(RequestContextHolder.currentRequestAttributes())).getRequest();
    }
}
