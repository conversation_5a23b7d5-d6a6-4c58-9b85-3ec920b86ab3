package com.yhd.admin.common.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Jackson JSON处理工具类
 *
 * <AUTHOR>
 * @date 2025/6/24 10:23
 */
public class JacksonUtils {
    private static final Logger logger = LoggerFactory.getLogger(JacksonUtils.class);

    private static final ObjectMapper mapper;
    // 日期格式化
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    static {
        // 初始化ObjectMapper
        mapper = new ObjectMapper();

        // 配置序列化选项
        // 忽略null字段
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // 忽略空Bean转json的错误
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        // 忽略 在json字符串中存在，但是在java对象中不存在对应属性的情况。防止错误
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 配置日期时间模块
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        JavaTimeModule timeModule = new JavaTimeModule();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT);
        timeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));
        timeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter));
        mapper.registerModule(timeModule);
    }

    public JacksonUtils() {
    }

    /**
     * 对象转Json格式字符串
     *
     * @param obj 对象
     * @return Json格式字符串
     */
    public static <T> String obj2JsonStr(T obj) {
        if (obj == null) {
            return null;
        }
        try {
            return obj instanceof String ? (String) obj : mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            logger.warn("Parse Object to String error : {}", e.getMessage());
            return null;
        }
    }

    /**
     * 对象转file
     *
     * @param fileName 文件名称
     * @param obj      对象
     */
    public static void obj2File(String fileName, Object obj) {
        if (obj == null) {
            return;
        }
        try {
            mapper.writeValue(new File(fileName), obj);
        } catch (IOException e) {
            logger.warn("Parse Object to String File : {}", e.getMessage());
        }
    }

    /**
     * 字符串转换为自定义对象
     *
     * @param jsonStr json字符串
     * @param clazz   自定义对象的class对象
     * @return 自定义对象
     */
    public static <T> T jsonStr2Obj(String jsonStr, Class<T> clazz) {
        if (StringUtils.isEmpty(jsonStr) || clazz == null) {
            return null;
        }
        try {
            return mapper.readValue(jsonStr, clazz);
        } catch (Exception e) {
            logger.warn("Parse String to Object error : {}", e.getMessage());
            return null;
        }
    }

    public static <T> T jsonStr2Obj(String jsonStr, TypeReference<T> typeReference) {
        if (StringUtils.isEmpty(jsonStr) || typeReference == null) {
            return null;
        }
        try {
            return (T) (typeReference.getType().equals(String.class) ? jsonStr
                : mapper.readValue(jsonStr, typeReference));
        } catch (IOException e) {
            logger.warn("Parse String to Object error", e);
            return null;
        }
    }

    public static <T> T jsonStr2Obj(String str, Class<?> collectionClazz, Class<?>... elementClass) {
        JavaType javaType = mapper.getTypeFactory().constructParametricType(collectionClazz, elementClass);
        try {
            return mapper.readValue(str, javaType);
        } catch (IOException e) {
            logger.warn("Parse String to Object error : {}" + e.getMessage());
            return null;
        }
    }

    /**
     * json字符串转换为自定义字段转为list
     *
     * @param jsonStr 字符串
     * @return list
     */
    public static <T> List<T> jsonStr2List(String jsonStr, Class<?>... elementClass) {
        if (StringUtils.isEmpty(jsonStr) || elementClass == null) {
            return null;
        }
        try {
            return mapper.readValue(jsonStr, mapper.getTypeFactory().constructParametricType(List.class, elementClass));
        } catch (JsonProcessingException e) {
            logger.warn("Parse String to List error", e);
            return null;
        }
    }

    /**
     * json字符串转换为自定义字段转为jsonNode
     *
     * @param jsonStr 字符串
     * @return list
     */
    public static JsonNode toJsonNode(String jsonStr) {
        if (StringUtils.isBlank(jsonStr)) {
            return null;
        }
        try {
            return mapper.readTree(jsonStr);
        } catch (JsonProcessingException e) {
            logger.warn("Parse String to jsonNode error", e);
            return null;
        }
    }

    /**
     * 从JsonNode提取特定字段
     *
     * @param node      JsonNode
     * @param fieldName fieldName
     * @return 值
     */
    public static String getNodeValue(JsonNode node, String fieldName) {
        JsonNode valueNode = node.get(fieldName);

        return valueNode != null ? valueNode.asText() : null;
    }
}
