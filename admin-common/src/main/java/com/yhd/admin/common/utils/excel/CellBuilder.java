package com.yhd.admin.common.utils.excel;

import com.google.common.base.Splitter;
import com.yhd.admin.common.domain.enums.RegEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0 @Description TODO
 * @createTime 2021/7/6 15:54
 */
@Slf4j
public class CellBuilder {
    public static void build(
        Row row,
        int column,
        String value,
        int wrapLength,
        CreationHelper creationHelper,
        CellStyle cellStyle) {
        Cell cell = row.createCell(column);
        cell.setCellStyle(cellStyle);
        if (StringUtils.isNotBlank(value)) {
            String joins =
                Splitter.fixedLength(wrapLength).splitToStream(value).collect(Collectors.joining(" \n "));
            cell.setCellValue(creationHelper.createRichTextString(joins));
        }
    }

    public static void build(
        Row row, int column, String value, CreationHelper creationHelper, CellStyle cellStyle) {
        Cell cell = row.createCell(column);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(
            creationHelper.createRichTextString((StringUtils.isNotBlank(value) ? value : "")));
    }

    public static void build(Row row, int column, String value, CellStyle cellStyle) {
        Cell cell = row.createCell(column);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(StringUtils.isNotBlank(value) ? value : "");
    }

    public static void build(Row row, int column, LocalDateTime value, CellStyle cellStyle) {
        Cell cell = row.createCell(column);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(value);
    }

    public static void build(Row row, int column, LocalDate value, CellStyle cellStyle) {
        Cell cell = row.createCell(column);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(value);
    }

    public static void build(Row row, int column, Date value, CellStyle cellStyle) {
        Cell cell = row.createCell(column);
        cell.setCellStyle(cellStyle);
        if (value != null) {
            cell.setCellValue(value);
        }
    }

    public static void build(Row row, int column, Double value, CellStyle cellStyle) {
        Cell cell = row.createCell(column);
        cell.setCellStyle(cellStyle);
        if (value != null) {
            cell.setCellValue(value);
        }
    }

    public static void build(Row row, int column, Integer value, CellStyle cellStyle) {
        Cell cell = row.createCell(column);
        cell.setCellStyle(cellStyle);
        if (value != null) {
            cell.setCellValue(value);
        }
    }

    public static void build(Row row, int column, Float value, CellStyle cellStyle) {
        Cell cell = row.createCell(column);
        cell.setCellStyle(cellStyle);
        if (value != null) {
            cell.setCellValue(value);
        }
    }

    public static void build(
        Row row, int column, Object value, CreationHelper creationHelper, CellStyle cellStyle) {
        String typeName = value.getClass().getName();
        switch (typeName) {
            case "java.lang.String":
                build(row, column, (String) value, 20, creationHelper, cellStyle);
                break;
            case "java.time.LocalDate":
                build(row, column, (LocalDate) value, cellStyle);
                break;
            case "java.time.LocalDateTime":
                build(row, column, (LocalDateTime) value, cellStyle);
                break;
            case "java.util.Date":
                build(row, column, (Date) value, cellStyle);
                break;
            case "java.lang.Float":
                build(row, column, (Float) value, cellStyle);
                break;
            case "java.lang.Double":
                build(row, column, (Double) value, cellStyle);
                break;
            case "java.lang.Integer":
                build(row, column, (Integer) value, cellStyle);
                break;
            default:
                build(row, column, value.toString(), 20, creationHelper, cellStyle);
        }
    }

    public static String getCellValue(Cell cell) {
        String value = "";
        if (Objects.isNull(cell)) {
            return value;
        }

        switch (cell.getCellType()) {
            case NUMERIC: // 数字
                value = stringDateProcess(cell);
                break;
            case STRING: // 字符串
                value = cell.getStringCellValue();
                break;
            case BOOLEAN: // Boolean
                value = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA: // 公式
                value = stringDateProcess(cell);
                break;
            case BLANK: // 空值
                value = "";
                break;
            case ERROR: // 故障
                value = String.valueOf(cell.getErrorCellValue());
                break;
            default:
                value = "未知类型";
                break;
        }
        log.debug(
            ">>>>>>>>>>> type={},value={},format={}",
            cell.getCellType(),
            value,
            cell.getCellStyle().getDataFormat());
        return value;
    }

    public static String getCellValueWithEvaluator(Cell cell, FormulaEvaluator formulaEvaluator) {
        String value = "";
        if (Objects.isNull(cell)) {
            return value;
        }
        log.debug(
            ">>>>>>>>>>> type={},format={}", cell.getCellType(), cell.getCellStyle().getDataFormat());
        short dataFormat = cell.getCellStyle().getDataFormat();
        if (Objects.isNull(formulaEvaluator.evaluate(cell))) {
            return value;
        }
        String cellVal = formulaEvaluator.evaluate(cell).formatAsString();
        if (StringUtils.isBlank(cellVal)) {
            return value;
        }

        boolean isNumber = Pattern.matches(RegEnum.REG_1.getReg(), cellVal);
        if (!isNumber) {
            return "";
        }

        if (cell.getCellStyle().getDataFormat() == 177 || cell.getCellStyle().getDataFormat() == 176) {
            value = new BigDecimal(cellVal).setScale(3, RoundingMode.HALF_UP).toString();
        } else {
            value = String.valueOf(cellVal);
        }
        log.debug(
            ">>>>>>>>>>> type={},format={},value={}",
            cell.getCellType(),
            cell.getCellStyle().getDataFormat(),
            value);

        return value;
    }

    public static BigDecimal numberConvert(Cell cell) {
        log.debug(
            ">>>>>>>>>>> type={},format={},value={}",
            cell.getCellType(),
            cell.getCellStyle().getDataFormat(),
            cell.getNumericCellValue());
        if (cell.getCellStyle().getDataFormat() == 177 || cell.getCellStyle().getDataFormat() == 176) {
            return BigDecimal.valueOf(cell.getNumericCellValue()).setScale(3, RoundingMode.HALF_UP);
        }
        return BigDecimal.valueOf(cell.getNumericCellValue());
    }

    public static String stringDateProcess(Cell cell) {
        String result;
        if (DateUtil.isCellDateFormatted(cell)) { // 处理日期格式、时间格式
            SimpleDateFormat sdf = null;
            if (cell.getCellStyle().getDataFormat() == HSSFDataFormat.getBuiltinFormat("h:mm")) { // 时间格式
                sdf = new SimpleDateFormat("HH:mm");
            } else { // 日期
                sdf = new SimpleDateFormat("yyyy-MM-dd");
            }
            Date date = cell.getDateCellValue();
            result = sdf.format(date);
        } else if (cell.getCellStyle().getDataFormat() == 58) {
            // 处理自定义日期格式：m月d日(通过判断单元格的格式id解决，id的值是58)
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            double value = cell.getNumericCellValue();
            Date date = DateUtil.getJavaDate(value);
            result = sdf.format(date);
        } else {
            double value = cell.getNumericCellValue();
            CellStyle style = cell.getCellStyle();
            DecimalFormat format = new DecimalFormat();
            String temp = style.getDataFormatString();
            // 单元格设置成常规
            if (temp.equals("General")) {
                format.applyPattern("###################.###########");
                result = format.format(value);
            } else {
                result = String.valueOf(value);
            }
        }

        return result;
    }
}
