package com.yhd.admin.common.domain.dto;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BaseDTO implements Serializable {

    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
