package com.yhd.admin.common.domain;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import com.yhd.admin.common.domain.enums.RspStateEnum;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName Response.java @Description
 * @createTime 2020年03月20日 14:47:00
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RespJson<T> {

    /** 响应代码 */
    protected String status;

    /** 消息 */
    protected String message;

    /** 响应的数据域 */
    @JsonProperty(access = Access.READ_ONLY)
    protected T data;

    /***
     * 时间戳
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Long timestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).getEpochSecond();

    /**
     * 构建成功响应
     *
     * @return RespJson<T>
     */
    public static <T> RespJson<T> success() {
        return success(null);
    }

    /**
     * 构建成功响应
     *
     * @param data 返回的数据对象 (JSon数组 或 Json对象)
     */
    public static <T> RespJson<T> success(T data) {
        RespJson<T> rspJson = new RespJson<>();
        rspJson.setStatus(RspStateEnum.SUCCESS.getStatus());
        rspJson.setMessage(RspStateEnum.SUCCESS.getDesc());
        rspJson.setData(data);
        return rspJson;
    }

    /**
     * 构建成功响应
     *
     * @param msg 返回给前端的信息
     * @param data 返回的数据对象 (JSon数组 或 Json对象)
     */
    public static <T> RespJson<T> success(String msg, T data) {
        RespJson<T> rspJson = new RespJson<T>();
        rspJson.setStatus(RspStateEnum.SUCCESS.getStatus());
        rspJson.setMessage(msg);
        rspJson.setData(data);
        return rspJson;
    }

    /**
     * 构建失败响应
     *
     * @param status 响应代码
     * @param msg 返回给前端的错误信息
     */
    public static <T> RespJson<T> failure(String status, String msg) {
        RespJson<T> rspJson = new RespJson<>();
        rspJson.setStatus(StringUtils.isEmpty(status) ? RspStateEnum.FAIL.getStatus() : status);
        rspJson.setMessage(msg);
        rspJson.setData(null);
        return rspJson;
    }

    /**
     * 构建失败响应
     *
     * @param msg 返回给前端的错误信息
     */
    public static <T> RespJson<T> failure(String msg) {
        return failure(null, msg);
    }
}
