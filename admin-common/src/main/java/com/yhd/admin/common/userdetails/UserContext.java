package com.yhd.admin.common.userdetails;

import java.util.Collection;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import com.yhd.admin.common.domain.dto.UserDTO;

public class UserContext extends User {

    private final UserDTO userInfo;

    public UserContext(String username, String password, boolean enabled, boolean accountNonExpired,
        boolean credentialsNonExpired, boolean accountNonLocked, Collection<? extends GrantedAuthority> authorities,
        UserDTO userInfo) {
        super(username, password, enabled, accountNonExpired, credentialsNonExpired, accountNonLocked, authorities);
        this.userInfo = userInfo;
    }

    public UserDTO getUserInfo() {
        return userInfo;
    }
}
