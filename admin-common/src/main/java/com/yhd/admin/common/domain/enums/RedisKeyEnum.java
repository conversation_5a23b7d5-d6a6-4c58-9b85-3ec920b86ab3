package com.yhd.admin.common.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/5 15:36
 */

@Getter
public enum RedisKeyEnum {
    /**
     * KEY为用户名
     */
    USER_ACCOUNT("PASSPORT:USER:ACCOUNT:%1$s", "账户REDIS 缓存 KEY，");

    private final String key;
    private final String desc;

    RedisKeyEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
