package com.yhd.admin.common.domain.entity;

import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class SysUser extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private String uid;
    /** 关联账户 */
    private String username;
    /** 姓名 */
    private String name;
    /** 所属单位 */
    private Long orgId;
    /** 单位名称 */
    private String orgTxt;
    /** 所属部门 */
    private Long deptId;
    /** 部门名称 */
    private String deptTxt;
    /** 头像 */
    private String avatar;
    /** 邮件 */
    private String email;
    /** 签名 */
    private String signature;
    /** 头衔 */
    private String title;
    /** 地址 */
    private String address;
    /** 手机电话 */
    private String phone;
    /** 标签 */
    private String tags;
    /** 档案号 */
    private String fileNo;
    /** 职务/工种 */
    private Long job;
    /** 职务/工种 */
    private String jobTxt;
    /** 级别 */
    private String level;
    /** 性别 */
    private String gender;
    /** 民族 */
    private String nation;
    /** 身份证号码 */
    private String sfz;
    /** 出生日期 */
    private LocalDate birthday;
    /** 年龄 */
    private Integer age;
    /** 文化程度 */
    private String education;
    /** 文化程度 文本 */
    private String educationTxt;
    /** 毕业院校 */
    private String graduatedSchool;
    /** 参加工作时间 */
    private LocalDate dateOfRecruitment;
    /** 工龄 */
    private Integer workingAge;
    /** 调入时间 */
    private LocalDate transferInTime;
    /** 工作状态 */
    private String workStatus;
    /** 工作状态 文本 */
    private String workStatusTxt;
    /** 调出时间 */
    private LocalDate callOutTime;
    /** 用工形式 */
    private String employmentForm;
    /** 用工形式 文本 */
    private String employmentFormTxt;
    /** 备注 */
    private String remark;
    /** 状态;0禁用，1启用 */
    private Boolean isEnable;
    /** 是否超级管理员 */
    private Boolean isAdmin;

    /** 账户未过期 */
    private Boolean isAccountNonExpired;
    /** 账户未锁定 */
    private Boolean isAccountNonLocked;
    /** 密码未过期 */
    private Boolean isCredentialsNonExpired;
}
