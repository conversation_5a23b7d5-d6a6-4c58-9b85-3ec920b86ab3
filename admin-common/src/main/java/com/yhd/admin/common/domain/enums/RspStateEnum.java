package com.yhd.admin.common.domain.enums;

/**
 * @desc 枚举返回状态
 */
public enum RspStateEnum {
    /** FAIL */
    FAIL("error", "失败"),
    /** SUCCESS */
    SUCCESS("ok", "成功"), VALIDATE_FAILED("validate_failed", "参数校验失败"), COMMON_FAILED("common_failed", "接口调用失败"),
    FORBIDDEN("forbidden", "没有权限访问资源");

    private final String status;
    private final String desc;

    /** */
    RspStateEnum(String status, String description) {
        this.status = status;
        this.desc = description;
    }

    /** 通过code获取枚举 */
    public static RspStateEnum getByCode(String code) {
        for (RspStateEnum yesNo : values()) {
            if (yesNo.status.equals(code)) {
                return yesNo;
            }
        }
        return null;
    }

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
