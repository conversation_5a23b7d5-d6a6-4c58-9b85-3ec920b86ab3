package com.yhd.admin.common.utils.excel;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description
 * @createTime 2021/7/6 11:17
 */

public class CellStyleBuilder {

    /**
     * 默认样式
     *
     * @param hssfWorkbook
     * @return
     */
    public static CellStyle defalutBuild(HSSFWorkbook hssfWorkbook) {

        CellStyle style = hssfWorkbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        style.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        style.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        style.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());

        return style;
    }

    /**
     * 换行
     *
     * @param hssfWorkbook
     * @return
     */
    public static CellStyle wrap(HSSFWorkbook hssfWorkbook) {
        CellStyle wrapStyle = defalutBuild(hssfWorkbook);
        wrapStyle.setWrapText(Boolean.TRUE);
        return wrapStyle;
    }

    /**
     * 日期默认格式
     *
     * @param hssfWorkbook
     * @return
     */
    public static CellStyle date(HSSFWorkbook hssfWorkbook) {
        CellStyle dateStyle = defalutBuild(hssfWorkbook);
        dateStyle.setDataFormat(hssfWorkbook.getCreationHelper().createDataFormat().getFormat("m/d/yy h:mm"));
        return dateStyle;
    }


}
