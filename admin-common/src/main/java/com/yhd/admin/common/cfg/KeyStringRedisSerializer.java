package com.yhd.admin.common.cfg;

import java.nio.charset.StandardCharsets;

import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

@Component
public class KeyStringRedisSerializer implements RedisSerializer<String> {

    private final String CACHE_PREFIX;

    public KeyStringRedisSerializer(String CACHE_PREFIX) {
        this.CACHE_PREFIX = CACHE_PREFIX + ":";
    }

    /*
     * (non-Javadoc)
     * @see org.springframework.data.redis.serializer.RedisSerializer#deserialize(byte[])
     */
    @Override
    public String deserialize(@Nullable byte[] bytes) {
        return (bytes == null ? null : new String(bytes, StandardCharsets.UTF_8).replaceFirst(CACHE_PREFIX, ""));
    }

    /*
     * (non-Javadoc)
     * @see org.springframework.data.redis.serializer.RedisSerializer#serialize(java.lang.Object)
     */
    @Override
    public byte[] serialize(@Nullable String string) {
        return (string == null ? null : (CACHE_PREFIX + string).getBytes(StandardCharsets.UTF_8));
    }
}
