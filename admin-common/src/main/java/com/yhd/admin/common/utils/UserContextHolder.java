package com.yhd.admin.common.utils;

import com.yhd.admin.common.domain.dto.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

@Slf4j
public class UserContextHolder {
    public static AdminUserDetail getUserDetail() {

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        return (AdminUserDetail) authentication.getPrincipal();
    }

    public static UserDTO getUserInfo() {
        return getUserDetail().getUserInfo();
    }
}
