package com.yhd.admin.common.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @program: admin-framework
 * @description: 数字格式化
 * @author: wangshengman
 * @create: 2024-10-10 10:53
 **/

public class NumberFormatUtils {

    /**
     * @param
     * @Description: % 是格式化的占位符的开始。 0 是一个标志，表明在数值前面应该用零进行填充。 5
     *               指定了字符串的最小宽度。如果orderNumber的数字位数不足5位，那么格式化的字符串前面会用零来填充，直到达到5位宽度。 d 表示整数类型（十进制）。
     */
    public static String formatNumber(String str, int num) {
        return str + String.format("%05d", num);
    }

    /**
     * @Description: str + 日期 + 3位数字 例：20240423001
     * @Param: * @Param null:
     * @return: * @return: null
     * @Author: wang<PERSON><PERSON><PERSON>
     * @Date: 2024/10/11
     */
    public static String formatDateNumber(String str, int lastNumber) {
        return str + getCurrentDate() + formatWithLeadingZeros(lastNumber, 3);
    }

    /**
     * 补零并将整数转换为字符串。
     *
     * @param number 需要转换的整数。
     * @param totalDigits 字符串总长度。
     * @return 补零后的字符串。
     */
    public static String formatWithLeadingZeros(int number, int totalDigits) {
        // 格式化字符串，使用0作为填充字符，使长度达到totalDigits。
        return String.format("%0" + totalDigits + "d", number);
    }

    /**
     * @return 格式化日期为yyMMdd 例 20240423 => 240423
     */
    public static String getCurrentDate() {
        Date now = new Date();
        // yyyyMMdd格式化为yyMMdd
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMdd");
        return dateFormat.format(now);
    }

    /**
     * @param fullCode 编码
     * @return 240401001 截取返回001
     */
    public static String extractSequence(String fullCode) {
        // 检查字符串长度
        if (fullCode.length() < 3) {
            throw new IllegalArgumentException("编码长度太短");
        }
        // 提取最后三位
        return fullCode.substring(fullCode.length() - 3);
    }
}
