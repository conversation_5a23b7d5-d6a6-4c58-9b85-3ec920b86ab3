package com.yhd.admin.common.domain;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.common.domain.enums.RspStateEnum;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName PageRespJson.java @Description
 * @createTime 2020年04月07日 22:16:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class PageRespJson<T> extends RespJson<List<T>> {

    /** 数据总数 */
    private Long total;

    /** 每页条数 */
    private Long pageSize;

    /** 当前页数 */
    private Long current;

    /** 总页数 */
    private Long pages;

    /**
     * 构建成功响应
     *
     * @param page mybatis plus 分页对象
     * @return PageRespJson 返回的数据对象 (JSon数组 或 Json对象)
     */
    public PageRespJson(IPage<T> page) {

        this.status = String.valueOf(RspStateEnum.SUCCESS.getStatus());
        this.message = RspStateEnum.SUCCESS.getDesc();
        this.data = page.getRecords();
        this.current = page.getCurrent();
        this.pageSize = page.getSize();
        this.pages = page.getPages();
        this.total = page.getTotal();
    }
}
