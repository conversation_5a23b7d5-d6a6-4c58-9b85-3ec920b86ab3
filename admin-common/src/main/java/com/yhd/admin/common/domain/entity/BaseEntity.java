package com.yhd.admin.common.domain.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BaseEntity {

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /** 创建时间 */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createdTime;
    /** 更新人 */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /** 更新时间 */
    @TableField(insertStrategy = FieldStrategy.NEVER)
    private LocalDateTime updatedTime;
}
