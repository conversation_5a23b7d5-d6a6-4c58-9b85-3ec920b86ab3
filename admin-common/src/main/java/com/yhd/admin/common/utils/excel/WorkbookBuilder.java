package com.yhd.admin.common.utils.excel;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

@Slf4j
public class WorkbookBuilder {

    /**
     * HSSFWorkbook:是操作Excel2003以前（包括2003）的版本，扩展名是.xls；
     */
    public static final String SUFFIX_XLSX = "xlsx";
    /**
     * XSSFWorkbook:是操作Excel2007的版本，扩展名是.xlsx；
     */
    public static final String SUFFIX_XLS = "xls";

    public static Workbook getWorkBook(File file) throws Exception {
        if (file == null) {
            throw new Exception("文件不存在");
        }

        Workbook workbook = null;
        String fileName = file.getName();
        if (StringUtils.endsWithIgnoreCase(fileName, SUFFIX_XLSX)) {
            workbook = new SXSSFWorkbook(new XSSFWorkbook(file));
        } else if (StringUtils.endsWithIgnoreCase(fileName, SUFFIX_XLS)) {
            workbook = new HSSFWorkbook(new FileInputStream(file));
        } else {
            throw new Exception("文件格式不正确");
        }
        return workbook;
    }

    public static Workbook getWorkBook(MultipartFile file) {
        // 获取文件名 判断excel文件名后缀
        String fileName = file.getOriginalFilename();
        Workbook workbook = null;
        InputStream is = null;
        try {
            is = file.getInputStream();
            if (Objects.requireNonNull(StringUtils.lowerCase(fileName)).endsWith(SUFFIX_XLS)) {
                // 2003版
                workbook = new HSSFWorkbook(is);
            } else if (StringUtils.lowerCase(fileName).endsWith(SUFFIX_XLSX)) {
                workbook = new XSSFWorkbook(is);
            }
        } catch (IOException e) {
            log.error("读取excel工作簿失败", e);
        } finally {
            if (is != null) {
                IOUtils.closeQuietly(is);
            }
        }
        return workbook;
    }

    public static Workbook getWorkBook(InputStream inputStream, String suffix) throws Exception {
        Workbook workbook = null;
        if (StringUtils.equals(suffix, SUFFIX_XLSX)) {
            workbook = new SXSSFWorkbook(new XSSFWorkbook(inputStream), -1);
        } else if (StringUtils.equals(suffix, SUFFIX_XLS)) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            throw new Exception("文件格式不正确");
        }
        return workbook;
    }

    public static Sheet getSheet(File excelFile, String sheetName) {
        try (Workbook workbook = getWorkBook(excelFile)) {
            if (workbook instanceof SXSSFWorkbook) {
                return ((SXSSFWorkbook) workbook).getXSSFWorkbook().getSheet(sheetName);
            } else {
                return workbook.getSheet(sheetName);
            }
        } catch (Exception e) {
            log.error("获取:{},sheet:{},发生异常:{}", excelFile, sheetName, e.getCause());
        }
        return null;
    }

    public static List<? extends Name> getSheet(File excelFile) {
        try (Workbook workbook = getWorkBook(excelFile)) {
            if (workbook instanceof SXSSFWorkbook) {
                return ((SXSSFWorkbook) workbook).getXSSFWorkbook().getAllNames();
            } else {
                return workbook.getAllNames();
            }
        } catch (Exception e) {
            log.error("获取:{},sheet:{},发生异常:{}", excelFile, e.getCause());
        }
        return null;
    }
}
