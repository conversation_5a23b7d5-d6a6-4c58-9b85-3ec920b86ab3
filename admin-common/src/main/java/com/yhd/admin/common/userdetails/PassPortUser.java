package com.yhd.admin.common.userdetails;

import java.util.Collection;
import java.util.Objects;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;



@Deprecated
public class PassPortUser extends User {
    private final String uid;

    private final Boolean isAdmin;

    public PassPortUser(String uid, boolean isAdmin, String username, String password, boolean enabled,
        boolean accountNonExpired, boolean credentialsNonExpired, boolean accountNonLocked,
        Collection<? extends GrantedAuthority> authorities) {
        super(username, password, enabled, accountNonExpired, credentialsNonExpired, accountNonLocked, authorities);

        this.uid = uid;
        this.isAdmin = isAdmin;
    }

    public String getUid() {
        return uid;
    }

    public Boolean getAdmin() {
        return isAdmin;
    }

    /**
     * Returns {@code true} if the supplied object is a {@code User} instance with the same {@code
     * username} value.
     *
     * <p>
     * In other words, the objects are equal if they have the same username, representing the same principal.
     *
     * @param obj
     */
    @Override
    public boolean equals(Object obj) {
        if (obj == null)
            return false;
        if (!(obj instanceof PassPortUser))
            return false;
        return (((PassPortUser)obj).getUsername().equals(getUsername()));
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), uid, getUsername(), isAdmin);
    }
}
