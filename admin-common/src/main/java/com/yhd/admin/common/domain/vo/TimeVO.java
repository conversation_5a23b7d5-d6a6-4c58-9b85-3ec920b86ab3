package com.yhd.admin.common.domain.vo;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/17 16:00
 */
@Data
public class TimeVO implements Cloneable, Serializable {

    private LocalDate date;

    private LocalDateTime dateTime;

    private LocalTime time;

    private Long timestamp;

    public TimeVO(LocalDate date, LocalDateTime dateTime, LocalTime time, Long timestamp) {
        this.date = date;
        this.dateTime = dateTime;
        this.time = time;
        this.timestamp = timestamp;
    }
}
