package com.yhd.admin.common.utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class DateUtil {

    private static final String DEFAULT_FORMAT = "yyyy-MM-dd";

    private static final String DATE_FORMAT = "yyyy-MM";

    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 当前时间
     *
     * @return
     */
    public static LocalDate now() {
        return LocalDate.now();
    }

    /**
     * 某一时间之前 n天
     *
     * @return
     */
    public static LocalDate minusDays(LocalDate date, Integer n) {
        return date.minusDays(n);
    }

    /**
     * 获取两个日期之间所有的日期
     *
     * @param start
     * @param end
     * @return
     */
    public static List<String> dateBetween(LocalDate start, LocalDate end) {
        List<LocalDate> dates = start.datesUntil(end).collect(Collectors.toList());
        dates.add(end);
        return dates.stream().map(LocalDate::toString).collect(Collectors.toList());
    }

    public static List<LocalDate> dateBetweenToLocalDate(LocalDate start, LocalDate end) {
        List<LocalDate> dates = start.datesUntil(end).collect(Collectors.toList());
        dates.add(end);
        return dates;
    }

    /**
     * 获取两个日期月份区间
     *
     * @param start
     * @param end
     * @return
     */
    public static List<String> dateMonthBetween(String start, String end) {
        List<String> dates = Lists.newArrayList();
        LocalDate startDate = LocalDate.parse(start + "-01");
        LocalDate endDate = LocalDate.parse(end + "-01");
        long distance = ChronoUnit.MONTHS.between(startDate, endDate);
        dates.add(start);
        if (distance > 0) {
            for (int i = 1; i <= distance; i++) {
                LocalDate date = startDate.plusMonths(i);
                dates.add(date.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            }
        }
        return dates;
    }

    /**
     * 获取两个日期年份区间
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<String> dateYearBetween(String startTime, String endTime) {
        List<String> res = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy");

        try {
            Date start = dateFormat.parse(startTime);
            Date end = dateFormat.parse(endTime);
            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);
            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.YEAR, 1); // 日期加1(包含结束)
            while (tempStart.before(tempEnd)) {
                String year = dateFormat.format(tempStart.getTime());
                res.add(year);
                tempStart.add(Calendar.YEAR, 1);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return res;
    }

    /**
     * 获取年月
     *
     * @param date
     * @return
     */
    public static String getYearMonth(LocalDate date) {
        return date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    /**
     * 获取系统月份
     *
     * @return
     */
    public static String getSysYearMonth() {
        SimpleDateFormat sm = new SimpleDateFormat("yyyy-MM");
        Calendar calast = Calendar.getInstance();
        String last = sm.format(calast.getTime());
        return last;
    }

    /**
     * 格式化日期
     *
     * @param date 日期对象
     * @return 日期字符串
     */
    public static String formatDate(Date date) {
        return formatDate(date, DEFAULT_FORMAT);
    }

    /**
     * 格式化日期
     *
     * @param localDate 日期对象
     * @return 日期字符串
     */
    public static String formatDate(LocalDate localDate) {
        return formatDate(localDate, DEFAULT_FORMAT);
    }

    public static String formatDate(LocalDate localDat, String format) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(format);
        return localDat.format(dtf);
    }

    /**
     * 格式化日期
     *
     * @param date 日期对象
     * @return 日期字符串
     */
    public static String formatDate(Date date, String format) {
        SimpleDateFormat f = new SimpleDateFormat(format);
        return f.format(date);
    }

    /**
     * 获取本月第一天日期
     *
     * @return 本月第一天日期
     * @date 2022/9/7 15:56
     */
    public static String getCurrentMonthFirst() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        return formatDate(calendar.getTime());
    }

    /**
     * 获取当前年份
     *
     * @return 本月第一天日期
     */
    public static int getCurrentYear() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 获取某年第一天日期
     *
     * @param year 年份
     * @return Date
     */
    public static String getYearFirst(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);

        return formatDate(calendar.getTime());
    }

    /**
     * 获取某年最后一天日期
     *
     * @param year 年份
     * @return Date
     */
    public static String getYearLast(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        return formatDate(calendar.getTime());
    }

    /**
     * 获取本年第一天日期
     *
     * @return String
     * @date 2022/9/7 15:56
     */
    public static String getCurrentYearFirst() {
        return new SimpleDateFormat("yyyy").format(new Date()) + "-01-01";
    }

    /**
     * 获取月的第一天
     *
     * @param month 月份(yyyy-MM)
     */
    public static String getMonthBeginDate(String month) {
        String result = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(sdf.parse(month + "-01"));
            calendar.add(Calendar.MONTH, 0);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            result = sdf.format(calendar.getTime());
        } catch (ParseException e) {
            log.error("日期格式转换异常", e);
        }
        return result;
    }

    /**
     * 获取月的最后一天
     *
     * @param month 月份(yyyy-MM)
     */
    public static String getMonthEndDate(String month) {
        String result = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(sdf.parse(month + "-01"));
            calendar.add(Calendar.MONTH, 0);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            result = sdf.format(calendar.getTime());
        } catch (ParseException e) {
            log.error("日期格式转换异常", e);
        }
        return result;
    }

    /**
     * 获取某月的所有天数
     *
     * @param month 月份(yyyy-MM)
     */
    public static List<String> daysInMonth(String month) {
        List<String> res = new ArrayList<>();
        String[] parts = month.split("-");
        int year = Integer.parseInt(parts[0]);
        int m = Integer.parseInt(parts[1]);
        YearMonth yearMonth = YearMonth.of(year, m);
        int daysInMonth = yearMonth.lengthOfMonth();
        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate date = yearMonth.atDay(day);
            res.add(date.toString());
        }
        return res;
    }

    /**
     * 判断给定的日期字符串是否符合 yyyy-MM 格式
     *
     * @param dateString 待验证的日期字符串
     * @return 如果日期字符串符合格式返回 true，否则返回 false
     */
    public static boolean isValidFormat(String dateString) {
        // 首先检查日期字符串长度是否与格式相匹配
        if (dateString == null || dateString.length() != DATE_FORMAT.length()) {
            return false;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT);
        dateFormat.setLenient(false); // 设置为严格解析模式
        try {
            dateFormat.parse(dateString); // 尝试解析字符串
            return true; // 解析成功，格式正确
        } catch (ParseException e) {
            return false; // 解析失败，格式不正确
        }
    }

    public static String getDateStr(LocalDate date) {
        String start = date.toString() + " 00:00:00";
        String end = date.plusDays(1).toString() + " 00:00:00";
        return start + "," + end;
    }
}
