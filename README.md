- 开发必看
- 单点登录URL
```html
#登录地址
http://127.0.0.1:7000/passport/oauth2/authorize?client_id=web001&response_type=code&scope=openid&redirect_uri=http://127.0.0.1:8000/authorize
#预留
http://127.0.0.1:7000/passport/oauth2/authorize?client_id=app002&response_type=code&scope=openid&redirect_uri=http://127.0.0.1:8080/authorized
```
-获取token 

```shell

curl --location --request POST 'http://localhost:3000/oauth2/token' \
--header 'Authorization: Basic YmFpZHU6YmFpZHU=' \
--header 'Content-Type: application/x-www-form-urlencoded' \
--data-urlencode 'grant_type=refresh_token' \
--data-urlencode 'refresh_token=ZaOys78E8VqESI9wAY5ISD2JWbPhOELnpICeiO6vPcE7bJik7qG-NWI59hXW6uw2lSJjrYzrxzFqohBk0TpA28Mj1_i030JiYniAf9hNWPt5iN0dld6J40y3fKfD_7Uq'
```
-查看授权服务配置

```shell
curl --location --request GET 'http://127.0.0.1:9000/passport/.well-known/openid-configuration'
```

-DOCKER构建

```shell
#build images
docker build -f zebra-passport/docker/Dockerfile -t ***********/yhd/admin-framework-passport .
#push images 
docker push ***********/yhd/admin-framework-passport
#多个images合并导出
docker save -o  ***********/yhd/passport-alpha-0.01 eclipse-temurin > images.tar
#创建容器
docker run -d --name=admin-framework-passport  -p 1000:1000 ***********/yhd/admin-framework-passport --server.port=1000 --spring.profiles.active=prd
```