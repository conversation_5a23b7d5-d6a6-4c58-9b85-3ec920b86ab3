server:
    tomcat:
        uri-encoding: UTF-8
        max-connections: 1000
        threads:
            max: 1000
            min-spare: 5
    servlet:
        context-path: /api

spring:
    profiles:
        active: dev
    jackson:
        date-format: yyyy-MM-dd HH:mm:ss
        time-zone: GMT+8
    serialization:
        WRITE_DATES_AS_TIMESTAMPS: false
    servlet:
        multipart:
            max-file-size: 100MB
            max-request-size: 100MB
            enabled: true
    main:
        allow-circular-references: true
#mybatis
mybatis-plus:
    configuration:
        map-underscore-to-camel-case: true
        cache-enabled: false
        call-setters-on-nulls: true
        jdbc-type-for-null: 'null'
    global-config:
        banner: false
        db-config:
            table-prefix: 'tb_'
            update-strategy: not_null
    mapper-locations: classpath*:mapper/**/*Mapper.xml
flowable:
    activity-font-name: \u5B8B\u4F53
    label-font-name: \u5B8B\u4F53
    annotation-font-name: \u5B8B\u4F53
    async-executor-activate: false
    database-schema-update: none
logging:
    file:
        path: ../logs
