<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.admin.api.dao.sys.SysFileTypeDao">
    <select id="selectChildren" resultType="com.yhd.admin.api.domain.sys.entity.SysFileType"
            parameterType="java.lang.Long">
        WITH recursive r AS (
            SELECT id,
                   parent_id,
                   type_name,
                   parent_name,
                   STATUS,
                   sort_num,
                   created_by,
                   created_time,
                   updated_by,
                   updated_time
            FROM tb_sys_file_type
            WHERE id = #{id}
            UNION ALL
            SELECT c.id,
                   c.parent_id,
                   c.type_name,
                   c.parent_name,
                   c.STATUS,
                   c.sort_num,
                   c.created_by,
                   c.created_time,
                   c.updated_by,
                   c.updated_time
            FROM tb_sys_file_type c
                     INNER JOIN r ON c.parent_id = r.id
        )
        SELECT r.id,
               r.parent_id,
               r.type_name,
               r.parent_name,
               r.STATUS,
               r.sort_num,
               r.created_by,
               r.created_time,
               r.updated_by,
               r.updated_time
        FROM r
        ORDER BY r.id,
                 r.sort_num;
    </select>

    <select id="selectParent" resultType="com.yhd.admin.api.domain.sys.entity.SysFileType"
            parameterType="java.lang.Long">
        WITH recursive r AS (
            SELECT id,
                   parent_id,
                   type_name,
                   parent_name,
                   STATUS,
                   sort_num,
                   created_by,
                   created_time,
                   updated_by,
                   updated_time
            FROM tb_sys_file_type
            WHERE id = #{id}
            UNION ALL
            SELECT c.id,
                   c.parent_id,
                   c.type_name,
                   c.parent_name,
                   c.STATUS,
                   c.sort_num,
                   c.created_by,
                   c.created_time,
                   c.updated_by,
                   c.updated_time
            FROM tb_sys_file_type c
                     INNER JOIN r ON c.id = r.parent_id
        )
        SELECT r.id,
               r.parent_id,
               r.type_name,
               r.parent_name,
               r.STATUS,
               r.sort_num,
               r.created_by,
               r.created_time,
               r.updated_by,
               r.updated_time
        FROM r
        ORDER BY r.parent_id
    </select>
</mapper>
