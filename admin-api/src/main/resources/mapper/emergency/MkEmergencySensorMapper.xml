<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.admin.api.dao.emergency.MkEmergencySensorDao">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yhd.admin.api.domain.emergency.entity.MkEmergencySensor">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="sensor_code" property="sensorCode" jdbcType="VARCHAR"/>
        <result column="sensor_name" property="sensorName" jdbcType="VARCHAR"/>
        <result column="sensor_type" property="sensorType" jdbcType="VARCHAR"/>
        <result column="location" property="location" jdbcType="VARCHAR"/>
        <result column="work_face" property="workFace" jdbcType="VARCHAR"/>
        <result column="tunnel" property="tunnel" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="INTEGER"/>
        <result column="level_type" property="levelType" jdbcType="VARCHAR"/>
        <result column="level_code" property="levelCode" jdbcType="VARCHAR"/>
        <result column="level_name" property="levelName" jdbcType="VARCHAR"/>
        <result column="level_path" property="levelPath" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="is_leaf" property="isLeaf" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="org_code" property="orgCode" jdbcType="VARCHAR"/>
        <result column="current_value" property="currentValue" jdbcType="DECIMAL"/>
        <result column="threshold_min" property="thresholdMin" jdbcType="DECIMAL"/>
        <result column="threshold_max" property="thresholdMax" jdbcType="DECIMAL"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, sensor_code, sensor_name, sensor_type, location, work_face, tunnel,
        parent_id, level_type, level_code, level_name, level_path, sort_order,
        is_leaf, status, org_code, current_value, threshold_min, threshold_max,
        unit, description, remark, created_by, created_time, updated_by, updated_time
    </sql>

    <!-- 根据父级ID查询子节点 -->
    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_mk_emergency_sensor
        WHERE parent_id = #{parentId}
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据层级路径查询节点 -->
    <select id="selectByLevelPath" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_mk_emergency_sensor
        WHERE level_path LIKE CONCAT(#{levelPath}, '%')
        ORDER BY level_path ASC, sort_order ASC
    </select>

    <!-- 根据层级类型查询节点 -->
    <select id="selectByLevelType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_mk_emergency_sensor
        WHERE level_type = #{levelType}
        <if test="orgCode != null and orgCode != ''">
            AND org_code = #{orgCode}
        </if>
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 查询异常传感器 -->
    <select id="selectAbnormalSensors" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_mk_emergency_sensor
        WHERE status IN (2, 3)
        <if test="orgCode != null and orgCode != ''">
            AND org_code = #{orgCode}
        </if>
        ORDER BY status DESC, id ASC
    </select>

    <!-- 查询超出阈值的传感器 -->
    <select id="selectOutOfRangeSensors" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_mk_emergency_sensor
        WHERE is_leaf = 1
        AND current_value IS NOT NULL
        AND (
            (threshold_min IS NOT NULL AND current_value &lt; threshold_min)
            OR
            (threshold_max IS NOT NULL AND current_value &gt; threshold_max)
        )
        <if test="orgCode != null and orgCode != ''">
            AND org_code = #{orgCode}
        </if>
        ORDER BY id ASC
    </select>

    <!-- 根据传感器编码查询 -->
    <select id="selectBySensorCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_mk_emergency_sensor
        WHERE sensor_code = #{sensorCode}
        LIMIT 1
    </select>

    <!-- 查询指定工作面的传感器 -->
    <select id="selectByWorkFace" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_mk_emergency_sensor
        WHERE work_face = #{workFace}
        <if test="orgCode != null and orgCode != ''">
            AND org_code = #{orgCode}
        </if>
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 查询指定巷道的传感器 -->
    <select id="selectByTunnel" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_mk_emergency_sensor
        WHERE tunnel = #{tunnel}
        <if test="orgCode != null and orgCode != ''">
            AND org_code = #{orgCode}
        </if>
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 查询指定传感器类型的传感器 -->
    <select id="selectBySensorType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_mk_emergency_sensor
        WHERE sensor_type = #{sensorType}
        <if test="orgCode != null and orgCode != ''">
            AND org_code = #{orgCode}
        </if>
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 统计各状态传感器数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT status, COUNT(*) as count
        FROM tb_mk_emergency_sensor
        WHERE is_leaf = 1
        <if test="orgCode != null and orgCode != ''">
            AND org_code = #{orgCode}
        </if>
        GROUP BY status
    </select>

    <!-- 统计各类型传感器数量 -->
    <select id="countBySensorType" resultType="java.util.Map">
        SELECT sensor_type, COUNT(*) as count
        FROM tb_mk_emergency_sensor
        WHERE is_leaf = 1
        <if test="orgCode != null and orgCode != ''">
            AND org_code = #{orgCode}
        </if>
        GROUP BY sensor_type
    </select>

    <!-- 更新传感器状态 -->
    <update id="updateStatus">
        UPDATE tb_mk_emergency_sensor
        SET status = #{status}, updated_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 批量更新传感器状态 -->
    <update id="batchUpdateStatus">
        UPDATE tb_mk_emergency_sensor
        SET status = #{status}, updated_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 删除指定父级下的所有子节点 -->
    <delete id="deleteByParentId">
        DELETE FROM tb_mk_emergency_sensor
        WHERE parent_id = #{parentId}
    </delete>

</mapper>
