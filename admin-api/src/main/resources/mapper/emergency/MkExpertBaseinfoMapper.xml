<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.admin.api.dao.emergency.MkExpertBaseinfoDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yhd.admin.api.domain.emergency.entity.MkExpertBaseinfo">
        <id column="id" property="id" />
        <result column="org_code" property="orgCode" />
        <result column="name" property="name" />
        <result column="age" property="age" />
        <result column="sex" property="sex" />
        <result column="phone1" property="phone1" />
        <result column="phone2" property="phone2" />
        <result column="pic_src" property="picSrc" />
        <result column="employer" property="employer" />
        <result column="duty" property="duty" />
        <result column="ranks" property="ranks" />
        <result column="education" property="education" />
        <result column="resume" property="resume" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, org_code, name, age, sex, phone1, phone2, pic_src, employer, duty, ranks, 
        education, resume, remark, status, created_by, created_time, updated_by, updated_time
    </sql>

</mapper>
