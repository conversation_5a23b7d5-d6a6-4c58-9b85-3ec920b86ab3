<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.admin.api.dao.sys.RoleDao">


    <select id="selectRoleListByUser" parameterType="com.yhd.admin.api.domain.sys.query.UserParam"
            resultType="com.yhd.admin.api.domain.sys.entity.SysRole">
        select role.id,
        role.is_enable,
        role.role_code,
        role.role_name,
        role.created_by,
        role.created_time,
        role.updated_by,
        role.updated_time
        from tb_sys_user_role urole
        inner join tb_sys_role role on urole.role_id = role.id
        inner join tb_sys_user acount on acount.uid = urole.uid
        <where>
            <if test="username!=null">
                and acount.userName = #{username}
            </if>
            <if test="uid!=null">
                and acount.uid = #{uid}
            </if>
        </where>
    </select>


</mapper>
