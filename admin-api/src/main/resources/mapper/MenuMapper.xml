<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.admin.api.dao.sys.MenuDao">


    <select id="selectMenuByRole" parameterType="com.yhd.admin.api.domain.sys.query.RoleParam"
            resultType="com.yhd.admin.api.domain.sys.entity.SysMenu">
        select menu.id,
               menu.parent_id,
               menu.`key`,
               menu.path,
               menu.name,
               menu.locale,
               menu.icon,
               menu.hide_in_menu,
               menu.hide_children_in_menu,
               menu.authority,
               menu.type,
               menu.order_num,
               menu.level,
               menu.created_by,
               menu.created_time,
               menu.updated_by,
               menu.updated_time
        from tb_sys_role_menu mr
                 inner join tb_sys_role role on mr.role_id = role.id
                 inner join tb_sys_menu menu on menu.id = mr.menu_id
        where mr.role_id = #{id}
    </select>

    <select id="selectBatchMenuByRole" parameterType="java.util.Map"
            resultType="com.yhd.admin.api.domain.sys.entity.SysMenu">
        select menu.id,
        menu.parent_id,
        menu.`key`,
        menu.path,
        menu.name,
        menu.locale,
        menu.icon,
        menu.hide_in_menu,
        menu.hide_children_in_menu,
        menu.authority,
        menu.type,
        menu.order_num,
        menu.level
        from tb_sys_role_menu mr
        inner join tb_sys_role role on mr.role_id = role.id
        inner join tb_sys_menu menu on menu.id = mr.menu_id and menu.client_id=#{clientId}
        where mr.role_id in
        <foreach collection="roleIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectParentById" parameterType="java.util.List"
            resultType="com.yhd.admin.api.domain.sys.entity.SysMenu">
        WITH recursive r AS (
        SELECT id,
        parent_id,
        client_id,
        `key`,
        path,
        NAME,
        locale,
        icon,
        hide_in_menu,
        hide_children_in_menu,
        authority,
        type,
        order_num,
        LEVEL
        FROM tb_sys_menu
        WHERE client_id=#{clientId} and id in
        <foreach collection="menuIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        UNION ALL
        SELECT c.id,
        c.parent_id,
        c.client_id,
        c.`key`,
        c.path,
        c.name,
        c.locale,
        c.icon,
        c.hide_in_menu,
        c.hide_children_in_menu,
        c.authority,
        c.type,
        c.order_num,
        c.LEVEL
        FROM tb_sys_menu c,
        r
        WHERE c.id = r.parent_id
        )
        SELECT r.id,
        r.parent_id,
        r.client_id,
        r.`key`,
        r.path,
        r.name,
        r.locale,
        r.icon,
        r.hide_in_menu,
        r.hide_children_in_menu,
        r.authority,
        r.type,
        r.order_num,
        r.LEVEL
        FROM r
        ORDER BY r.id, r.order_num;
    </select>

    <select id="selectChildren" resultType="com.yhd.admin.api.domain.sys.entity.SysMenu"
            parameterType="java.lang.Long">
        WITH recursive r AS (
            SELECT id,
                   parent_id,
                   `key`,
                   client_id,
                   path,
                   name,
                   locale,
                   icon,
                   hide_in_menu,
                   hide_children_in_menu,
                   authority,
                   type,
                   order_num,
                   LEVEL,
                   created_by,
                   created_time,
                   updated_by,
                   updated_time
            FROM tb_sys_menu
            WHERE parent_id = #{id}
            UNION ALL
            SELECT c.id,
                   c.parent_id,
                   c.`key`,
                   c.client_id,
                   c.path,
                   c.NAME,
                   c.locale,
                   c.icon,
                   c.hide_in_menu,
                   c.hide_children_in_menu,
                   c.authority,
                   c.type,
                   c.order_num,
                   c.LEVEL,
                   c.created_by,
                   c.created_time,
                   c.updated_by,
                   c.updated_time
            FROM tb_sys_menu c
                     INNER JOIN r ON c.parent_id = r.id
        )
        SELECT r.id,
               r.parent_id,
               r.`key`,
               r.client_id,
               r.path,
               r.NAME,
               r.locale,
               r.icon,
               r.hide_in_menu,
               r.hide_children_in_menu,
               r.authority,
               r.type,
               r.order_num,
               r.LEVEL,
               r.created_by,
               r.created_time,
               r.updated_by,
               r.updated_time
        FROM r
        ORDER BY r.id,
                 r.order_num;
    </select>

</mapper>
