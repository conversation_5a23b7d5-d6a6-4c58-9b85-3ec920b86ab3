package com.yhd.admin.api.domain.sys.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import com.yhd.admin.api.domain.sys.dto.MenuDTO;
import com.yhd.admin.api.domain.sys.entity.SysMenu;
import com.yhd.admin.api.domain.sys.query.MenuParam;
import com.yhd.admin.api.domain.sys.vo.MenuVO;
import com.yhd.admin.api.domain.sys.vo.TreeNode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName MenuConvert.java @Description TODO
 * @createTime 2020年03月30日 15:31:00
 */
@Mapper(componentModel = "spring", uses = {Transform.class})
public interface MenuConvert {

    @Mapping(target = "children", ignore = true)
    @Mapping(target = "permission", source = "permission", qualifiedByName = {"Transform", "toList"})
    MenuDTO toDto(SysMenu menu);

    List<MenuDTO> toDto(List<SysMenu> menus);

    MenuVO toVo(MenuDTO menuDTO);

    @Mapping(target = "permission", source = "permission", qualifiedByName = {"Transform", "join"})
    SysMenu toSys(MenuParam param);

    @Mappings({@Mapping(source = "id", target = "key"), @Mapping(source = "id", target = "value"),
        @Mapping(source = "name", target = "title"),
        @Mapping(target = "isLeaf", source = "children", qualifiedByName = {"Transform", "checkIncludeLeafNode"}),
        @Mapping(target = "disabled", source = "type", qualifiedByName = {"Transform", "checkIsButton"})})
    TreeNode toTreeNode(MenuDTO menuDTO);

    List<TreeNode> toTreeNode(List<MenuDTO> menuDTOList);
}
