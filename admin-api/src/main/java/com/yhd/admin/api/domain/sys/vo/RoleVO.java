package com.yhd.admin.api.domain.sys.vo;

import java.io.Serializable;
import java.util.List;

import com.yhd.admin.common.domain.vo.BaseVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName RoleVO.java @Description TODO
 * @createTime 2020年04月28日 15:28:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RoleVO extends BaseVO implements Cloneable, Serializable {
    private Long id;
    /** 角色名称 */
    private String roleName;
    /** 角色编码 */
    private String roleCode;
    /** 角色状态 */
    private Boolean isEnable;

    /** 权限 */
    private List<Long> authority;
}
