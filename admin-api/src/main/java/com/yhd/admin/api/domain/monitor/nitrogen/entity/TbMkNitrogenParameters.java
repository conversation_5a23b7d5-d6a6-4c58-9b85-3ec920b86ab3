package com.yhd.admin.api.domain.monitor.nitrogen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 制氮系统参数表
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_mk_nitrogen_parameters")
public class TbMkNitrogenParameters extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联制氮系统主表ID
     */
    @TableField("nitrogen_system_id")
    private Long nitrogenSystemId;

    /**
     * 氮气压力(MPa)
     */
    @TableField("nitrogen_pressure")
    private BigDecimal nitrogenPressure;

    /**
     * 氮气流量(Nm³/h)
     */
    @TableField("nitrogen_flow_rate")
    private BigDecimal nitrogenFlowRate;

    /**
     * 氮气纯度(%)
     */
    @TableField("nitrogen_purity")
    private BigDecimal nitrogenPurity;
}
