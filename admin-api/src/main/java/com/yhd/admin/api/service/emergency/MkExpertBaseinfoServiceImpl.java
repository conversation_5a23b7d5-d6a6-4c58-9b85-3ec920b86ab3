package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.emergency.MkExpertBaseinfoDao;
import com.yhd.admin.api.domain.emergency.convert.MkExpertBaseinfoConvert;
import com.yhd.admin.api.domain.emergency.dto.MkExpertBaseinfoDTO;
import com.yhd.admin.api.domain.emergency.entity.MkExpertBaseinfo;
import com.yhd.admin.api.domain.emergency.query.MkExpertBaseinfoParam;
import com.yhd.admin.common.domain.query.BatchParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 应急救援中心-专家库 Service实现类
 * <AUTHOR>
 */
@Service
public class MkExpertBaseinfoServiceImpl extends ServiceImpl<MkExpertBaseinfoDao, MkExpertBaseinfo>
        implements MkExpertBaseinfoService {

    @Resource
    private MkExpertBaseinfoConvert convert;

    @Override
    public IPage<MkExpertBaseinfoDTO> pagingQuery(MkExpertBaseinfoParam param) {
        IPage<MkExpertBaseinfo> iPage = new Page<>(param.getCurrent(), param.getPageSize());

        LambdaQueryChainWrapper<MkExpertBaseinfo> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);

        // 组织编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getOrgCode()),
                MkExpertBaseinfo::getOrgCode, param.getOrgCode());

        // 专家姓名模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getName()),
                MkExpertBaseinfo::getName, param.getName());

        // 任职单位模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getEmployer()),
                MkExpertBaseinfo::getEmployer, param.getEmployer());

        // 职称模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getRanks()),
                MkExpertBaseinfo::getRanks, param.getRanks());

        // 状态查询
        queryChainWrapper.eq(param.getStatus() != null,
                MkExpertBaseinfo::getStatus, param.getStatus());

        // 性别查询
        queryChainWrapper.eq(param.getSex() != null,
                MkExpertBaseinfo::getSex, param.getSex());

        // 排序：按创建时间倒序
        queryChainWrapper.orderByDesc(MkExpertBaseinfo::getCreatedTime);

        return queryChainWrapper.page(iPage).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkExpertBaseinfoParam param) {
        // 必填字段校验
        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getPhone1())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (param.getStatus() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        return super.save(convert.toEntity(param));
    }

    @Override
    public Boolean modify(MkExpertBaseinfoParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        // 必填字段校验
        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getPhone1())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (param.getStatus() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        return super.updateById(convert.toEntity(param));
    }

    @Override
    public MkExpertBaseinfoDTO getCurrentDetail(MkExpertBaseinfoParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        MkExpertBaseinfo entity = super.getById(param.getId());
        return convert.toDTO(entity);
    }

    @Override
    public Boolean removeBatch(BatchParam param) {
        if (param.getId() == null || param.getId().isEmpty()) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        return super.removeByIds(param.getId());
    }
}
