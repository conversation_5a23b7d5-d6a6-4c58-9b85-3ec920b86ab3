package com.yhd.admin.api.domain.conduct.entity.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 掘进队信息(MkExcavationTeamInfo)实体类
 *
 * <AUTHOR>
 * @since 2025-07-28 09:25:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkExcavationTeamInfo extends BaseEntity implements Serializable {

    /** Id */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 所属煤矿 */
    private String orgName;
    /** 组织机构编码 */
    private String orgCode;
    /** 名称 */
    private String name;
    /** 单位 */
    private String company;
    /** 队组类型名称 */
    private String typeName;
    /** 队组类型代码 */
    private String typeCode;
    /** 在册人数 */
    private Integer memberNumber;
    /** 管理人数 */
    private Integer managementNumber;
    /** 队长姓名 */
    private String leaderName;
    /** 队长账号 */
    private String leaderAccount;
    /** 技术员姓名 */
    private String technicianName;
    /** 技术员账号 */
    private String technicianAccount;
    /** 巷道名称 */
    private String tunnelName;
    /** 巷道 Id */
    private Long tunnelId;
}

