package com.yhd.admin.api.domain.emergency.vo;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 能耗统计VO
 *
 * <AUTHOR>
 */
@Data
public class MkEnergyConsumptionStatsVO {

    /**
     * 设备类型
     */
    private String equipmentType;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 能耗值
     */
    private BigDecimal consumptionValue;

    /**
     * 能耗占比(%)
     */
    private BigDecimal consumptionPercentage;

    /**
     * 颜色代码
     */
    private String colorCode;

    /**
     * 排序
     */
    private Integer sortOrder;
}