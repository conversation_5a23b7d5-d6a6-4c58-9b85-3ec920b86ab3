package com.yhd.admin.api.domain.video.convert;

import com.yhd.admin.api.domain.video.dto.MkVideoRegionDTO;
import com.yhd.admin.api.domain.video.entity.MkVideoRegion;
import com.yhd.admin.api.domain.video.query.MkVideoRegionParam;
import com.yhd.admin.api.domain.video.vo.MkVideoRegionVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MkVideoRegionConvert {
  MkVideoRegion toEntity(MkVideoRegionParam param);

  @Mappings({@Mapping(target = "children", ignore = true)})
  MkVideoRegionDTO toDTO(MkVideoRegion entity);

  MkVideoRegionVO toVO(MkVideoRegionDTO dto);

  List<MkVideoRegionVO> toVO(List<MkVideoRegionDTO> dtoList);
}
