package com.yhd.admin.api.domain.conduct.vo.transfer;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 收发文部门表(MkTransferOrg)VO
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkTransferOrgVO extends BaseVO implements Serializable {
    /** 收发文部门id */
    private Long id;
    /** 组织机构编码 */
    private String orgCode;
    /** 收发文部门名称 */
    private String orgName;
    /** 收发文部门简称 */
    private String orgSubName;
}

