package com.yhd.admin.api.domain.emergency.convert;

import com.yhd.admin.api.domain.emergency.entity.MkAreaSafetyAssessment;
import com.yhd.admin.api.domain.emergency.vo.MkAreaSafetyAssessmentVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkAreaSafetyAssessmentConvert.java
 * @Description 区域安全评估转换器
 * @createTime 2025年08月02日 10:00:00
 */
@Mapper(componentModel = "spring")
public interface MkAreaSafetyAssessmentConvert {

    /**
     * 实体转VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    MkAreaSafetyAssessmentVO toVO(MkAreaSafetyAssessment entity);

    /**
     * 获取状态名称
     *
     * @param status 状态值
     * @return 状态名称
     */
    default String getStatusName(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "正常";
            case 2:
                return "异常";
            case 3:
                return "离线";
            default:
                return "未知";
        }
    }
}
