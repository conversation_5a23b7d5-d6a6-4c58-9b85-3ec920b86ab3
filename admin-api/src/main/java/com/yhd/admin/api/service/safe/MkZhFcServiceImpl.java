package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.safe.MkZhFcDao;
import com.yhd.admin.api.domain.safe.convert.MkZhFcConvert;
import com.yhd.admin.api.domain.safe.dto.MkZhFcDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhFc;
import com.yhd.admin.api.domain.safe.query.MkZhFcParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;


@Service
public class MkZhFcServiceImpl extends ServiceImpl<MkZhFcDao, MkZhFc> implements MkZhFcService {

    private final MkZhFcConvert convert;

    public MkZhFcServiceImpl(MkZhFcConvert convert) {
        this.convert = convert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addOrModify(MkZhFcParam param) {
        MkZhFc entity = convert.toEntity(param);
        return super.saveOrUpdate(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(MkZhFcParam param) {
        return super.removeById(param.getId());
    }

    @Override
    public MkZhFcDTO getCurrentDetail(MkZhFcParam param) {
        MkZhFcDTO result;
        if (Objects.isNull(param.getId())) {
            throw new BMSException(ExceptionEnum.MISS_PARAM_EXCEPTION);
        }
        MkZhFc entity = super.getById(param);
        result = convert.toDTO(entity);
        return result;
    }

    @Override
    public IPage<MkZhFcDTO> pagingQuery(MkZhFcParam param) {
        IPage<MkZhFc> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkZhFc> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        // 排序
        wrapper.orderByAsc(MkZhFc::getUpdatedTime);
        IPage<MkZhFc> iPage = wrapper.page(page);
        return iPage.convert(convert::toDTO);
    }

    @Override
    public MkZhFcDTO getData() {
        LambdaQueryChainWrapper<MkZhFc> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return convert.toDTO(wrapper.list().get(0));
    }
}
