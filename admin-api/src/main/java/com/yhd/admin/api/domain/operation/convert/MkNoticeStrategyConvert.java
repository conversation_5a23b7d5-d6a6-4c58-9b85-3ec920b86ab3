package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkNoticeStrategyDTO;
import com.yhd.admin.api.domain.operation.entity.MkNoticeStrategy;
import com.yhd.admin.api.domain.operation.query.MkNoticeStrategyParam;
import com.yhd.admin.api.domain.operation.vo.MkNoticeStrategyVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 消息策略表(MkNoticeStrategy)Convert
 *
 * <AUTHOR>
 * @since 2025-08-08 09:12:47
 */
@Mapper(componentModel = "spring")
public interface MkNoticeStrategyConvert {
    MkNoticeStrategy toEntity(MkNoticeStrategyParam param);

    MkNoticeStrategyDTO toDTO(MkNoticeStrategy entity);

    MkNoticeStrategyVO toVO(MkNoticeStrategyDTO dto);

    List<MkNoticeStrategyDTO> toDTOList(List<MkNoticeStrategy> entityList);

    List<MkNoticeStrategyVO> toVOList(List<MkNoticeStrategyDTO> dtoList);
}

