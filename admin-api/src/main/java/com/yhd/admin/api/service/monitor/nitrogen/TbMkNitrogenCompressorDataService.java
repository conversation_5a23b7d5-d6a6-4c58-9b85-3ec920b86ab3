package com.yhd.admin.api.service.monitor.nitrogen;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.monitor.nitrogen.dto.TbMkNitrogenCompressorDataDTO;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenCompressorData;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenCompressorDataCreateVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.page.TbMkNitrogenCompressorDataPageVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.update.TbMkNitrogenCompressorDataUpdateVO;

import java.util.List;

/**
 * 制氮空压机详情表 服务类
 *
 * <AUTHOR>
 * @since
 */
public interface TbMkNitrogenCompressorDataService  extends IService<TbMkNitrogenCompressorData> {

    /**
     * 新建
     *
     * @param createVO 新建VO
     * @return id
     */
    Long create(TbMkNitrogenCompressorDataCreateVO createVO);

    /**
     * 更新
     *
     * @param updateVO 更新VO
     */
    int update(TbMkNitrogenCompressorDataUpdateVO updateVO);

    /**
     * 删除
     *
     * @param id id
     */
    int delete(Long id);

    /**
     * 查询单个
     *
     * @param id id
     * @return po
     */
    TbMkNitrogenCompressorData get(Long id);

    /**
     * 分页查询
     *
     * @param pageVO 分页VO
     * @return pageResult
     */
    IPage<TbMkNitrogenCompressorDataDTO> page(TbMkNitrogenCompressorDataPageVO pageVO);

    /**
      * 批量删除
      *
      * @param ids 要删除的记录ID列表
      * @return 受影响的行数
      */
    int batchDelete(List<Long> ids);

    /**
     * 批量新增
     *
     * @param createVOs 新增VO列表
     * @return 新增的记录ID列表
     */
    int batchCreate(List<TbMkNitrogenCompressorDataCreateVO> createVOs);

    /**
     * 批量更新
     *
     * @param updateVOs 更新VO列表
     * @return 受影响的行数
     */
    int batchUpdate(List<TbMkNitrogenCompressorDataUpdateVO> updateVOs);
}
