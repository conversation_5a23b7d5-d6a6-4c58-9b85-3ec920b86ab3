package com.yhd.admin.api.service.conduct.resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.resource.MkWorkFaceInfoDao;
import com.yhd.admin.api.domain.conduct.convert.resource.MkWorkFaceInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.resource.MkWorkFaceInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.resource.MkWorkFaceInfo;
import com.yhd.admin.api.domain.conduct.query.resource.MkWorkFaceInfoParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 工作面信息服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-28 08:21:47
 */
@Service
public class MkWorkFaceInfoServiceImpl extends ServiceImpl<MkWorkFaceInfoDao, MkWorkFaceInfo> implements MkWorkFaceInfoService {

    public final MkWorkFaceInfoConvert mkWorkFaceInfoConvert;

    public MkWorkFaceInfoServiceImpl(MkWorkFaceInfoConvert mkWorkFaceInfoConvert) {
        this.mkWorkFaceInfoConvert = mkWorkFaceInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkWorkFaceInfoDTO> pagingQuery(MkWorkFaceInfoParam param) {
        IPage<MkWorkFaceInfo> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkWorkFaceInfo> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.eq(StringUtils.isNotBlank(param.getStatusCode()), MkWorkFaceInfo::getStatusCode, param.getStatusCode());
        wrapper.eq(StringUtils.isNotBlank(param.getWorkFaceName()), MkWorkFaceInfo::getWorkFaceName, param.getWorkFaceName());
        return wrapper.page(page).convert(mkWorkFaceInfoConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkWorkFaceInfoDTO> queryList(MkWorkFaceInfoParam param) {
        LambdaQueryWrapper<MkWorkFaceInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(param.getWorkFaceName()), MkWorkFaceInfo::getWorkFaceName, param.getWorkFaceName());
        return mkWorkFaceInfoConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkWorkFaceInfoParam param) {
        MkWorkFaceInfo entity = mkWorkFaceInfoConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkWorkFaceInfoDTO getCurrentDetails(MkWorkFaceInfoParam param) {
        return mkWorkFaceInfoConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkWorkFaceInfoParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }
}
