package com.yhd.admin.api.service.monitor.digging.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.digging.TbMkDiggingNavigationDao;
import com.yhd.admin.api.domain.monitor.digging.convert.TbMkDiggingNavigationConvert;
import com.yhd.admin.api.domain.monitor.digging.dto.TbMkDiggingNavigationDTO;
import com.yhd.admin.api.domain.monitor.digging.entity.TbMkDiggingNavigation;
import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingNavigationCreateVO;
import com.yhd.admin.api.domain.monitor.digging.vo.page.TbMkDiggingNavigationPageVO;
import com.yhd.admin.api.domain.monitor.digging.vo.update.TbMkDiggingNavigationUpdateVO;
import com.yhd.admin.api.service.monitor.digging.TbMkDiggingNavigationService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 掘进机导航参数 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkDiggingNavigationServiceImpl extends ServiceImpl<TbMkDiggingNavigationDao, TbMkDiggingNavigation> implements TbMkDiggingNavigationService {

    @Resource
    private TbMkDiggingNavigationConvert tbMkDiggingNavigationConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkDiggingNavigationCreateVO createVO) {
        TbMkDiggingNavigation tbMkDiggingNavigation = tbMkDiggingNavigationConvert.convert(createVO);
        baseMapper.insert(tbMkDiggingNavigation);
        return tbMkDiggingNavigation.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkDiggingNavigationUpdateVO updateVO) {
        TbMkDiggingNavigation tbMkDiggingNavigation = tbMkDiggingNavigationConvert.convert(updateVO);
        return baseMapper.updateById(tbMkDiggingNavigation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkDiggingNavigation get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkDiggingNavigationDTO> page(TbMkDiggingNavigationPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkDiggingNavigation> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkDiggingNavigation> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.eq(param.getDiggingId() != null, TbMkDiggingNavigation::getDiggingId, param.getDiggingId());
        queryChainWrapper.orderByDesc(TbMkDiggingNavigation::getId);
        return queryChainWrapper.page(iPage).convert(tbMkDiggingNavigationConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkDiggingNavigationCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 掘进机导航参数：传入的列表为空");
            return 0;
        }
        List<TbMkDiggingNavigation> entities = createVOs.stream()
                .map(tbMkDiggingNavigationConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 掘进机导航参数 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkDiggingNavigationUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 掘进机导航参数：传入的列表为空");
            return 0;
        }
        List<TbMkDiggingNavigation> entities = updateVOs.stream()
                .map(tbMkDiggingNavigationConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 掘进机导航参数：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 掘进机导航参数 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
