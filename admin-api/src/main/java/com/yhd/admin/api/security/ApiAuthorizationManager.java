package com.yhd.admin.api.security;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AuthenticationTrustResolverImpl;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.AuthorizationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.web.access.intercept.RequestAuthorizationContext;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2024/8/24
 */
@Slf4j
@Component
public class ApiAuthorizationManager implements AuthorizationManager<RequestAuthorizationContext> {
    AuthenticationTrustResolverImpl authenticationTrustResolver = new AuthenticationTrustResolverImpl();

    /**
     * Determines if access is granted for a specific authentication and object.
     *
     * @param authentication the {@link Supplier} of the {@link Authentication} to check
     * @param object the {@link T} object to check
     * @return an {@link AuthorizationDecision} or null if no decision could be made
     */
    @Override
    public AuthorizationDecision check(Supplier<Authentication> authentication,
        RequestAuthorizationContext requestAuthorizationContext) {
        HttpServletRequest request = requestAuthorizationContext.getRequest();
        String uri = request.getRequestURI();
        String contextPath = request.getContextPath();
        StringUtils.substringAfter(uri, "/" + contextPath);
        String requestMapping = StringUtils.substringAfter(uri, contextPath);
        if (log.isDebugEnabled()) {
            log.debug(">>>>>>>>>>>>>> {},{},{}", uri, requestMapping, authentication.get().getAuthorities());
        }
        Authentication userAuthentication = authentication.get();
        return new AuthorizationDecision(isAuthenticated(userAuthentication));
    }

    protected boolean isAuthenticated(Authentication authentication) {
        return authentication != null && authentication.isAuthenticated()
            && !authenticationTrustResolver.isAnonymous(authentication);
    }

    protected boolean hasRole(String role, Authentication authentication) {
        return authentication.getAuthorities().stream().anyMatch(authority -> authority.getAuthority().equals(role));
    }

    protected boolean hasPermission(String permission, Authentication authentication) {
        List<String> roles = authentication.getAuthorities().stream().map(GrantedAuthority::getAuthority).toList();

        return authentication.getAuthorities().stream()
            .anyMatch(authority -> authority.getAuthority().equals(permission));
    }
}
