package com.yhd.admin.api.controller.stats;

import com.yhd.admin.api.domain.stats.convert.MkUserDownholeStatisticConvert;
import com.yhd.admin.api.domain.stats.query.MkUserDownholeStatisticParam;
import com.yhd.admin.api.domain.stats.vo.MkUserDownholeStatisticVO;
import com.yhd.admin.api.service.stats.MkUserDownholeStatisticService;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 人员下井统计-控制层
 *
 * <AUTHOR>
 * @date 2025/7/25 16:58
 */
@RestController
@RequestMapping("/stats/downhole")
public class MkUserDownholeStatisticController {
  private final MkUserDownholeStatisticService service;
  private final MkUserDownholeStatisticConvert convert;

  public MkUserDownholeStatisticController(
      MkUserDownholeStatisticService service, MkUserDownholeStatisticConvert convert) {
    this.service = service;
    this.convert = convert;
  }

  @PostMapping(
      value = "/getUserDownholeRecordList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<MkUserDownholeStatisticVO>> getUserDownholeRecordList(
      @RequestBody MkUserDownholeStatisticParam param) {
    return RespJson.success(convert.toVO(service.getUserDownholeRecordList(param)));
  }

  @PostMapping(
      value = "/userCountList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<MkUserDownholeStatisticVO>> userCountList(
      @RequestBody MkUserDownholeStatisticParam param) {
    return RespJson.success(convert.toVO(service.userCountList(param)));
  }
}
