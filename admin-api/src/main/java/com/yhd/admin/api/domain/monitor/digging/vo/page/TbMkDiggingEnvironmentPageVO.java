package com.yhd.admin.api.domain.monitor.digging.vo.page;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 掘进机环境参数
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkDiggingEnvironmentPageVO extends QueryParam {

    private static final long serialVersionUID = 1L;



        /**
         * 关联掘进机基本信息表ID
         */
        private Long diggingId;

        /**
         * 人员数量
         */
        private Integer personnelCount;

        /**
         * 粉尘浓度(mg/m³)
         */
        private BigDecimal dustConcentration;

        /**
         * 瓦斯浓度(ppm)
         */
        private BigDecimal methaneConcentration;

        /**
         * 温度(℃)
         */
        private BigDecimal temperature;

        /**
         * 风筒风速(m/s)
         */
        private BigDecimal windSpeed;
}
