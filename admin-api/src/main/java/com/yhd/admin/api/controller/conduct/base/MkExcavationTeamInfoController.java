package com.yhd.admin.api.controller.conduct.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.base.MkExcavationTeamInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.base.MkExcavationTeamInfoDTO;
import com.yhd.admin.api.domain.conduct.query.base.MkExcavationTeamInfoParam;
import com.yhd.admin.api.domain.conduct.vo.base.MkExcavationTeamInfoVO;
import com.yhd.admin.api.service.conduct.base.MkExcavationTeamInfoService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 掘进队信息控制层
 *
 * <AUTHOR>
 * @since 2025-07-25 10:34:53
 * @module 掘进队信息
 */
@RestController
@RequestMapping("/conduct/excavationTeam")
public class MkExcavationTeamInfoController {

    public final MkExcavationTeamInfoService mkExcavationTeamInfoService;
    public final MkExcavationTeamInfoConvert mkExcavationTeamInfoConvert;

    public MkExcavationTeamInfoController(
        MkExcavationTeamInfoService mkExcavationTeamInfoService,
        MkExcavationTeamInfoConvert mkExcavationTeamInfoConvert
    ) {
        this.mkExcavationTeamInfoService = mkExcavationTeamInfoService;
        this.mkExcavationTeamInfoConvert = mkExcavationTeamInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkExcavationTeamInfoParam param) {
        IPage<MkExcavationTeamInfoDTO> page = mkExcavationTeamInfoService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkExcavationTeamInfoConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkExcavationTeamInfoParam param) {
        return RespJson.success(mkExcavationTeamInfoConvert.toVOList(mkExcavationTeamInfoService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkExcavationTeamInfoVO> getCurrentDetails(@RequestBody MkExcavationTeamInfoParam param) {
        return RespJson.success(mkExcavationTeamInfoConvert.toVO(mkExcavationTeamInfoService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkExcavationTeamInfoParam param) {
        Boolean retVal = mkExcavationTeamInfoService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkExcavationTeamInfoParam param) {
        Boolean retVal = mkExcavationTeamInfoService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

