package com.yhd.admin.api.service.conduct.project;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.project.MkExcavationProjectInfoDao;
import com.yhd.admin.api.domain.conduct.convert.project.MkExcavationProjectInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.project.MkExcavationProjectInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.project.MkExcavationProjectInfo;
import com.yhd.admin.api.domain.conduct.query.project.MkExcavationProjectInfoParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.DicService;
import com.yhd.admin.common.constant.DicConstant;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 掘进项目信息服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30 14:05:52
 */
@Service
public class MkExcavationProjectInfoServiceImpl extends ServiceImpl<MkExcavationProjectInfoDao, MkExcavationProjectInfo> implements MkExcavationProjectInfoService {

    public final MkExcavationProjectInfoConvert mkExcavationProjectInfoConvert;
    public final DicService dicService;

    public MkExcavationProjectInfoServiceImpl(MkExcavationProjectInfoConvert mkExcavationProjectInfoConvert, DicService dicService) {
        this.mkExcavationProjectInfoConvert = mkExcavationProjectInfoConvert;
        this.dicService = dicService;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkExcavationProjectInfoDTO> pagingQuery(MkExcavationProjectInfoParam param) {
        IPage<MkExcavationProjectInfo> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkExcavationProjectInfo> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.like(StringUtils.isNotBlank(param.getName()), MkExcavationProjectInfo::getName, param.getName());
        wrapper.eq(StringUtils.isNotBlank(param.getStatusCode()), MkExcavationProjectInfo::getStatusCode, param.getStatusCode());
        return wrapper.page(page).convert(mkExcavationProjectInfoConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkExcavationProjectInfoDTO> queryList(MkExcavationProjectInfoParam param) {
        LambdaQueryWrapper<MkExcavationProjectInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(param.getName()), MkExcavationProjectInfo::getName, param.getName());
        return mkExcavationProjectInfoConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkExcavationProjectInfoParam param) {
        MkExcavationProjectInfo entity = mkExcavationProjectInfoConvert.toEntity(param);
        entity.setStatusName(dicService.transform(DicConstant.PROJECT_STATUS, entity.getStatusCode()));
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            entity.setExcavationTechnologyName(dicService.transform(DicConstant.EXCAVATION_TECHNOLOGY, entity.getExcavationTechnologyCode()));
            entity.setReviewStatusCode(DicConstant.PROJECT_REVIEW_STATUS_ONE);
            entity.setReviewStatusName(dicService.transform(DicConstant.PROJECT_REVIEW_STATUS, entity.getReviewStatusCode()));
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkExcavationProjectInfoDTO getCurrentDetails(MkExcavationProjectInfoParam param) {
        return mkExcavationProjectInfoConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkExcavationProjectInfoParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }

    /**
     * 提交审批
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean submit(MkExcavationProjectInfoParam param) {
        if (Objects.isNull(param.getId())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (param.getStatusCode().equals(DicConstant.PROJECT_REVIEW_STATUS_TWO)) {
            throw new BMSException(ExceptionEnum.REPEAT_SUBMIT_ERROR);
        }
        MkExcavationProjectInfo entity = mkExcavationProjectInfoConvert.toEntity(param);
        entity.setStatusCode(DicConstant.PROJECT_REVIEW_STATUS_TWO);
        entity.setStatusName(dicService.transform(DicConstant.PROJECT_STATUS, entity.getStatusCode()));
        return updateById(entity);
    }

    /**
     * 通过审批、驳回
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean handle(MkExcavationProjectInfoParam param) {
        if (Objects.isNull(param.getId())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        MkExcavationProjectInfo entity = mkExcavationProjectInfoConvert.toEntity(param);
        entity.setStatusName(dicService.transform(DicConstant.PROJECT_STATUS, entity.getStatusCode()));
        return updateById(entity);
    }
}
