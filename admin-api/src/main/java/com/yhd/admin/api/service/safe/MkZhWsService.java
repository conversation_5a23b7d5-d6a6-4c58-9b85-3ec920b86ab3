package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.safe.dto.MkZhWsDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhWs;
import com.yhd.admin.api.domain.safe.query.MkZhWsParam;


public interface MkZhWsService extends IService<MkZhWs> {

    Boolean addOrModify(MkZhWsParam param);

    Boolean remove(MkZhWsParam param);

    MkZhWsDTO getCurrentDetail(MkZhWsParam param);

    IPage<MkZhWsDTO> pagingQuery(MkZhWsParam param);

    MkZhWsDTO getData();
}
