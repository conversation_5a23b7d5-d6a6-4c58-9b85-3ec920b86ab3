package com.yhd.admin.api.dao.emergency;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyDeviceHistory;
import org.apache.ibatis.annotations.Mapper;

/**
 * 应急救援设备历史数据表 DAO
 * 
 * <AUTHOR>
 */
@Mapper
public interface MkEmergencyDeviceHistoryDao extends BaseMapper<MkEmergencyDeviceHistory> {
    // 使用MyBatis-Plus的BaseMapper，所有基础CRUD操作都已包含
    // 复杂查询通过Service层使用QueryWrapper实现
}