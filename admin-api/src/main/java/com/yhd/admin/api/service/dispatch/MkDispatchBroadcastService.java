package com.yhd.admin.api.service.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.dispatch.dto.MkDispatchBroadcastDTO;
import com.yhd.admin.api.domain.dispatch.entity.MkDispatchBroadcast;
import com.yhd.admin.api.domain.dispatch.query.MkDispatchBroadcastParam;
import com.yhd.admin.api.domain.home.dto.MkHomeDTO;
import com.yhd.admin.api.domain.home.entity.MkHome;
import com.yhd.admin.api.domain.home.query.MkHomeParam;

/**
 * 智能调度中心-应急广播
 *
 * <AUTHOR>
 * @date 2025/7/31 09:34
 */
public interface MkDispatchBroadcastService extends IService<MkDispatchBroadcast> {

  /**
   * 新增
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  Boolean addOrModify(MkDispatchBroadcastParam param);

  /**
   * 删除-根据主键
   *
   * @param param id
   * @return true成功，false失败
   */
  Boolean remove(MkDispatchBroadcastParam param);

  /**
   * 查询详情信息
   *
   * @param param 主键id or 编号
   * @return 详情信息
   */
  MkDispatchBroadcastDTO getCurrentDetail(MkDispatchBroadcastParam param);

  /**
   * 根据条件查询通讯信息分页列表
   *
   * @param param 查询条件
   * @return 分页列表
   */
  IPage<MkDispatchBroadcastDTO> pagingQuery(MkDispatchBroadcastParam param);

  /**
   * 查询综合管控大屏
   *
   * @return 详情信息
   */
  MkDispatchBroadcastDTO getData();
}
