package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkNoticeRuleDTO;
import com.yhd.admin.api.domain.operation.entity.MkNoticeRule;
import com.yhd.admin.api.domain.operation.query.MkNoticeRuleParam;

import java.util.List;

/**
 * 消息下发规则表服务接口
 *
 * <AUTHOR>
 * @since 2025-08-08 09:12:47
 */
public interface MkNoticeRuleService extends IService<MkNoticeRule> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkNoticeRuleDTO> pagingQuery(MkNoticeRuleParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkNoticeRuleDTO> queryList(MkNoticeRuleParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkNoticeRuleParam param);

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    MkNoticeRuleDTO getCurrentDetails(MkNoticeRuleParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkNoticeRuleParam param);

}
