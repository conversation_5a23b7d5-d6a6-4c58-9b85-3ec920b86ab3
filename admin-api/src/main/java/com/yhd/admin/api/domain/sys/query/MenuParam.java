package com.yhd.admin.api.domain.sys.query;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName MenuParam.java @Description TODO
 * @createTime 2020年04月07日 21:44:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MenuParam extends QueryParam implements Cloneable, Serializable {

    private Long id;
    /** 父主键 */
    private Long parentId;

    private String clientId;
    /** 菜单标识 */
    private String key;
    /** 路径 */
    private String path;
    /** 菜单名称 */
    private String name;
    /** 国际化 */
    private String locale;
    /** 图标 */
    private String icon;
    /** 隐藏菜单;0:显示1:隐藏 */
    private Boolean hideInMenu;
    /** 隐藏子菜单;0:显示1:隐藏 */
    private Boolean hideChildrenInMenu;
    /** 权限 */
    private String authority;
    /** 类型;0：目录 1：菜单 2：按钮 */
    private String type;
    /** 排序 */
    private Integer orderNum;
    /** 层级 */
    private Integer level;

    /** 创建人 */
    private String createdBy;
    /** 创建时间 */
    private LocalDateTime createdTime;
    /** 更新人 */
    private String updatedBy;
    /** 更新时间 */
    private LocalDateTime updatedTime;

    /**
     * 后端权限。
     */
    private List<String> permission;
}
