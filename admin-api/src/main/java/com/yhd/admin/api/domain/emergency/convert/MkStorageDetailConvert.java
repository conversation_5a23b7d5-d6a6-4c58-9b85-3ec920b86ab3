package com.yhd.admin.api.domain.emergency.convert;

import com.yhd.admin.api.domain.emergency.dto.MkStorageDetailDTO;
import com.yhd.admin.api.domain.emergency.entity.MkStorageDetail;
import com.yhd.admin.api.domain.emergency.query.MkStorageDetailParam;
import com.yhd.admin.api.domain.emergency.vo.MkStorageDetailVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 应急物资记录表转换类
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface MkStorageDetailConvert {

    /**
     * Entity转DTO
     */
    MkStorageDetailDTO toDTO(MkStorageDetail entity);

    /**
     * Entity列表转DTO列表
     */
    List<MkStorageDetailDTO> toDTO(List<MkStorageDetail> entityList);

    /**
     * DTO转VO
     */
    MkStorageDetailVO toVO(MkStorageDetailDTO dto);

    /**
     * DTO列表转VO列表
     */
    List<MkStorageDetailVO> toVO(List<MkStorageDetailDTO> dtoList);

    /**
     * Param转Entity
     */
    MkStorageDetail toEntity(MkStorageDetailParam param);

    /**
     * Param列表转Entity列表
     */
    List<MkStorageDetail> toEntity(List<MkStorageDetailParam> paramList);
}
