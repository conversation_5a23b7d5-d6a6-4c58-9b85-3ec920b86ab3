package com.yhd.admin.api.controller.monitor.ventilation;

import com.yhd.admin.api.domain.monitor.ventilation.convert.TbMkMotorInfoConvert;
import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkMotorInfoDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkMotorInfo;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkMotorInfoCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.page.TbMkMotorInfoPageVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkMotorInfoUpdateVO;
import com.yhd.admin.api.service.monitor.ventilation.TbMkMotorInfoService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 电机基本信息 Controller
 * <AUTHOR>
 * @since
 */
@Slf4j
@RestController
@RequestMapping("/monitor/ventilation/tbMkMotorInfo")
public class TbMkMotorInfoController {

    @Resource
    private TbMkMotorInfoService tbMkMotorInfoService;

    @Resource
    private TbMkMotorInfoConvert tbMkMotorInfoConvert;

    /**
     * 分页查询
     *
     * @param pageVO 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<TbMkMotorInfoDTO> page(@RequestBody @Valid TbMkMotorInfoPageVO pageVO) {
        log.debug("分页查询 电机基本信息，参数：{}", pageVO);
        return new PageRespJson<>(tbMkMotorInfoService.page(pageVO));
    }

    /**
     * 新增
     *
     * @param createVO 创建参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> create(@RequestBody @Valid TbMkMotorInfoCreateVO createVO) {
        log.debug("新增 电机基本信息，参数：{}", createVO);
        return RespJson.success(tbMkMotorInfoService.create(createVO));
    }

    /**
     * 更新
     *
     * @param updateVO 更新参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> update(@RequestBody @Valid TbMkMotorInfoUpdateVO updateVO) {
        log.debug("更新 电机基本信息，参数：{}", updateVO);
        return RespJson.success(tbMkMotorInfoService.update(updateVO));
    }


    /**
      * 删除
      *
      * @param id 要删除的记录ID
      * @return 操作结果
      */
    @DeleteMapping("/{id}")
    public RespJson<?> delete(@PathVariable Long id) {
        log.debug("删除 电机基本信息，ID：{}", id);
        return RespJson.success(tbMkMotorInfoService.delete(id));
    }

    /**
     * 查看详情
     *
     * @param id 要查看的记录ID
     * @return 详情信息
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<TbMkMotorInfoDTO> getDetail(@PathVariable Long id) {
        log.debug("查看详情 电机基本信息，ID：{}", id);
        TbMkMotorInfo entity = tbMkMotorInfoService.get(id);
        if (entity == null) {
            return RespJson.failure("未找到对应记录");
        }
        return RespJson.success(tbMkMotorInfoConvert.toDTO(entity));
    }
    /**
     * 批量删除
     *
     * @param ids 要删除的记录ID列表
     * @return 操作结果
     */
    @PostMapping(value = "/batchDelete", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchDelete(@RequestBody List<Long> ids) {
        log.debug("批量删除 电机基本信息，IDs：{}", ids);
        return RespJson.success(tbMkMotorInfoService.batchDelete(ids));
    }

    /**
     * 批量新增
     *
     * @param createVOs 新增参数列表
     * @return 新增的记录ID列表
     */
    @PostMapping(value = "/batchAdd", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchCreate(@RequestBody @Valid List<TbMkMotorInfoCreateVO> createVOs) {
        log.debug("批量新增 电机基本信息，数量：{}", createVOs.size());
        return RespJson.success(tbMkMotorInfoService.batchCreate(createVOs));
    }

    /**
     * 批量更新
     *
     * @param updateVOs 更新参数列表
     * @return 更新的记录数量
     */
    @PostMapping(value = "/batchModify", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> batchUpdate(@RequestBody @Valid List<TbMkMotorInfoUpdateVO> updateVOs) {
        log.debug("批量更新 电机基本信息，数量：{}", updateVOs.size());
        return RespJson.success(tbMkMotorInfoService.batchUpdate(updateVOs));
    }
}
