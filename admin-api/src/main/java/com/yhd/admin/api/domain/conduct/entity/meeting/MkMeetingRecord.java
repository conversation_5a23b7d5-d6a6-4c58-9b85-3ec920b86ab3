package com.yhd.admin.api.domain.conduct.entity.meeting;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 调度会议记录(MkMeetingRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 15:35:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkMeetingRecord extends BaseEntity implements Serializable {

    /** 会议记录id */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 会议日期 */
    private LocalDate date;
    /** 会议名称 */
    private String name;
    /** 会议类型名称 */
    private String typeName;
    /** 会议类型id */
    private Long typeId;
    /** 上传日期 */
    private LocalDate uploadDate;
    /** 创建人 */
    private String creator;
}

