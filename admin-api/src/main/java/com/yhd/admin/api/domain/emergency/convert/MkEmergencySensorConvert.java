package com.yhd.admin.api.domain.emergency.convert;

import com.yhd.admin.api.domain.emergency.dto.MkEmergencySensorDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencySensor;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySensorParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencySensorVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 应急救援监测信息表(传感器表) 对象转换器
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
@Component
public interface MkEmergencySensorConvert {

    /**
     * Entity转DTO
     */
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "parentName", ignore = true)
    @Mapping(target = "statusText", expression = "java(getStatusText(entity.getStatus()))")
    @Mapping(target = "sensorTypeText", source = "sensorType")
    @Mapping(target = "levelTypeText", expression = "java(getLevelTypeText(entity.getLevelType()))")
    MkEmergencySensorDTO toDTO(MkEmergencySensor entity);

    /**
     * Entity列表转DTO列表
     */
    List<MkEmergencySensorDTO> toDTO(List<MkEmergencySensor> entityList);

    /**
     * DTO转VO
     */
    @Mapping(target = "isOutOfRange", expression = "java(checkOutOfRange(dto.getCurrentValue(), dto.getThresholdMin(), dto.getThresholdMax()))")
    @Mapping(target = "alarmLevel", expression = "java(getAlarmLevel(dto.getStatus(), dto.getCurrentValue(), dto.getThresholdMin(), dto.getThresholdMax()))")
    MkEmergencySensorVO toVO(MkEmergencySensorDTO dto);

    /**
     * DTO列表转VO列表
     */
    List<MkEmergencySensorVO> toVO(List<MkEmergencySensorDTO> dtoList);

    /**
     * Param转Entity
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    MkEmergencySensor toEntity(MkEmergencySensorParam param);

    /**
     * DTO转Entity
     */
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    MkEmergencySensor toEntity(MkEmergencySensorDTO dto);

    /**
     * 更新Entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    void updateEntity(MkEmergencySensorParam param, @MappingTarget MkEmergencySensor entity);

    /**
     * 获取状态文本
     */
    default String getStatusText(Integer status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 1:
                return "正常";
            case 2:
                return "异常";
            case 3:
                return "离线";
            default:
                return "未知";
        }
    }

    /**
     * 获取层级类型文本
     */
    default String getLevelTypeText(String levelType) {
        if (levelType == null) {
            return "";
        }
        switch (levelType) {
            case "mine":
                return "矿井";
            case "area":
                return "采区";
            case "workface":
                return "工作面";
            case "tunnel":
                return "巷道";
            case "point":
                return "监测点";
            default:
                return levelType;
        }
    }

    /**
     * 检查是否超出阈值
     */
    default Boolean checkOutOfRange(BigDecimal currentValue, BigDecimal thresholdMin, BigDecimal thresholdMax) {
        if (currentValue == null) {
            return false;
        }
        if (thresholdMin != null && currentValue.compareTo(thresholdMin) < 0) {
            return true;
        }
        if (thresholdMax != null && currentValue.compareTo(thresholdMax) > 0) {
            return true;
        }
        return false;
    }

    /**
     * 获取告警级别
     */
    default String getAlarmLevel(Integer status, BigDecimal currentValue, BigDecimal thresholdMin, BigDecimal thresholdMax) {
        if (status != null && status == 3) {
            return "OFFLINE"; // 离线
        }
        if (status != null && status == 2) {
            return "ERROR"; // 异常
        }
        if (checkOutOfRange(currentValue, thresholdMin, thresholdMax)) {
            return "WARNING"; // 超出阈值
        }
        return "NORMAL"; // 正常
    }
}
