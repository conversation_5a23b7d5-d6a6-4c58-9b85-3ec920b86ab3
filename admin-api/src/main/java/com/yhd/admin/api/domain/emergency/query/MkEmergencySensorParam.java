package com.yhd.admin.api.domain.emergency.query;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.yhd.admin.common.domain.query.QueryParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 应急救援监测信息表(传感器表)查询参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
public class MkEmergencySensorParam extends QueryParam implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 传感器编码
     */
    private String sensorCode;

    /**
     * 传感器名称（模糊查询）
     */
    private String sensorName;

    /**
     * 传感器类型
     */
    private String sensorType;

    /**
     * 安装位置（模糊查询）
     */
    private String location;

    /**
     * 关联工作面（模糊查询）
     */
    private String workFace;

    /**
     * 关联巷道（模糊查询）
     */
    private String tunnel;

    /**
     * 父级传感器ID
     */
    private Integer parentId;

    /**
     * 层级类型：mine-矿井，area-采区，workface-工作面，tunnel-巷道，point-监测点
     */
    private String levelType;

    /**
     * 层级编码
     */
    private String levelCode;

    /**
     * 层级名称（模糊查询）
     */
    private String levelName;

    /**
     * 层级路径（模糊查询）
     */
    private String levelPath;

    /**
     * 是否叶子节点：0-否，1-是
     */
    private Integer isLeaf;

    /**
     * 传感器状态：1-正常，2-异常，3-离线
     */
    private Integer status;

    /**
     * 传感器状态列表（用于多状态查询）
     */
    private List<Integer> statusList;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 当前值最小范围
     */
    private BigDecimal currentValueMin;

    /**
     * 当前值最大范围
     */
    private BigDecimal currentValueMax;

    /**
     * 当前值（用于新增/修改）
     */
    private BigDecimal currentValue;

    /**
     * 最小阈值（用于新增/修改）
     */
    private BigDecimal thresholdMin;

    /**
     * 最大阈值（用于新增/修改）
     */
    private BigDecimal thresholdMax;

    /**
     * 同级排序号（用于新增/修改）
     */
    private Integer sortOrder;

    /**
     * 测量单位
     */
    private String unit;

    /**
     * 传感器描述（模糊查询）
     */
    private String description;

    /**
     * 备注（模糊查询）
     */
    private String remark;

    /**
     * 传感器类型列表（用于多类型查询）
     */
    private List<String> sensorTypeList;

    /**
     * 层级类型列表（用于多层级查询）
     */
    private List<String> levelTypeList;

    /**
     * 是否只查询异常传感器
     */
    private Boolean onlyAbnormal;

    /**
     * 是否只查询超出阈值的传感器
     */
    private Boolean onlyOutOfRange;

    /**
     * 是否构建树形结构
     */
    private Boolean buildTree;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向：ASC/DESC
     */
    private String orderDirection;
}
