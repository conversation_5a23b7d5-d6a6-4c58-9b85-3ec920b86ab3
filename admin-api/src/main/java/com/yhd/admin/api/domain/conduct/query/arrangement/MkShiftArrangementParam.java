package com.yhd.admin.api.domain.conduct.query.arrangement;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 值班带班安排表(MkShiftArrangement)Param
 *
 * <AUTHOR>
 * @since 2025-07-29 14:11:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkShiftArrangementParam extends QueryParam implements Serializable {

    /** 主键列表ids */
    private List<Long> ids;
    /** id */
    private Long id;
    /** 班次日期 */
    private LocalDate shiftDate;
    /** 班次星期 */
    private String shiftWeek;
    /** 班次类型名称 1：值班 2：夜班 3：早班 4：中班 5：晚班*/
    private String shiftTypeName;
    /** 班次类型代码 1：值班 2：夜班 3：早班 4：中班 5：晚班*/
    private String shiftTypeCode;
    /** 值班人员账号 */
    private String account;
    /** 值班人员姓名 */
    private String name;
    /** 计划值班人员账号 */
    private String planAccount;
    /** 计划值班人员姓名 */
    private String planName;
    /** 是否调班（0：否 1：是） */
    private Integer planFlag;

    private String year;

    private String month;
}

