package com.yhd.admin.api.domain.sys.vo;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
public class AppVO extends BaseVO implements Serializable {

    private Long id;
    /**
     * app 图标
     */
    private String appIcon;
    /**
     * app名称
     */
    private String appName;
    /**
     * app访问地址
     */
    private String appUrl;
    /**
     * 角色状态
     */
    private Boolean isEnable;
    /**
     * 排序
     */
    private Integer sorted;
}
