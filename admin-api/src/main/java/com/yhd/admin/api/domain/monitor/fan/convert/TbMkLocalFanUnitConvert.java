package com.yhd.admin.api.domain.monitor.fan.convert;

import com.yhd.admin.api.domain.monitor.fan.dto.TbMkLocalFanUnitDTO;
import com.yhd.admin.api.domain.monitor.fan.entity.TbMkLocalFanUnit;
import com.yhd.admin.api.domain.monitor.fan.vo.TbMkLocalFanUnitVO;
import com.yhd.admin.api.domain.monitor.fan.vo.create.TbMkLocalFanUnitCreateVO;
import com.yhd.admin.api.domain.monitor.fan.vo.update.TbMkLocalFanUnitUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 局扇风机单元
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkLocalFanUnitConvert {

    @Mapping(target = "id", ignore = true)
    TbMkLocalFanUnit convert(TbMkLocalFanUnitCreateVO createVO);

    TbMkLocalFanUnit convert(TbMkLocalFanUnitUpdateVO updateVO);

    TbMkLocalFanUnitVO convert(TbMkLocalFanUnit po);

    TbMkLocalFanUnitDTO toDTO(TbMkLocalFanUnit po);



}
