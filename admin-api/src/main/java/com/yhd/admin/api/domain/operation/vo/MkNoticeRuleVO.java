package com.yhd.admin.api.domain.operation.vo;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 消息下发规则表(MkNoticeRule)VO
 *
 * <AUTHOR>
 * @since 2025-08-08 09:12:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkNoticeRuleVO extends BaseVO implements Serializable {
    /** id */
    private Integer id;
    /** 单位名称 */
    private String orgName;
    /** 单位代码 */
    private String orgCode;
    /** 规则名称 */
    private String name;
    /** 类型名称 1：策略模式 2：直发模式 3：混合模式 */
    private String typeName;
    /** 类型代码 NOTICE_RULE_TYPE */
    private String typeCode;
    /** 消息模板 */
    private String template;
    /** 创建人姓名 */
    private String creator;
}

