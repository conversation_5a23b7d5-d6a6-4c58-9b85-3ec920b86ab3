package com.yhd.admin.api.domain.monitor.fan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 局扇风机单元
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_mk_local_fan_unit")
public class TbMkLocalFanUnit extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属局扇系统ID
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 风机编号（如：FAN-01）
     */
    @TableField("fan_number")
    private String fanNumber;

    /**
     * 风机型号
     */
    @TableField("model")
    private String model;

    /**
     * 额定功率(kW)
     */
    @TableField("rated_power")
    private BigDecimal ratedPower;

    /**
     * 额定风量(m³/min)
     */
    @TableField("rated_air_volume")
    private BigDecimal ratedAirVolume;

    /**
     * 安装日期
     */
    @TableField("installation_date")
    private LocalDate installationDate;

    /**
     * 状态：1-运行，0-停用
     */
    @TableField("status")
    private Byte status;
}
