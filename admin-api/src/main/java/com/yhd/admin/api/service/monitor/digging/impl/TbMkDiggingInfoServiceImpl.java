package com.yhd.admin.api.service.monitor.digging.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.digging.TbMkDiggingInfoDao;
import com.yhd.admin.api.domain.monitor.digging.convert.TbMkDiggingInfoConvert;
import com.yhd.admin.api.domain.monitor.digging.dto.TbMkDiggingInfoDTO;
import com.yhd.admin.api.domain.monitor.digging.entity.TbMkDiggingInfo;
import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingInfoCreateVO;
import com.yhd.admin.api.domain.monitor.digging.vo.page.TbMkDiggingInfoPageVO;
import com.yhd.admin.api.domain.monitor.digging.vo.update.TbMkDiggingInfoUpdateVO;
import com.yhd.admin.api.service.monitor.digging.TbMkDiggingInfoService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 掘进机基本信息 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkDiggingInfoServiceImpl extends ServiceImpl<TbMkDiggingInfoDao, TbMkDiggingInfo> implements TbMkDiggingInfoService {

    @Resource
    private TbMkDiggingInfoConvert tbMkDiggingInfoConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkDiggingInfoCreateVO createVO) {
        TbMkDiggingInfo tbMkDiggingInfo = tbMkDiggingInfoConvert.convert(createVO);
        baseMapper.insert(tbMkDiggingInfo);
        return tbMkDiggingInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkDiggingInfoUpdateVO updateVO) {
        TbMkDiggingInfo tbMkDiggingInfo = tbMkDiggingInfoConvert.convert(updateVO);
        return baseMapper.updateById(tbMkDiggingInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkDiggingInfo get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkDiggingInfoDTO> page(TbMkDiggingInfoPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkDiggingInfo> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkDiggingInfo> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.like(StringUtils.isNotBlank(param.getDeviceNumber()), TbMkDiggingInfo::getDeviceNumber, param.getDeviceNumber());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getDeviceName()), TbMkDiggingInfo::getDeviceName, param.getDeviceName());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getRemarks()), TbMkDiggingInfo::getRemarks, param.getRemarks());
        queryChainWrapper.orderByDesc(TbMkDiggingInfo::getId);
        return queryChainWrapper.page(iPage).convert(tbMkDiggingInfoConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkDiggingInfoCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 掘进机基本信息：传入的列表为空");
            return 0;
        }
        List<TbMkDiggingInfo> entities = createVOs.stream()
                .map(tbMkDiggingInfoConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 掘进机基本信息 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkDiggingInfoUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 掘进机基本信息：传入的列表为空");
            return 0;
        }
        List<TbMkDiggingInfo> entities = updateVOs.stream()
                .map(tbMkDiggingInfoConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 掘进机基本信息：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 掘进机基本信息 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
