package com.yhd.admin.api.domain.monitor.ventilation.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 通风机及其电机运行参数数据传输对象
 * 用于封装从数据库查询出的通风机和电机的综合运行信息，供前端展示。
 *
 * <AUTHOR>
 * @since
 */
@Data
public class TbMkVentilationDetailDTO {
    /**
     * 电机ID
     */
    private Long motorId;

    /**
     * 电机编号
     */
    private String motorNumber;

    /**
     * 电机名称
     */
    private String motorName;

    /**
     * 电压Uab(KV)
     */
    private BigDecimal voltageUab;

    /**
     * 电压Ubc(KV)
     */
    private BigDecimal voltageUbc;

    /**
     * 电压Uca(KV)
     */
    private BigDecimal voltageUca;

    /**
     * 电流Ia(A)
     */
    private BigDecimal currentIa;

    /**
     * 电流Ib(A)
     */
    private BigDecimal currentIb;

    /**
     * 电流Ic(A)
     */
    private BigDecimal currentIc;

    /**
     * 水平振动(mm/s)
     */
    private BigDecimal horizontalVibration;

    /**
     * 垂直振动(mm/s)
     */
    private BigDecimal verticalVibration;

    /**
     * 前轴温度(°C)
     */
    private BigDecimal frontBearingTemperature;

    /**
     * 后轴温度(°C)
     */
    private BigDecimal rearBearingTemperature;

    /**
     * 母线电压(V)
     */
    private BigDecimal busVoltage;
}
