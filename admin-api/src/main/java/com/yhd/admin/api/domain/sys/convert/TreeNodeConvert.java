package com.yhd.admin.api.domain.sys.convert;

import com.yhd.admin.api.domain.sys.dto.MenuDTO;
import com.yhd.admin.api.domain.sys.dto.OrgDTO;
import com.yhd.admin.api.domain.sys.vo.TreeNode;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName TreeNode.java @Description TODO
 * @createTime 2020年05月11日 14:42:00
 */
@Mapper(componentModel = "spring", uses = {Transform.class})
public interface TreeNodeConvert {

    @Mappings({@Mapping(source = "id", target = "key"), @Mapping(source = "name", target = "title"),
        @Mapping(target = "isLeaf", source = "children", qualifiedByName = {"Transform", "checkIncludeLeafNode"}),
        @Mapping(target = "disabled", source = "type")})
    TreeNode toTreeNode(MenuDTO menuDTO);

    List<TreeNode> toTreeNode(List<MenuDTO> menuDTOList);

    @Mappings({@Mapping(source = "id", target = "key"), @Mapping(source = "orgName", target = "title"),
        @Mapping(target = "isLeaf", source = "children", qualifiedByName = {"Transform", "checkIncludeLeafNode"}),
        @Mapping(source = "status", target = "disabled", qualifiedByName = {"Transform", "negDisabled"})})
    TreeNode orgToTreeNode(OrgDTO orgDTO);

    List<TreeNode> orgToTreeNode(List<OrgDTO> orgDTOList);
}
