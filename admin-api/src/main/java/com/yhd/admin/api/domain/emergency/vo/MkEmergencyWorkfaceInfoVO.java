package com.yhd.admin.api.domain.emergency.vo;

import lombok.Data;

/**
 * 工作面信息VO
 *
 * <AUTHOR>
 */
@Data
public class MkEmergencyWorkfaceInfoVO {
    /**
     * 工作面编码
     */
    private String workfaceCode;

    /**
     * 工作面名称
     */
    private String workfaceName;

    /**
     * 工作面类型
     */
    private String workfaceType;

    /**
     * X坐标
     */
    private String coordinateX;

    /**
     * Y坐标
     */
    private String coordinateY;

    /**
     * Z坐标
     */
    private String coordinateZ;

    /**
     * 安全状态：0-危险，1-正常，2-警告
     */
    private Integer safetyStatus;

    /**
     * 安全状态名称
     */
    private String safetyStatusName;

    /**
     * 是否活跃
     */
    private Boolean isActive;
}