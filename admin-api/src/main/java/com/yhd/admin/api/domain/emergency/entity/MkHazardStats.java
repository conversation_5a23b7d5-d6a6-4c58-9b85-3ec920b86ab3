package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 综合决策隐患统计表
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_mk_hazard_stats")
public class MkHazardStats {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 统计编码
     */
    private String statsCode;

    /**
     * 统计日期
     */
    private LocalDate statsDate;

    /**
     * 未整改数量
     */
    private Integer unrectifiedCount;

    /**
     * 已整改未验收数量
     */
    private Integer rectifiedUnverifiedCount;

    /**
     * 已验收收回数量
     */
    private Integer verifiedUnacceptedCount;

    /**
     * 已验通过数量
     */
    private Integer verifiedPassedCount;

    /**
     * 隐患总数
     */
    private Integer totalCount;

    /**
     * 组织机构编码
     */
    private String orgCode;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}