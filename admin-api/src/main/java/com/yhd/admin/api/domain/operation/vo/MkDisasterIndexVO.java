package com.yhd.admin.api.domain.operation.vo;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 算法模型-自然灾害评价指标表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MkDisasterIndexVO extends BaseVO implements Cloneable, Serializable {
    /**
     * Id
     */
    private Long id;

    /**
     * 评价级别code
     */
    private String indexCode;

    /**
     * 评价级别name
     */
    private String indexName;

    /**
     * 初始值
     */
    private Integer initialValue;

    /**
     * 最大值
     */
    private Integer maxValue;

    /**
     * 起始分数
     */
    private Integer startNum;

    /**
     * 终止分数
     */
    private Integer endNum;

    /**
     * 权重
     */
    private BigDecimal weight;

    /**
     * 风险等级
     */
    private String level;

}
