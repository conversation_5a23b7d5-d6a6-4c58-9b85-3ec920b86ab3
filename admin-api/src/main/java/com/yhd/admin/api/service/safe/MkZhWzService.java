package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.safe.dto.MkZhWzDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhWz;
import com.yhd.admin.api.domain.safe.query.MkZhWzParam;


public interface MkZhWzService extends IService<MkZhWz> {

    Boolean addOrModify(MkZhWzParam param);

    Boolean remove(MkZhWzParam param);

    MkZhWzDTO getCurrentDetail(MkZhWzParam param);

    IPage<MkZhWzDTO> pagingQuery(MkZhWzParam param);

    MkZhWzDTO getData();
}
