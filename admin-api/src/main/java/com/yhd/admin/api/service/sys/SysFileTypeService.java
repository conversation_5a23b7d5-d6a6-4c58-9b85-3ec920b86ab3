package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.sys.dto.SysFileTypeDTO;
import com.yhd.admin.api.domain.sys.entity.SysFileType;
import com.yhd.admin.api.domain.sys.query.SysFileTypeParam;

import java.util.List;

/**
 * 图文类别-业务层接口
 *
 * <AUTHOR>
 * @date 2025/7/29 9:31
 */
public interface SysFileTypeService extends IService<SysFileType> {

  /**
   * 查询图文列表
   *
   * @param id 主键id
   * @return List<SysFileTypeDTO>
   */
  List<SysFileTypeDTO> queryList(Long id);

  /**
   * 新增
   *
   * @param param 表单
   * @return true成功，false失败
   */
  Boolean add(SysFileTypeParam param);

  /**
   * 编辑
   *
   * @param param 表单
   * @return true成功，false失败
   */
  Boolean modify(SysFileTypeParam param);

  /**
   * 删除
   *
   * @param param 主键id
   * @return true成功，false失败
   */
  Boolean remove(SysFileTypeParam param);

  /**
   * 查询图文类别详情
   *
   * @param param 主键id
   * @return 图文类别详情
   */
  SysFileTypeDTO getCurrentDetail(SysFileTypeParam param);

  /**
   * 根据ID查询父级节点
   *
   * @param id 主键id
   * @return 父级节点
   */
  List<SysFileTypeDTO> listParentById(Long id);

  /**
   * 根据ID查询子级
   *
   * @param id 主键id
   * @return 子级列表
   */
  List<SysFileTypeDTO> listChildrenById(Long id);
}
