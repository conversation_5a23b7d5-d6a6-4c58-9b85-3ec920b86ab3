package com.yhd.admin.api.domain.monitor.digging.convert;

import com.yhd.admin.api.domain.monitor.digging.dto.TbMkDiggingInfoDTO;
import com.yhd.admin.api.domain.monitor.digging.entity.TbMkDiggingInfo;
import com.yhd.admin.api.domain.monitor.digging.vo.TbMkDiggingInfoVO;
import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingInfoCreateVO;
import com.yhd.admin.api.domain.monitor.digging.vo.update.TbMkDiggingInfoUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 掘进机基本信息
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkDiggingInfoConvert {

    @Mapping(target = "id", ignore = true)
    TbMkDiggingInfo convert(TbMkDiggingInfoCreateVO createVO);

    TbMkDiggingInfo convert(TbMkDiggingInfoUpdateVO updateVO);

    TbMkDiggingInfoVO convert(TbMkDiggingInfo po);

    TbMkDiggingInfoDTO toDTO(TbMkDiggingInfo po);



}
