package com.yhd.admin.api.domain.emergency.convert;

import com.yhd.admin.api.domain.emergency.dto.MkEmergencyHistoryDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyHistory;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyHistoryParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyHistoryVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 应急历史事件记录表转换类
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface MkEmergencyHistoryConvert {

    /**
     * Entity转DTO
     */
    MkEmergencyHistoryDTO toDTO(MkEmergencyHistory entity);

    /**
     * Entity列表转DTO列表
     */
    List<MkEmergencyHistoryDTO> toDTO(List<MkEmergencyHistory> entityList);

    /**
     * DTO转VO
     */
    @Mapping(target = "eventTypeName", expression = "java(getEventTypeName(dto.getEventType()))")
    @Mapping(target = "eventStatusName", expression = "java(getEventStatusName(dto.getEventStatus()))")
    MkEmergencyHistoryVO toVO(MkEmergencyHistoryDTO dto);

    /**
     * DTO列表转VO列表
     */
    List<MkEmergencyHistoryVO> toVO(List<MkEmergencyHistoryDTO> dtoList);

    /**
     * Param转Entity
     */
    MkEmergencyHistory toEntity(MkEmergencyHistoryParam param);

    /**
     * Param列表转Entity列表
     */
    List<MkEmergencyHistory> toEntity(List<MkEmergencyHistoryParam> paramList);

    /**
     * 获取事件类型名称
     */
    default String getEventTypeName(Integer eventType) {
        if (eventType == null) {
            return null;
        }
        switch (eventType) {
            case 1:
                return "应急事件";
            case 2:
                return "演练事件";
            default:
                return "未知";
        }
    }

    /**
     * 获取事件状态名称
     */
    default String getEventStatusName(Integer eventStatus) {
        if (eventStatus == null) {
            return null;
        }
        switch (eventStatus) {
            case 1:
                return "进行中";
            case 2:
                return "已完成";
            case 3:
                return "已关闭";
            default:
                return "未知";
        }
    }
}
