package com.yhd.admin.api.service.monitor.digging;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.monitor.digging.dto.TbMkDiggingNavigationDTO;
import com.yhd.admin.api.domain.monitor.digging.entity.TbMkDiggingNavigation;
import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingNavigationCreateVO;
import com.yhd.admin.api.domain.monitor.digging.vo.page.TbMkDiggingNavigationPageVO;
import com.yhd.admin.api.domain.monitor.digging.vo.update.TbMkDiggingNavigationUpdateVO;

import java.util.List;

/**
 * 掘进机导航参数 服务类
 *
 * <AUTHOR>
 * @since
 */
public interface TbMkDiggingNavigationService  extends IService<TbMkDiggingNavigation> {

    /**
     * 新建
     *
     * @param createVO 新建VO
     * @return id
     */
    Long create(TbMkDiggingNavigationCreateVO createVO);

    /**
     * 更新
     *
     * @param updateVO 更新VO
     */
    int update(TbMkDiggingNavigationUpdateVO updateVO);

    /**
     * 删除
     *
     * @param id id
     */
    int delete(Long id);

    /**
     * 查询单个
     *
     * @param id id
     * @return po
     */
    TbMkDiggingNavigation get(Long id);

    /**
     * 分页查询
     *
     * @param pageVO 分页VO
     * @return pageResult
     */
    IPage<TbMkDiggingNavigationDTO> page(TbMkDiggingNavigationPageVO pageVO);

    /**
      * 批量删除
      *
      * @param ids 要删除的记录ID列表
      * @return 受影响的行数
      */
    int batchDelete(List<Long> ids);

    /**
     * 批量新增
     *
     * @param createVOs 新增VO列表
     * @return 新增的记录ID列表
     */
    int batchCreate(List<TbMkDiggingNavigationCreateVO> createVOs);

    /**
     * 批量更新
     *
     * @param updateVOs 更新VO列表
     * @return 受影响的行数
     */
    int batchUpdate(List<TbMkDiggingNavigationUpdateVO> updateVOs);
}
