package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkDisasterGradeDTO;
import com.yhd.admin.api.domain.operation.entity.MkDisasterGrade;
import com.yhd.admin.api.domain.operation.query.MkDisasterGradeParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 算法模型-报警次级表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
public interface MkDisasterGradeService extends IService<MkDisasterGrade> {

    IPage<MkDisasterGradeDTO> pagingQuery(MkDisasterGradeParam queryParam);

     Boolean add(MkDisasterGradeParam param);

    Boolean modify(MkDisasterGradeParam param);

    Boolean removeBatch(BatchParam param);

    MkDisasterGradeDTO getCurrentDetail(MkDisasterGradeParam param);
}
