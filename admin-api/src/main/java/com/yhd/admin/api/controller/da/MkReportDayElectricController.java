package com.yhd.admin.api.controller.da;

import com.yhd.admin.api.domain.da.convert.MkReportDayElectricConvert;
import com.yhd.admin.api.domain.da.query.MkReportDayElectricParam;
import com.yhd.admin.api.domain.da.vo.MkReportDayElectricVO;
import com.yhd.admin.api.service.da.MkReportDayElectricService;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 电度能耗分析-控制层
 *
 * <AUTHOR>
 * @date 2025/8/1 14:55
 */
@RestController
@RequestMapping("/da/electric")
public class MkReportDayElectricController {
  private final MkReportDayElectricConvert convert;
  private final MkReportDayElectricService service;

  public MkReportDayElectricController(
      MkReportDayElectricConvert convert, MkReportDayElectricService service) {
    this.convert = convert;
    this.service = service;
  }

  @PostMapping(
      value = "/dayElectricRatioCount",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<MkReportDayElectricVO> dayElectricRatioCount(
      @RequestBody MkReportDayElectricParam param) {
    return RespJson.success(convert.toVO(service.dayElectricRatioCount(param)));
  }

  @PostMapping(
      value = "/monthElectricRatioCount",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<MkReportDayElectricVO> monthElectricRatioCount(
      @RequestBody MkReportDayElectricParam param) {
    return RespJson.success(convert.toVO(service.monthElectricRatioCount(param)));
  }

  @PostMapping(
      value = "/yearElectricRatioCount",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<MkReportDayElectricVO> yearElectricRatioCount(
      @RequestBody MkReportDayElectricParam param) {
    return RespJson.success(convert.toVO(service.yearElectricRatioCount(param)));
  }

  @PostMapping(
      value = "/dayElectricCountList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<MkReportDayElectricVO>> dayElectricCountList(
      @RequestBody MkReportDayElectricParam param) {
    return RespJson.success(convert.toVO(service.dayElectricCountList(param)));
  }

  @PostMapping(
      value = "/monthElectricCountList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<MkReportDayElectricVO>> monthElectricCountList(
      @RequestBody MkReportDayElectricParam param) {
    return RespJson.success(convert.toVO(service.monthElectricCountList(param)));
  }

  @PostMapping(
      value = "/yearElectricCountList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<MkReportDayElectricVO>> yearElectricCountList(
      @RequestBody MkReportDayElectricParam param) {
    return RespJson.success(convert.toVO(service.yearElectricCountList(param)));
  }
}
