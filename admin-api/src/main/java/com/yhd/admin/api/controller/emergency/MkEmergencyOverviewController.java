package com.yhd.admin.api.controller.emergency;

import com.yhd.admin.api.domain.emergency.query.MkEmergencyOverviewParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyOverviewVO;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyListItemInfoVO;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyNavigationInfoVO;
import com.yhd.admin.api.service.emergency.MkEmergencyOverviewService;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 应急救援中心总览 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/emergency/overview")
public class MkEmergencyOverviewController {

    @Resource
    private MkEmergencyOverviewService emergencyOverviewService;

    /**
     * 获取应急救援中心总览数据
     *
     * @param param 查询参数
     * @return 总览数据
     */
    @PostMapping(value = "/getOverviewData", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkEmergencyOverviewVO> getOverviewData(
            @RequestBody(required = false) MkEmergencyOverviewParam param) {
        log.info("获取应急救援中心总览数据，参数：{}", param);

        if (param == null) {
            param = new MkEmergencyOverviewParam();
        }

        MkEmergencyOverviewVO result = emergencyOverviewService.getOverviewData(param);
        return RespJson.success(result);
    }

    /**
     * 根据导航类型获取列表项目
     *
     * @param param 查询参数，包含导航类型（可选，不传查全部）
     * @return 列表项目
     */
    @PostMapping(value = "/getListItems", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyListItemInfoVO>> getListItems(
            @RequestBody(required = false) MkEmergencyOverviewParam param) {
        log.info("根据导航类型获取列表项目，参数：{}", param);

        String navType = param != null ? param.getNavType() : null;
        List<MkEmergencyListItemInfoVO> result = emergencyOverviewService
                .getListItemsByNavType(navType);
        return RespJson.success(result);
    }

    /**
     * 获取功能导航列表
     *
     * @param param 查询参数，包含组织编码
     * @return 导航列表
     */
    @PostMapping(value = "/getNavigations", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyNavigationInfoVO>> getNavigations(
            @RequestBody(required = false) MkEmergencyOverviewParam param) {
        log.info("获取功能导航列表，参数：{}", param);

        String orgCode = param != null ? param.getOrgCode() : null;
        List<MkEmergencyNavigationInfoVO> result = emergencyOverviewService.getNavigations(orgCode);
        return RespJson.success(result);
    }
}
