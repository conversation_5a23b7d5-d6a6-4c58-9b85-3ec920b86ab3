package com.yhd.admin.api.domain.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;

/**
 * 算法模型-报警次级表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MkDisasterGrade extends BaseEntity implements Cloneable, Serializable {
    /**
     * Id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

	/**
	* 报警次级code
	*/
	private String gradeCode;

	/**
	* 报警次级name
	*/
	private String gradeName;

	/**
	* 初始值
	*/
	private Integer initialValue;

	/**
	* 结束值
	*/
	private Integer endValue;

	/**
	* 监测名称
	*/
	private String monitorName;

	/**
	* 类型
	*/
	private String type;

}
