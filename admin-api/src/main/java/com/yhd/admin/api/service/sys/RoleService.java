package com.yhd.admin.api.service.sys;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.sys.dto.RoleDTO;
import com.yhd.admin.api.domain.sys.entity.SysRole;
import com.yhd.admin.api.domain.sys.query.RoleParam;
import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RoleService.java
 * @Description TODO
 * @createTime 2020年03月30日 11:16:00
 */
public interface RoleService extends IService<SysRole> {

    /**
     * 根据用户查询用户对应的角色
     *
     * @param param {@link UserParam}
     * @return {@link List<RoleDTO>}
     */
    List<RoleDTO> getRoleByUser(UserParam param);

    /**
     * 分页查询
     *
     * @param queryParam 分页查询参数
     * @return {@link IPage<RoleDTO>}
     */
    IPage<RoleDTO> pagingQuery(RoleParam queryParam);

    /**
     * 新增角色
     *
     * @param param 新增角色信息
     * @return true 成功，false失败
     */
    boolean addOrUpdate(RoleParam param);

    /**
     * 根据主键修改角色
     *
     * @param param 角色信息
     * @return true 成功，false失败
     */
    boolean modify(RoleParam param);

    /**
     * 批量删除 根据主键
     *
     * @param batchParam List<Long>
     * @return true 成功，false失败
     */
    boolean removeBatch(BatchParam batchParam);

    /**
     * 根据ID 查询角色详情。
     *
     * @param param 主键ID
     * @return {@link RoleDTO}
     */
    RoleDTO currentDetail(RoleParam param);

    /**
     * 获取所有角色列表
     *
     * @return {@link List<RoleDTO>}
     */
    List<RoleDTO> queryRoleList();

    /**
     * 校验角色编码是否存在
     *
     * @param param
     * @return
     */
    Boolean validateIfNotExist(RoleParam param);
}
