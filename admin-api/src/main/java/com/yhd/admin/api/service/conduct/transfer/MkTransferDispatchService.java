package com.yhd.admin.api.service.conduct.transfer;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.transfer.MkTransferDispatchDTO;
import com.yhd.admin.api.domain.conduct.entity.transfer.MkTransferDispatch;
import com.yhd.admin.api.domain.conduct.query.transfer.MkTransferDispatchParam;

import java.util.List;

/**
 * 收发文登记表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
public interface MkTransferDispatchService extends IService<MkTransferDispatch> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkTransferDispatchDTO> pagingQuery(MkTransferDispatchParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkTransferDispatchDTO> queryList(MkTransferDispatchParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkTransferDispatchParam param);

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    MkTransferDispatchDTO getCurrentDetails(MkTransferDispatchParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkTransferDispatchParam param);

}
