package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.emergency.MkEmergencyDrillDao;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyDrillConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyDrillDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyDrill;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyDrillParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 应急演练记录表 Service实现类
 * <AUTHOR>
 */
@Service
public class MkEmergencyDrillServiceImpl extends ServiceImpl<MkEmergencyDrillDao, MkEmergencyDrill> 
        implements MkEmergencyDrillService {

    @Resource
    private MkEmergencyDrillConvert convert;

    @Override
    public IPage<MkEmergencyDrillDTO> pagingQuery(MkEmergencyDrillParam param) {
        IPage<MkEmergencyDrill> iPage = new Page<>(param.getCurrent(), param.getPageSize());
        
        LambdaQueryChainWrapper<MkEmergencyDrill> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        
        // 组织编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getOrgCode()), 
                MkEmergencyDrill::getOrgCode, param.getOrgCode());
        
        // 演练编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getDrillCode()), 
                MkEmergencyDrill::getDrillCode, param.getDrillCode());
        
        // 演练名称模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getDrillName()), 
                MkEmergencyDrill::getDrillName, param.getDrillName());
        
        // 演练地点模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getDrillLocation()), 
                MkEmergencyDrill::getDrillLocation, param.getDrillLocation());
        
        // 演练方式查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getDrillType()), 
                MkEmergencyDrill::getDrillType, param.getDrillType());
        
        // 负责人模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getResponsiblePerson()), 
                MkEmergencyDrill::getResponsiblePerson, param.getResponsiblePerson());
        
        // 演练状态查询
        queryChainWrapper.eq(param.getDrillStatus() != null, 
                MkEmergencyDrill::getDrillStatus, param.getDrillStatus());
        
        // 演练日期范围查询（基于演练时间）
        if (param.getDrillDateBegin() != null) {
            queryChainWrapper.ge(MkEmergencyDrill::getDrillTime,
                    param.getDrillDateBegin().atStartOfDay());
        }
        if (param.getDrillDateEnd() != null) {
            queryChainWrapper.le(MkEmergencyDrill::getDrillTime,
                    param.getDrillDateEnd().atTime(23, 59, 59));
        }
        
        // 排序：按演练时间倒序
        queryChainWrapper.orderByDesc(MkEmergencyDrill::getDrillTime);
        
        return queryChainWrapper.page(iPage).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkEmergencyDrillParam param) {
        // 必填字段校验
        if (StringUtils.isBlank(param.getDrillCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getDrillName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (param.getDrillTime() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getDrillLocation())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getDrillType())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getResponsiblePerson())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        return super.save(convert.toEntity(param));
    }

    @Override
    public Boolean modify(MkEmergencyDrillParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        // 必填字段校验
        if (StringUtils.isBlank(param.getDrillCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getDrillName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (param.getDrillTime() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getDrillLocation())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getDrillType())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getResponsiblePerson())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        return super.updateById(convert.toEntity(param));
    }

    @Override
    public MkEmergencyDrillDTO getCurrentDetail(MkEmergencyDrillParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        MkEmergencyDrill entity = super.getById(param.getId());
        return convert.toDTO(entity);
    }

    @Override
    public Boolean removeBatch(BatchParam param) {
        if (param.getId() == null || param.getId().isEmpty()) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        return super.removeByIds(param.getId());
    }
}
