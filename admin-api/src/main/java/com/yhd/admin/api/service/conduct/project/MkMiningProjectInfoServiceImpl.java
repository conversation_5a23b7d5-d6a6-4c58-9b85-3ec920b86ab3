package com.yhd.admin.api.service.conduct.project;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.project.MkMiningProjectInfoDao;
import com.yhd.admin.api.domain.conduct.convert.project.MkMiningProjectInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.project.MkMiningProjectInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.project.MkMiningProjectInfo;
import com.yhd.admin.api.domain.conduct.query.project.MkMiningProjectInfoParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.DicService;
import com.yhd.admin.common.constant.DicConstant;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 回采项目信息服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30 14:05:52
 */
@Service
public class MkMiningProjectInfoServiceImpl extends ServiceImpl<MkMiningProjectInfoDao, MkMiningProjectInfo> implements MkMiningProjectInfoService {

    private final MkMiningProjectInfoConvert mkMiningProjectInfoConvert;
    private final DicService dicService;

    public MkMiningProjectInfoServiceImpl(MkMiningProjectInfoConvert mkMiningProjectInfoConvert, DicService dicService) {
        this.mkMiningProjectInfoConvert = mkMiningProjectInfoConvert;
        this.dicService = dicService;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkMiningProjectInfoDTO> pagingQuery(MkMiningProjectInfoParam param) {
        IPage<MkMiningProjectInfo> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkMiningProjectInfo> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.like(StringUtils.isNotBlank(param.getName()), MkMiningProjectInfo::getName, param.getName());
        wrapper.eq(StringUtils.isNotBlank(param.getStatusCode()), MkMiningProjectInfo::getStatusCode, param.getStatusCode());
        return wrapper.page(page).convert(mkMiningProjectInfoConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkMiningProjectInfoDTO> queryList(MkMiningProjectInfoParam param) {
        LambdaQueryWrapper<MkMiningProjectInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(param.getName()), MkMiningProjectInfo::getName, param.getName());
        return mkMiningProjectInfoConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkMiningProjectInfoParam param) {
        MkMiningProjectInfo entity = mkMiningProjectInfoConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            entity.setStatusName(dicService.transform(DicConstant.PROJECT_STATUS, entity.getStatusCode()));
            entity.setReviewStatusCode(DicConstant.PROJECT_REVIEW_STATUS_ONE);
            entity.setReviewStatusName(dicService.transform(DicConstant.PROJECT_REVIEW_STATUS, entity.getReviewStatusCode()));
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkMiningProjectInfoDTO getCurrentDetails(MkMiningProjectInfoParam param) {
        return mkMiningProjectInfoConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkMiningProjectInfoParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }

    /**
     * 提交审批
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean submit(MkMiningProjectInfoParam param) {
        if (Objects.isNull(param.getId())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (param.getStatusCode().equals(DicConstant.PROJECT_REVIEW_STATUS_TWO)) {
            throw new BMSException(ExceptionEnum.REPEAT_SUBMIT_ERROR);
        }
        MkMiningProjectInfo entity = mkMiningProjectInfoConvert.toEntity(param);
        entity.setStatusCode(DicConstant.PROJECT_REVIEW_STATUS_TWO);
        entity.setStatusName(dicService.transform(DicConstant.PROJECT_STATUS, entity.getStatusCode()));
        return updateById(entity);
    }

    /**
     * 通过审批、驳回
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean handle(MkMiningProjectInfoParam param) {
        if (Objects.isNull(param.getId())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        MkMiningProjectInfo entity = mkMiningProjectInfoConvert.toEntity(param);
        entity.setStatusName(dicService.transform(DicConstant.PROJECT_STATUS, entity.getStatusCode()));
        return updateById(entity);
    }
}
