package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkUserDeptDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserDept;
import com.yhd.admin.api.domain.operation.query.MkUserDeptParam;
import com.yhd.admin.common.domain.query.BatchParam;

import java.util.List;

/**
 * 运维配置中心-部门管理-Service
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */
public interface MkUserDeptService extends IService<MkUserDept> {

    IPage<MkUserDeptDTO> pagingQuery(MkUserDeptParam queryParam);

    Boolean addOrModify(MkUserDeptParam param);

    Boolean removeBatch(BatchParam param);
    /**
     * 获取当前部门详情
     * @param param
     * @return
     */
    MkUserDeptDTO getCurrentDetail(MkUserDeptParam param);
    /**
     * 获取部门集合
     *
     * @param param
     * @return
     */
    List<MkUserDeptDTO> getDeptList(MkUserDeptParam param);
}
