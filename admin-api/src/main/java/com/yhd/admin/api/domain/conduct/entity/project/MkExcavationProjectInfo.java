package com.yhd.admin.api.domain.conduct.entity.project;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 掘进项目信息(MkExcavationProjectInfo)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 14:25:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkExcavationProjectInfo extends BaseEntity implements Serializable {

    /** id */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 名称 */
    private String name;
    /** 状态名称 1：计划 2：执行 3：结束 */
    private String statusName;
    /** 状态代码 1：计划 2：执行 3：结束 */
    private String statusCode;
    /** 巷道名称 */
    private String tunnelName;
    /** 巷道id */
    private Long tunnelId;
    /** 作业区队名称 */
    private String excavationTeamName;
    /** 作业区队id */
    private Long excavationTeamId;
    /** 掘进工艺名称 1：综掘 2：连采 */
    private String excavationTechnologyName;
    /** 掘进工艺代码 1：综掘 2：连采 */
    private String excavationTechnologyCode;
    /** 计划开始日期 */
    private LocalDate planStartDate;
    /** 计划结束日期 */
    private LocalDate planEndDate;
    /** 实际开始日期 */
    private LocalDate startDate;
    /** 实际结束日期 */
    private LocalDate endDate;
    /** 接续项目名称 */
    private String excavationProjectName;
    /** 接续项目id */
    private Long excavationProjectId;
    /** 审核状态名称 1：未提交 2：已提交 3：已通过 4：驳回 */
    private String reviewStatusName;
    /** 审核状态代码 1：未提交 2：已提交 3：已通过 4：驳回 */
    private String reviewStatusCode;
}

