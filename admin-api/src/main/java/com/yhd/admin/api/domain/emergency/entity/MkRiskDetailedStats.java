package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 综合决策风险详细统计表
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_mk_risk_detailed_stats")
public class MkRiskDetailedStats {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 统计编码
     */
    private String statsCode;

    /**
     * 统计日期
     */
    private LocalDate statsDate;

    /**
     * 重大风险数量
     */
    private Integer majorRiskCount;

    /**
     * 较大风险数量
     */
    private Integer significantRiskCount;

    /**
     * 一般风险数量
     */
    private Integer generalRiskCount;

    /**
     * 低风险数量
     */
    private Integer lowRiskCount;

    /**
     * 一般A级数量
     */
    private Integer generalALevelCount;

    /**
     * 一般B级数量
     */
    private Integer generalBLevelCount;

    /**
     * 一般C级数量
     */
    private Integer generalCLevelCount;

    /**
     * 风险总数
     */
    private Integer totalRiskCount;

    /**
     * 组织机构编码
     */
    private String orgCode;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}