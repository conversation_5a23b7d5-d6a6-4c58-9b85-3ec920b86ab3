package com.yhd.admin.api.service.conduct.base;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.base.MkMineInfoDao;
import com.yhd.admin.api.domain.conduct.convert.base.MkMineInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.base.MkMineInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkMineInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkMineInfoParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 矿井基础信息表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25 14:01:30
 */
@Service
public class MkMineInfoServiceImpl extends ServiceImpl<MkMineInfoDao, MkMineInfo> implements MkMineInfoService {

    public final MkMineInfoConvert mkMineInfoConvert;

    public MkMineInfoServiceImpl(MkMineInfoConvert mkMineInfoConvert) {
        this.mkMineInfoConvert = mkMineInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkMineInfoDTO> pagingQuery(MkMineInfoParam param) {
        IPage<MkMineInfo> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkMineInfo> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return wrapper.page(page).convert(mkMineInfoConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkMineInfoDTO> queryList(MkMineInfoParam param) {
        LambdaQueryWrapper<MkMineInfo> wrapper = new LambdaQueryWrapper<>();
        return mkMineInfoConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param  数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkMineInfoParam param) {
        MkMineInfo entity = mkMineInfoConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkMineInfoDTO getCurrentDetails(MkMineInfoParam param) {
        return mkMineInfoConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkMineInfoParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }
}
