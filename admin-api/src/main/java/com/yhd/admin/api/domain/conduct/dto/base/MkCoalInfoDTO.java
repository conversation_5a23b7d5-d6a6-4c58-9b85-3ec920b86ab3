package com.yhd.admin.api.domain.conduct.dto.base;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 煤层信息(MkCoalInfo)DTO
 *
 * <AUTHOR>
 * @since 2025-07-28 09:25:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkCoalInfoDTO extends BaseDTO implements Serializable {
    /** Id */
    private Long id;
    /** 所属煤矿 */
    private String orgName;
    /** 组织机构编码 */
    private String orgCode;
    /** 煤层编码 */
    private String code;
    /** 名称 */
    private String name;
    /** 厚度 */
    private BigDecimal thickness;
    /** 倾角 */
    private BigDecimal dipAngle;
    /** 埋深 */
    private BigDecimal burialDepth;
    /** 煤层结构 */
    private String coalStructure;
    /** 稳定性 */
    private String stability;
    /** 可采性 */
    private String admissibility;
    /** 所属煤田 */
    private String coalfield;
}

