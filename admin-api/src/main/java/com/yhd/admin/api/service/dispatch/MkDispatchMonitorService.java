package com.yhd.admin.api.service.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.dispatch.dto.MkDispatchMonitorDTO;
import com.yhd.admin.api.domain.dispatch.entity.MkDispatchMonitor;
import com.yhd.admin.api.domain.dispatch.query.MkDispatchMonitorParam;

/**
 * 智能调度中心-视频监控
 *
 * <AUTHOR>
 * @date 2025/7/31 09:34
 */
public interface MkDispatchMonitorService extends IService<MkDispatchMonitor> {

  /**
   * 新增
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  Boolean addOrModify(MkDispatchMonitorParam param);

  /**
   * 删除-根据主键
   *
   * @param param id
   * @return true成功，false失败
   */
  Boolean remove(MkDispatchMonitorParam param);

  /**
   * 查询详情信息
   *
   * @param param 主键id or 编号
   * @return 详情信息
   */
  MkDispatchMonitorDTO getCurrentDetail(MkDispatchMonitorParam param);

  /**
   * 根据条件查询通讯信息分页列表
   *
   * @param param 查询条件
   * @return 分页列表
   */
  IPage<MkDispatchMonitorDTO> pagingQuery(MkDispatchMonitorParam param);

  /**
   * 查询综合管控大屏
   *
   * @return 详情信息
   */
  MkDispatchMonitorDTO getData();
}
