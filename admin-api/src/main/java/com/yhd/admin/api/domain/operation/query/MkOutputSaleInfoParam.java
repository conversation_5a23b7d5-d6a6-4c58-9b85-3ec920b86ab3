package com.yhd.admin.api.domain.operation.query;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产录入-产量销售库存表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MkOutputSaleInfoParam extends QueryParam implements Cloneable, Serializable {
    /**
     * Id
     */
    private Long id;

	/**
	* 日期
	*/
	private Date saleDate;

	/**
	* 销售类型：1:年累计销量 2:库存量 3:当日销量，4:月累积销量，
	*/
	private Integer saleType;

	/**
	* 混煤
	*/
	private BigDecimal hm;

	/**
	* 块煤
	*/
	private BigDecimal km;

}
