package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.sys.dto.MesNoticeDTO;
import com.yhd.admin.api.domain.sys.entity.MesNotice;
import com.yhd.admin.api.domain.sys.query.DicItemParam;
import com.yhd.admin.api.domain.sys.query.MesNoticeParam;
import com.yhd.admin.api.domain.sys.vo.MesNoticeVO;
import com.yhd.admin.api.domain.sys.vo.TreeNode;
import com.yhd.admin.common.domain.query.BatchParam;

import java.util.List;

/**
 * 消息通知表
 */
public interface MesNoticeService extends IService<MesNotice> {

    /**
     * 分页查询
     *
     * @param queryParam {@link MesNoticeParam}
     * @return IPage<MesNoticeDTO>
     */
    IPage<MesNoticeDTO> pagingQuery(MesNoticeParam queryParam);

    /**
     * 根据ID进行新增或者修改
     *
     * @param queryParam {@link MesNoticeParam}
     * @return true 成功，false 失败
     */
    Boolean addOrModify(MesNoticeParam queryParam);

    /**
     * 批量删除
     *
     * @param param {@link BatchParam}
     * @return true 成功，false 失败
     */
    Boolean removeBatch(BatchParam param);

    /**
     * 根据ID查询详情
     *
     * @param queryParam {@link MesNoticeParam}
     * @return {@link MesNoticeDTO}
     */
    MesNoticeDTO getCurrentDetail(MesNoticeParam queryParam);

    /**
     * 获取通知类型的字典项
     *
     * @return
     */
    List<TreeNode> getNoticeDictList(DicItemParam queryParam);

    /**
     * 保存一条消息通知信息
     *
     * @param params 目前支持4个参数，第1：接收人账号 第2：业务code（字典值） 第3：业务id 第4：紧急程度（1-一级，2-二级，3-三级，4-四级）
     * @return
     */
    Boolean saveMesNotice(String... params);

    /**
     * 获取用户消息总数
     *
     * @param param
     * @return
     */
    Integer getCurrentNoticeCount(MesNoticeParam param);

    /**
     * 全部已读
     *
     * @param param
     * @return
     */
    Boolean readBatch(BatchParam param);

    Boolean readtype(String type, String name);

    Boolean read(MesNotice notify);

    List<MesNoticeVO> queryList(String name);
}
