package com.yhd.admin.api.domain.emergency.vo;

import lombok.Data;

/**
 * 列表项目信息VO
 *
 * <AUTHOR>
 */
@Data
public class MkEmergencyListItemInfoVO {
    /**
     * 项目编码
     */
    private String itemCode;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 项目类型
     */
    private String itemType;

    /**
     * 项目状态：0-异常，1-正常，2-警告
     */
    private Integer itemStatus;

    /**
     * 项目状态名称
     */
    private String itemStatusName;

    /**
     * 项目值
     */
    private String itemValue;

    /**
     * 项目单位
     */
    private String itemUnit;

    /**
     * 位置信息
     */
    private String locationInfo;

    /**
     * 操作链接
     */
    private String actionUrl;

    /**
     * 图标样式类
     */
    private String iconClass;

    /**
     * 颜色代码
     */
    private String colorCode;

    /**
     * 显示顺序
     */
    private Integer displayOrder;
}