package com.yhd.admin.api.domain.monitor.nitrogen.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 制氮空压机详情表
 *
 * <AUTHOR>
 * @since
 */
@Data
public class TbMkNitrogenCompressorDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 关联制氮空压机信息表ID
     */
    private Long nitrogenCompressorInfoId;

    /**
     * 空压机状态: 0-停止, 1-运行, 2-故障, 3-待机
     */
    private Byte status;

    /**
     * 排气压力(MPa)
     */
    private BigDecimal dischargePressure;

    /**
     * 螺杆温度(℃)
     */
    private BigDecimal screwTemperature;

    /**
     * 运行时间(h)
     */
    private Integer runtimeHours;

    /**
     * 电机电流(A)
     */
    private BigDecimal motorCurrent;

    /**
     * 电源电压(V)
     */
    private BigDecimal powerVoltage;

    /**
     * 风扇电流(A)
     */
    private BigDecimal fanCurrent;
}
