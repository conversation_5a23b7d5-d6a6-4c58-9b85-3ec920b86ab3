package com.yhd.admin.api.domain.sys.convert;

import org.mapstruct.Mapper;

import com.yhd.admin.api.domain.sys.dto.ClientDTO;
import com.yhd.admin.api.domain.sys.entity.SysClient;
import com.yhd.admin.api.domain.sys.query.ClientQueryParam;
import com.yhd.admin.api.domain.sys.vo.ClientVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/3 10:54
 */
@Mapper(componentModel = "spring")
public interface ClientConvert {

    SysClient toEntity(ClientQueryParam param);

    ClientDTO toDTO(SysClient sysClient);

    ClientVO toVO(ClientDTO clientDTO);
}
