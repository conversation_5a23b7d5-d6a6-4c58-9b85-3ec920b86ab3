package com.yhd.admin.api.domain.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 运维配置中心-部门管理-部门类型表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class MkUserDeptType extends BaseEntity implements Serializable {
	/**
	* Id
	*/
    @TableId(type = IdType.AUTO)
	private Long id;

	/**
	* 部门类型名称
	*/
	private String deptName;

	/**
	* 部门类型分类（0：部门类型:1：子类型）
	*/
	private String deptType;

}
