package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.sys.dto.SysOpsLogDTO;
import com.yhd.admin.api.domain.sys.entity.SysOpsLog;
import com.yhd.admin.api.domain.sys.query.SysOpsLogParam;

public interface SysOpsLogService extends IService<SysOpsLog> {

    /**
     * 分页查询
     *
     * @param queryParam 分页查询参数
     * @return {@link IPage< SysOpsLogDTO >}
     */
    IPage<SysOpsLogDTO> pagingQuery(SysOpsLogParam queryParam);

}
