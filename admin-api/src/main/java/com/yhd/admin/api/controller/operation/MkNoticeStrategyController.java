package com.yhd.admin.api.controller.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.operation.convert.MkNoticeStrategyConvert;
import com.yhd.admin.api.domain.operation.dto.MkNoticeStrategyDTO;
import com.yhd.admin.api.domain.operation.query.MkNoticeStrategyParam;
import com.yhd.admin.api.domain.operation.vo.MkNoticeStrategyVO;
import com.yhd.admin.api.service.operation.MkNoticeStrategyService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 消息策略表控制层
 *
 * <AUTHOR>
 * @since 2025-08-08 09:12:47
 */
@RestController
@RequestMapping("/operation/notice/strategy")
public class MkNoticeStrategyController {

    private final MkNoticeStrategyService mkNoticeStrategyService;
    private final MkNoticeStrategyConvert mkNoticeStrategyConvert;

    public MkNoticeStrategyController(
        MkNoticeStrategyService mkNoticeStrategyService,
        MkNoticeStrategyConvert mkNoticeStrategyConvert
    ) {
        this.mkNoticeStrategyService = mkNoticeStrategyService;
        this.mkNoticeStrategyConvert = mkNoticeStrategyConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkNoticeStrategyParam param) {
        IPage<MkNoticeStrategyDTO> page = mkNoticeStrategyService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkNoticeStrategyConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkNoticeStrategyParam param) {
        return RespJson.success(mkNoticeStrategyConvert.toVOList(mkNoticeStrategyService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkNoticeStrategyVO> getCurrentDetails(@RequestBody MkNoticeStrategyParam param) {
        return RespJson.success(mkNoticeStrategyConvert.toVO(mkNoticeStrategyService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> addOrModify(@RequestBody MkNoticeStrategyParam param) {
        Boolean retVal = mkNoticeStrategyService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkNoticeStrategyParam param) {
        Boolean retVal = mkNoticeStrategyService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

