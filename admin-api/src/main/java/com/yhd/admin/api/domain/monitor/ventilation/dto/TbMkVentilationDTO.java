package com.yhd.admin.api.domain.monitor.ventilation.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 通风机及其电机运行参数数据传输对象
 * 用于封装从数据库查询出的通风机和电机的综合运行信息，供前端展示。
 *
 * <AUTHOR>
 * @since
 */
@Data
public class TbMkVentilationDTO {

    /**
     * 通风机名ID
     */
    private Long ventilationId;

    /**
     * 通风机名称
     */
    private String ventilationName;


    /**
     * 蝶阀状态
     */
    private String valveStatus;

    /**
     * 风速(m/s)
     */
    private BigDecimal windSpeed;


    /**
     * 电机详情
     */
    private List<TbMkVentilationDetailDTO> motorDetails;
}
