package com.yhd.admin.api.domain.da.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 电度能耗分析表
 *
 * <AUTHOR>
 * @date 2025/8/1 14:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkReportDayElectricDTO extends BaseDTO implements Serializable {

  private Long id;

  /** 统计日期 */
  private LocalDate statsDate;
  /** 组织机构编码 */
  private String orgCode;
  /** 组织机构名称 */
  private String orgName;
  /** 峰用电 */
  private Double peakValue;
  /** 谷用电 */
  private Double valleyValue;
  /** 平用电 */
  private Double normalValue;
  /** 总用电 */
  private Double totalValue;

  /** 月份(yyyy-MM) */
  private String month;
  /** 年(yyyy) */
  private String year;
}
