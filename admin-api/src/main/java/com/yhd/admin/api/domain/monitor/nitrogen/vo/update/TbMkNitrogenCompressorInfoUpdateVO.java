package com.yhd.admin.api.domain.monitor.nitrogen.vo.update;

import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenCompressorInfoCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 制氮系统空压机基本信息表
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkNitrogenCompressorInfoUpdateVO extends TbMkNitrogenCompressorInfoCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
