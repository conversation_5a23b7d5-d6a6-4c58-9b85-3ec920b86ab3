package com.yhd.admin.api.domain.operation.vo;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 运维配置中心-单位管理-单位类型表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class MkUserOrgTypeVO extends BaseVO implements Serializable {
	/**
	* Id
	*/
	private Long id;

	/**
	* 类型名称
	*/
	private String name;

}
