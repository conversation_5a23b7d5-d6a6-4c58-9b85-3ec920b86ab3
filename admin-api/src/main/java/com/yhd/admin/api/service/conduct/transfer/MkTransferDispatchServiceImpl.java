package com.yhd.admin.api.service.conduct.transfer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.configuration.FastDFSClient;
import com.yhd.admin.api.dao.conduct.transfer.MkTransferDispatchDao;
import com.yhd.admin.api.domain.conduct.convert.transfer.MkTransferDispatchConvert;
import com.yhd.admin.api.domain.conduct.dto.transfer.MkTransferDispatchDTO;
import com.yhd.admin.api.domain.conduct.entity.transfer.MkTransferDispatch;
import com.yhd.admin.api.domain.conduct.query.transfer.MkTransferDispatchParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.FileService;
import com.yhd.admin.api.service.sys.UserService;
import com.yhd.admin.common.utils.excel.CellBuilder;
import com.yhd.admin.common.utils.excel.WorkbookBuilder;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 收发文登记表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
@Service
public class MkTransferDispatchServiceImpl extends ServiceImpl<MkTransferDispatchDao, MkTransferDispatch> implements MkTransferDispatchService {

    private final MkTransferDispatchConvert mkTransferDispatchConvert;
    private final FileService fileService;
    private final FastDFSClient fastDFSClient;
    private final UserService userService;

    public MkTransferDispatchServiceImpl(MkTransferDispatchConvert mkTransferDispatchConvert, FileService fileService, FastDFSClient fastDFSClient, UserService userService) {
        this.mkTransferDispatchConvert = mkTransferDispatchConvert;
        this.fileService = fileService;
        this.fastDFSClient = fastDFSClient;
        this.userService = userService;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkTransferDispatchDTO> pagingQuery(MkTransferDispatchParam param) {
        if (StringUtils.isBlank(param.getDispatchType())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        IPage<MkTransferDispatch> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkTransferDispatch> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.eq(MkTransferDispatch::getDispatchType, param.getDispatchType());
        wrapper.ge(Objects.nonNull(param.getStartDate()), MkTransferDispatch::getDispatchDate, param.getStartDate());
        wrapper.le(Objects.nonNull(param.getEndDate()), MkTransferDispatch::getDispatchDate, param.getEndDate());
        wrapper.eq(StringUtils.isNotBlank(param.getDispatchName()), MkTransferDispatch::getDispatchName, param.getDispatchName());
        wrapper.eq(Objects.nonNull(param.getTransferOrgId()), MkTransferDispatch::getTransferOrgId,
            param.getTransferOrgId());
        IPage<MkTransferDispatchDTO> resultPage = wrapper.page(page).convert(mkTransferDispatchConvert::toDTO);
        List<MkTransferDispatchDTO> list = resultPage.getRecords();
        if (CollectionUtils.isEmpty(list)) {
            return resultPage;
        }
        for (MkTransferDispatchDTO dto : list) {
            List<String> textFile = fileService.getFileList(dto.getId(), "收发文", "文件正文");
            List<String> annexFile = fileService.getFileList(dto.getId(), "收发文", "文件附件");
            List<String> approveFile = fileService.getFileList(dto.getId(), "收发文", "审批附件");
            dto.setTextFile(textFile);
            dto.setAnnexFile(annexFile);
            dto.setApproveFile(approveFile);
            dto.setCreator(userService.getUserByUsername(dto.getCreatedBy()).getName());
        }
        return resultPage;
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkTransferDispatchDTO> queryList(MkTransferDispatchParam param) {
        LambdaQueryWrapper<MkTransferDispatch> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(param.getDispatchType()), MkTransferDispatch::getDispatchType,
            param.getDispatchType());
        wrapper.like(StringUtils.isNotBlank(param.getDispatchName()), MkTransferDispatch::getDispatchName,
            param.getDispatchName());
        List<MkTransferDispatchDTO> result = mkTransferDispatchConvert.toDTOList(baseMapper.selectList(wrapper));

        for (MkTransferDispatchDTO dto : result) {
            List<String> textFile = fileService.getFileList(dto.getId(), "收发文", "文件正文");
            List<String> annexFile = fileService.getFileList(dto.getId(), "收发文", "文件附件");
            List<String> approveFile = fileService.getFileList(dto.getId(), "收发文", "审批附件");
            dto.setTextFile(textFile);
            dto.setAnnexFile(annexFile);
            dto.setApproveFile(approveFile);
            dto.setCreator(userService.getUserByUsername(dto.getCreatedBy()).getName());
        }
        return result;
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkTransferDispatchParam param) {
        MkTransferDispatch entity = mkTransferDispatchConvert.toEntity(param);
        boolean result;
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            result = save(entity);
        } else {
            // 更新操作
            result = updateById(entity);
        }
        if (result) {
            MkTransferDispatch  dispatch = getById(entity.getId());
            String creator = userService.getUserByUsername(dispatch.getCreatedBy()).getName();
            entity.setCreator(creator);
            updateById(entity);
            try {
                fileService.removeFile(entity.getId(), "收发文", "文件正文");
                fileService.removeFile(entity.getId(), "收发文", "文件附件");
                fileService.removeFile(entity.getId(), "收发文", "审批附件");
                fileService.insertFile(entity.getId(), "收发文", "文件正文", param.getTextFile());
                fileService.insertFile(entity.getId(), "收发文", "文件附件", param.getAnnexFile());
                fileService.insertFile(entity.getId(), "收发文", "审批附件", param.getApproveFile());
            } catch (Exception e) {
                throw new BMSException(ExceptionEnum.FILE_UPLOAD_ERROR);
            }
            return true;
        } else {
            return false;
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkTransferDispatchDTO getCurrentDetails(MkTransferDispatchParam param) {
        MkTransferDispatchDTO dto = mkTransferDispatchConvert.toDTO(super.getById(param.getId()));
        if (Objects.nonNull(dto)) {
            return getAttachment(dto);
        } else {
            return null;
        }
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkTransferDispatchParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (removeByIds(param.getIds())) {
            for (Long id : param.getIds()) {
                fileService.removeFile(id, "收发文", "文件正文");
                fileService.removeFile(id, "收发文", "文件附件");
                fileService.removeFile(id, "收发文", "审批附件");
            }
            return true;
        } else {
            return false;
        }
    }

    /**
     * 导出文件
     *
     * @return 文件地址
     */
    @Override
    public String export(MkTransferDispatchParam param) {
        if (StringUtils.isBlank(param.getDispatchType())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        List<MkTransferDispatchDTO> dtoList = queryList(param);

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String fileName;
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 读取模板文件
            File tplFile;
            if ("收文".equals(param.getDispatchType())) {
                fileName = "收文列表导出-" + timestamp + ".xlsx";
                tplFile = ResourceUtils.getFile("classpath:template" + File.separator + "收文列表导出模板.xlsx");
            } else {
                fileName = "发文列表导出-" + timestamp + ".xlsx";
                tplFile = ResourceUtils.getFile("classpath:template" + File.separator + "发文列表导出模板.xlsx");
            }
            Workbook workbook = WorkbookBuilder.getWorkBook(tplFile);
            Sheet sheet = workbook.getSheetAt(0);
            // 跳过标题行
            int rowNum = 1;
            CellStyle contentStyle = createContentStyle(workbook);

            for (MkTransferDispatchDTO dto : dtoList) {
                Row row = sheet.createRow(rowNum++);
                CellBuilder.build(row, 0, dto.getDispatchDate(), contentStyle);
                CellBuilder.build(row, 1, dto.getTransferOrgName(), contentStyle);
                CellBuilder.build(row, 2, dto.getDispatchName(), contentStyle);
                CellBuilder.build(row, 3, dto.getCreator(), contentStyle);
                CellBuilder.build(row, 10, StringUtils.isNotBlank(dto.getRemark()) ? dto.getRemark() : "", contentStyle);

                processAndBuildCell(dto.getTextFile(), row, 4, 5, contentStyle);
                processAndBuildCell(dto.getAnnexFile(), row, 6, 7, contentStyle);
                processAndBuildCell(dto.getApproveFile(), row, 8, 9, contentStyle);
            }
            workbook.write(outputStream);

            // 上传到文件服务器
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            // 执行上传
            return fastDFSClient.getFullPath(fastDFSClient.uploadFile(inputStream, fileName));

        } catch (Exception e) {
            throw new BMSException(ExceptionEnum.EXPORT_ERROR);
        }
    }

    private MkTransferDispatchDTO getAttachment(MkTransferDispatchDTO dto) {
        List<String> textFile = fileService.getFileList(dto.getId(), "收发文", "文件正文");
        List<String> annexFile = fileService.getFileList(dto.getId(), "收发文", "文件附件");
        List<String> approveFile = fileService.getFileList(dto.getId(), "收发文", "审批附件");
        dto.setTextFile(textFile);
        dto.setAnnexFile(annexFile);
        dto.setApproveFile(approveFile);
        return dto;
    }

    private CellStyle createContentStyle(Workbook workbook) {
        CellStyle contentStyle = workbook.createCellStyle();
        DataFormat format = workbook.createDataFormat();
        contentStyle.setDataFormat(format.getFormat("yyyy-mm-dd"));
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setAlignment(HorizontalAlignment.CENTER);
        contentStyle.setWrapText(true);
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);
        contentStyle.setBorderTop(BorderStyle.THIN);
        return contentStyle;
    }

    private void processAndBuildCell(List<String> fileList, Row row, int nameColIndex, int urlColIndex, CellStyle style) {
        if (CollectionUtils.isEmpty(fileList)) {
            CellBuilder.build(row, nameColIndex, "", style);
            CellBuilder.build(row, urlColIndex, "", style);
            return;
        }
        StringBuilder names = new StringBuilder();
        StringBuilder urls = new StringBuilder();

        for (String item : fileList) {
            String[] parts = item.split("#@@#");
            if (parts.length == 2) {
                if (!names.isEmpty()) {
                    names.append(",");
                    urls.append(",");
                }
                names.append(parts[1]);
                urls.append(parts[0]);
            }
        }
        CellBuilder.build(row, nameColIndex, names.toString(), style);
        CellBuilder.build(row, urlColIndex, urls.toString(), style);
    }
}
