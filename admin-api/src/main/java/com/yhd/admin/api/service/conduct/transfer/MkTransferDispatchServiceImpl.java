package com.yhd.admin.api.service.conduct.transfer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.transfer.MkTransferDispatchDao;
import com.yhd.admin.api.domain.conduct.convert.transfer.MkTransferDispatchConvert;
import com.yhd.admin.api.domain.conduct.dto.transfer.MkTransferDispatchDTO;
import com.yhd.admin.api.domain.conduct.entity.transfer.MkTransferDispatch;
import com.yhd.admin.api.domain.conduct.query.transfer.MkTransferDispatchParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.FileService;
import com.yhd.admin.common.utils.UserContextHolder;
import io.micrometer.common.util.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 收发文登记表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
@Service
public class MkTransferDispatchServiceImpl extends ServiceImpl<MkTransferDispatchDao, MkTransferDispatch> implements MkTransferDispatchService {

    public final MkTransferDispatchConvert mkTransferDispatchConvert;
    private final FileService fileService;

    public MkTransferDispatchServiceImpl(MkTransferDispatchConvert mkTransferDispatchConvert, FileService fileService) {
        this.mkTransferDispatchConvert = mkTransferDispatchConvert;
        this.fileService = fileService;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkTransferDispatchDTO> pagingQuery(MkTransferDispatchParam param) {
        if (StringUtils.isBlank(param.getDispatchType())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        IPage<MkTransferDispatch> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkTransferDispatch> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.eq(MkTransferDispatch::getDispatchType, param.getDispatchType());
        wrapper.ge(Objects.nonNull(param.getStartDate()), MkTransferDispatch::getDispatchDate, param.getStartDate());
        wrapper.le(Objects.nonNull(param.getEndDate()), MkTransferDispatch::getDispatchDate, param.getEndDate());
        wrapper.eq(StringUtils.isNotBlank(param.getDispatchName()), MkTransferDispatch::getDispatchName, param.getDispatchName());
        wrapper.eq(StringUtils.isNotBlank(param.getTransferOrgName()), MkTransferDispatch::getTransferOrgName,
            param.getTransferOrgName());
        IPage<MkTransferDispatchDTO> resultPage = wrapper.page(page).convert(mkTransferDispatchConvert::toDTO);
        List<MkTransferDispatchDTO> list = resultPage.getRecords();
        if (CollectionUtils.isEmpty(list)) {
            return resultPage;
        }
        for (MkTransferDispatchDTO dto : list) {
            List<String> recordFile = fileService.getFileList(dto.getId(), "收发文", "文件正文");
            List<String> signUpSheet = fileService.getFileList(dto.getId(), "收发文", "文件附件");
            List<String> pictureFile = fileService.getFileList(dto.getId(), "收发文", "审批附件");
            dto.setTextFile(recordFile);
            dto.setAnnexFile(signUpSheet);
            dto.setApproveFile(pictureFile);
        }
        return resultPage;
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkTransferDispatchDTO> queryList(MkTransferDispatchParam param) {
        LambdaQueryWrapper<MkTransferDispatch> wrapper = new LambdaQueryWrapper<>();
        return mkTransferDispatchConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkTransferDispatchParam param) {
        MkTransferDispatch entity = mkTransferDispatchConvert.toEntity(param);
        if (StringUtils.isBlank(entity.getCreator())){
            // 当前登录人
            String creator = UserContextHolder.getUserDetail().getUserInfo().getName();
            entity.setCreator(creator);
        }
        boolean result;
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            result = save(entity);
        } else {
            // 更新操作
            result = updateById(entity);
        }
        if (result) {
            try {
                fileService.insertFile(entity.getId(), "收发文", "文件正文", param.getTextFile());
                fileService.insertFile(entity.getId(), "收发文", "文件附件", param.getAnnexFile());
                fileService.insertFile(entity.getId(), "收发文", "审批附件", param.getApproveFile());
            } catch (Exception e) {
                throw new BMSException(ExceptionEnum.FILE_UPLOAD_ERROR);
            }
            return true;
        } else {
            return false;
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkTransferDispatchDTO getCurrentDetails(MkTransferDispatchParam param) {
        MkTransferDispatchDTO dto = mkTransferDispatchConvert.toDTO(super.getById(param.getId()));
        if (Objects.nonNull(dto)) {
            return getAttachment(dto);
        } else {
            return null;
        }
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkTransferDispatchParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (removeByIds(param.getIds())) {
            for (Long id : param.getIds()) {
                fileService.removeFile(id, "收发文", "文件正文");
                fileService.removeFile(id, "收发文", "文件附件");
                fileService.removeFile(id, "收发文", "审批附件");
            }
            return true;
        } else {
            return false;
        }
    }

    private MkTransferDispatchDTO getAttachment(MkTransferDispatchDTO dto) {
        List<String> recordFile = fileService.getFileList(dto.getId(), "收发文", "文件正文");
        List<String> signUpSheet = fileService.getFileList(dto.getId(), "收发文", "文件附件");
        List<String> pictureFile = fileService.getFileList(dto.getId(), "收发文", "审批附件");
        dto.setTextFile(recordFile);
        dto.setAnnexFile(signUpSheet);
        dto.setApproveFile(pictureFile);
        return dto;
    }
}
