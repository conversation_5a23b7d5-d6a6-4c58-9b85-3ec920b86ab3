package com.yhd.admin.api.domain.dispatch.convert;

import com.yhd.admin.api.domain.dispatch.dto.MkDispatchMonitorDTO;
import com.yhd.admin.api.domain.dispatch.entity.MkDispatchMonitor;
import com.yhd.admin.api.domain.dispatch.query.MkDispatchMonitorParam;
import org.mapstruct.Mapper;

/**
 * 智能调度中心-视频监控
 *
 * <AUTHOR>
 * @date 2025/7/31 09:34
 */
@Mapper(componentModel = "spring")
public interface MkDispatchMonitorConvert {
  MkDispatchMonitorDTO toDTO(MkDispatchMonitor entity);

  MkDispatchMonitor toEntity(MkDispatchMonitorParam param);
}
