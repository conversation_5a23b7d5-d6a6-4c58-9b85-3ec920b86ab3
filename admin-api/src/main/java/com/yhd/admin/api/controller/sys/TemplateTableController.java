package com.yhd.admin.api.controller.sys;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.sys.convert.TemplateTableConvert;
import com.yhd.admin.api.domain.sys.dto.TemplateTableDTO;
import com.yhd.admin.api.domain.sys.query.TemplateTableParam;
import com.yhd.admin.api.domain.sys.vo.TemplateTableVO;
import com.yhd.admin.api.service.sys.TemplateTableService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;

import lombok.extern.slf4j.Slf4j;

/**
 * 表格模版控制类
 *
 * <AUTHOR>
 * @version 1.0
 * @description: 表格模版控制
 * @date 2021/3/17 16:01
 */
@Slf4j
@RestController
@RequestMapping("/table")
public class TemplateTableController {

    private final TemplateTableConvert convert;

    private final TemplateTableService tableService;

    public TemplateTableController(TemplateTableConvert convert, TemplateTableService tableService) {

        this.convert = convert;
        this.tableService = tableService;
    }

    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<TemplateTableVO> pagingQuery(@RequestBody TemplateTableParam queryParam) {
        if (log.isDebugEnabled()) {
            log.debug("{}", queryParam);
        }
        IPage<TemplateTableDTO> iPage = tableService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::transformDTOToVO));
    }

    @PostMapping(value = "/addOrModify", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<String> add(@RequestBody TemplateTableParam addParam) {
        if (log.isDebugEnabled()) {
            log.debug("{}", addParam);
        }
        Boolean retVal = tableService.addOrModify(addParam);
        return retVal ? RespJson.success() : RespJson.failure("错误");
    }

    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<String> removeBatch(@RequestBody BatchParam batchParam) {
        if (log.isDebugEnabled()) {
            log.debug("{}", batchParam);
        }
        Boolean retVal = tableService.removeBatch(batchParam);
        return retVal ? RespJson.success() : RespJson.failure("错误");
    }

    @PostMapping(value = "/getDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<TemplateTableVO> getDetail(@RequestBody TemplateTableParam param) {
        if (log.isDebugEnabled()) {
            log.debug("{}", param);
        }
        return RespJson.success(convert.transformDTOToVO(tableService.getDetail(param.getId())));
    }
}
