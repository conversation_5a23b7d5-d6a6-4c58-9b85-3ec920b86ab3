package com.yhd.admin.api.domain.dispatch.convert;

import com.yhd.admin.api.domain.dispatch.dto.MkDispatchBroadcastDTO;
import com.yhd.admin.api.domain.dispatch.entity.MkDispatchBroadcast;
import com.yhd.admin.api.domain.dispatch.query.MkDispatchBroadcastParam;
import com.yhd.admin.api.domain.home.dto.MkHomeDTO;
import com.yhd.admin.api.domain.home.entity.MkHome;
import com.yhd.admin.api.domain.home.query.MkHomeParam;
import org.mapstruct.Mapper;

/**
 * 智能调度中心-应急广播
 *
 * <AUTHOR>
 * @date 2025/7/31 09:34
 */
@Mapper(componentModel = "spring")
public interface MkDispatchBroadcastConvert {
  MkDispatchBroadcastDTO toDTO(MkDispatchBroadcast entity);

  MkDispatchBroadcast toEntity(MkDispatchBroadcastParam param);
}
