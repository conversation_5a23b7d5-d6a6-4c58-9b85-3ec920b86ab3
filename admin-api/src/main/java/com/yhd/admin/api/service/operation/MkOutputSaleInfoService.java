package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkOutputSaleInfoDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputSaleInfo;
import com.yhd.admin.api.domain.operation.query.MkOutputBatchParam;
import com.yhd.admin.api.domain.operation.query.MkOutputSaleInfoParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 生产录入-产量销售库存表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
public interface MkOutputSaleInfoService extends IService<MkOutputSaleInfo> {

    IPage<MkOutputSaleInfoDTO> pagingQuery(MkOutputSaleInfoParam queryParam);

    Boolean removeBatch(BatchParam param);

    MkOutputSaleInfoDTO getCurrentDetail(MkOutputSaleInfoParam param);
    /**
     * 批量新增和修改
     * @param batchParam 批量操作参数
     * @return 操作结果
     */
    Boolean batchAddAndModify(MkOutputBatchParam batchParam);
}
