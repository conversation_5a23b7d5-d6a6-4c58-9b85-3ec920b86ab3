package com.yhd.admin.api.domain.emergency.vo;

import lombok.Data;
import java.util.List;

/**
 * 三违统计VO
 *
 * <AUTHOR>
 */
@Data
public class MkSafetyViolationStatsVO {

    /**
     * 当日三违数量
     */
    private Integer dailyViolationCount;

    /**
     * 趋势数据(近7日) - 支持键值对格式，如: {"05-15": 0, "05-17": 2}
     */
    private Object trendData;

    /**
     * 三违类型分解
     */
    private Object violationTypeBreakdown;
}