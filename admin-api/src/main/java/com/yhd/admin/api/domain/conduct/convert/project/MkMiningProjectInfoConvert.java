package com.yhd.admin.api.domain.conduct.convert.project;

import com.yhd.admin.api.domain.conduct.dto.project.MkMiningProjectInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.project.MkMiningProjectInfo;
import com.yhd.admin.api.domain.conduct.query.project.MkMiningProjectInfoParam;
import com.yhd.admin.api.domain.conduct.vo.project.MkMiningProjectInfoVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 回采项目信息(MkMiningProjectInfo)Convert
 *
 * <AUTHOR>
 * @since 2025-07-30 14:05:52
 */
@Mapper(componentModel = "spring")
public interface MkMiningProjectInfoConvert {
    MkMiningProjectInfo toEntity(MkMiningProjectInfoParam param);

    MkMiningProjectInfoDTO toDTO(MkMiningProjectInfo entity);

    MkMiningProjectInfoVO toVO(MkMiningProjectInfoDTO dto);

    List<MkMiningProjectInfoDTO> toDTOList(List<MkMiningProjectInfo> entityList);

    List<MkMiningProjectInfoVO> toVOList(List<MkMiningProjectInfoDTO> dtoList);
}

