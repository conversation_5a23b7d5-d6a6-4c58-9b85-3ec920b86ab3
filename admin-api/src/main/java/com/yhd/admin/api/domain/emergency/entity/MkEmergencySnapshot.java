package com.yhd.admin.api.domain.emergency.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应急快照实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_mk_emergency_snapshot")
public class MkEmergencySnapshot extends BaseEntity implements Serializable, Cloneable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 快照名称
     */
    private String snapshotName;

    /**
     * 快照类型
     */
    private String snapshotType;

    /**
     * 矿井ID
     */
    private Long mineId;

    /**
     * 矿井名称
     */
    private String mineName;

    /**
     * 快照时间
     */
    private LocalDateTime snapshotTime;

    /**
     * 设置时间
     */
    private LocalDateTime setTime;

    /**
     * 回溯时间
     */
    private LocalDateTime backtrackTime;

    /**
     * 快照状态(ACTIVE:有效,INACTIVE:无效)
     */
    private String snapshotStatus;

    /**
     * 快照描述
     */
    private String description;

    /**
     * 快照文件路径
     */
    private String filePath;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;
}
