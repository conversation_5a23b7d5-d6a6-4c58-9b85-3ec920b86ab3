package com.yhd.admin.api.domain.sys.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = false)
@Data
public class SysOrg extends BaseEntity implements Serializable, Cloneable {

    @TableId(type = IdType.AUTO)
    private Long id;
    /** 组织名称 */
    private String orgName;
    /** 父级ID */
    private Long parentId;
    /** 父级名称 */
    private String parentName;
    /** 状态 */
    private Boolean status;
    /** 排序 */
    private Long sortNum;
}
