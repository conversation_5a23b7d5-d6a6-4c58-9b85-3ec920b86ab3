package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 应急救援生产安全总结表
 * 
 * <AUTHOR>
 */
@Data
@TableName("tb_mk_emergency_safety_summary")
public class MkEmergencySafetySummary implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 总结编码
     */
    private String summaryCode;

    /**
     * 总结日期
     */
    private LocalDate summaryDate;

    /**
     * 总结标题
     */
    private String summaryTitle;

    /**
     * 总结内容
     */
    private String summaryContent;

    /**
     * 是否有内容：0-无内容，1-有内容
     */
    private Boolean hasContent;

    /**
     * 警告级别：0-正常，1-提醒，2-警告，3-严重
     */
    private Integer warningLevel;

    /**
     * 警告信息
     */
    private String warningMessage;

    /**
     * 负责人
     */
    private String responsiblePerson;

    /**
     * 负责部门
     */
    private String department;

    /**
     * 组织机构编码
     */
    private String orgCode;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}