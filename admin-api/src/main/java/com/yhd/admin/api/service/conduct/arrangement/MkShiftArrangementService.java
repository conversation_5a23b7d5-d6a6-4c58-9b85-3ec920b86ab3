package com.yhd.admin.api.service.conduct.arrangement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.arrangement.MkShiftArrangementDTO;
import com.yhd.admin.api.domain.conduct.entity.arrangement.MkShiftArrangement;
import com.yhd.admin.api.domain.conduct.query.arrangement.MkShiftArrangementParam;

import java.util.List;

/**
 * 值班带班安排表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-29 11:30:39
 */
public interface MkShiftArrangementService extends IService<MkShiftArrangement> {

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkShiftArrangementDTO> queryList(MkShiftArrangementParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkShiftArrangementParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkShiftArrangementParam param);
}
