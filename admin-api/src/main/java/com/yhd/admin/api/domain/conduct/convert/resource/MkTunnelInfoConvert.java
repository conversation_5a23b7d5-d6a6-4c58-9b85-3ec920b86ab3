package com.yhd.admin.api.domain.conduct.convert.resource;

import com.yhd.admin.api.domain.conduct.dto.resource.MkTunnelInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.resource.MkTunnelInfo;
import com.yhd.admin.api.domain.conduct.query.resource.MkTunnelInfoParam;
import com.yhd.admin.api.domain.conduct.vo.resource.MkTunnelInfoVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 巷道信息(MkTunnelInfo)Convert
 *
 * <AUTHOR>
 * @since 2025-07-28 08:21:47
 */
@Mapper(componentModel = "spring")
public interface MkTunnelInfoConvert {
    MkTunnelInfo toEntity(MkTunnelInfoParam param);

    MkTunnelInfoDTO toDTO(MkTunnelInfo entity);

    MkTunnelInfoVO toVO(MkTunnelInfoDTO dto);

    List<MkTunnelInfoDTO> toDTOList(List<MkTunnelInfo> entityList);

    List<MkTunnelInfoVO> toVOList(List<MkTunnelInfoDTO> dtoList);
}

