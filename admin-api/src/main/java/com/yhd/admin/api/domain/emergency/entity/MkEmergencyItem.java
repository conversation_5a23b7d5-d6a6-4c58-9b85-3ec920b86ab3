package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 应急救援项目管理表
 * 
 * <AUTHOR>
 */
@Data
@TableName("tb_mk_emergency_item")
public class MkEmergencyItem implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 项目编码
     */
    private String itemCode;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 项目类型：NAV-导航模块，PERSON-人员，VEHICLE-车辆，SAFETY-安全监测，ENVIRONMENT-环境监测，WAREHOUSE-应急仓库
     */
    private String itemType;

    /**
     * 父级项目编码，列表项目关联到导航项目
     */
    private String parentCode;

    /**
     * 显示顺序
     */
    private Integer displayOrder;

    /**
     * 项目状态：0-异常，1-正常，2-警告
     */
    private Integer itemStatus;

    /**
     * 项目值
     */
    private String itemValue;

    /**
     * 项目单位
     */
    private String itemUnit;

    /**
     * 最小阈值
     */
    private BigDecimal thresholdMin;

    /**
     * 最大阈值
     */
    private BigDecimal thresholdMax;

    /**
     * 导航图标（导航类型使用）
     */
    private String navIcon;

    /**
     * 导航链接（导航类型使用）
     */
    private String navUrl;

    /**
     * 是否默认选中（导航类型使用）：0-否，1-是
     */
    private Boolean isDefault;

    /**
     * 是否启用：0-禁用，1-启用
     */
    private Boolean isEnabled;

    /**
     * 是否可见：0-不可见，1-可见
     */
    private Boolean isVisible;

    /**
     * 操作链接
     */
    private String actionUrl;

    /**
     * 图标样式类
     */
    private String iconClass;

    /**
     * 颜色代码
     */
    private String colorCode;

    /**
     * 位置信息
     */
    private String locationInfo;

    /**
     * X坐标
     */
    private BigDecimal coordinateX;

    /**
     * Y坐标
     */
    private BigDecimal coordinateY;

    /**
     * Z坐标
     */
    private BigDecimal coordinateZ;

    /**
     * 权限编码
     */
    private String permissionCode;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 组织机构编码
     */
    private String orgCode;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}