package com.yhd.admin.api.service.conduct.resource;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.resource.MkTunnelInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.resource.MkTunnelInfo;
import com.yhd.admin.api.domain.conduct.query.resource.MkTunnelInfoParam;

import java.util.List;

/**
 * 巷道信息服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28 08:21:47
 */
public interface MkTunnelInfoService extends IService<MkTunnelInfo> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkTunnelInfoDTO> pagingQuery(MkTunnelInfoParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkTunnelInfoDTO> queryList(MkTunnelInfoParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkTunnelInfoParam param);

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    MkTunnelInfoDTO getCurrentDetails(MkTunnelInfoParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkTunnelInfoParam param);

}
