package com.yhd.admin.api.domain.sys.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysApp extends BaseEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * app 图标
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String appIcon;
    /**
     * app名称
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String appName;
    /**
     * app访问地址
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String appUrl;
    /**
     * 角色状态
     */
    private Boolean isEnable;

    /**
     * 排序
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer sorted;
}
