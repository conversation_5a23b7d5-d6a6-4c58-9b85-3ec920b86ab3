package com.yhd.admin.api.domain.monitor.ventilation.vo;

import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkMotorOperationCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 电机运行参数
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkMotorOperationVO extends TbMkMotorOperationCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
