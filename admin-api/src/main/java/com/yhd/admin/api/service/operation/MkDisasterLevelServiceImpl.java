package com.yhd.admin.api.service.operation;

import com.yhd.admin.api.dao.operation.MkDisasterLevelDao;
import com.yhd.admin.api.domain.operation.convert.MkDisasterLevelConvert;
import com.yhd.admin.api.domain.operation.dto.MkDisasterLevelDTO;
import com.yhd.admin.api.domain.operation.entity.MkDisasterGrade;
import com.yhd.admin.api.domain.operation.entity.MkDisasterLevel;
import com.yhd.admin.api.domain.operation.query.MkDisasterLevelParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.DicService;
import com.yhd.admin.common.constant.DicConstant;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;


/**
 * 算法模型-报警级别表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@Service
public class MkDisasterLevelServiceImpl extends ServiceImpl<MkDisasterLevelDao, MkDisasterLevel> implements MkDisasterLevelService {

    @Resource
    private MkDisasterLevelConvert convert;

    @Resource
    private MkDisasterLevelService service;

    @Resource
    private DicService dicService;

    @Override
    public IPage<MkDisasterLevelDTO> pagingQuery(MkDisasterLevelParam queryParam) {
        Page<MkDisasterLevel> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkDisasterLevel> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        //监测名称
        if (StringUtils.isBlank(queryParam.getMonitorName())) {
            throw new BMSException("error","监测名称不能为空");
        }
        queryChain.eq(MkDisasterLevel::getMonitorName, queryParam.getMonitorName());
        queryChain.orderByDesc(MkDisasterLevel::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkDisasterLevelParam param) {
        if (StringUtils.isNotBlank(param.getStartCode())){
            param.setStartName(dicService.transform(DicConstant.DISASTER_LEVEL_TYPE, param.getStartCode()));
        }
        if (StringUtils.isNotBlank(param.getEndCode())){
            param.setEndName(dicService.transform(DicConstant.DISASTER_LEVEL_TYPE, param.getEndCode()));
        }
        MkDisasterLevel entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MkDisasterLevelParam param) {
        if (StringUtils.isNotBlank(param.getStartCode())){
            param.setStartName(dicService.transform(DicConstant.DISASTER_LEVEL_TYPE, param.getStartCode()));
        }
        if (StringUtils.isNotBlank(param.getEndCode())){
            param.setEndName(dicService.transform(DicConstant.DISASTER_LEVEL_TYPE, param.getEndCode()));
        }
        MkDisasterLevel entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MkDisasterLevelDTO getCurrentDetail(MkDisasterLevelParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

}
