package com.yhd.admin.api.service.conduct.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.base.MkCoalInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkCoalInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkCoalInfoParam;

import java.util.List;

/**
 * 煤层信息服务接口
 *
 * <AUTHOR>
 * @since 2025-07-25 10:32:52
 */
public interface MkCoalInfoService extends IService<MkCoalInfo> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkCoalInfoDTO> pagingQuery(MkCoalInfoParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkCoalInfoDTO> queryList(MkCoalInfoParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkCoalInfoParam param);

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    MkCoalInfoDTO getCurrentDetails(MkCoalInfoParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkCoalInfoParam param);

}
