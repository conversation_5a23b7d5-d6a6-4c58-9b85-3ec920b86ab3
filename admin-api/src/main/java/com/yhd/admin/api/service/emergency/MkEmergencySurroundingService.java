package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencySurrounding;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySurroundingParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencySurroundingService.java
 * @Description 应急救援周边信息Service接口
 * @createTime 2025年01月20日 14:00:00
 */
public interface MkEmergencySurroundingService {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<MkEmergencySurrounding> pagingQuery(MkEmergencySurroundingParam param);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return 周边信息
     */
    MkEmergencySurrounding getById(Integer id);

    /**
     * 新增
     *
     * @param param 参数
     * @return 是否成功
     */
    boolean add(MkEmergencySurroundingParam param);

    /**
     * 修改
     *
     * @param param 参数
     * @return 是否成功
     */
    boolean modify(MkEmergencySurroundingParam param);

    /**
     * 删除
     *
     * @param id ID
     * @return 是否成功
     */
    boolean remove(Integer id);

    /**
     * 批量删除
     *
     * @param ids ID列表
     * @return 是否成功
     */
    boolean removeBatch(List<Integer> ids);

    /**
     * 根据项目类型查询
     *
     * @param itemType 项目类型
     * @param orgCode  组织编码
     * @return 周边信息列表
     */
    List<MkEmergencySurrounding> getByItemType(String itemType, String orgCode);

    /**
     * 根据位置查询
     *
     * @param location 位置信息
     * @param orgCode  组织编码
     * @return 周边信息列表
     */
    List<MkEmergencySurrounding> getByLocation(String location, String orgCode);

    /**
     * 根据状态查询
     *
     * @param status  状态
     * @param orgCode 组织编码
     * @return 周边信息列表
     */
    List<MkEmergencySurrounding> getByStatus(Integer status, String orgCode);

    /**
     * 根据参数查询周边信息列表
     * 
     * @param param 查询参数，如果为null或参数为空则查询全部
     * @return 周边信息列表
     */
    List<MkEmergencySurrounding> queryList(MkEmergencySurroundingParam param);
}