package com.yhd.admin.api.controller.monitor.digging;

import com.yhd.admin.api.domain.monitor.digging.convert.TbMkDiggingInfoConvert;
import com.yhd.admin.api.domain.monitor.digging.dto.TbMkDiggingInfoDTO;
import com.yhd.admin.api.domain.monitor.digging.entity.TbMkDiggingInfo;
import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingInfoCreateVO;
import com.yhd.admin.api.domain.monitor.digging.vo.page.TbMkDiggingInfoPageVO;
import com.yhd.admin.api.domain.monitor.digging.vo.update.TbMkDiggingInfoUpdateVO;
import com.yhd.admin.api.service.monitor.digging.TbMkDiggingInfoService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 掘进机基本信息 Controller
 * <AUTHOR>
 * @since
 */
@Slf4j
@RestController
@RequestMapping("/monitor/digging/tbMkDiggingInfo")
public class TbMkDiggingInfoController {

    @Resource
    private TbMkDiggingInfoService tbMkDiggingInfoService;

    @Resource
    private TbMkDiggingInfoConvert tbMkDiggingInfoConvert;

    /**
     * 分页查询
     *
     * @param pageVO 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<TbMkDiggingInfoDTO> page(@RequestBody @Valid TbMkDiggingInfoPageVO pageVO) {
        log.debug("分页查询 掘进机基本信息，参数：{}", pageVO);
        return new PageRespJson<>(tbMkDiggingInfoService.page(pageVO));
    }

    /**
     * 新增
     *
     * @param createVO 创建参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> create(@RequestBody @Valid TbMkDiggingInfoCreateVO createVO) {
        log.debug("新增 掘进机基本信息，参数：{}", createVO);
        return RespJson.success(tbMkDiggingInfoService.create(createVO));
    }

    /**
     * 更新
     *
     * @param updateVO 更新参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> update(@RequestBody @Valid TbMkDiggingInfoUpdateVO updateVO) {
        log.debug("更新 掘进机基本信息，参数：{}", updateVO);
        return RespJson.success(tbMkDiggingInfoService.update(updateVO));
    }


    /**
      * 删除
      *
      * @param id 要删除的记录ID
      * @return 操作结果
      */
    @DeleteMapping("/{id}")
    public RespJson<?> delete(@PathVariable Long id) {
        log.debug("删除 掘进机基本信息，ID：{}", id);
        return RespJson.success(tbMkDiggingInfoService.delete(id));
    }

    /**
     * 查看详情
     *
     * @param id 要查看的记录ID
     * @return 详情信息
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<TbMkDiggingInfoDTO> getDetail(@PathVariable Long id) {
        log.debug("查看详情 掘进机基本信息，ID：{}", id);
        TbMkDiggingInfo entity = tbMkDiggingInfoService.get(id);
        if (entity == null) {
            return RespJson.failure("未找到对应记录");
        }
        return RespJson.success(tbMkDiggingInfoConvert.toDTO(entity));
    }
    /**
     * 批量删除
     *
     * @param ids 要删除的记录ID列表
     * @return 操作结果
     */
    @PostMapping(value = "/batchDelete", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchDelete(@RequestBody List<Long> ids) {
        log.debug("批量删除 掘进机基本信息，IDs：{}", ids);
        return RespJson.success(tbMkDiggingInfoService.batchDelete(ids));
    }

    /**
     * 批量新增
     *
     * @param createVOs 新增参数列表
     * @return 新增的记录ID列表
     */
    @PostMapping(value = "/batchAdd", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchCreate(@RequestBody @Valid List<TbMkDiggingInfoCreateVO> createVOs) {
        log.debug("批量新增 掘进机基本信息，数量：{}", createVOs.size());
        return RespJson.success(tbMkDiggingInfoService.batchCreate(createVOs));
    }

    /**
     * 批量更新
     *
     * @param updateVOs 更新参数列表
     * @return 更新的记录数量
     */
    @PostMapping(value = "/batchModify", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> batchUpdate(@RequestBody @Valid List<TbMkDiggingInfoUpdateVO> updateVOs) {
        log.debug("批量更新 掘进机基本信息，数量：{}", updateVOs.size());
        return RespJson.success(tbMkDiggingInfoService.batchUpdate(updateVOs));
    }
}
