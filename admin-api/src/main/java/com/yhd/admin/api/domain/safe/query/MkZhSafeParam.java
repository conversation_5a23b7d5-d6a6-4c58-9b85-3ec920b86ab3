package com.yhd.admin.api.domain.safe.query;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
public class MkZhSafeParam extends QueryParam implements Serializable {
    /**
     * 主键id列表
     */
    private List<Long> ids;

    private Long id;
}
