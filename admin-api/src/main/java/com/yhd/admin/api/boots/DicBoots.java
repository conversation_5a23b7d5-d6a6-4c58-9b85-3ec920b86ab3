package com.yhd.admin.api.boots;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.yhd.admin.api.domain.sys.dto.DicItemDTO;
import com.yhd.admin.api.domain.sys.enums.APIRedisKeyEnum;
import com.yhd.admin.api.service.sys.DicItemService;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class DicBoots implements CommandLineRunner {

    private final DicItemService dicItemService;

    private final RedisTemplate<String, Object> redisTemplate;

    public DicBoots(DicItemService dicItemService, RedisTemplate<String, Object> redisTemplate) {
        this.dicItemService = dicItemService;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void run(String... args) throws Exception {
        if (log.isInfoEnabled()) {
            log.info("系统初始化加载字典项开始");
        }

        List<DicItemDTO> allDicItem = dicItemService.queryAllDicItem();
        Map<String, List<DicItemDTO>> allDicMap =
            allDicItem.stream().collect(Collectors.groupingBy(DicItemDTO::getCategory));
        HashOperations<String, String, List<DicItemDTO>> hashOperations = redisTemplate.opsForHash();
        Set<String> dicHashKey = hashOperations.keys(APIRedisKeyEnum.DIC.getKey());
        if (log.isDebugEnabled()) {
            log.debug("获取字典缓存历史keys:[{}]", dicHashKey.size());
        }
        if (!CollectionUtils.isEmpty(dicHashKey)) {
            if (log.isDebugEnabled()) {
                log.debug("开始删除字典缓存历史keys:[{}]", dicHashKey);
            }
            hashOperations.delete(APIRedisKeyEnum.DIC.getKey(), dicHashKey.toArray(new Object[0]));
            if (log.isDebugEnabled()) {
                log.debug("删除字典缓存历史keys完毕");
            }
        }
        hashOperations.putAll(APIRedisKeyEnum.DIC.getKey(), allDicMap);
        if (log.isInfoEnabled()) {
            log.info("系统初始化加载字典项结束,{}", allDicMap);
        }
    }
}
