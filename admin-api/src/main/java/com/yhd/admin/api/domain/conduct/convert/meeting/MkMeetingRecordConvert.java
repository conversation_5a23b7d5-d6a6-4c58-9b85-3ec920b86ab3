package com.yhd.admin.api.domain.conduct.convert.meeting;

import com.yhd.admin.api.domain.conduct.dto.meeting.MkMeetingRecordDTO;
import com.yhd.admin.api.domain.conduct.entity.meeting.MkMeetingRecord;
import com.yhd.admin.api.domain.conduct.query.meeting.MkMeetingRecordParam;
import com.yhd.admin.api.domain.conduct.vo.meeting.MkMeetingRecordVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 调度会议记录(MkMeetingRecord)Convert
 *
 * <AUTHOR>
 * @since 2025-07-28 10:36:44
 */
@Mapper(componentModel = "spring")
public interface MkMeetingRecordConvert {
    MkMeetingRecord toEntity(MkMeetingRecordParam param);

    MkMeetingRecordDTO toDTO(MkMeetingRecord entity);

    MkMeetingRecordVO toVO(MkMeetingRecordDTO dto);

    List<MkMeetingRecordDTO> toDTOList(List<MkMeetingRecord> entityList);

    List<MkMeetingRecordVO> toVOList(List<MkMeetingRecordDTO> dtoList);
}

