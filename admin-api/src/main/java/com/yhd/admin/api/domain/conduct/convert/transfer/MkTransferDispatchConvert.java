package com.yhd.admin.api.domain.conduct.convert.transfer;

import com.yhd.admin.api.domain.conduct.dto.transfer.MkTransferDispatchDTO;
import com.yhd.admin.api.domain.conduct.entity.transfer.MkTransferDispatch;
import com.yhd.admin.api.domain.conduct.query.transfer.MkTransferDispatchParam;
import com.yhd.admin.api.domain.conduct.vo.transfer.MkTransferDispatchVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 收发文登记表(MkTransferDispatch)Convert
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
@Mapper(componentModel = "spring")
public interface MkTransferDispatchConvert {
    MkTransferDispatch toEntity(MkTransferDispatchParam param);

    MkTransferDispatchDTO toDTO(MkTransferDispatch entity);

    MkTransferDispatchVO toVO(MkTransferDispatchDTO dto);

    List<MkTransferDispatchDTO> toDTOList(List<MkTransferDispatch> entityList);

    List<MkTransferDispatchVO> toVOList(List<MkTransferDispatchDTO> dtoList);
}

