package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkOutputScxtDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputScxt;
import com.yhd.admin.api.domain.operation.query.MkOutputScxtParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 生产录入-生产系统内容表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
public interface MkOutputScxtService extends IService<MkOutputScxt> {

    IPage<MkOutputScxtDTO> pagingQuery(MkOutputScxtParam queryParam);

    Boolean add(MkOutputScxtParam param);

    Boolean modify(MkOutputScxtParam param);

    Boolean removeBatch(BatchParam param);

    MkOutputScxtDTO getCurrentDetail(MkOutputScxtParam param);
}
