package com.yhd.admin.api.domain.sys.convert;

import org.mapstruct.Mapper;

import com.yhd.admin.api.domain.sys.dto.DicDTO;
import com.yhd.admin.api.domain.sys.entity.SysDic;
import com.yhd.admin.api.domain.sys.query.DicParam;
import com.yhd.admin.api.domain.sys.vo.DicVO;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName DicConvert.java @Description TODO
 * @createTime 2020年05月20日 16:28:00
 */
@Mapper(componentModel = "spring")
public interface DicConvert {

    DicDTO toDTO(SysDic dic);

    SysDic toEntity(DicParam param);

    DicVO toVO(DicDTO dicDTO);
}
