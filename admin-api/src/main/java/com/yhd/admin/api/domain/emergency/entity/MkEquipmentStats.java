package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 综合决策设备统计表
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_mk_equipment_stats")
public class MkEquipmentStats {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 统计编码
     */
    private String statsCode;

    /**
     * 统计日期
     */
    private LocalDate statsDate;

    /**
     * 设备开机数量
     */
    private Integer onlineEquipmentCount;

    /**
     * 设备总数量
     */
    private Integer totalEquipmentCount;

    /**
     * 开机率(%)
     */
    private BigDecimal startupRate;

    /**
     * 故障设备数量
     */
    private Integer faultEquipmentCount;

    /**
     * 故障率(%)
     */
    private BigDecimal faultRate;

    /**
     * 组织机构编码
     */
    private String orgCode;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}