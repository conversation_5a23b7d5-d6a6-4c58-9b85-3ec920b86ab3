package com.yhd.admin.api.service.sys;

import java.io.InputStream;
import java.util.concurrent.TimeUnit;

import org.springframework.stereotype.Service;

import io.minio.*;
import io.minio.http.Method;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName StorageServicesImpl.java @Description TODO
 * @createTime 2020年04月23日 14:40:00
 */
@Service
public class StorageServicesImpl implements StorageServices {
    private MinioClient minioClient;

    @Override
    public void uploadObject(String bucketName, String objectName, InputStream stream, String contentType) {

        try {

            boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!isExist) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            }
            minioClient.putObject(PutObjectArgs.builder().bucket(bucketName).stream(stream, -1, -1)
                .contentType(contentType).object(objectName).build());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String getStorageUrl(String bucketName, String objectName) {
        try {

            return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder().method(Method.GET)
                .bucket(bucketName).object(objectName).expiry(365, TimeUnit.DAYS).build());
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
