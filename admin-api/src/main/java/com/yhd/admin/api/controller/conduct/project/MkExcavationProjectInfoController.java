package com.yhd.admin.api.controller.conduct.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.project.MkExcavationProjectInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.project.MkExcavationProjectInfoDTO;
import com.yhd.admin.api.domain.conduct.query.project.MkExcavationProjectInfoParam;
import com.yhd.admin.api.domain.conduct.vo.project.MkExcavationProjectInfoVO;
import com.yhd.admin.api.service.conduct.project.MkExcavationProjectInfoService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 掘进项目信息控制层
 *
 * <AUTHOR>
 * @since 2025-07-30 14:05:52
 */
@RestController
@RequestMapping("/conduct/excavation/project")
public class MkExcavationProjectInfoController {

    private final MkExcavationProjectInfoService mkExcavationProjectInfoService;
    private final MkExcavationProjectInfoConvert mkExcavationProjectInfoConvert;

    public MkExcavationProjectInfoController(
        MkExcavationProjectInfoService mkExcavationProjectInfoService,
        MkExcavationProjectInfoConvert mkExcavationProjectInfoConvert
    ) {
        this.mkExcavationProjectInfoService = mkExcavationProjectInfoService;
        this.mkExcavationProjectInfoConvert = mkExcavationProjectInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkExcavationProjectInfoParam param) {
        IPage<MkExcavationProjectInfoDTO> page = mkExcavationProjectInfoService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkExcavationProjectInfoConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkExcavationProjectInfoParam param) {
        return RespJson.success(mkExcavationProjectInfoConvert.toVOList(mkExcavationProjectInfoService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkExcavationProjectInfoVO> getCurrentDetails(@RequestBody MkExcavationProjectInfoParam param) {
        return RespJson.success(mkExcavationProjectInfoConvert.toVO(mkExcavationProjectInfoService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkExcavationProjectInfoParam param) {
        Boolean retVal = mkExcavationProjectInfoService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkExcavationProjectInfoParam param) {
        Boolean retVal = mkExcavationProjectInfoService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 提交审批
     *
     * @param param 数据
     * @return 是否成功
     */
    @PostMapping(
        value = "/submit",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> submit(@RequestBody MkExcavationProjectInfoParam param) {
        Boolean retVal = mkExcavationProjectInfoService.submit(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 通过审批、驳回
     *
     * @param param 数据
     * @return 是否成功
     */
    @PostMapping(
        value = "/handle",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> handle(@RequestBody MkExcavationProjectInfoParam param) {
        Boolean retVal = mkExcavationProjectInfoService.handle(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

