package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkDisasterIndexDTO;
import com.yhd.admin.api.domain.operation.entity.MkDisasterIndex;
import com.yhd.admin.api.domain.operation.query.MkDisasterIndexParam;
import com.yhd.admin.api.domain.operation.vo.MkDisasterIndexVO;
import org.mapstruct.Mapper;




/**
* 算法模型-自然灾害评价指标表
*
* <AUTHOR>
* @since 1.0.0 2025-08-01
*/
@Mapper(componentModel = "spring")
public interface MkDisasterIndexConvert {

    MkDisasterIndex toEntity(MkDisasterIndexParam param);

    MkDisasterIndexVO toVO(MkDisasterIndexDTO dto);

    MkDisasterIndexDTO toDTO(MkDisasterIndex entity);

}
