package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.sys.MenuDao;
import com.yhd.admin.api.domain.sys.convert.MenuConvert;
import com.yhd.admin.api.domain.sys.dto.MenuDTO;
import com.yhd.admin.api.domain.sys.dto.RoleDTO;
import com.yhd.admin.api.domain.sys.entity.SysMenu;
import com.yhd.admin.api.domain.sys.enums.APIRedisKeyEnum;
import com.yhd.admin.api.domain.sys.query.MenuParam;
import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName MenuServiceImpl.java @Description TODO
 * @createTime 2020年03月30日 15:04:00
 */
@Service
public class MenuServiceImpl extends ServiceImpl<MenuDao, SysMenu> implements MenuService {

    private final MenuConvert convert;
    private final RoleService roleService;
    private final RedisTemplate redisTemplate;
    @Resource
    private CacheCleanUp cacheCleanUp;

    public MenuServiceImpl(MenuConvert convert, RoleService roleService, RedisTemplate redisTemplate) {
        this.convert = convert;
        this.roleService = roleService;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public List<MenuDTO> queryMenuByUserWithCache(UserParam param) {
        ValueOperations<String, List<MenuDTO>> menuOps = redisTemplate.opsForValue();
        String redisKey = String.format(APIRedisKeyEnum.USER_MENU.getKey(), param.getUsername(), param.getClientId());

        return Optional.ofNullable(menuOps.get(redisKey)).orElseGet(() -> {
            List<MenuDTO> userMenus = this.queryMenuByUser(param).stream().filter(o -> o.getParentId() == 0)
                .sorted(Comparator.comparing(MenuDTO::getOrderNum)).collect(Collectors.toList());
            menuOps.set(redisKey, userMenus, 1, TimeUnit.DAYS);
            return userMenus;
        });
    }

    @Override
    public List<MenuDTO> queryMenuByUser(UserParam param) {
        // 角色
        List<RoleDTO> roleDTOList = roleService.getRoleByUser(param);

        if (CollectionUtils.isEmpty(roleDTOList)) {
            return Collections.EMPTY_LIST;
        }
        // 根据角色，查询所属的菜单
        List<Long> roleIds =
            roleDTOList.stream().filter(RoleDTO::getIsEnable).map(RoleDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.EMPTY_LIST;
        }

        List<Long> menuIds = baseMapper.selectBatchMenuByRole(param.getClientId(), roleIds).stream().map(SysMenu::getId)
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(menuIds)) {
            return Collections.EMPTY_LIST;
        }
        // 查找所有自节点的父节点。
        List<MenuDTO> allMenu = this.findParentBySubId(param.getClientId(), menuIds).stream().distinct()
            .sorted(Comparator.comparing(MenuDTO::getOrderNum)).collect(Collectors.toList());
        Map<Long, List<MenuDTO>> menuGroup = allMenu.stream().collect(Collectors.groupingBy(MenuDTO::getParentId));

        for (MenuDTO menu : allMenu) {
            List<MenuDTO> children = menuGroup.get(menu.getId());
            menu.setChildren(children);
        }
        // 根节点下所有的菜单
        return allMenu;
    }

    @Override
    public List<MenuDTO> querySubMenuById(Long menuId) {
        // 查询所有的菜单节点
        List<MenuDTO> allMenus = convert.toDto(baseMapper.selectChildren(menuId));

        Map<Long, List<MenuDTO>> menuMap = allMenus.stream().sorted(Comparator.comparing(MenuDTO::getOrderNum))
            .collect(Collectors.groupingBy(MenuDTO::getParentId));

        allMenus.forEach(o -> {
            if (menuMap.containsKey(o.getId())) {
                o.setChildren(menuMap.get(o.getId()));
            }
        });

        return allMenus.stream().filter(o -> o.getParentId().equals(menuId)).collect(Collectors.toList());
    }

    @Override
    public List<MenuDTO> querySubMenu(MenuParam menuParam) {
        // 查询所有的菜单节点
        List<MenuDTO> allMenus = convert.toDto(baseMapper.selectChildren(menuParam.getId()));

        List<MenuDTO> retVal = allMenus.stream()
            .filter(o -> o.getParentId() == 0 && StringUtils.equals(o.getClientId(), menuParam.getClientId()))
            .sorted(Comparator.comparingInt(MenuDTO::getOrderNum)).collect(Collectors.toList());
        retVal.forEach(o -> o.setChildren(this.getChildren(allMenus, o.getId())));
        return retVal;
    }

    @Override
    public MenuDTO querySubMenuIncludeSelf(Long menuId) {
        MenuDTO menuDto = convert.toDto(this.getById(menuId));
        menuDto.getChildren().addAll(querySubMenuById(menuDto.getId()));
        return menuDto;
    }

    @Override
    public IPage<MenuDTO> pagingQuery(MenuParam queryParam) {
        IPage<MenuDTO> iPage = new Page<>();
        List<MenuDTO> allMenus = this.querySubMenu(queryParam);

        MenuDTO root = new MenuDTO();
        root.setId(0L);
        root.setParentId(-1L);
        root.setName("根节点");
        root.setChildren(allMenus);

        return iPage.setRecords(List.of(root));
    }

    @Override
    public Boolean modify(MenuParam modifyParam) {
        cacheCleanUp.userMenu();
        LambdaUpdateChainWrapper<SysMenu> updateChainWrapper = new LambdaUpdateChainWrapper<>(baseMapper);
        return updateChainWrapper.eq(SysMenu::getId, modifyParam.getId()).update(convert.toSys(modifyParam));
    }

    @Override
    public Boolean removeBatch(BatchParam batchParam) {
        cacheCleanUp.userMenu();
        return this.removeByIds(batchParam.getId());
    }

    @Override
    public MenuDTO obtainDetailById(Long menuId) {
        return convert.toDto(this.getById(menuId));
    }

    @Override
    public List<MenuDTO> findParentBySubId(String clientId, List<Long> menuIds) {
        return convert.toDto(baseMapper.selectParentById(clientId, menuIds));
    }

    /**
     * 校验菜单的 路由是否存在。
     *
     * @param param
     * @return
     */
    @Override
    public Boolean validateIfNotExist(MenuParam param) {
        LambdaQueryChainWrapper<SysMenu> validateChain = new LambdaQueryChainWrapper<>(baseMapper);
        validateChain.notIn(param.getId() != null, SysMenu::getId, param.getId()).eq(SysMenu::getPath, param.getPath());
        return CollectionUtils.isEmpty(validateChain.list());
    }

    @Override
    public List<String> queryUserAuthority(UserParam param) {
        List<MenuDTO> menuDTOS = this.queryMenuByUser(param);
        return menuDTOS.stream().map(MenuDTO::getAuthority).collect(Collectors.toList());
    }

    @Override
    public Boolean addOrUpdate(MenuParam addParam) {
        cacheCleanUp.userMenu();
        return this.saveOrUpdate(convert.toSys(addParam));
    }

    private List<MenuDTO> getChildren(List<MenuDTO> otherMenus, Long parentId) {
        List<MenuDTO> childrenMenus = otherMenus.stream().filter(m -> m.getParentId().equals(parentId))
            .sorted(Comparator.comparing(MenuDTO::getOrderNum)).collect(Collectors.toList());
        childrenMenus.forEach(m -> m.setChildren(getChildren(otherMenus, m.getId())));
        return childrenMenus;
    }
}
