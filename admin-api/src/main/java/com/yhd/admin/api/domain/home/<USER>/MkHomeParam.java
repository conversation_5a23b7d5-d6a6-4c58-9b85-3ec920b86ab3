package com.yhd.admin.api.domain.home.query;

import com.yhd.admin.common.domain.query.QueryParam;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 综合管控大屏
 *
 * <AUTHOR>
 * @date 2025/7/29 09:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkHomeParam extends QueryParam implements Serializable {
  /** 主键id列表 */
  private List<Long> ids;

  private Long id;
}
