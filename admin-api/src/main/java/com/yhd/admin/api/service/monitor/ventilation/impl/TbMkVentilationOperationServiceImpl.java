package com.yhd.admin.api.service.monitor.ventilation.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.ventilation.TbMkVentilationOperationDao;
import com.yhd.admin.api.domain.monitor.ventilation.convert.TbMkVentilationOperationConvert;
import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkVentilationOperationDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkVentilationOperation;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkVentilationOperationCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.page.TbMkVentilationOperationPageVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkVentilationOperationUpdateVO;
import com.yhd.admin.api.service.monitor.ventilation.TbMkVentilationOperationService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 通风机整体运行参数 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkVentilationOperationServiceImpl extends ServiceImpl<TbMkVentilationOperationDao, TbMkVentilationOperation> implements TbMkVentilationOperationService {

    @Resource
    private TbMkVentilationOperationConvert tbMkVentilationOperationConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkVentilationOperationCreateVO createVO) {
        TbMkVentilationOperation tbMkVentilationOperation = tbMkVentilationOperationConvert.convert(createVO);
        baseMapper.insert(tbMkVentilationOperation);
        return tbMkVentilationOperation.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkVentilationOperationUpdateVO updateVO) {
        TbMkVentilationOperation tbMkVentilationOperation = tbMkVentilationOperationConvert.convert(updateVO);
        return baseMapper.updateById(tbMkVentilationOperation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkVentilationOperation get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkVentilationOperationDTO> page(TbMkVentilationOperationPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkVentilationOperation> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkVentilationOperation> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.eq(param.getVentilationId() != null, TbMkVentilationOperation::getVentilationId, param.getVentilationId());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getValveStatus()), TbMkVentilationOperation::getValveStatus, param.getValveStatus());
        queryChainWrapper.orderByDesc(TbMkVentilationOperation::getId);
        return queryChainWrapper.page(iPage).convert(tbMkVentilationOperationConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkVentilationOperationCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 通风机整体运行参数：传入的列表为空");
            return 0;
        }
        List<TbMkVentilationOperation> entities = createVOs.stream()
                .map(tbMkVentilationOperationConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 通风机整体运行参数 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkVentilationOperationUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 通风机整体运行参数：传入的列表为空");
            return 0;
        }
        List<TbMkVentilationOperation> entities = updateVOs.stream()
                .map(tbMkVentilationOperationConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 通风机整体运行参数：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 通风机整体运行参数 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
