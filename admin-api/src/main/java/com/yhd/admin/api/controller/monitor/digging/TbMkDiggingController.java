package com.yhd.admin.api.controller.monitor.digging;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yhd.admin.api.dao.monitor.digging.*;
import com.yhd.admin.api.domain.monitor.digging.dto.TbMkDiggingComprehensiveDTO;
import com.yhd.admin.api.domain.monitor.digging.entity.*;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 掘进机监控控制器
 */
@Slf4j
@RestController
@RequestMapping("/monitor/digging/screen")
public class TbMkDiggingController {

    @Resource
    private TbMkDiggingInfoDao tbMkDiggingInfoDao;

    @Resource
    private TbMkDiggingOperationDao tbMkDiggingOperationDao;

    @Resource
    private TbMkDiggingNavigationDao tbMkDiggingNavigationDao;

    @Resource
    private TbMkDiggingEnvironmentDao tbMkDiggingEnvironmentDao;

    /**
     * 获取所有掘进机及其最新的运行、导航、环境参数
     *
     * @return 包含掘进机综合信息的列表
     */
    @GetMapping(value = "/info", produces = org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<TbMkDiggingComprehensiveDTO>> getDiggingInfo() {
        log.debug("获取掘进机及其运行、导航、环境参数");

        List<TbMkDiggingComprehensiveDTO> result = new ArrayList<>();

        try {
            List<TbMkDiggingInfo> diggingInfos = tbMkDiggingInfoDao.selectList(null);

            for (TbMkDiggingInfo diggingInfo : diggingInfos) {
                TbMkDiggingComprehensiveDTO dto = new TbMkDiggingComprehensiveDTO();
                dto.setDiggingId(diggingInfo.getId());
                dto.setDeviceNumber(diggingInfo.getDeviceNumber());
                dto.setDeviceName(diggingInfo.getDeviceName());

                QueryWrapper<TbMkDiggingOperation> operationQuery = new QueryWrapper<>();
                operationQuery.eq("digging_id", diggingInfo.getId());
                operationQuery.orderByDesc("updated_time");
                TbMkDiggingOperation operation = tbMkDiggingOperationDao.selectOne(operationQuery);
                if (operation != null) {
                    BeanUtils.copyProperties(operation, dto);
                }

                QueryWrapper<TbMkDiggingNavigation> navigationQuery = new QueryWrapper<>();
                navigationQuery.eq("digging_id", diggingInfo.getId());
                navigationQuery.orderByDesc("updated_time");
                TbMkDiggingNavigation navigation = tbMkDiggingNavigationDao.selectOne(navigationQuery);
                if (navigation != null) {
                    BeanUtils.copyProperties(navigation, dto);
                }

                QueryWrapper<TbMkDiggingEnvironment> environmentQuery = new QueryWrapper<>();
                environmentQuery.eq("digging_id", diggingInfo.getId());
                environmentQuery.orderByDesc("updated_time");
                TbMkDiggingEnvironment environment = tbMkDiggingEnvironmentDao.selectOne(environmentQuery);
                if (environment != null) {
                    BeanUtils.copyProperties(environment, dto);
                }

                result.add(dto);
            }
            return RespJson.success(result);

        } catch (Exception e) {
            log.error("查询掘进机信息时发生异常", e);
            return RespJson.failure("查询失败");
        }
    }
}
