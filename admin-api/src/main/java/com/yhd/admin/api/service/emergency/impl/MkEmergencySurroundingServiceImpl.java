package com.yhd.admin.api.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.emergency.MkEmergencySurroundingDao;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencySurroundingConvert;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencySurrounding;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySurroundingParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.emergency.MkEmergencySurroundingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencySurroundingServiceImpl.java
 * @Description 应急救援周边信息Service实现类
 * @createTime 2025年01月20日 14:00:00
 */
@Slf4j
@Service
public class MkEmergencySurroundingServiceImpl extends ServiceImpl<MkEmergencySurroundingDao, MkEmergencySurrounding>
        implements MkEmergencySurroundingService {

    private final MkEmergencySurroundingConvert convert;

    public MkEmergencySurroundingServiceImpl(MkEmergencySurroundingConvert convert) {
        this.convert = convert;
    }

    @Override
    public IPage<MkEmergencySurrounding> pagingQuery(MkEmergencySurroundingParam param) {
        LambdaQueryWrapper<MkEmergencySurrounding> queryWrapper = buildQueryWrapper(param);
        Page<MkEmergencySurrounding> page = new Page<>(param.getCurrent(), param.getPageSize());
        return page(page, queryWrapper);
    }

    @Override
    public MkEmergencySurrounding getById(Integer id) {
        if (id == null) {
            throw new BMSException("0002", "周边信息ID不能为空");
        }
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(MkEmergencySurroundingParam param) {
        // 参数校验
        validateParam(param, false);

        // 检查项目编码是否存在
        LambdaQueryWrapper<MkEmergencySurrounding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkEmergencySurrounding::getItemCode, param.getItemCode());
        if (count(queryWrapper) > 0) {
            throw new BMSException("0005", "项目编码已存在");
        }

        // 转换为实体
        MkEmergencySurrounding entity = convert.toEntity(param);

        // 设置默认值
        if (entity.getStatus() == null) {
            entity.setStatus(1);
        }

        return save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean modify(MkEmergencySurroundingParam param) {
        // 参数校验
        validateParam(param, true);

        // 检查ID是否存在
        MkEmergencySurrounding existEntity = getById(param.getId());
        if (existEntity == null) {
            throw new BMSException("0002", "周边信息不存在");
        }

        // 检查项目编码是否存在
        if (StringUtils.isNotBlank(param.getItemCode())) {
            LambdaQueryWrapper<MkEmergencySurrounding> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MkEmergencySurrounding::getItemCode, param.getItemCode())
                    .ne(MkEmergencySurrounding::getId, param.getId());
            if (count(queryWrapper) > 0) {
                throw new BMSException("0005", "项目编码已存在");
            }
        }

        // 更新实体
        convert.updateEntity(param, existEntity);
        return updateById(existEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(Integer id) {
        if (id == null) {
            throw new BMSException("0002", "周边信息ID不能为空");
        }

        // 检查ID是否存在
        MkEmergencySurrounding existEntity = getById(id);
        if (existEntity == null) {
            throw new BMSException("0002", "周边信息不存在");
        }

        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatch(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BMSException("0002", "周边信息ID不能为空");
        }

        return removeByIds(ids);
    }

    @Override
    public List<MkEmergencySurrounding> getByItemType(String itemType, String orgCode) {
        LambdaQueryWrapper<MkEmergencySurrounding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(itemType), MkEmergencySurrounding::getItemType, itemType);
        queryWrapper.eq(StringUtils.isNotBlank(orgCode), MkEmergencySurrounding::getOrgCode, orgCode);
        queryWrapper.orderByDesc(MkEmergencySurrounding::getCreatedTime);
        return list(queryWrapper);
    }

    @Override
    public List<MkEmergencySurrounding> getByLocation(String location, String orgCode) {
        LambdaQueryWrapper<MkEmergencySurrounding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(location), MkEmergencySurrounding::getLocation, location);
        queryWrapper.eq(StringUtils.isNotBlank(orgCode), MkEmergencySurrounding::getOrgCode, orgCode);
        queryWrapper.orderByDesc(MkEmergencySurrounding::getCreatedTime);
        return list(queryWrapper);
    }

    @Override
    public List<MkEmergencySurrounding> getByStatus(Integer status, String orgCode) {
        LambdaQueryWrapper<MkEmergencySurrounding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(status != null, MkEmergencySurrounding::getStatus, status);
        queryWrapper.eq(StringUtils.isNotBlank(orgCode), MkEmergencySurrounding::getOrgCode, orgCode);
        queryWrapper.orderByDesc(MkEmergencySurrounding::getCreatedTime);
        return list(queryWrapper);
    }

    @Override
    public List<MkEmergencySurrounding> queryList(MkEmergencySurroundingParam param) {
        // 如果参数为null，创建一个空的参数对象，这样会查询全部数据
        if (param == null) {
            param = new MkEmergencySurroundingParam();
        }
        LambdaQueryWrapper<MkEmergencySurrounding> queryWrapper = buildQueryWrapper(param);
        return list(queryWrapper);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<MkEmergencySurrounding> buildQueryWrapper(MkEmergencySurroundingParam param) {
        LambdaQueryWrapper<MkEmergencySurrounding> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotBlank(param.getItemCode())) {
            queryWrapper.like(MkEmergencySurrounding::getItemCode, param.getItemCode());
        }

        if (StringUtils.isNotBlank(param.getItemName())) {
            queryWrapper.like(MkEmergencySurrounding::getItemName, param.getItemName());
        }

        if (StringUtils.isNotBlank(param.getItemType())) {
            queryWrapper.eq(MkEmergencySurrounding::getItemType, param.getItemType());
        }

        if (StringUtils.isNotBlank(param.getLocation())) {
            queryWrapper.like(MkEmergencySurrounding::getLocation, param.getLocation());
        }

        if (param.getStatus() != null) {
            queryWrapper.eq(MkEmergencySurrounding::getStatus, param.getStatus());
        }

        if (StringUtils.isNotBlank(param.getOrgCode())) {
            queryWrapper.eq(MkEmergencySurrounding::getOrgCode, param.getOrgCode());
        }

        if (StringUtils.isNotBlank(param.getContactPerson())) {
            queryWrapper.like(MkEmergencySurrounding::getContactPerson, param.getContactPerson());
        }

        if (StringUtils.isNotBlank(param.getContactPhone())) {
            queryWrapper.like(MkEmergencySurrounding::getContactPhone, param.getContactPhone());
        }

        if (param.getCreatedTimeStart() != null) {
            queryWrapper.ge(MkEmergencySurrounding::getCreatedTime, param.getCreatedTimeStart());
        }

        if (param.getCreatedTimeEnd() != null) {
            queryWrapper.le(MkEmergencySurrounding::getCreatedTime, param.getCreatedTimeEnd());
        }

        // 排序
        queryWrapper.orderByDesc(MkEmergencySurrounding::getCreatedTime);

        return queryWrapper;
    }

    /**
     * 参数校验
     */
    private void validateParam(MkEmergencySurroundingParam param, boolean isUpdate) {
        if (isUpdate && param.getId() == null) {
            throw new BMSException("0002", "周边信息ID不能为空");
        }

        if (StringUtils.isBlank(param.getItemCode())) {
            throw new BMSException("0002", "项目编码不能为空");
        }

        if (StringUtils.isBlank(param.getItemName())) {
            throw new BMSException("0002", "项目名称不能为空");
        }

        if (StringUtils.isBlank(param.getItemType())) {
            throw new BMSException("0002", "项目类型不能为空");
        }

        if (StringUtils.isBlank(param.getLocation())) {
            throw new BMSException("0002", "位置信息不能为空");
        }

        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException("0002", "组织编码不能为空");
        }
    }
}