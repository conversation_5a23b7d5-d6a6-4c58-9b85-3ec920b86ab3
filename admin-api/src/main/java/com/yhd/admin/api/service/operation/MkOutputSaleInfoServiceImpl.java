package com.yhd.admin.api.service.operation;

import com.yhd.admin.api.dao.operation.MkOutputSaleInfoDao;
import com.yhd.admin.api.domain.operation.convert.MkOutputSaleInfoConvert;
import com.yhd.admin.api.domain.operation.dto.MkOutputSaleInfoDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputSaleInfo;
import com.yhd.admin.api.domain.operation.query.MkOutputSaleInfoParam;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;


/**
 * 生产录入-产量销售库存表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@Service
public class MkOutputSaleInfoServiceImpl extends ServiceImpl<MkOutputSaleInfoDao, MkOutputSaleInfo> implements MkOutputSaleInfoService {

    @Resource
    private MkOutputSaleInfoConvert convert;

    @Resource
    private MkOutputSaleInfoService service;

    @Override
    public IPage<MkOutputSaleInfoDTO> pagingQuery(MkOutputSaleInfoParam queryParam) {
        Page<MkOutputSaleInfo> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkOutputSaleInfo> queryChain = new LambdaQueryChainWrapper<>(baseMapper);

        queryChain.orderByDesc(MkOutputSaleInfo::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkOutputSaleInfoParam param) {
        MkOutputSaleInfo entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MkOutputSaleInfoParam param) {
        MkOutputSaleInfo entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MkOutputSaleInfoDTO getCurrentDetail(MkOutputSaleInfoParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

}
