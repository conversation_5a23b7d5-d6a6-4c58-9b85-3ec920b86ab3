package com.yhd.admin.api.service.conduct.base;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.base.MkMiningAreaInfoDao;
import com.yhd.admin.api.domain.conduct.convert.base.MkMiningAreaInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.base.MkMiningAreaInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkMiningAreaInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkMiningAreaInfoParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 采区信息服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25 14:01:30
 */
@Service
public class MkMiningAreaInfoServiceImpl extends ServiceImpl<MkMiningAreaInfoDao, MkMiningAreaInfo> implements MkMiningAreaInfoService {

    public final MkMiningAreaInfoConvert mkMiningAreaInfoConvert;

    public MkMiningAreaInfoServiceImpl(MkMiningAreaInfoConvert mkMiningAreaInfoConvert) {
        this.mkMiningAreaInfoConvert = mkMiningAreaInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkMiningAreaInfoDTO> pagingQuery(MkMiningAreaInfoParam param) {
        IPage<MkMiningAreaInfo> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkMiningAreaInfo> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return wrapper.page(page).convert(mkMiningAreaInfoConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkMiningAreaInfoDTO> queryList(MkMiningAreaInfoParam param) {
        LambdaQueryWrapper<MkMiningAreaInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(param.getOrgCode()), MkMiningAreaInfo::getOrgCode, param.getOrgCode());
        return mkMiningAreaInfoConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param  数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkMiningAreaInfoParam param) {
        MkMiningAreaInfo entity = mkMiningAreaInfoConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkMiningAreaInfoDTO getCurrentDetails(MkMiningAreaInfoParam param) {
        return mkMiningAreaInfoConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkMiningAreaInfoParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }
}
