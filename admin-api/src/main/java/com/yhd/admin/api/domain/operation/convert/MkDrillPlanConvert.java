package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkDrillPlanDTO;
import com.yhd.admin.api.domain.operation.entity.MkDrillPlan;
import com.yhd.admin.api.domain.operation.query.MkDrillPlanParam;
import com.yhd.admin.api.domain.operation.vo.MkDrillPlanVO;
import org.mapstruct.Mapper;

/**
* 应急演练-演练计划表
*
* <AUTHOR>
* @since 1.0.0 2025-08-01
*/
@Mapper(componentModel = "spring")
public interface MkDrillPlanConvert {

    MkDrillPlan toEntity(MkDrillPlanParam param);

    MkDrillPlanVO toVO(MkDrillPlanDTO dto);

    MkDrillPlanDTO toDTO(MkDrillPlan entity);

} 