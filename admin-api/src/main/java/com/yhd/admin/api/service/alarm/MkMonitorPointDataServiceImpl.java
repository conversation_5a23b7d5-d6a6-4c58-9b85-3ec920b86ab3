package com.yhd.admin.api.service.alarm;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.alarm.MkMonitorPointDataDao;
import com.yhd.admin.api.domain.alarm.convert.MkMonitorPointDataConvert;
import com.yhd.admin.api.domain.alarm.dto.MkMonitorPointDataDTO;
import com.yhd.admin.api.domain.alarm.entity.MkMonitorPointData;
import com.yhd.admin.api.domain.alarm.query.MkMonitorPointDataParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 测点数据-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2025/8/7 17:01
 */
@Service
public class MkMonitorPointDataServiceImpl
    extends ServiceImpl<MkMonitorPointDataDao, MkMonitorPointData>
    implements MkMonitorPointDataService {
  private static final Logger logger = LoggerFactory.getLogger(MkMonitorPointDataServiceImpl.class);

  private final MkMonitorPointDataConvert convert;

  public MkMonitorPointDataServiceImpl(MkMonitorPointDataConvert convert) {
    this.convert = convert;
  }

  /**
   * 查询详情信息
   *
   * @param param 主键id
   * @return 测点数据
   */
  @Override
  public MkMonitorPointDataDTO getCurrentDetail(MkMonitorPointDataParam param) {
    logger.debug("进入service层，调用【查询测点数据详情信息】接口开始，参数：id={}", param.getId());
    MkMonitorPointData entity = super.getById(param.getId());
    return convert.toDTO(entity);
  }

  /**
   * 根据条件查询测点数据分页列表
   *
   * @param param 查询条件
   * @return 测点数据分页列表
   */
  @Override
  public IPage<MkMonitorPointDataDTO> pagingQuery(MkMonitorPointDataParam param) {
    logger.debug("进入service层，调用【根据条件查询测点数据分页列表】接口开始，参数：{}", param);
    IPage<MkMonitorPointData> page = new Page<>(param.getCurrent(), param.getPageSize());
    LambdaQueryChainWrapper<MkMonitorPointData> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
    // 查询条件
    // 单位编码
    wrapper.eq(
        StringUtils.isNotBlank(param.getOrgCode()),
        MkMonitorPointData::getOrgCode,
        param.getOrgCode());
    // 子系统
    wrapper.eq(
        StringUtils.isNotBlank(param.getSystemCode()),
        MkMonitorPointData::getSystemCode,
        param.getSystemCode());
    // 监测类型
    wrapper.eq(
        StringUtils.isNotBlank(param.getMonitorTypeCode()),
        MkMonitorPointData::getMonitorTypeCode,
        param.getMonitorTypeCode());
    // 测点名称
    wrapper.like(
        StringUtils.isNotBlank(param.getPointName()),
        MkMonitorPointData::getPointName,
        param.getPointName());
    // 测点编号
    wrapper.like(
        StringUtils.isNotBlank(param.getPointCode()),
        MkMonitorPointData::getPointCode,
        param.getPointCode());

    // 排序
    wrapper.orderByDesc(MkMonitorPointData::getCreatedTime);

    IPage<MkMonitorPointData> iPage = wrapper.page(page);

    return iPage.convert(convert::toDTO);
  }
}
