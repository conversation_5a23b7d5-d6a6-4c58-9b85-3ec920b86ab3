package com.yhd.admin.api.domain.sys.convert;

import org.mapstruct.Mapper;

import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.api.domain.sys.vo.UserVO;
import com.yhd.admin.common.domain.dto.UserDTO;
import com.yhd.admin.common.domain.entity.SysUser;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName UserConvert.java @Description TODO
 * @createTime 2020年04月01日 14:56:00
 */
@Mapper(componentModel = "spring")
public interface UserConvert {

    UserDTO toDTO(SysUser sysUser);

    UserVO toVO(UserDTO userDTO);

    SysUser toEntity(UserParam userParam);
}
