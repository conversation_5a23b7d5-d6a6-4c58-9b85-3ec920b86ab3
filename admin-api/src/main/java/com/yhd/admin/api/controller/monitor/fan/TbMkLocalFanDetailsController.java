package com.yhd.admin.api.controller.monitor.fan;

import com.yhd.admin.api.domain.monitor.fan.convert.TbMkLocalFanDetailsConvert;
import com.yhd.admin.api.domain.monitor.fan.dto.TbMkLocalFanDetailsDTO;
import com.yhd.admin.api.domain.monitor.fan.entity.TbMkLocalFanDetails;
import com.yhd.admin.api.domain.monitor.fan.vo.create.TbMkLocalFanDetailsCreateVO;
import com.yhd.admin.api.domain.monitor.fan.vo.page.TbMkLocalFanDetailsPageVO;
import com.yhd.admin.api.domain.monitor.fan.vo.update.TbMkLocalFanDetailsUpdateVO;
import com.yhd.admin.api.service.monitor.fan.TbMkLocalFanDetailsService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 局扇风机详细监控数据 Controller
 * <AUTHOR>
 * @since
 */
@Slf4j
@RestController
@RequestMapping("/monitor/fan/tbMkLocalFanDetails")
public class TbMkLocalFanDetailsController {

    @Resource
    private TbMkLocalFanDetailsService tbMkLocalFanDetailsService;

    @Resource
    private TbMkLocalFanDetailsConvert tbMkLocalFanDetailsConvert;

    /**
     * 分页查询
     *
     * @param pageVO 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<TbMkLocalFanDetailsDTO> page(@RequestBody @Valid TbMkLocalFanDetailsPageVO pageVO) {
        log.debug("分页查询 局扇风机详细监控数据，参数：{}", pageVO);
        return new PageRespJson<>(tbMkLocalFanDetailsService.page(pageVO));
    }

    /**
     * 新增
     *
     * @param createVO 创建参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> create(@RequestBody @Valid TbMkLocalFanDetailsCreateVO createVO) {
        log.debug("新增 局扇风机详细监控数据，参数：{}", createVO);
        return RespJson.success(tbMkLocalFanDetailsService.create(createVO));
    }

    /**
     * 更新
     *
     * @param updateVO 更新参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> update(@RequestBody @Valid TbMkLocalFanDetailsUpdateVO updateVO) {
        log.debug("更新 局扇风机详细监控数据，参数：{}", updateVO);
        return RespJson.success(tbMkLocalFanDetailsService.update(updateVO));
    }


    /**
      * 删除
      *
      * @param id 要删除的记录ID
      * @return 操作结果
      */
    @DeleteMapping("/{id}")
    public RespJson<?> delete(@PathVariable Long id) {
        log.debug("删除 局扇风机详细监控数据，ID：{}", id);
        return RespJson.success(tbMkLocalFanDetailsService.delete(id));
    }

    /**
     * 查看详情
     *
     * @param id 要查看的记录ID
     * @return 详情信息
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<TbMkLocalFanDetailsDTO> getDetail(@PathVariable Long id) {
        log.debug("查看详情 局扇风机详细监控数据，ID：{}", id);
        TbMkLocalFanDetails entity = tbMkLocalFanDetailsService.get(id);
        if (entity == null) {
            return RespJson.failure("未找到对应记录");
        }
        return RespJson.success(tbMkLocalFanDetailsConvert.toDTO(entity));
    }
    /**
     * 批量删除
     *
     * @param ids 要删除的记录ID列表
     * @return 操作结果
     */
    @PostMapping(value = "/batchDelete", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchDelete(@RequestBody List<Long> ids) {
        log.debug("批量删除 局扇风机详细监控数据，IDs：{}", ids);
        return RespJson.success(tbMkLocalFanDetailsService.batchDelete(ids));
    }

    /**
     * 批量新增
     *
     * @param createVOs 新增参数列表
     * @return 新增的记录ID列表
     */
    @PostMapping(value = "/batchAdd", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchCreate(@RequestBody @Valid List<TbMkLocalFanDetailsCreateVO> createVOs) {
        log.debug("批量新增 局扇风机详细监控数据，数量：{}", createVOs.size());
        return RespJson.success(tbMkLocalFanDetailsService.batchCreate(createVOs));
    }

    /**
     * 批量更新
     *
     * @param updateVOs 更新参数列表
     * @return 更新的记录数量
     */
    @PostMapping(value = "/batchModify", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> batchUpdate(@RequestBody @Valid List<TbMkLocalFanDetailsUpdateVO> updateVOs) {
        log.debug("批量更新 局扇风机详细监控数据，数量：{}", updateVOs.size());
        return RespJson.success(tbMkLocalFanDetailsService.batchUpdate(updateVOs));
    }
}
