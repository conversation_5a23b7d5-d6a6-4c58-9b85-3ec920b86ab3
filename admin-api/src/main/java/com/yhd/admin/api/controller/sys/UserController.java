package com.yhd.admin.api.controller.sys;

import java.io.IOException;
import java.io.InputStream;
import java.security.Principal;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.houbb.pinyin.constant.enums.PinyinStyleEnum;
import com.github.houbb.pinyin.util.PinyinHelper;
import com.yhd.admin.api.domain.sys.convert.UserConvert;
import com.yhd.admin.api.domain.sys.query.UpdatePassWdParam;
import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.api.domain.sys.vo.UserVO;
import com.yhd.admin.api.service.sys.MenuService;
import com.yhd.admin.api.service.sys.UserPinSrv;
import com.yhd.admin.api.service.sys.UserService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.dto.UserDTO;
import com.yhd.admin.common.domain.query.BatchParam;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName UserController.java @Description TODO
 * @createTime 2020年04月01日 16:00:00
 */
@RestController
@RequestMapping("/user")
@Slf4j
public class UserController {

    private final UserConvert convert;

    private final UserService userService;

    private final MenuService menuService;

    private final UserPinSrv userPinSrv;

    public UserController(UserConvert convert, UserService userService, MenuService menuService,
        UserPinSrv userPinSrv) {
        this.convert = convert;
        this.userService = userService;
        this.menuService = menuService;
        this.userPinSrv = userPinSrv;
    }

    @PostMapping(value = "/obtainCurrentUser", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<UserVO> currentUser(Principal principal, @RequestBody UserParam userParam) {
        userParam.setUsername(principal.getName());
        UserDTO userDTO = userService.getUserByUsername(principal.getName());

        UserVO retVal = convert.toVO(userDTO);
        retVal.setAuthorities(menuService.queryUserAuthority(userParam));
        log.info("{}", userDTO);
        return RespJson.success(retVal);
    }

    @PostMapping(value = "/getUser", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<UserVO> getUser(@RequestBody UserParam userParam) {
        UserDTO userDTO = userService.getUserByUid(userParam.getUid());
        log.info("{}", userDTO);
        return RespJson.success(convert.toVO(userDTO));
    }

    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<UserVO> pagingQuery(@RequestBody UserParam queryParam) {
        IPage<UserDTO> iPage = userService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson addUser(@RequestBody UserParam addParam, Principal authentication) {
        addParam.setCreatedBy(authentication.getName());
        userService.add(addParam);
        return RespJson.success();
    }

    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modifyUser(@RequestBody UserParam modifyParam, Principal authentication) {
        modifyParam.setUpdatedBy(authentication.getName());
        userService.modify(modifyParam);
        return RespJson.success();
    }

    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeUser(@RequestBody BatchParam batchParam) {
        userService.removeBatch(batchParam);
        return RespJson.success();
    }

    @PostMapping(value = "/checkUserNameIfNotExist", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> checkUserNameIfNotExist(@RequestBody UserParam params) {
        Boolean retVal = userService.checkUsernameIfNotExist(params);
        return RespJson.success(retVal);
    }

    @PostMapping(value = "/getUserNameTxt", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<String> getUserNameTxt(@RequestBody UserParam params) {

        return RespJson
            .success(StringUtils.deleteWhitespace(PinyinHelper.toPinyin(params.getName(), PinyinStyleEnum.NORMAL)));
    }

    @PostMapping(value = "/resetPwd", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> resetPwd(@RequestBody UserParam params) {
        UpdatePassWdParam pwd = new UpdatePassWdParam();
        pwd.setUid(params.getUid());
        pwd.setNewPassword(params.getPassword());
        return RespJson.success(userPinSrv.resetPassWord(params.getUid(), params.getUsername(), params.getPassword()));
    }

    @PostMapping(value = "/exportUser", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<String> exportUser(@RequestBody UserParam params) {
        String filePath = userService.exportUser(params);
        return RespJson.success(filePath);
    }

    @PostMapping(value = "/downloadUserTemplate", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<String> downloadUserTemplate(@RequestBody UserParam params) {
        String filePath = userService.generationUserTemplate();
        return RespJson.success(filePath);
    }

    @PostMapping("/importUser")
    public RespJson upload(@RequestParam("file") MultipartFile file, @RequestParam("isGranted") Boolean isGranted,
        Principal principal) {
        try (InputStream inputStream = file.getInputStream()) {
            return RespJson.success(userService.importUser(inputStream, isGranted, principal.getName()));
        } catch (IOException e) {
            log.error("上传失败{}", e.getMessage());
        }
        return RespJson.failure("");
    }

    @PostMapping(value = "/queryUser", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryUser(@RequestBody UserParam queryParam) {
        List<UserDTO> users = userService.queryUsers(queryParam);
        return RespJson.success(users.stream().map(convert::toVO).collect(Collectors.toList()));
    }
}
