package com.yhd.admin.api.domain.video.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 视频区域表
 *
 * <AUTHOR>
 * @date 2025/8/7 14:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkVideoRegion extends BaseEntity implements Serializable {

  @TableId(type = IdType.AUTO)
  private Long id;
  /** 区域名称 */
  private String regionName;
  /** 父级ID */
  private Long parentId;
  /** 父级名称 */
  private String parentName;
  /** 排序，越小越靠前 */
  private Long sortNum;
}
