package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 应急历史事件记录表
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tb_mk_emergency_history")
public class MkEmergencyHistory extends BaseEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 事件编码
     */
    private String eventCode;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件位置
     */
    private String eventLocation;

    /**
     * 事件类型：1-应急事件，2-演练事件
     */
    private Integer eventType;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 事件等级
     */
    private String eventLevel;

    /**
     * 事件状态：1-进行中，2-已完成，3-已关闭
     */
    private Integer eventStatus;

    /**
     * 事件时间
     */
    private LocalDateTime eventTime;

    /**
     * 处理人
     */
    private String handler;

    /**
     * 事件描述
     */
    private String description;

    /**
     * 处理方案
     */
    private String solution;

    /**
     * 处理结果
     */
    private String result;

    /**
     * 备注
     */
    private String remark;
}
