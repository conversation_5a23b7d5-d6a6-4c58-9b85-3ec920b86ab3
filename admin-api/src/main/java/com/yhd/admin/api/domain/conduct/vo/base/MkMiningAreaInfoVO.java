package com.yhd.admin.api.domain.conduct.vo.base;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采区信息(MkMiningAreaInfo)VO
 *
 * <AUTHOR>
 * @since 2025-07-28 09:25:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkMiningAreaInfoVO extends BaseVO implements Serializable {
    /** Id */
    private Long id;
    /** 所属煤矿 */
    private String orgName;
    /** 组织机构编码 */
    private String orgCode;
    /** 采区名称 */
    private String name;
    /** 煤层名称 */
    private String coalName;
    /** 煤层 Id */
    private Long coalId;
    /** 水平名称 */
    private String levelName;
    /** 水平 id */
    private Long levelId;
    /** 可采储量 */
    private BigDecimal validReserves;
    /** 采高 */
    private BigDecimal miningHeight;
}

