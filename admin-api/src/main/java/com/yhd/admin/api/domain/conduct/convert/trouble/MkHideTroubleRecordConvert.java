package com.yhd.admin.api.domain.conduct.convert.trouble;

import com.yhd.admin.api.domain.conduct.dto.trouble.MkHideTroubleRecordDTO;
import com.yhd.admin.api.domain.conduct.entity.trouble.MkHideTroubleRecord;
import com.yhd.admin.api.domain.conduct.query.trouble.MkHideTroubleRecordParam;
import com.yhd.admin.api.domain.conduct.vo.trouble.MkHideTroubleRecordVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 隐患记录表(MkHideTroubleRecord)Convert
 *
 * <AUTHOR>
 * @since 2025-08-04 10:28:34
 */
@Mapper(componentModel = "spring")
public interface MkHideTroubleRecordConvert {
    MkHideTroubleRecord toEntity(MkHideTroubleRecordParam param);

    MkHideTroubleRecordDTO toDTO(MkHideTroubleRecord entity);

    MkHideTroubleRecordVO toVO(MkHideTroubleRecordDTO dto);

    List<MkHideTroubleRecordDTO> toDTOList(List<MkHideTroubleRecord> entityList);

    List<MkHideTroubleRecordVO> toVOList(List<MkHideTroubleRecordDTO> dtoList);
}

