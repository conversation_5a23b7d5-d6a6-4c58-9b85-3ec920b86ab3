package com.yhd.admin.api.controller.sys;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yhd.admin.api.domain.sys.vo.TimeVO;
import com.yhd.admin.common.domain.RespJson;

@RestController
@RequestMapping("/open")
public class TimerController {

    @RequestMapping(value = "/currentTime", method = {RequestMethod.GET, RequestMethod.POST})
    public RespJson getCurrentDateTime() {
        return RespJson.success(new TimeVO(LocalDate.now(), LocalDateTime.now(), LocalTime.now(),
            LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"))));
    }
}
