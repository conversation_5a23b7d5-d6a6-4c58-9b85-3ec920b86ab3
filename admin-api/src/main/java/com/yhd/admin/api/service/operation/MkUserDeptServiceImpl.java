package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.admin.api.dao.operation.MkUserDeptDao;
import com.yhd.admin.api.domain.operation.convert.MkUserDeptConvert;
import com.yhd.admin.api.domain.operation.dto.MkUserDeptDTO;
import com.yhd.admin.api.domain.operation.dto.MkUserOrgDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserDept;
import com.yhd.admin.api.domain.operation.entity.MkUserOrg;
import com.yhd.admin.api.domain.operation.query.MkUserDeptParam;
import com.yhd.admin.api.domain.operation.query.MkUserOrgParam;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 运维配置中心-部门管理-Service
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */
@Service
public class MkUserDeptServiceImpl extends ServiceImpl<MkUserDeptDao, MkUserDept> implements MkUserDeptService {

    @Resource
    private MkUserDeptConvert convert;

    @Resource
    private MkUserDeptTypeService typeService;

    @Resource
    private MkUserOrgService orgService;

    @Override
    public IPage<MkUserDeptDTO> pagingQuery(MkUserDeptParam queryParam) {
        Page<MkUserDept> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkUserDept> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        //单位
        if (queryParam.getOrgId() != null) {
            queryChain.eq(MkUserDept::getOrgId, queryParam.getOrgId());
        }
        //部门编码
        if (StringUtils.isNotBlank(queryParam.getDeptCode())) {
            queryChain.like(MkUserDept::getDeptCode, queryParam.getDeptCode());
        }
        //部门名称
        if (StringUtils.isNotBlank(queryParam.getDeptName())) {
            queryChain.like(MkUserDept::getDeptName, queryParam.getDeptName());
        }
        //部门类型
        if (queryParam.getDeptTypeId() != null) {
            queryChain.eq(MkUserDept::getDeptTypeId, queryParam.getDeptTypeId());
        }
        //子类型
        if (queryParam.getDownId() != null) {
            queryChain.eq(MkUserDept::getDownId, queryParam.getDownId());
        }
        queryChain.orderByDesc(MkUserDept::getCreatedTime);
        IPage<MkUserDeptDTO> deptPage = queryChain.page(page).convert(convert::toDTO);
        deptPage.getRecords().stream().forEach(item -> {
            //单位名称
            MkUserOrgParam orgParam = new MkUserOrgParam();
            orgParam.setId(item.getOrgId());
            item.setOrgName(orgService.getCurrentDetail(orgParam).getOrgName());
        });
        return deptPage;
    }

    @Override
    public Boolean addOrModify(MkUserDeptParam param) {
        MkUserDept entity = convert.toEntity(param);
        //上级部门
        Optional.ofNullable(entity.getUpId()).ifPresent(upId ->
            entity.setUpName(this.getById(upId).getDeptName()));
        //部门类型
        Optional.ofNullable(entity.getDeptTypeId()).ifPresent(deptTypeId ->
            entity.setDeptTypeName(typeService.getById(deptTypeId).getDeptName()));
        //子类型
        Optional.ofNullable(entity.getDownId()).ifPresent(deptTypeId ->
            entity.setDownName(typeService.getById(deptTypeId).getDeptName()));
        return super.saveOrUpdate(entity);
    }

    @Override
    public MkUserDeptDTO getCurrentDetail(MkUserDeptParam param) {
        MkUserDeptDTO deptDTO = convert.toDTO(super.getById(param.getId()));
        //单位名称
        MkUserOrgParam orgParam = new MkUserOrgParam();
        orgParam.setId(deptDTO.getOrgId());
        deptDTO.setOrgName(orgService.getCurrentDetail(orgParam).getOrgName());
        return deptDTO;
    }

    @Override
    public List<MkUserDeptDTO> getDeptList(MkUserDeptParam param) {
        LambdaQueryWrapper<MkUserDept> wrapper = new LambdaQueryWrapper<>();
        if (param.getOrgId() != null) {
            wrapper.eq(MkUserDept::getOrgId, param.getOrgId());
        }
        //编辑时，上级部门去除本身
        if (param.getId() != null) {
            wrapper.ne(MkUserDept::getId, param.getId());
        }
        List<MkUserDept> depts = this.list(wrapper);
        return convert.toDTOList(depts);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

}
