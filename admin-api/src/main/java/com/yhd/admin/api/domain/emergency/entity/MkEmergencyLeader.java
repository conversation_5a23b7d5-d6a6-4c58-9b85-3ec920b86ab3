package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 应急救援入井领导信息表
 * 
 * <AUTHOR>
 */
@Data
@TableName("tb_mk_emergency_leader")
public class MkEmergencyLeader implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 领导编码
     */
    private String leaderCode;

    /**
     * 领导姓名
     */
    private String leaderName;

    /**
     * 职位
     */
    private String position;

    /**
     * 部门
     */
    private String department;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 是否下井：0-未下井，1-已下井
     */
    private Boolean isUnderground;

    /**
     * 入井时间
     */
    private LocalDateTime entryTime;

    /**
     * 出井时间
     */
    private LocalDateTime exitTime;

    /**
     * 当前位置
     */
    private String currentLocation;

    /**
     * 组织机构编码
     */
    private String orgCode;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}