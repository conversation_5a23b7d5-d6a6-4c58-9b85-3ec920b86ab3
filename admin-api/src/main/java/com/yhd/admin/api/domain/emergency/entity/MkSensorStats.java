package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 综合决策传感器统计表
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_mk_sensor_stats")
public class MkSensorStats {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 统计编码
     */
    private String statsCode;

    /**
     * 统计日期
     */
    private LocalDate statsDate;

    /**
     * CO传感器数量
     */
    private Integer coSensorCount;

    /**
     * 甲烷传感器数量
     */
    private Integer methaneSensorCount;

    /**
     * 接入传感器总数
     */
    private Integer totalSensorCount;

    /**
     * 趋势数据
     */
    private String trendData;

    /**
     * 组织机构编码
     */
    private String orgCode;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}