package com.yhd.admin.api.domain.emergency.vo;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 风险管控清单VO
 *
 * <AUTHOR>
 */
@Data
public class MkSafetyRiskManagementVO {

    /**
     * 清单编码
     */
    private String itemCode;

    /**
     * 风险描述
     */
    private String riskDescription;

    /**
     * 风险等级
     */
    private String riskLevel;

    /**
     * 风险等级颜色
     */
    private String riskLevelColor;

    /**
     * 责任人
     */
    private String responsiblePerson;

    /**
     * 部门
     */
    private String department;

    /**
     * 录入时间
     */
    private LocalDateTime entryTime;

    /**
     * 完成状态
     */
    private String completionStatus;
}