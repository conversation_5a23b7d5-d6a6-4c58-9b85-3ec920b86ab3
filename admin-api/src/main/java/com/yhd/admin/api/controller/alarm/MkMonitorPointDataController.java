package com.yhd.admin.api.controller.alarm;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.alarm.convert.MkMonitorPointDataConvert;
import com.yhd.admin.api.domain.alarm.dto.MkMonitorPointDataDTO;
import com.yhd.admin.api.domain.alarm.query.MkMonitorPointDataParam;
import com.yhd.admin.api.domain.alarm.vo.MkMonitorPointDataVO;
import com.yhd.admin.api.service.alarm.MkMonitorPointDataService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测点数据-控制层
 *
 * <AUTHOR>
 * @date 2025/8/7 17:03
 */
@RestController
@RequestMapping("/monitor/pointData")
public class MkMonitorPointDataController {

  private final MkMonitorPointDataConvert convert;
  private final MkMonitorPointDataService service;

  public MkMonitorPointDataController(
      MkMonitorPointDataConvert convert, MkMonitorPointDataService service) {
    this.convert = convert;
    this.service = service;
  }

  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<MkMonitorPointDataVO> getCurrentDetail(
      @RequestBody MkMonitorPointDataParam param) {
    return RespJson.success(convert.toVO(service.getCurrentDetail(param)));
  }

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MkMonitorPointDataVO> pagingQuery(
      @RequestBody MkMonitorPointDataParam param) {
    IPage<MkMonitorPointDataDTO> iPage = service.pagingQuery(param);
    return new PageRespJson<>(iPage.convert(convert::toVO));
  }
}
