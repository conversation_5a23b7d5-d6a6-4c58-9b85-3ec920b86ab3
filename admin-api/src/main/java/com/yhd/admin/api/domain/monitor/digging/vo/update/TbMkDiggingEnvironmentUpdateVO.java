package com.yhd.admin.api.domain.monitor.digging.vo.update;

import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingEnvironmentCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 掘进机环境参数
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkDiggingEnvironmentUpdateVO extends TbMkDiggingEnvironmentCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
