package com.yhd.admin.api.domain.monitor.nitrogen.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 制氮系统综合信息数据传输对象
 */
@Data
public class TbMkNitrogenDTO {

    /**
     * 系统主键
     */
    private Long systemId;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 制氮控制箱状态: 0-停止, 1-运行, 2-故障, 3-待机
     */
    private Byte controlBoxStatus;

    /**
     * 氮气压力(MPa)
     */
    private BigDecimal nitrogenPressure;

    /**
     * 氮气流量(Nm³/h)
     */
    private BigDecimal nitrogenFlowRate;

    /**
     * 氮气纯度(%)
     */
    private BigDecimal nitrogenPurity;

    /**
     * 空压机详情
     */
    private List<TbMkNitrogenDetailDTO> compressorList;
}
