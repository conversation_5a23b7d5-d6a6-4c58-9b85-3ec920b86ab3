package com.yhd.admin.api.domain.sys.entity;

import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName SysMenu.java @Description TODO
 * @createTime 2020年03月30日 09:57:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysMenu extends BaseEntity implements Serializable, Cloneable {
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 父主键 */
    private Long parentId;

    private String clientId;
    /** 菜单标识 */
    @TableField(value = "`key`")
    private String key;
    /** 路径 */
    private String path;
    /** 菜单名称 */
    private String name;
    /** 国际化 */
    private String locale;
    /** 图标 */
    private String icon;
    /** 隐藏菜单;0:显示1:隐藏 */
    private Boolean hideInMenu;
    /** 隐藏子菜单;0:显示1:隐藏 */
    private Boolean hideChildrenInMenu;
    /** 权限 */
    private String authority;

    /** 类型;0：目录 1：菜单 2：按钮 */
    private String type;
    /** 排序 */
    private Integer orderNum;
    /** 层级 */
    private Integer level;

    /**
     * 后端 URL 权限,逗号拼接。
     */
    private String permission;
}
