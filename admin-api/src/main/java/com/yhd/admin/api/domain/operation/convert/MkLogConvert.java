package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkLogDTO;
import com.yhd.admin.api.domain.sys.entity.SysOpsLog;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 日志DTO转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface MkLogConvert {

    /**
     * 将SysOpsLog转换为MkLogDTO
     */
    @Mapping(source = "opsName", target = "userAccount")
    @Mapping(source = "clientIp", target = "ip")
    @Mapping(source = "url", target = "url")
    @Mapping(source = "method", target = "method")
    @Mapping(source = "createdTime", target = "createdTime")
    MkLogDTO toDTO(SysOpsLog sysOpsLog);

    /**
     * 将SysOpsLog列表转换为MkLogDTO列表
     */
    List<MkLogDTO> toDTOList(List<SysOpsLog> sysOpsLogList);
}
