package com.yhd.admin.api.domain.sys.query;

import java.io.Serializable;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName ParameterParam.java @Description TODO
 * @createTime 2020年05月12日 14:38:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ParameterParam extends QueryParam implements Serializable, Cloneable {
    private String id;
    /** 参数KEY */
    private String paramKey;
    /** 参数值 */
    private String paramVal;
    /** 状态;0否1是 */
    private Boolean isEnable;

    /** 备注 */
    private String remark;
}
