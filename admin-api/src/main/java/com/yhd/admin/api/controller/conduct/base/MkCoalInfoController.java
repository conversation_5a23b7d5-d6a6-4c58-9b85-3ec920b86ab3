package com.yhd.admin.api.controller.conduct.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.base.MkCoalInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.base.MkCoalInfoDTO;
import com.yhd.admin.api.domain.conduct.query.base.MkCoalInfoParam;
import com.yhd.admin.api.domain.conduct.vo.base.MkCoalInfoVO;
import com.yhd.admin.api.service.conduct.base.MkCoalInfoService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 煤层信息控制层
 *
 * <AUTHOR>
 * @since 2025-07-25 10:34:53
 * @module 煤层基础信息
 */
@RestController
@RequestMapping("/conduct/coal")
public class MkCoalInfoController {

    private final MkCoalInfoService mkCoalInfoService;
    private final MkCoalInfoConvert mkCoalInfoConvert;

    public MkCoalInfoController(
        MkCoalInfoService mkCoalInfoService,
        MkCoalInfoConvert mkCoalInfoConvert
    ) {
        this.mkCoalInfoService = mkCoalInfoService;
        this.mkCoalInfoConvert = mkCoalInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkCoalInfoParam param) {
        IPage<MkCoalInfoDTO> page = mkCoalInfoService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkCoalInfoConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkCoalInfoParam param) {
        return RespJson.success(mkCoalInfoConvert.toVOList(mkCoalInfoService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkCoalInfoVO> getCurrentDetails(@RequestBody MkCoalInfoParam param) {
        return RespJson.success(mkCoalInfoConvert.toVO(mkCoalInfoService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkCoalInfoParam param) {
        Boolean retVal = mkCoalInfoService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkCoalInfoParam param) {
        Boolean retVal = mkCoalInfoService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

