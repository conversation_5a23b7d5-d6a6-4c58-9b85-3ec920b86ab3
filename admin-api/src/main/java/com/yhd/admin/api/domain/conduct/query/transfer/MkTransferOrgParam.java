package com.yhd.admin.api.domain.conduct.query.transfer;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 收发文部门表(MkTransferOrg)Param
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkTransferOrgParam extends QueryParam implements Serializable {

    /** 主键列表ids */
    private List<Long> ids;
    /** 收发文部门id */
    private Long id;
    /** 组织机构编码 */
    private String orgCode;
    /** 收发文部门名称 */
    private String orgName;
    /** 收发文部门简称 */
    private String orgSubName;
}

