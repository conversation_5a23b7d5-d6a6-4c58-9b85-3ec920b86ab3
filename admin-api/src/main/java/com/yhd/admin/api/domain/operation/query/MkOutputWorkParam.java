package com.yhd.admin.api.domain.operation.query;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产录入-主要工作录入表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MkOutputWorkParam extends QueryParam implements Cloneable, Serializable {
    /**
     * Id
     */
    private Long id;
	/**
	* 日期
	*/
	private Date outputDate;

	/**
	* 施工单位
	*/
	private String constructionTeam;

	/**
	* 描述
	*/
	private String description;

}
