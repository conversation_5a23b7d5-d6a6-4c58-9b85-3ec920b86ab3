package com.yhd.admin.api.service.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.dispatch.MkDispatchBroadcastDao;
import com.yhd.admin.api.domain.dispatch.convert.MkDispatchBroadcastConvert;
import com.yhd.admin.api.domain.dispatch.dto.MkDispatchBroadcastDTO;
import com.yhd.admin.api.domain.dispatch.dto.MkDispatchBroadcastStructureDTO;
import com.yhd.admin.api.domain.dispatch.entity.MkDispatchBroadcast;
import com.yhd.admin.api.domain.dispatch.query.MkDispatchBroadcastParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.core.collection.CollectionUtil;
import com.yhd.json.JSONUtil;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 智能调度中心-应急广播
 *
 * <AUTHOR>
 * @date 2025/7/31 09:34
 */
@Service
public class MkDispatchBroadcastServiceImpl
    extends ServiceImpl<MkDispatchBroadcastDao, MkDispatchBroadcast>
    implements MkDispatchBroadcastService {

  private final MkDispatchBroadcastConvert convert;

  public MkDispatchBroadcastServiceImpl(MkDispatchBroadcastConvert convert) {
    this.convert = convert;
  }

  /**
   * 新增
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean addOrModify(MkDispatchBroadcastParam param) {
    MkDispatchBroadcast entity = convert.toEntity(param);
    return super.saveOrUpdate(entity);
  }

  /**
   * 删除-根据主键
   *
   * @param param id
   * @return true成功，false失败
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean remove(MkDispatchBroadcastParam param) {
    Long count = this.lambdaQuery().count();
    if (count == 1) {
      throw new BMSException("error", "需要至少保留一条数据，不能全部删除，请检查！");
    }
    return super.removeById(param.getId());
  }

  /**
   * 查询详情信息
   *
   * @param param 主键id or 编号
   * @return 通讯详情信息
   */
  @Override
  public MkDispatchBroadcastDTO getCurrentDetail(MkDispatchBroadcastParam param) {
    MkDispatchBroadcastDTO result = null;
    if (Objects.isNull(param.getId())) {
      throw new BMSException(ExceptionEnum.MISS_PARAM_EXCEPTION);
    }
    MkDispatchBroadcast entity = super.getById(param);
    result = convert.toDTO(entity);
    return result;
  }

  /**
   * 根据条件查询通讯信息分页列表
   *
   * @param param 查询条件
   * @return 通讯信息分页列表
   */
  @Override
  public IPage<MkDispatchBroadcastDTO> pagingQuery(MkDispatchBroadcastParam param) {
    IPage<MkDispatchBroadcast> page = new Page<>(param.getCurrent(), param.getPageSize());
    LambdaQueryChainWrapper<MkDispatchBroadcast> wrapper =
        new LambdaQueryChainWrapper<>(baseMapper);
    // 排序
    wrapper.orderByAsc(MkDispatchBroadcast::getUpdatedTime);
    IPage<MkDispatchBroadcast> iPage = wrapper.page(page);
    return iPage.convert(convert::toDTO);
  }

  @Override
  public MkDispatchBroadcastDTO getData() {
    List<MkDispatchBroadcast> list =
        this.lambdaQuery().orderByDesc(MkDispatchBroadcast::getUpdatedTime).last("limit 1").list();
    MkDispatchBroadcastDTO result = new MkDispatchBroadcastDTO();
    if (CollectionUtil.isEmpty(list)) {
      return result;
    }
    MkDispatchBroadcast entity = list.get(0);

    result = convert.toDTO(entity);
    // 广播状态
    if (StringUtils.isNotBlank(entity.getBroadcastStatus())) {
      result.setBroadcastStatusList(
          JSONUtil.toList(entity.getBroadcastStatus(), MkDispatchBroadcastStructureDTO.class));
    }
    // 文字广播
    if (StringUtils.isNotBlank(entity.getBroadcastWriting())) {
      result.setBroadcastWritingList(
          JSONUtil.toList(entity.getBroadcastWriting(), MkDispatchBroadcastStructureDTO.class));
    }
    // 广播列表
    if (StringUtils.isNotBlank(entity.getBroadcast())) {
      result.setBroadcastList(
          JSONUtil.toList(entity.getBroadcast(), MkDispatchBroadcastStructureDTO.class));
    }

    return result;
  }
}
