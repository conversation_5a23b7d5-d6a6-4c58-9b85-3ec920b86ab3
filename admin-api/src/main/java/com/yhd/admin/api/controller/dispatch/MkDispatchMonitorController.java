package com.yhd.admin.api.controller.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.dispatch.dto.MkDispatchMonitorDTO;
import com.yhd.admin.api.domain.dispatch.query.MkDispatchMonitorParam;
import com.yhd.admin.api.service.dispatch.MkDispatchMonitorService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 智能调度中心-视频监控
 *
 * <AUTHOR>
 * @date 2025/7/31 09:34
 */
@RestController
@RequestMapping("/dispatch/monitor")
public class MkDispatchMonitorController {
  private final MkDispatchMonitorService service;

  public MkDispatchMonitorController(MkDispatchMonitorService service) {
    this.service = service;
  }

  /**
   * 分页查询专家信息
   *
   * @param queryParam 查询参数
   * @return 分页结果
   */
  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MkDispatchMonitorDTO> pagingQuery(
      @RequestBody MkDispatchMonitorParam queryParam) {
    IPage<MkDispatchMonitorDTO> iPage = service.pagingQuery(queryParam);
    return new PageRespJson<>(iPage);
  }

  /**
   * 查看详情
   *
   * @param param 查询参数
   * @return 结果
   */
  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<MkDispatchMonitorDTO> getCurrentDetail(
      @RequestBody MkDispatchMonitorParam param) {
    try {
      return RespJson.success(service.getCurrentDetail(param));
    } catch (Exception e) {
      return RespJson.failure(e.toString());
    }
  }

  /**
   * 新增或编辑
   *
   * @param param 查询参数
   * @return 结果
   */
  @PostMapping(
      value = "/addOrModify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> addOrModify(@RequestBody MkDispatchMonitorParam param) {
    try {
      Boolean retVal = service.addOrModify(param);
      return RespJson.success(retVal);
    } catch (Exception e) {
      return RespJson.failure(e.toString());
    }
  }

  /**
   * 智能调度中心-视频监控
   *
   * @return 结果
   */
  @PostMapping(
      value = "/getData",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<MkDispatchMonitorDTO> getData() {
    try {
      return RespJson.success(service.getData());
    } catch (Exception e) {
      return RespJson.failure(e.toString());
    }
  }
}
