package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkDisasterLevelDTO;
import com.yhd.admin.api.domain.operation.entity.MkDisasterLevel;
import com.yhd.admin.api.domain.operation.query.MkDisasterLevelParam;
import com.yhd.admin.common.domain.query.BatchParam;


/**
 * 算法模型-报警级别表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
public interface MkDisasterLevelService extends IService<MkDisasterLevel> {

    IPage<MkDisasterLevelDTO> pagingQuery(MkDisasterLevelParam queryParam);

     Boolean add(MkDisasterLevelParam param);

    Boolean modify(MkDisasterLevelParam param);

    Boolean removeBatch(BatchParam param);

    MkDisasterLevelDTO getCurrentDetail(MkDisasterLevelParam param);
}
