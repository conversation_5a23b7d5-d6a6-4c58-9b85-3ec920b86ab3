package com.yhd.admin.api.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yhd.admin.api.dao.emergency.MkDustMonitoringDataDao;
import com.yhd.admin.api.dao.emergency.MkDustSensorDao;
import com.yhd.admin.api.domain.emergency.entity.MkDustMonitoringData;
import com.yhd.admin.api.domain.emergency.entity.MkDustSensor;
import com.yhd.admin.api.domain.emergency.query.MkDustSensorParam;
import com.yhd.admin.api.service.emergency.MkDustSensorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkDustSensorServiceImpl.java
 * @Description 粉尘传感器Service实现类
 * @createTime 2025年08月02日 10:00:00
 */
@Slf4j
@Service
public class MkDustSensorServiceImpl implements MkDustSensorService {

    private final MkDustSensorDao dustSensorDao;
    private final MkDustMonitoringDataDao dustMonitoringDataDao;

    public MkDustSensorServiceImpl(MkDustSensorDao dustSensorDao, MkDustMonitoringDataDao dustMonitoringDataDao) {
        this.dustSensorDao = dustSensorDao;
        this.dustMonitoringDataDao = dustMonitoringDataDao;
    }

    @Override
    public IPage<MkDustSensor> pagingQuery(MkDustSensorParam param) {
        log.debug("分页查询粉尘传感器，参数：{}", param);

        Page<MkDustSensor> page = new Page<>(1, 10); // 默认分页参数
        LambdaQueryWrapper<MkDustSensor> queryWrapper = buildQueryWrapper(param);

        return dustSensorDao.selectPage(page, queryWrapper);
    }

    @Override
    public List<MkDustSensor> queryList(MkDustSensorParam param) {
        log.debug("查询粉尘传感器列表，参数：{}", param);

        LambdaQueryWrapper<MkDustSensor> queryWrapper = buildQueryWrapper(param);
        return dustSensorDao.selectList(queryWrapper);
    }

    @Override
    public MkDustSensor getBySensorCode(String sensorCode) {
        log.debug("根据传感器编码查询：{}", sensorCode);

        if (!StringUtils.hasText(sensorCode)) {
            return null;
        }

        LambdaQueryWrapper<MkDustSensor> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkDustSensor::getSensorCode, sensorCode);

        return dustSensorDao.selectOne(queryWrapper);
    }

    @Override
    public List<Map<String, Object>> statisticsByMonitoringType(String orgCode) {
        log.debug("按监测类型统计传感器数量，组织编码：{}", orgCode);

        LambdaQueryWrapper<MkDustSensor> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(orgCode)) {
            queryWrapper.eq(MkDustSensor::getOrgCode, orgCode);
        }

        List<MkDustSensor> list = dustSensorDao.selectList(queryWrapper);

        // 按监测类型分组统计
        Map<String, Long> monitoringStats = list.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getMonitoringType() != null ? item.getMonitoringType() : "未知",
                        Collectors.counting()));

        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<String, Long> entry : monitoringStats.entrySet()) {
            Map<String, Object> stat = new HashMap<>();
            stat.put("monitoringType", entry.getKey());
            stat.put("count", entry.getValue());
            result.add(stat);
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> statisticsByStatus(String orgCode, String monitoringType) {
        log.debug("按状态统计传感器数量，组织编码：{}，监测类型：{}", orgCode, monitoringType);

        LambdaQueryWrapper<MkDustSensor> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(orgCode)) {
            queryWrapper.eq(MkDustSensor::getOrgCode, orgCode);
        }
        if (StringUtils.hasText(monitoringType)) {
            queryWrapper.eq(MkDustSensor::getMonitoringType, monitoringType);
        }

        List<MkDustSensor> list = dustSensorDao.selectList(queryWrapper);

        // 按状态分组统计
        Map<Integer, Long> statusStats = list.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getStatus() != null ? item.getStatus() : 0,
                        Collectors.counting()));

        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<Integer, Long> entry : statusStats.entrySet()) {
            Map<String, Object> stat = new HashMap<>();
            stat.put("status", entry.getKey());
            stat.put("count", entry.getValue());
            result.add(stat);
        }

        return result;
    }

    @Override
    public Map<String, Object> getLatestMonitoringData(String sensorCode) {
        log.debug("获取传感器最新监测数据，传感器编码：{}", sensorCode);

        if (!StringUtils.hasText(sensorCode)) {
            return null;
        }

        LambdaQueryWrapper<MkDustMonitoringData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkDustMonitoringData::getSensorCode, sensorCode);
        queryWrapper.orderByDesc(MkDustMonitoringData::getMeasurementTime);
        queryWrapper.last("LIMIT 1");

        MkDustMonitoringData latestData = dustMonitoringDataDao.selectOne(queryWrapper);

        if (latestData != null) {
            Map<String, Object> result = new HashMap<>();
            result.put("measurement_value", latestData.getMeasurementValue());
            result.put("unit", latestData.getUnit());
            result.put("measurement_time", latestData.getMeasurementTime());
            result.put("data_status", latestData.getDataStatus());
            result.put("alert_level", latestData.getAlertLevel());
            return result;
        }

        return null;
    }

    @Override
    public List<MkDustSensor> queryByMonitoringType(String monitoringType, String orgCode) {
        log.debug("根据监测类型查询传感器列表，监测类型：{}，组织编码：{}", monitoringType, orgCode);

        LambdaQueryWrapper<MkDustSensor> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(monitoringType)) {
            queryWrapper.eq(MkDustSensor::getMonitoringType, monitoringType);
        }

        if (StringUtils.hasText(orgCode)) {
            queryWrapper.eq(MkDustSensor::getOrgCode, orgCode);
        }

        // 只查询启用的传感器
        queryWrapper.eq(MkDustSensor::getIsActive, true);

        // 按更新时间倒序
        queryWrapper.orderByDesc(MkDustSensor::getUpdatedTime);

        return dustSensorDao.selectList(queryWrapper);
    }

    /**
     * 构建查询条件
     *
     * @param param 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<MkDustSensor> buildQueryWrapper(MkDustSensorParam param) {
        LambdaQueryWrapper<MkDustSensor> queryWrapper = new LambdaQueryWrapper<>();

        if (param == null) {
            return queryWrapper;
        }

        // 传感器编码
        if (StringUtils.hasText(param.getSensorCode())) {
            queryWrapper.eq(MkDustSensor::getSensorCode, param.getSensorCode());
        }

        // 传感器名称（模糊搜索）
        if (StringUtils.hasText(param.getSensorName())) {
            queryWrapper.like(MkDustSensor::getSensorName, param.getSensorName());
        }

        // 传感器类型
        if (StringUtils.hasText(param.getSensorType())) {
            queryWrapper.eq(MkDustSensor::getSensorType, param.getSensorType());
        }

        // 监测类型
        if (StringUtils.hasText(param.getMonitoringType())) {
            queryWrapper.eq(MkDustSensor::getMonitoringType, param.getMonitoringType());
        }

        // 威胁类别
        if (StringUtils.hasText(param.getThreatCategory())) {
            queryWrapper.eq(MkDustSensor::getThreatCategory, param.getThreatCategory());
        }

        // 所属区域编码
        if (StringUtils.hasText(param.getAreaCode())) {
            queryWrapper.eq(MkDustSensor::getAreaCode, param.getAreaCode());
        }

        // 所属区域名称
        if (StringUtils.hasText(param.getAreaName())) {
            queryWrapper.like(MkDustSensor::getAreaName, param.getAreaName());
        }

        // 安装位置
        if (StringUtils.hasText(param.getLocation())) {
            queryWrapper.like(MkDustSensor::getLocation, param.getLocation());
        }

        // 状态
        if (param.getStatus() != null) {
            queryWrapper.eq(MkDustSensor::getStatus, param.getStatus());
        }

        // 是否启用
        if (param.getIsActive() != null) {
            queryWrapper.eq(MkDustSensor::getIsActive, param.getIsActive());
        }

        // 组织编码
        if (StringUtils.hasText(param.getOrgCode())) {
            queryWrapper.eq(MkDustSensor::getOrgCode, param.getOrgCode());
        }

        // 关键词搜索（传感器名称或位置信息）
        if (StringUtils.hasText(param.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(MkDustSensor::getSensorName, param.getKeyword())
                    .or()
                    .like(MkDustSensor::getLocation, param.getKeyword()));
        }

        // 按更新时间倒序
        queryWrapper.orderByDesc(MkDustSensor::getUpdatedTime);

        return queryWrapper;
    }
}