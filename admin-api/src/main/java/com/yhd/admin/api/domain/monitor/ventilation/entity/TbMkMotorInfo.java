package com.yhd.admin.api.domain.monitor.ventilation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.*;

/**
 * 电机基本信息
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_mk_motor_info")
public class TbMkMotorInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联通风机基本信息表ID
     */
    @TableField("ventilation_id")
    private Long ventilationId;

    /**
     * 电机名称
     */
    @TableField("motor_name")
    private String motorName;

    /**
     * 电机编号
     */
    @TableField("motor_number")
    private String motorNumber;

    /**
     * 备注信息
     */
    @TableField("remarks")
    private String remarks;
}
