package com.yhd.admin.api.domain.monitor.ventilation.vo;

import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkVentilationOperationCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通风机整体运行参数
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkVentilationOperationVO extends TbMkVentilationOperationCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
