package com.yhd.admin.api.service.monitor.digging;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.monitor.digging.dto.TbMkDiggingInfoDTO;
import com.yhd.admin.api.domain.monitor.digging.entity.TbMkDiggingInfo;
import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingInfoCreateVO;
import com.yhd.admin.api.domain.monitor.digging.vo.page.TbMkDiggingInfoPageVO;
import com.yhd.admin.api.domain.monitor.digging.vo.update.TbMkDiggingInfoUpdateVO;

import java.util.List;

/**
 * 掘进机基本信息 服务类
 *
 * <AUTHOR>
 * @since
 */
public interface TbMkDiggingInfoService  extends IService<TbMkDiggingInfo> {

    /**
     * 新建
     *
     * @param createVO 新建VO
     * @return id
     */
    Long create(TbMkDiggingInfoCreateVO createVO);

    /**
     * 更新
     *
     * @param updateVO 更新VO
     */
    int update(TbMkDiggingInfoUpdateVO updateVO);

    /**
     * 删除
     *
     * @param id id
     */
    int delete(Long id);

    /**
     * 查询单个
     *
     * @param id id
     * @return po
     */
    TbMkDiggingInfo get(Long id);

    /**
     * 分页查询
     *
     * @param pageVO 分页VO
     * @return pageResult
     */
    IPage<TbMkDiggingInfoDTO> page(TbMkDiggingInfoPageVO pageVO);

    /**
      * 批量删除
      *
      * @param ids 要删除的记录ID列表
      * @return 受影响的行数
      */
    int batchDelete(List<Long> ids);

    /**
     * 批量新增
     *
     * @param createVOs 新增VO列表
     * @return 新增的记录ID列表
     */
    int batchCreate(List<TbMkDiggingInfoCreateVO> createVOs);

    /**
     * 批量更新
     *
     * @param updateVOs 更新VO列表
     * @return 受影响的行数
     */
    int batchUpdate(List<TbMkDiggingInfoUpdateVO> updateVOs);
}
