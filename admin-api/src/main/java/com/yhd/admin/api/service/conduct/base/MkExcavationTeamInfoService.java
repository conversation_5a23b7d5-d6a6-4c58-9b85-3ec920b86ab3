package com.yhd.admin.api.service.conduct.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.base.MkExcavationTeamInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkExcavationTeamInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkExcavationTeamInfoParam;

import java.util.List;

/**
 * 掘进队信息服务接口
 *
 * <AUTHOR>
 * @since 2025-07-25 10:32:52
 */
public interface MkExcavationTeamInfoService extends IService<MkExcavationTeamInfo> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkExcavationTeamInfoDTO> pagingQuery(MkExcavationTeamInfoParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkExcavationTeamInfoDTO> queryList(MkExcavationTeamInfoParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkExcavationTeamInfoParam param);

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    MkExcavationTeamInfoDTO getCurrentDetails(MkExcavationTeamInfoParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkExcavationTeamInfoParam param);

}
