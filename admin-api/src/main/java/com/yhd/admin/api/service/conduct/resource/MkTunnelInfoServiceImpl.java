package com.yhd.admin.api.service.conduct.resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.resource.MkTunnelInfoDao;
import com.yhd.admin.api.domain.conduct.convert.resource.MkTunnelInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.resource.MkTunnelInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.resource.MkTunnelInfo;
import com.yhd.admin.api.domain.conduct.query.resource.MkTunnelInfoParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 巷道信息服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-28 08:21:47
 */
@Service
public class MkTunnelInfoServiceImpl extends ServiceImpl<MkTunnelInfoDao, MkTunnelInfo> implements MkTunnelInfoService {

    private final MkTunnelInfoConvert mkTunnelInfoConvert;

    public MkTunnelInfoServiceImpl(MkTunnelInfoConvert mkTunnelInfoConvert) {
        this.mkTunnelInfoConvert = mkTunnelInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkTunnelInfoDTO> pagingQuery(MkTunnelInfoParam param) {
        IPage<MkTunnelInfo> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkTunnelInfo> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.eq(StringUtils.isNotBlank(param.getStatusCode()), MkTunnelInfo::getStatusCode, param.getStatusCode());
        wrapper.eq(StringUtils.isNotBlank(param.getName()), MkTunnelInfo::getName, param.getName());
        return wrapper.page(page).convert(mkTunnelInfoConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkTunnelInfoDTO> queryList(MkTunnelInfoParam param) {
        LambdaQueryWrapper<MkTunnelInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(param.getName()), MkTunnelInfo::getName, param.getName());
        return mkTunnelInfoConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkTunnelInfoParam param) {
        MkTunnelInfo entity = mkTunnelInfoConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkTunnelInfoDTO getCurrentDetails(MkTunnelInfoParam param) {
        return mkTunnelInfoConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkTunnelInfoParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }
}
