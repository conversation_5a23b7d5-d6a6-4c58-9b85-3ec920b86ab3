package com.yhd.admin.api.domain.emergency.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.yhd.admin.api.domain.emergency.dto.MkEmergencySnapshotDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencySnapshot;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySnapshotParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencySnapshotVO;

/**
 * 应急快照转换器
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Mapper(componentModel = "spring")
public interface MkEmergencySnapshotConvert {

    /**
     * 实体转DTO
     *
     * @param entity 实体
     * @return DTO
     */
    MkEmergencySnapshotDTO toDTO(MkEmergencySnapshot entity);

    /**
     * 实体列表转DTO列表
     *
     * @param entityList 实体列表
     * @return DTO列表
     */
    List<MkEmergencySnapshotDTO> toDTO(List<MkEmergencySnapshot> entityList);

    /**
     * DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    MkEmergencySnapshot toEntity(MkEmergencySnapshotDTO dto);

    /**
     * 参数转实体
     *
     * @param param 参数
     * @return 实体
     */
    MkEmergencySnapshot toEntity(MkEmergencySnapshotParam param);

    /**
     * DTO转VO
     *
     * @param dto DTO
     * @return VO
     */
    MkEmergencySnapshotVO toVO(MkEmergencySnapshotDTO dto);

    /**
     * DTO列表转VO列表
     *
     * @param dtoList DTO列表
     * @return VO列表
     */
    List<MkEmergencySnapshotVO> toVO(List<MkEmergencySnapshotDTO> dtoList);
}
