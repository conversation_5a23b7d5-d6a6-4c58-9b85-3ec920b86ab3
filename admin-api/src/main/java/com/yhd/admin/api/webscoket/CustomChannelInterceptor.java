package com.yhd.admin.api.webscoket;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.stereotype.Component;

import java.security.Principal;
import java.text.MessageFormat;
import java.util.Objects;

@Component
public class CustomChannelInterceptor implements ChannelInterceptor {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * Invoked before the Message is actually sent to the channel.
     * This allows for modification of the Message if necessary.
     * If this method returns {@code null} then the actual
     * send invocation will not occur.
     *
     * @param message
     * @param channel
     */
    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
        if (StompCommand.CONNECT.equals(accessor.getCommand())) {
            accessor.setUser(new Principal() {
                @Override
                public String getName() {
                    return Objects.requireNonNull(accessor.getNativeHeader("username")).get(0);
                }
            });
            logger.debug(MessageFormat.format("用户{0}-{1},连接成功", Objects.requireNonNull(accessor.getUser()).getName(), accessor.getSessionId()));

        }
        return message;
    }

    /**
     * Invoked after the completion of a send regardless of any exception that
     * have been raised thus allowing for proper resource cleanup.
     * <p>Note that this will be invoked only if {@link #preSend} successfully
     * completed and returned a Message, i.e. it did not return {@code null}.
     *
     * @param message
     * @param channel
     * @param sent
     * @param ex
     * @since 4.1
     */
    @Override
    public void afterSendCompletion(Message<?> message, MessageChannel channel, boolean sent, Exception ex) {
        StompHeaderAccessor accessor = StompHeaderAccessor.wrap(message);
        StompCommand command = accessor.getCommand();

        //用户已经断开连接
        if (StompCommand.DISCONNECT.equals(command)) {
            String user = "";
            Principal principal = accessor.getUser();
            if (principal != null && StringUtils.isNoneBlank(principal.getName())) {
                user = principal.getName();
            } else {
                user = accessor.getSessionId();
            }
            if (logger.isDebugEnabled()) {
                logger.debug(MessageFormat.format("用户{0}-{1}的WebSocket连接已经断开", user, accessor.getSessionId()));
            }
        }

    }
}
