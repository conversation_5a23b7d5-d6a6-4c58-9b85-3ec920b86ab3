package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 应急救援下井人员统计表
 * 
 * <AUTHOR>
 */
@Data
@TableName("tb_mk_emergency_personnel_stats")
public class MkEmergencyPersonnelStats implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 统计编码
     */
    private String statsCode;

    /**
     * 统计日期
     */
    private LocalDate statsDate;

    /**
     * 分类类型：TEAM-区队类型，AREA-区域类型
     */
    private String categoryType;

    /**
     * 分类名称：搬家公司、机电队、运转队、综掘队、车辆等
     */
    private String categoryName;

    /**
     * 人员数量
     */
    private Integer personnelCount;

    /**
     * 总人数
     */
    private Integer totalCount;

    /**
     * 占比百分比
     */
    private BigDecimal percentage;

    /**
     * 显示颜色代码
     */
    private String colorCode;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 组织机构编码
     */
    private String orgCode;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}