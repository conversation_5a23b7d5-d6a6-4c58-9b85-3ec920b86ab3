package com.yhd.admin.api.domain.operation.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 应急演练-演练计划表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MkDrillPlanDTO extends BaseDTO implements Cloneable, Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * Id
     */
    private Long id;

    /**
     * 演练名称
     */
    private String drillName;

    /**
     * 演练方式code
     */
    private String method;

    /**
     * 计划演练时间
     */
    private LocalDateTime planTime;

    /**
     * 负责人账号
     */
    private String leaderNo;

    /**
     * 负责人名称
     */
    private String leaderName;

    /**
     * 演练类型code
     */
    private String drillTypeCode;

    /**
     * 演练类型name
     */
    private String drillTypeName;

    /**
     * 演练预案
     */
    private Long contingencyPlanId;

    /**
     * 演练地点
     */
    private String location;

    /**
     * 演练脚本文件路径
     */
    private String scriptFile;

    /**
     * 参加部门集合
     */
    private String deptName;

    /**
     * 演练目的
     */
    private String purpose;

    /**
     * 状态
     */
    private Integer status;
} 