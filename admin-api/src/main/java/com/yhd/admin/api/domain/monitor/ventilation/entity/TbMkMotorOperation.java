package com.yhd.admin.api.domain.monitor.ventilation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 电机运行参数
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_mk_motor_operation")
public class TbMkMotorOperation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联电机基本信息表ID
     */
    @TableField("motor_id")
    private Long motorId;

    /**
     * 电压Uab(KV)
     */
    @TableField("voltage_uab")
    private BigDecimal voltageUab;

    /**
     * 电压Ubc(KV)
     */
    @TableField("voltage_ubc")
    private BigDecimal voltageUbc;

    /**
     * 电压Uca(KV)
     */
    @TableField("voltage_uca")
    private BigDecimal voltageUca;

    /**
     * 电流Ia(A)
     */
    @TableField("current_ia")
    private BigDecimal currentIa;

    /**
     * 电流Ib(A)
     */
    @TableField("current_ib")
    private BigDecimal currentIb;

    /**
     * 电流Ic(A)
     */
    @TableField("current_ic")
    private BigDecimal currentIc;

    /**
     * 水平振动(mm/s)
     */
    @TableField("horizontal_vibration")
    private BigDecimal horizontalVibration;

    /**
     * 垂直振动(mm/s)
     */
    @TableField("vertical_vibration")
    private BigDecimal verticalVibration;

    /**
     * 前轴温度(°C)
     */
    @TableField("front_bearing_temperature")
    private BigDecimal frontBearingTemperature;

    /**
     * 后轴温度(°C)
     */
    @TableField("rear_bearing_temperature")
    private BigDecimal rearBearingTemperature;

    /**
     * 母线电压(V)
     */
    @TableField("bus_voltage")
    private BigDecimal busVoltage;
}
