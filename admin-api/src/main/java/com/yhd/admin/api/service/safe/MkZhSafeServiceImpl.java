package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.safe.MkZhSafeDao;
import com.yhd.admin.api.domain.safe.convert.MkZhSafeConvert;
import com.yhd.admin.api.domain.safe.dto.MkZhSafeDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhSafe;
import com.yhd.admin.api.domain.safe.query.MkZhSafeParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;


@Service
public class MkZhSafeServiceImpl extends ServiceImpl<MkZhSafeDao, MkZhSafe> implements MkZhSafeService {

    private final MkZhSafeConvert convert;

    public MkZhSafeServiceImpl(MkZhSafeConvert convert) {
        this.convert = convert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addOrModify(MkZhSafeParam param) {
        MkZhSafe entity = convert.toEntity(param);
        return super.saveOrUpdate(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(MkZhSafeParam param) {
        return super.removeById(param.getId());
    }

    @Override
    public MkZhSafeDTO getCurrentDetail(MkZhSafeParam param) {
        MkZhSafeDTO result;
        if (Objects.isNull(param.getId())) {
            throw new BMSException(ExceptionEnum.MISS_PARAM_EXCEPTION);
        }
        MkZhSafe entity = super.getById(param);
        result = convert.toDTO(entity);
        return result;
    }

    @Override
    public IPage<MkZhSafeDTO> pagingQuery(MkZhSafeParam param) {
        IPage<MkZhSafe> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkZhSafe> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        // 排序
        wrapper.orderByAsc(MkZhSafe::getUpdatedTime);
        IPage<MkZhSafe> iPage = wrapper.page(page);
        return iPage.convert(convert::toDTO);
    }

    @Override
    public MkZhSafeDTO getData() {
        LambdaQueryChainWrapper<MkZhSafe> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return convert.toDTO(wrapper.list().get(0));
    }
}
