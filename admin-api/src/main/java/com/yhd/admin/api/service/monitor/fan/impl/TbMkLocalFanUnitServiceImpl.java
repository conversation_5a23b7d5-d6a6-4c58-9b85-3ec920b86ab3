package com.yhd.admin.api.service.monitor.fan.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.fan.TbMkLocalFanUnitDao;
import com.yhd.admin.api.domain.monitor.fan.convert.TbMkLocalFanUnitConvert;
import com.yhd.admin.api.domain.monitor.fan.dto.TbMkLocalFanUnitDTO;
import com.yhd.admin.api.domain.monitor.fan.entity.TbMkLocalFanUnit;
import com.yhd.admin.api.domain.monitor.fan.vo.create.TbMkLocalFanUnitCreateVO;
import com.yhd.admin.api.domain.monitor.fan.vo.page.TbMkLocalFanUnitPageVO;
import com.yhd.admin.api.domain.monitor.fan.vo.update.TbMkLocalFanUnitUpdateVO;
import com.yhd.admin.api.service.monitor.fan.TbMkLocalFanUnitService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 局扇风机单元 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkLocalFanUnitServiceImpl extends ServiceImpl<TbMkLocalFanUnitDao, TbMkLocalFanUnit> implements TbMkLocalFanUnitService {

    @Resource
    private TbMkLocalFanUnitConvert tbMkLocalFanUnitConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkLocalFanUnitCreateVO createVO) {
        TbMkLocalFanUnit tbMkLocalFanUnit = tbMkLocalFanUnitConvert.convert(createVO);
        baseMapper.insert(tbMkLocalFanUnit);
        return tbMkLocalFanUnit.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkLocalFanUnitUpdateVO updateVO) {
        TbMkLocalFanUnit tbMkLocalFanUnit = tbMkLocalFanUnitConvert.convert(updateVO);
        return baseMapper.updateById(tbMkLocalFanUnit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkLocalFanUnit get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkLocalFanUnitDTO> page(TbMkLocalFanUnitPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkLocalFanUnit> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkLocalFanUnit> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.eq(param.getSystemId() != null, TbMkLocalFanUnit::getSystemId, param.getSystemId());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getFanNumber()), TbMkLocalFanUnit::getFanNumber, param.getFanNumber());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getModel()), TbMkLocalFanUnit::getModel, param.getModel());
        queryChainWrapper.eq(param.getStatus() != null, TbMkLocalFanUnit::getStatus, param.getStatus());
        queryChainWrapper.orderByDesc(TbMkLocalFanUnit::getId);
        return queryChainWrapper.page(iPage).convert(tbMkLocalFanUnitConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkLocalFanUnitCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 局扇风机单元：传入的列表为空");
            return 0;
        }
        List<TbMkLocalFanUnit> entities = createVOs.stream()
                .map(tbMkLocalFanUnitConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 局扇风机单元 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkLocalFanUnitUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 局扇风机单元：传入的列表为空");
            return 0;
        }
        List<TbMkLocalFanUnit> entities = updateVOs.stream()
                .map(tbMkLocalFanUnitConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 局扇风机单元：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 局扇风机单元 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
