package com.yhd.admin.api.domain.operation.vo;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 算法模型-报警次级表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MkDisasterGradeVO extends BaseVO implements Cloneable, Serializable {
    /**
     * Id
     */
    private Long id;

    /**
     * 报警次级code
     */
    private String gradeCode;

    /**
     * 报警次级name
     */
    private String gradeName;

    /**
     * 初始值
     */
    private Integer initialValue;

    /**
     * 结束值
     */
    private Integer endValue;

    /**
     * 监测名称
     */
    private String monitorName;

    /**
     * 类型
     */
    private String type;

}
