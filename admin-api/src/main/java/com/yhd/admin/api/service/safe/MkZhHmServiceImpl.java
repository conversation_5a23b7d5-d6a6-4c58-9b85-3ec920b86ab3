package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.safe.MkZhHmDao;
import com.yhd.admin.api.domain.safe.convert.MkZhHmConvert;
import com.yhd.admin.api.domain.safe.dto.MkZhHmDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhHm;
import com.yhd.admin.api.domain.safe.query.MkZhHmParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;


@Service
public class MkZhHmServiceImpl extends ServiceImpl<MkZhHmDao, MkZhHm> implements MkZhHmService {

    private final MkZhHmConvert convert;

    public MkZhHmServiceImpl(MkZhHmConvert convert) {
        this.convert = convert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addOrModify(MkZhHmParam param) {
        MkZhHm entity = convert.toEntity(param);
        return super.saveOrUpdate(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(MkZhHmParam param) {
        return super.removeById(param.getId());
    }

    @Override
    public MkZhHmDTO getCurrentDetail(MkZhHmParam param) {
        MkZhHmDTO result;
        if (Objects.isNull(param.getId())) {
            throw new BMSException(ExceptionEnum.MISS_PARAM_EXCEPTION);
        }
        MkZhHm entity = super.getById(param);
        result = convert.toDTO(entity);
        return result;
    }

    @Override
    public IPage<MkZhHmDTO> pagingQuery(MkZhHmParam param) {
        IPage<MkZhHm> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkZhHm> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        // 排序
        wrapper.orderByAsc(MkZhHm::getUpdatedTime);
        IPage<MkZhHm> iPage = wrapper.page(page);
        return iPage.convert(convert::toDTO);
    }

    @Override
    public MkZhHmDTO getData() {
        LambdaQueryChainWrapper<MkZhHm> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return convert.toDTO(wrapper.list().get(0));
    }
}
