package com.yhd.admin.api.service.sys;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.sys.dto.AppDTO;
import com.yhd.admin.api.domain.sys.query.AppParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * <AUTHOR>
 * @date 2024/9/6
 */
public interface AppSrv {

    IPage<AppDTO> pagingQuery(AppParam queryParam);

    Boolean add(AppParam queryParam);

    Boolean modify(AppParam queryParam);

    AppDTO getCurrentDetail(AppParam queryParam);

    Boolean removeBatch(BatchParam param);
}
