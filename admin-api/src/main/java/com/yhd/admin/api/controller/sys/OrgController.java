package com.yhd.admin.api.controller.sys;

import java.util.List;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yhd.admin.api.domain.sys.convert.OrgConvert;
import com.yhd.admin.api.domain.sys.dto.OrgDTO;
import com.yhd.admin.api.domain.sys.query.OrgParam;
import com.yhd.admin.api.service.sys.OrgService;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/1/9 15:13
 */

@RestController
@RequestMapping(value = "/org")
@Slf4j
public class OrgController {
    private final OrgService orgService;
    private final OrgConvert orgConvert;

    private final ObjectMapper objectMapper;

    public OrgController(OrgService orgService, OrgConvert orgConvert, ObjectMapper objectMapper) {
        this.orgService = orgService;
        this.orgConvert = orgConvert;
        this.objectMapper = objectMapper;
    }

    @PostMapping(value = "/queryList", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson pagingQuery(@RequestBody OrgParam orgParam) {

        List<OrgDTO> orgDTOList = orgService.queryList(orgParam.getId());
        return RespJson.success(orgConvert.toVO(orgDTOList));
    }

    @PostMapping(value = "/addOrModify", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson addOrModify(@RequestBody OrgParam orgParam) {
        Boolean retVal = orgService.addOrModifyOrg(orgParam);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam batchParam) {
        Boolean retVal = orgService.removeBatch(batchParam.getId());
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(value = "/currentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson currentDetail(@RequestBody OrgParam orgParam) {
        OrgDTO orgDTO = orgService.currentDetail(orgParam.getId());
        return RespJson.success(orgConvert.toVO(orgDTO));
    }
}
