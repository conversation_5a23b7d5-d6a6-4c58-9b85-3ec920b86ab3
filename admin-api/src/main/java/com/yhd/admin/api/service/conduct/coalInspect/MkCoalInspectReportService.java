package com.yhd.admin.api.service.conduct.coalInspect;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.coalInspect.MkCoalInspectReportDTO;
import com.yhd.admin.api.domain.conduct.entity.coalInspect.MkCoalBlock;
import com.yhd.admin.api.domain.conduct.entity.coalInspect.MkCoalData;
import com.yhd.admin.api.domain.conduct.entity.coalInspect.MkCoalInspectReport;
import com.yhd.admin.api.domain.conduct.query.coalInspect.MkCoalInspectReportParam;

import java.util.List;

/**
 * 煤质检验报告服务接口
 *
 * <AUTHOR>
 * @since 2025-08-05 09:12:06
 */
public interface MkCoalInspectReportService extends IService<MkCoalInspectReport> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkCoalInspectReportDTO> pagingQuery(MkCoalInspectReportParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkCoalInspectReportDTO> queryList(MkCoalInspectReportParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkCoalInspectReportParam param);

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    MkCoalInspectReportDTO getCurrentDetails(MkCoalInspectReportParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkCoalInspectReportParam param);

    /**
     * 导出文件
     *
     * @return 文件地址
     */
    String export(MkCoalInspectReportParam param);

    /**
     * 获取煤质数据
     * @param param 查询参数（包含煤种名称）
     * @return 煤质数据（包含10天数据曲线）
     */
    MkCoalData getData(MkCoalInspectReportParam param);

    /**
     * 获取出块率曲线数据
     * 生成包含最近10天、30天和12个月的出块率数据曲线，并计算各种平均值
     *
     * @return MkCoalBlock 包含三类出块率曲线数据及各种平均值的对象
     */
    MkCoalBlock getDataOfBlock();
}
