package com.yhd.admin.api.service.conduct.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.base.MkMineInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkMineInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkMineInfoParam;

import java.util.List;

/**
 * 矿井基础信息表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-25 10:32:53
 */
public interface MkMineInfoService extends IService<MkMineInfo> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkMineInfoDTO> pagingQuery(MkMineInfoParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkMineInfoDTO> queryList(MkMineInfoParam param);

    /**
     * 新增或修改数据
     *
     * @param param 数据
     * @return 是否成功
     */
    Boolean addOrModify(MkMineInfoParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    MkMineInfoDTO getCurrentDetails(MkMineInfoParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkMineInfoParam param);

}
