package com.yhd.admin.api.service.monitor.digging;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.monitor.digging.dto.TbMkDiggingOperationDTO;
import com.yhd.admin.api.domain.monitor.digging.entity.TbMkDiggingOperation;
import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingOperationCreateVO;
import com.yhd.admin.api.domain.monitor.digging.vo.page.TbMkDiggingOperationPageVO;
import com.yhd.admin.api.domain.monitor.digging.vo.update.TbMkDiggingOperationUpdateVO;

import java.util.List;

/**
 * 掘进机运行参数 服务类
 *
 * <AUTHOR>
 * @since
 */
public interface TbMkDiggingOperationService  extends IService<TbMkDiggingOperation> {

    /**
     * 新建
     *
     * @param createVO 新建VO
     * @return id
     */
    Long create(TbMkDiggingOperationCreateVO createVO);

    /**
     * 更新
     *
     * @param updateVO 更新VO
     */
    int update(TbMkDiggingOperationUpdateVO updateVO);

    /**
     * 删除
     *
     * @param id id
     */
    int delete(Long id);

    /**
     * 查询单个
     *
     * @param id id
     * @return po
     */
    TbMkDiggingOperation get(Long id);

    /**
     * 分页查询
     *
     * @param pageVO 分页VO
     * @return pageResult
     */
    IPage<TbMkDiggingOperationDTO> page(TbMkDiggingOperationPageVO pageVO);

    /**
      * 批量删除
      *
      * @param ids 要删除的记录ID列表
      * @return 受影响的行数
      */
    int batchDelete(List<Long> ids);

    /**
     * 批量新增
     *
     * @param createVOs 新增VO列表
     * @return 新增的记录ID列表
     */
    int batchCreate(List<TbMkDiggingOperationCreateVO> createVOs);

    /**
     * 批量更新
     *
     * @param updateVOs 更新VO列表
     * @return 受影响的行数
     */
    int batchUpdate(List<TbMkDiggingOperationUpdateVO> updateVOs);
}
