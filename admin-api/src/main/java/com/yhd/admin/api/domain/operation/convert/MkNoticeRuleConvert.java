package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkNoticeRuleDTO;
import com.yhd.admin.api.domain.operation.entity.MkNoticeRule;
import com.yhd.admin.api.domain.operation.query.MkNoticeRuleParam;
import com.yhd.admin.api.domain.operation.vo.MkNoticeRuleVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 消息下发规则表(MkNoticeRule)Convert
 *
 * <AUTHOR>
 * @since 2025-08-08 09:12:47
 */
@Mapper(componentModel = "spring")
public interface MkNoticeRuleConvert {
    MkNoticeRule toEntity(MkNoticeRuleParam param);

    MkNoticeRuleDTO toDTO(MkNoticeRule entity);

    MkNoticeRuleVO toVO(MkNoticeRuleDTO dto);

    List<MkNoticeRuleDTO> toDTOList(List<MkNoticeRule> entityList);

    List<MkNoticeRuleVO> toVOList(List<MkNoticeRuleDTO> dtoList);
}

