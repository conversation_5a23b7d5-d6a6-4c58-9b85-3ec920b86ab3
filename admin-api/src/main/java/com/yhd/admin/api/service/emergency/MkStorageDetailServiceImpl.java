package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.emergency.MkStorageDetailDao;
import com.yhd.admin.api.domain.emergency.convert.MkStorageDetailConvert;
import com.yhd.admin.api.domain.emergency.dto.MkStorageDetailDTO;
import com.yhd.admin.api.domain.emergency.entity.MkStorageDetail;
import com.yhd.admin.api.domain.emergency.query.MkStorageDetailParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 应急物资记录表 Service实现类
 * <AUTHOR>
 */
@Service
public class MkStorageDetailServiceImpl extends ServiceImpl<MkStorageDetailDao, MkStorageDetail> 
        implements MkStorageDetailService {

    @Resource
    private MkStorageDetailConvert convert;

    @Override
    public IPage<MkStorageDetailDTO> pagingQuery(MkStorageDetailParam param) {
        IPage<MkStorageDetail> iPage = new Page<>(param.getCurrent(), param.getPageSize());
        
        LambdaQueryChainWrapper<MkStorageDetail> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        
        // 组织编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getOrgCode()), 
                MkStorageDetail::getOrgCode, param.getOrgCode());
        
        // 物资系统编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getMaterialSystemCode()), 
                MkStorageDetail::getMaterialSystemCode, param.getMaterialSystemCode());
        
        // 物资编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getMaterialCode()), 
                MkStorageDetail::getMaterialCode, param.getMaterialCode());
        
        // 仓库标识精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getStorageId()), 
                MkStorageDetail::getStorageId, param.getStorageId());
        
        // 物资名称模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getMaterialName()), 
                MkStorageDetail::getMaterialName, param.getMaterialName());
        
        // 物资大类模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getCatalogType()), 
                MkStorageDetail::getCatalogType, param.getCatalogType());
        
        // 物资分类模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getMaterialType()), 
                MkStorageDetail::getMaterialType, param.getMaterialType());
        
        // 规格型号模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getMaterialModel()), 
                MkStorageDetail::getMaterialModel, param.getMaterialModel());
        
        // 供货厂家模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getMaterialManufacture()), 
                MkStorageDetail::getMaterialManufacture, param.getMaterialManufacture());
        
        // 状态查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getMaterialStatus()), 
                MkStorageDetail::getMaterialStatus, param.getMaterialStatus());
        
        // 库存数量范围查询
        queryChainWrapper.ge(param.getQtyInStockMin() != null, 
                MkStorageDetail::getQtyInStock, param.getQtyInStockMin());
        queryChainWrapper.le(param.getQtyInStockMax() != null, 
                MkStorageDetail::getQtyInStock, param.getQtyInStockMax());
        
        // 检测时间范围查询
        queryChainWrapper.ge(param.getCheckDateStart() != null, 
                MkStorageDetail::getCheckDate, param.getCheckDateStart());
        queryChainWrapper.le(param.getCheckDateEnd() != null, 
                MkStorageDetail::getCheckDate, param.getCheckDateEnd());
        
        // 到期时间范围查询
        queryChainWrapper.ge(param.getDqsjDateStart() != null, 
                MkStorageDetail::getDqsjDate, param.getDqsjDateStart());
        queryChainWrapper.le(param.getDqsjDateEnd() != null, 
                MkStorageDetail::getDqsjDate, param.getDqsjDateEnd());
        
        // 排序：按创建时间倒序
        queryChainWrapper.orderByDesc(MkStorageDetail::getCreatedTime);
        
        return queryChainWrapper.page(iPage).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkStorageDetailParam param) {
        // 必填字段校验
        if (StringUtils.isBlank(param.getMaterialSystemCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getStorageId())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getMaterialName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getCatalogType())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getMaterialType())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getMaterialModel())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (param.getQtyInStock() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        return super.save(convert.toEntity(param));
    }

    @Override
    public Boolean modify(MkStorageDetailParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        // 必填字段校验
        if (StringUtils.isBlank(param.getMaterialSystemCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getStorageId())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getMaterialName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getCatalogType())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getMaterialType())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getMaterialModel())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (param.getQtyInStock() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        return super.updateById(convert.toEntity(param));
    }

    @Override
    public MkStorageDetailDTO getCurrentDetail(MkStorageDetailParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        MkStorageDetail entity = super.getById(param.getId());
        return convert.toDTO(entity);
    }

    @Override
    public Boolean removeBatch(BatchParam param) {
        if (param.getId() == null || param.getId().isEmpty()) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        return super.removeByIds(param.getId());
    }
}
