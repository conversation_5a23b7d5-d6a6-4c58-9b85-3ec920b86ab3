package com.yhd.admin.api.controller.sys;

import java.security.Principal;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.sys.convert.RoleConvert;
import com.yhd.admin.api.domain.sys.dto.RoleDTO;
import com.yhd.admin.api.domain.sys.query.RoleParam;
import com.yhd.admin.api.domain.sys.vo.RoleVO;
import com.yhd.admin.api.service.sys.RoleService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName RoleController.java @Description TODO
 * @createTime 2020年04月28日 15:22:00
 */
@RestController
@RequestMapping("/role")
public class RoleController {

    private final RoleConvert convert;

    private final RoleService roleService;

    public RoleController(RoleConvert convert, RoleService roleService) {
        this.convert = convert;
        this.roleService = roleService;
    }

    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<RoleVO> pagingQuery(@RequestBody RoleParam queryParam) {
        IPage<RoleDTO> iPage = roleService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(value = "/addOrUpdate", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson addRole(@RequestBody RoleParam roleParam, Principal principal) {
        roleParam.setCreatedBy(principal.getName());
        roleService.addOrUpdate(roleParam);
        return RespJson.success();
    }

    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modifyRole(@RequestBody RoleParam roleParam, Principal authentication) {
        roleParam.setUpdatedBy(authentication.getName());
        roleService.modify(roleParam);
        return RespJson.success();
    }

    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam batchParam) {
        roleService.removeBatch(batchParam);
        return RespJson.success();
    }

    @PostMapping(value = "/currentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody RoleParam param) {

        return RespJson.success(convert.toVO(roleService.currentDetail(param)));
    }

    @PostMapping(value = "/queryRoleList", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson queryRoleList() {

        return RespJson.success(convert.toVO(roleService.queryRoleList()));
    }

    @PostMapping(value = "/validateIfNotExist", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> validateIfNotExist(@RequestBody RoleParam param) {

        return RespJson.success(roleService.validateIfNotExist(param));
    }
}
