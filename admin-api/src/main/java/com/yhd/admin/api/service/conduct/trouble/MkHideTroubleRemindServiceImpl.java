package com.yhd.admin.api.service.conduct.trouble;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.trouble.MkHideTroubleRemindDao;
import com.yhd.admin.api.domain.conduct.convert.trouble.MkHideTroubleRemindConvert;
import com.yhd.admin.api.domain.conduct.dto.trouble.MkHideTroubleRemindDTO;
import com.yhd.admin.api.domain.conduct.entity.trouble.MkHideTroubleRecord;
import com.yhd.admin.api.domain.conduct.entity.trouble.MkHideTroubleRemind;
import com.yhd.admin.api.domain.conduct.query.trouble.MkHideTroubleRemindParam;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 隐患催办记录表服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-04 10:28:36
 */
@Service
public class MkHideTroubleRemindServiceImpl extends ServiceImpl<MkHideTroubleRemindDao, MkHideTroubleRemind> implements MkHideTroubleRemindService {

    private final MkHideTroubleRemindConvert mkHideTroubleRemindConvert;

    public MkHideTroubleRemindServiceImpl(MkHideTroubleRemindConvert mkHideTroubleRemindConvert) {
        this.mkHideTroubleRemindConvert = mkHideTroubleRemindConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkHideTroubleRemindDTO> pagingQuery(MkHideTroubleRemindParam param) {
        IPage<MkHideTroubleRemind> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkHideTroubleRemind> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return wrapper.page(page).convert(mkHideTroubleRemindConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkHideTroubleRemindDTO> queryList(MkHideTroubleRemindParam param) {
        LambdaQueryWrapper<MkHideTroubleRemind> wrapper = new LambdaQueryWrapper<>();
        return mkHideTroubleRemindConvert.toDTOList(baseMapper.selectList(wrapper));
    }


    /**
     * 新增
     *
     * @param record 隐患数据
     * @return 是否成功
     */
    @Override
    public Boolean add(MkHideTroubleRecord record) {
        MkHideTroubleRemind entity = new MkHideTroubleRemind();
        entity.setTroubleId(record.getId());
        entity.setRemindDesc(record.getRemindDesc());
        entity.setShiftName(record.getShiftName());
        entity.setShiftCode(record.getShiftCode());
        return save(entity);
    }
}
