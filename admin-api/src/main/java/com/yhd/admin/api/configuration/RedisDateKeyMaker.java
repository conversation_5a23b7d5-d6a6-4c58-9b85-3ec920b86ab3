package com.yhd.admin.api.configuration;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * @program: admin-framework
 * @description:
 * @author: wangshengman
 * @create: 2024-10-16 10:00
 **/
@Component
@Slf4j
public class RedisDateKeyMaker {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * @Description: 设置redis key 过期时间
     * @Param: * @Param null:
     * @return: * @return: null
     * @Author: wangshengman
     * @Date: 2024/10/16
     */
    public void setExpirationDate(String key) {
        ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();
        opsForValue.set(key, getExpireAtData());
        redisTemplate.expireAt(key, getExpireAtData());

    }

    /**
     * @Description: 根据每周几 设置redis key 过期时间 比如 每周一凌晨一点
     * @Param: * @Param null:
     * @return: * @return: null
     * @Author: wangshengman
     * @Date: 2024/10/16
     */
    public void setExpirationDate(String key, DayOfWeek targetDayOfWeek) {
        ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();
        opsForValue.set(key, getExpireAtData(targetDayOfWeek, LocalTime.of(1, 0)));
        redisTemplate.expireAt(key, getExpireAtData(targetDayOfWeek, LocalTime.of(1, 0)));

    }

    /**
     * @Description: 月份 1-31天 定时设置redis key 过期时间
     * @Param: * @Param null:
     * @return: * @return: null
     * @Author: wangshengman
     * @Date: 2024/10/16
     */
    public void setExpirationDate(String key, int targetDayOfMonth) {
        ValueOperations<String, Object> opsForValue = redisTemplate.opsForValue();
        opsForValue.set(key, getExpireAtData(targetDayOfMonth, LocalTime.of(1, 0)));
        redisTemplate.expireAt(key, getExpireAtData(targetDayOfMonth, LocalTime.of(1, 0)));

    }

    /**
     * 过期时间每天凌晨一点
     *
     * @return
     */
    public Date getExpireAtData() {
        // 获取系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();
        // 获取当前日期并加上一天
        LocalDate localDate = LocalDate.now().plusDays(1);

        // 将LocalDate转换为ZonedDateTime，并设置时间为凌晨一点
        // 注意：atTime(int hour, int minute, int second) 方法用于设置时间
        ZonedDateTime zdt = localDate.atTime(1, 0).atZone(zoneId);
        return Date.from(zdt.toInstant());
        // // 获取系统默认时区
        // ZoneId zoneId = ZoneId.systemDefault();
        // // 获取当前时间
        // ZonedDateTime now = ZonedDateTime.now(zoneId);
        // // 获取当前日期
        // LocalDate today = now.toLocalDate();
        // // 设定目标时间为下午1点15分
        // LocalTime targetTime = LocalTime.of(17, 7);
        //
        // // 如果当前时间已经超过了目标时间，则目标日期应该是明天
        // if (now.toLocalTime().isAfter(targetTime)) {
        // today = today.plusDays(1);
        // }
        //
        // // 构建ZonedDateTime对象
        // ZonedDateTime zdt = today.atTime(targetTime).atZone(zoneId);
        //
        // // 将ZonedDateTime转换为Date
        // Date from = Date.from(zdt.toInstant());
        // SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // String format = sdf.format(from);
        // log.info("过期时间：{}", format);
        // return from;
        // }
    }

    /**
     * @Description:
     * @Param: * @Param targetDayOfWeek: 每周几 targetTime: 目标时间
     * @return: * @return: null
     * @Author: wangshengman
     * @Date: 2024/10/16
     */
    public Date getExpireAtData(DayOfWeek targetDayOfWeek, LocalTime targetTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        LocalDate today = now.toLocalDate();
        // 如果今天就是目标星期几，并且当前时间已经过了目标时间，则目标日期应该是明天的目标星期几
        if (today.getDayOfWeek().equals(targetDayOfWeek) && now.toLocalTime().isAfter(targetTime)) {
            today = today.plusWeeks(1); // 或者使用plusDays(7)来获取下周的同一天
            today = today.with(TemporalAdjusters.next(targetDayOfWeek)); // 调整为下周的目标星期几
        } else if (!today.getDayOfWeek().equals(targetDayOfWeek)) {
            // 如果今天不是目标星期几，则直接调整到本周的目标星期几
            today = today.with(TemporalAdjusters.nextOrSame(targetDayOfWeek));
        }
        // 构建ZonedDateTime对象
        ZonedDateTime zdt = today.atTime(targetTime).atZone(zoneId);

        // 将ZonedDateTime转换为Date
        return Date.from(zdt.toInstant());
    }

    /**
     * 获取基于月份中特定日期和时间的到期日期
     *
     * @param targetDayOfMonth 每月的目标日期（1-31）
     * @param targetTime 目标时间
     * @return 到期时间的Date对象
     */
    public Date getExpireAtData(int targetDayOfMonth, LocalTime targetTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        LocalDate today = now.toLocalDate();
        // 创建目标月份的LocalDate对象，默认年份和月份与当前日期相同
        LocalDate targetDate = today.withDayOfMonth(targetDayOfMonth);
        // 如果目标日期小于当前日期（即已经过去），则滚动到下一个月
        if (targetDate.isBefore(today)) {
            targetDate = targetDate.plusMonths(1);
        }
        // 构建ZonedDateTime对象
        ZonedDateTime zdt = targetDate.atTime(targetTime).atZone(zoneId);
        // 将ZonedDateTime转换为Date
        return Date.from(zdt.toInstant());
    }
}
