package com.yhd.admin.api.domain.conduct.dto.transfer;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 收发文登记表(MkTransferDispatch)DTO
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkTransferDispatchDTO extends BaseDTO implements Serializable {
    /** 收发文id */
    private Long id;
    /** 收发文名称 */
    private String dispatchName;
    /** 类型名称 1：收文 2：发文 */
    private String dispatchType;
    /** 部门名称 */
    private String transferOrgName;
    /** 部门id */
    private Long transferOrgId;
    /** 保存期限名称 1：一年 2：两年 3：5年 */
    private String dispatchDeadlineName;
    /** 保存期限代码 1：一年 2：两年 3：5年 */
    private String dispatchDeadlineCode;
    /** 收发文日期 */
    private LocalDate dispatchDate;
    /** 备注 */
    private String remark;
    /** 正文文件 */
    private List<String> textFile;
    /** 附件文件 */
    private List<String> annexFile;
    /** 审批附件文件 */
    private List<String> approveFile;
}

