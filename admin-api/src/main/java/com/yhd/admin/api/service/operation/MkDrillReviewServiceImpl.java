package com.yhd.admin.api.service.operation;

import com.yhd.admin.api.dao.operation.MkDrillReviewDao;
import com.yhd.admin.api.domain.operation.convert.MkDrillReviewConvert;
import com.yhd.admin.api.domain.operation.dto.MkDrillReviewDTO;
import com.yhd.admin.api.domain.operation.entity.MkDrillReview;
import com.yhd.admin.api.domain.operation.query.MkDrillReviewParam;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;

/**
 * 应急演练-演练审核表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@Service
public class MkDrillReviewServiceImpl extends ServiceImpl<MkDrillReviewDao, MkDrillReview> implements MkDrillReviewService {

    @Resource
    private MkDrillReviewConvert convert;

    @Resource
    private MkDrillReviewService service;

    @Override
    public IPage<MkDrillReviewDTO> pagingQuery(MkDrillReviewParam queryParam) {
        Page<MkDrillReview> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkDrillReview> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        
        // 添加查询条件
        if (queryParam.getPlanId() != null) {
            queryChain.eq(MkDrillReview::getPlanId, queryParam.getPlanId());
        }
        if (queryParam.getDrillName() != null && !queryParam.getDrillName().isEmpty()) {
            queryChain.like(MkDrillReview::getDrillName, queryParam.getDrillName());
        }
        if (queryParam.getMethod() != null && !queryParam.getMethod().isEmpty()) {
            queryChain.eq(MkDrillReview::getMethod, queryParam.getMethod());
        }
        if (queryParam.getLeaderName() != null && !queryParam.getLeaderName().isEmpty()) {
            queryChain.like(MkDrillReview::getLeaderName, queryParam.getLeaderName());
        }
        if (queryParam.getStatus() != null) {
            queryChain.eq(MkDrillReview::getStatus, queryParam.getStatus());
        }
        if (queryParam.getReviewStatus() != null) {
            queryChain.eq(MkDrillReview::getReviewStatus, queryParam.getReviewStatus());
        }
        if (queryParam.getReviewBy() != null && !queryParam.getReviewBy().isEmpty()) {
            queryChain.eq(MkDrillReview::getReviewBy, queryParam.getReviewBy());
        }
        if (queryParam.getStartTime() != null) {
            queryChain.ge(MkDrillReview::getPlanTime, queryParam.getStartTime());
        }
        if (queryParam.getEndTime() != null) {
            queryChain.le(MkDrillReview::getPlanTime, queryParam.getEndTime());
        }
        
        queryChain.orderByDesc(MkDrillReview::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkDrillReviewParam param) {
        MkDrillReview entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MkDrillReviewParam param) {
        MkDrillReview entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MkDrillReviewDTO getCurrentDetail(MkDrillReviewParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

} 