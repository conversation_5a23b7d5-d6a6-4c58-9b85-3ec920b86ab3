package com.yhd.admin.api.advice;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Objects;

import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yhd.admin.api.dao.sys.SysOpsLogDao;
import com.yhd.admin.api.domain.sys.entity.SysOpsLog;
import com.yhd.admin.common.utils.IpUtil;
import com.yhd.admin.common.utils.RequestUtil;

import jakarta.servlet.http.HttpServletRequest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 请求日志拦截
 *
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/4 11:15
 */
@ControllerAdvice(basePackages = {"com.yhd.admin.api.controller"})
@Slf4j
public class RequestAdvice implements RequestBodyAdvice {
    private final ObjectMapper objectMapper;
    private final SysOpsLogDao sysOpsLogDao;

    public RequestAdvice(ObjectMapper objectMapper, SysOpsLogDao sysOpsLogDao) {
        this.objectMapper = objectMapper;
        this.sysOpsLogDao = sysOpsLogDao;
    }

    /**
     * Invoked first to determine if this interceptor applies.
     *
     * @param methodParameter the method parameter
     * @param targetType the target type, not necessarily the same as the method parameter type, e.g. for
     *            {@code HttpEntity<String>}.
     * @param converterType the selected converter type
     * @return whether this interceptor should be invoked or not
     */
    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType,
        Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    /**
     * Invoked second before the request body is read and converted.
     *
     * @param inputMessage the request
     * @param parameter the target method parameter
     * @param targetType the target type, not necessarily the same as the method parameter type, e.g. for
     *            {@code HttpEntity<String>}.
     * @param converterType the converter used to deserialize the body
     * @return the input request or a new instance, never {@code null}
     */
    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage, MethodParameter parameter, Type targetType,
        Class<? extends HttpMessageConverter<?>> converterType) throws IOException {

        return inputMessage;
    }

    /**
     * Invoked third (and last) after the request body is converted to an Object.
     *
     * @param body set to the converter Object before the first advice is called
     * @param inputMessage the request
     * @param parameter the target method parameter
     * @param targetType the target type, not necessarily the same as the method parameter type, e.g. for
     *            {@code HttpEntity<String>}.
     * @param converterType the converter used to deserialize the body
     * @return the same body or a new instance
     */
    @NotNull
    @SneakyThrows
    @Override
    public Object afterBodyRead(@NotNull Object body, @NotNull HttpInputMessage inputMessage,
        @NotNull MethodParameter parameter, @NotNull Type targetType,
        @NotNull Class<? extends HttpMessageConverter<?>> converterType) {
        HttpServletRequest httpRequest = RequestUtil.get();
        if (Objects.requireNonNull(parameter.getMethod()).getName().contains("add")
            || Objects.requireNonNull(parameter.getMethod()).getName().contains("modify")
            || Objects.requireNonNull(parameter.getMethod()).getName().contains("remove")) {
            SysOpsLog insertObj = new SysOpsLog();
            insertObj.setMethod(Objects.requireNonNull(parameter.getMethod()).getName());
            insertObj.setParams(objectMapper.writeValueAsString(body));
            insertObj.setUrl(httpRequest.getRequestURI());
            insertObj.setOpsName(httpRequest.getUserPrincipal().getName());
            insertObj.setClientIp(IpUtil.getIpAddr(httpRequest));
            sysOpsLogDao.insert(insertObj);
        }
        return body;
    }

    /**
     * Invoked second (and last) if the body is empty.
     *
     * @param body usually set to {@code null} before the first advice is called
     * @param inputMessage the request
     * @param parameter the method parameter
     * @param targetType the target type, not necessarily the same as the method parameter type, e.g. for
     *            {@code HttpEntity<String>}.
     * @param converterType the selected converter type
     * @return the value to use or {@code null} which may then raise an {@code
     *     HttpMessageNotReadableException} if the argument is required.
     */
    @Override
    public Object handleEmptyBody(Object body, HttpInputMessage inputMessage, MethodParameter parameter,
        Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return body;
    }
}
