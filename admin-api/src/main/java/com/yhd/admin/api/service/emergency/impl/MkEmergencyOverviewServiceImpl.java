package com.yhd.admin.api.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.admin.api.dao.emergency.*;
import com.yhd.admin.api.domain.emergency.entity.*;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyOverviewParam;
import com.yhd.admin.api.domain.emergency.vo.*;
import com.yhd.admin.api.service.emergency.MkEmergencyOverviewService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 应急救援中心总览 Service 实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MkEmergencyOverviewServiceImpl implements MkEmergencyOverviewService {

    @Resource
    private MkEmergencyLeaderDao emergencyLeaderDao;

    @Resource
    private MkEmergencyPersonnelStatsDao emergencyPersonnelStatsDao;

    @Resource
    private MkEmergencySafetySummaryDao emergencySafetySummaryDao;

    @Resource
    private MkEmergencyWorkfaceDao emergencyWorkfaceDao;

    @Resource
    private MkEmergencyItemDao emergencyItemDao;

    @Override
    public MkEmergencyOverviewVO getOverviewData(MkEmergencyOverviewParam param) {
        log.info("获取应急救援中心总览数据，参数：{}", param);

        MkEmergencyOverviewVO result = new MkEmergencyOverviewVO();

        // 获取入井领导信息
        result.setLeaders(getLeaderInfos(param));

        // 获取下井人员统计信息
        result.setPersonnelStats(getPersonnelStatsInfo(param));

        // 获取生产安全总结信息
        result.setSafetySummary(getSafetySummaryInfo(param));

        // 获取矿井工作面信息
        result.setWorkfaces(getWorkfaceInfos(param));

        // 获取功能导航信息
        result.setNavigations(getNavigations(param.getOrgCode()));

        // 获取默认导航的列表项目（安全监测）
        result.setListItems(getListItemsByNavType("SAFETY", param.getOrgCode()));

        return result;
    }

    @Override
    public List<MkEmergencyListItemInfoVO> getListItemsByNavType(String navType, String orgCode) {
        log.info("根据导航类型获取列表项目，navType：{}，orgCode：{}", navType, orgCode);

        LambdaQueryWrapper<MkEmergencyItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.hasText(navType), MkEmergencyItem::getItemType, navType)
                .eq(MkEmergencyItem::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkEmergencyItem::getOrgCode, orgCode)
                .orderByAsc(MkEmergencyItem::getDisplayOrder);

        List<MkEmergencyItem> items = emergencyItemDao.selectList(queryWrapper);

        return items.stream().map(this::convertToListItemInfo).collect(Collectors.toList());
    }

    @Override
    public List<MkEmergencyListItemInfoVO> getListItemsByNavType(String navType) {
        log.info("根据导航类型获取列表项目，navType：{}", navType);

        LambdaQueryWrapper<MkEmergencyItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.hasText(navType), MkEmergencyItem::getItemType, navType)
                .eq(MkEmergencyItem::getStatus, 1)
                .orderByAsc(MkEmergencyItem::getDisplayOrder);

        List<MkEmergencyItem> items = emergencyItemDao.selectList(queryWrapper);

        return items.stream().map(this::convertToListItemInfo).collect(Collectors.toList());
    }

    @Override
    public List<MkEmergencyNavigationInfoVO> getNavigations(String orgCode) {
        log.info("获取功能导航列表，orgCode：{}", orgCode);

        LambdaQueryWrapper<MkEmergencyItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkEmergencyItem::getItemType, "NAV")
                .eq(MkEmergencyItem::getStatus, 1)
                .eq(MkEmergencyItem::getIsEnabled, true)
                .eq(MkEmergencyItem::getIsVisible, true)
                .eq(StringUtils.hasText(orgCode), MkEmergencyItem::getOrgCode, orgCode)
                .orderByAsc(MkEmergencyItem::getDisplayOrder);

        List<MkEmergencyItem> navItems = emergencyItemDao.selectList(queryWrapper);

        return navItems.stream().map(this::convertToNavigationInfo).collect(Collectors.toList());
    }

    /**
     * 获取入井领导信息
     */
    private MkEmergencyLeaderInfoVO getLeaderInfos(MkEmergencyOverviewParam param) {
        LambdaQueryWrapper<MkEmergencyLeader> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkEmergencyLeader::getStatus, 1)
                .eq(StringUtils.hasText(param.getOrgCode()), MkEmergencyLeader::getOrgCode, param.getOrgCode())
                .eq(Objects.nonNull(param.getOnlyUnderground()) && param.getOnlyUnderground(),
                        MkEmergencyLeader::getIsUnderground, true)
                .orderByDesc(MkEmergencyLeader::getEntryTime);

        List<MkEmergencyLeader> leaders = emergencyLeaderDao.selectList(queryWrapper);

        if (!leaders.isEmpty()) {
            return convertToLeaderInfo(leaders.get(0));
        }

        return null;
    }

    /**
     * 获取下井人员统计信息
     */
    private MkEmergencyPersonnelStatsInfoVO getPersonnelStatsInfo(MkEmergencyOverviewParam param) {
        LocalDate statsDate = param.getStatsDate() != null ? param.getStatsDate() : LocalDate.now();

        MkEmergencyPersonnelStatsInfoVO statsInfo = new MkEmergencyPersonnelStatsInfoVO();

        // 获取区队类型统计
        List<MkEmergencyStatsItemVO> teamStats = getStatsByType("TEAM", statsDate, param.getOrgCode());
        statsInfo.setTeamStats(teamStats);

        // 获取区域类型统计
        List<MkEmergencyStatsItemVO> areaStats = getStatsByType("AREA", statsDate, param.getOrgCode());
        statsInfo.setAreaStats(areaStats);

        // 计算总人数（取区队统计的总数）
        Integer totalCount = teamStats.stream()
                .mapToInt(MkEmergencyStatsItemVO::getPersonnelCount)
                .sum();
        statsInfo.setTotalCount(totalCount);

        return statsInfo;
    }

    /**
     * 根据类型获取统计数据
     */
    private List<MkEmergencyStatsItemVO> getStatsByType(String categoryType, LocalDate statsDate,
            String orgCode) {
        LambdaQueryWrapper<MkEmergencyPersonnelStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkEmergencyPersonnelStats::getCategoryType, categoryType)
                .eq(MkEmergencyPersonnelStats::getStatsDate, statsDate)
                .eq(MkEmergencyPersonnelStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkEmergencyPersonnelStats::getOrgCode, orgCode)
                .orderByAsc(MkEmergencyPersonnelStats::getSortOrder);

        List<MkEmergencyPersonnelStats> statsList = emergencyPersonnelStatsDao.selectList(queryWrapper);

        return statsList.stream().map(this::convertToStatsItem).collect(Collectors.toList());
    }

    /**
     * 获取生产安全总结信息
     */
    private MkEmergencySafetySummaryInfoVO getSafetySummaryInfo(MkEmergencyOverviewParam param) {
        LocalDate summaryDate = param.getSummaryDate() != null ? param.getSummaryDate() : LocalDate.now();

        LambdaQueryWrapper<MkEmergencySafetySummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkEmergencySafetySummary::getSummaryDate, summaryDate)
                .eq(MkEmergencySafetySummary::getStatus, 1)
                .eq(StringUtils.hasText(param.getOrgCode()), MkEmergencySafetySummary::getOrgCode, param.getOrgCode())
                .orderByDesc(MkEmergencySafetySummary::getCreateTime)
                .last("LIMIT 1");

        List<MkEmergencySafetySummary> summaries = emergencySafetySummaryDao.selectList(queryWrapper);

        if (!summaries.isEmpty()) {
            return convertToSafetySummaryInfo(summaries.get(0));
        }

        return null;
    }

    /**
     * 获取矿井工作面信息
     */
    private List<MkEmergencyWorkfaceInfoVO> getWorkfaceInfos(MkEmergencyOverviewParam param) {
        LambdaQueryWrapper<MkEmergencyWorkface> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkEmergencyWorkface::getStatus, 1)
                .eq(StringUtils.hasText(param.getOrgCode()), MkEmergencyWorkface::getOrgCode, param.getOrgCode())
                .eq(Objects.nonNull(param.getOnlyActive()) && param.getOnlyActive(),
                        MkEmergencyWorkface::getIsActive, true)
                .orderByAsc(MkEmergencyWorkface::getWorkfaceCode);

        List<MkEmergencyWorkface> workfaces = emergencyWorkfaceDao.selectList(queryWrapper);

        return workfaces.stream().map(this::convertToWorkfaceInfo).collect(Collectors.toList());
    }

    /**
     * 转换为入井领导信息
     */
    private MkEmergencyLeaderInfoVO convertToLeaderInfo(MkEmergencyLeader leader) {
        MkEmergencyLeaderInfoVO info = new MkEmergencyLeaderInfoVO();
        info.setLeaderCode(leader.getLeaderCode());
        info.setLeaderName(leader.getLeaderName());
        info.setPosition(leader.getPosition());
        info.setDepartment(leader.getDepartment());
        info.setPhone(leader.getPhone());
        info.setAvatarUrl(leader.getAvatarUrl());
        info.setIsUnderground(leader.getIsUnderground());
        info.setIsUndergroundName(Boolean.TRUE.equals(leader.getIsUnderground()) ? "已下井" : "未下井");
        info.setEntryTime(leader.getEntryTime() != null
                ? leader.getEntryTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                : null);
        info.setCurrentLocation(leader.getCurrentLocation());
        return info;
    }

    /**
     * 转换为统计项目
     */
    private MkEmergencyStatsItemVO convertToStatsItem(MkEmergencyPersonnelStats stats) {
        MkEmergencyStatsItemVO item = new MkEmergencyStatsItemVO();
        item.setCategoryName(stats.getCategoryName());
        item.setPersonnelCount(stats.getPersonnelCount());
        item.setPercentage(stats.getPercentage() != null ? stats.getPercentage().toString() + "%" : "0%");
        item.setColorCode(stats.getColorCode());
        item.setSortOrder(stats.getSortOrder());
        return item;
    }

    /**
     * 转换为安全总结信息
     */
    private MkEmergencySafetySummaryInfoVO convertToSafetySummaryInfo(MkEmergencySafetySummary summary) {
        MkEmergencySafetySummaryInfoVO info = new MkEmergencySafetySummaryInfoVO();
        info.setSummaryDate(summary.getSummaryDate());
        info.setSummaryTitle(summary.getSummaryTitle());
        info.setSummaryContent(summary.getSummaryContent());
        info.setHasContent(summary.getHasContent());
        info.setWarningLevel(summary.getWarningLevel());
        info.setWarningLevelName(getWarningLevelName(summary.getWarningLevel()));
        info.setWarningMessage(summary.getWarningMessage());
        info.setResponsiblePerson(summary.getResponsiblePerson());
        info.setDepartment(summary.getDepartment());
        return info;
    }

    /**
     * 转换为工作面信息
     */
    private MkEmergencyWorkfaceInfoVO convertToWorkfaceInfo(MkEmergencyWorkface workface) {
        MkEmergencyWorkfaceInfoVO info = new MkEmergencyWorkfaceInfoVO();
        info.setWorkfaceCode(workface.getWorkfaceCode());
        info.setWorkfaceName(workface.getWorkfaceName());
        info.setWorkfaceType(workface.getWorkfaceType());
        info.setCoordinateX(workface.getCoordinateX() != null ? workface.getCoordinateX().toString() : null);
        info.setCoordinateY(workface.getCoordinateY() != null ? workface.getCoordinateY().toString() : null);
        info.setCoordinateZ(workface.getCoordinateZ() != null ? workface.getCoordinateZ().toString() : null);
        info.setSafetyStatus(workface.getSafetyStatus());
        info.setSafetyStatusName(getSafetyStatusName(workface.getSafetyStatus()));
        info.setIsActive(workface.getIsActive());
        return info;
    }

    /**
     * 转换为导航信息
     */
    private MkEmergencyNavigationInfoVO convertToNavigationInfo(MkEmergencyItem item) {
        MkEmergencyNavigationInfoVO info = new MkEmergencyNavigationInfoVO();
        info.setItemCode(item.getItemCode());
        info.setItemName(item.getItemName());
        info.setNavIcon(item.getNavIcon());
        info.setNavUrl(item.getNavUrl());
        info.setIsDefault(item.getIsDefault());
        info.setDisplayOrder(item.getDisplayOrder());
        return info;
    }

    /**
     * 转换为列表项目信息
     */
    private MkEmergencyListItemInfoVO convertToListItemInfo(MkEmergencyItem item) {
        MkEmergencyListItemInfoVO info = new MkEmergencyListItemInfoVO();
        info.setItemCode(item.getItemCode());
        info.setItemName(item.getItemName());
        info.setItemType(item.getItemType());
        info.setItemStatus(item.getItemStatus());
        info.setItemStatusName(getItemStatusName(item.getItemStatus()));
        info.setItemValue(item.getItemValue());
        info.setItemUnit(item.getItemUnit());
        info.setLocationInfo(item.getLocationInfo());
        info.setActionUrl(item.getActionUrl());
        info.setIconClass(item.getIconClass());
        info.setColorCode(item.getColorCode());
        info.setDisplayOrder(item.getDisplayOrder());
        return info;
    }

    /**
     * 获取警告级别名称
     */
    private String getWarningLevelName(Integer warningLevel) {
        if (warningLevel == null) {
            return "未知";
        }
        switch (warningLevel) {
            case 0:
                return "正常";
            case 1:
                return "提醒";
            case 2:
                return "警告";
            case 3:
                return "严重";
            default:
                return "未知";
        }
    }

    /**
     * 获取安全状态名称
     */
    private String getSafetyStatusName(Integer safetyStatus) {
        if (safetyStatus == null) {
            return "未知";
        }
        switch (safetyStatus) {
            case 0:
                return "危险";
            case 1:
                return "正常";
            case 2:
                return "警告";
            default:
                return "未知";
        }
    }

    /**
     * 获取项目状态名称
     */
    private String getItemStatusName(Integer itemStatus) {
        if (itemStatus == null) {
            return "未知";
        }
        switch (itemStatus) {
            case 0:
                return "异常";
            case 1:
                return "正常";
            case 2:
                return "警告";
            default:
                return "未知";
        }
    }
}