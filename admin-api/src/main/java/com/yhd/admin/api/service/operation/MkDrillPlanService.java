package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkDrillPlanDTO;
import com.yhd.admin.api.domain.operation.entity.MkDrillPlan;
import com.yhd.admin.api.domain.operation.query.MkDrillPlanParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 应急演练-演练计划表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
public interface MkDrillPlanService extends IService<MkDrillPlan> {

    IPage<MkDrillPlanDTO> pagingQuery(MkDrillPlanParam queryParam);

     Boolean add(MkDrillPlanParam param);

    Boolean modify(MkDrillPlanParam param);

    Boolean removeBatch(BatchParam param);

    MkDrillPlanDTO getCurrentDetail(MkDrillPlanParam param);
} 