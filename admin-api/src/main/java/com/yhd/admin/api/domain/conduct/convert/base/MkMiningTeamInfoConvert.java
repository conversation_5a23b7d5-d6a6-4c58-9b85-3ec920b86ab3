package com.yhd.admin.api.domain.conduct.convert.base;

import com.yhd.admin.api.domain.conduct.dto.base.MkMiningTeamInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkMiningTeamInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkMiningTeamInfoParam;
import com.yhd.admin.api.domain.conduct.vo.base.MkMiningTeamInfoVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 采煤队(MkMiningTeamInfo)Convert
 *
 * <AUTHOR>
 * @since 2025-07-25 10:32:53
 */
@Mapper(componentModel = "spring")
public interface MkMiningTeamInfoConvert {
    MkMiningTeamInfo toEntity(MkMiningTeamInfoParam param);

    MkMiningTeamInfoDTO toDTO(MkMiningTeamInfo entity);

    MkMiningTeamInfoVO toVO(MkMiningTeamInfoDTO dto);

    List<MkMiningTeamInfoDTO> toDTOList(List<MkMiningTeamInfo> entityList);

    List<MkMiningTeamInfoVO> toVOList(List<MkMiningTeamInfoDTO> dtoList);
}

