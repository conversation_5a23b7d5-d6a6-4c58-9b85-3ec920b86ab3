package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkOutputSaleInfoDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputSaleInfo;
import com.yhd.admin.api.domain.operation.query.MkOutputSaleInfoParam;
import com.yhd.admin.api.domain.operation.vo.MkOutputSaleInfoVO;
import org.mapstruct.Mapper;


/**
* 生产录入-产量销售库存表
*
* <AUTHOR>
* @since 1.0.0 2025-07-30
*/
@Mapper(componentModel = "spring")
public interface MkOutputSaleInfoConvert {

    MkOutputSaleInfo toEntity(MkOutputSaleInfoParam param);

    MkOutputSaleInfoVO toVO(MkOutputSaleInfoDTO dto);

    MkOutputSaleInfoDTO toDTO(MkOutputSaleInfo entity);

}
