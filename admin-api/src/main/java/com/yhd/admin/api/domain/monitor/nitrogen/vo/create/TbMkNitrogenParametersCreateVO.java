package com.yhd.admin.api.domain.monitor.nitrogen.vo.create;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 制氮系统参数表
 *
 * <AUTHOR>
 * @since
 */
@Data
public class TbMkNitrogenParametersCreateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联制氮系统主表ID
     */
    private Long nitrogenSystemId;

    /**
     * 氮气压力(MPa)
     */
    private BigDecimal nitrogenPressure;

    /**
     * 氮气流量(Nm³/h)
     */
    private BigDecimal nitrogenFlowRate;

    /**
     * 氮气纯度(%)
     */
    private BigDecimal nitrogenPurity;

}
