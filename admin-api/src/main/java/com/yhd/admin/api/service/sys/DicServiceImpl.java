package com.yhd.admin.api.service.sys;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.sys.DicDao;
import com.yhd.admin.api.domain.sys.convert.DicConvert;
import com.yhd.admin.api.domain.sys.convert.DicItemConvert;
import com.yhd.admin.api.domain.sys.dto.DicDTO;
import com.yhd.admin.api.domain.sys.dto.DicItemDTO;
import com.yhd.admin.api.domain.sys.entity.SysDic;
import com.yhd.admin.api.domain.sys.entity.SysDicItem;
import com.yhd.admin.api.domain.sys.enums.APIRedisKeyEnum;
import com.yhd.admin.api.domain.sys.query.DicItemParam;
import com.yhd.admin.api.domain.sys.query.DicParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName SysDicServiceImpl.java @Description TODO
 * @createTime 2020年05月20日 16:13:00
 */
@Service
public class DicServiceImpl extends ServiceImpl<DicDao, SysDic> implements DicService {

    private final DicConvert dicConvert;

    private final DicItemService dicItemService;

    private final DicItemConvert itemConvert;

    private final RedisTemplate<String, Object> redisTemplate;

    private final HashOperations<String, String, List<DicItemDTO>> hashOperations;

    public DicServiceImpl(DicConvert dicConvert, DicItemService dicItemService, DicItemConvert itemConvert,
        RedisTemplate<String, Object> redisTemplate) {
        this.dicConvert = dicConvert;
        this.dicItemService = dicItemService;
        this.itemConvert = itemConvert;
        this.redisTemplate = redisTemplate;
        this.hashOperations = redisTemplate.opsForHash();
    }

    @Override
    public IPage<DicDTO> pagingQuery(DicParam queryParam) {
        IPage<SysDic> iPage = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<SysDic> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper
            .eq(StringUtils.isNotBlank(queryParam.getCategory()), SysDic::getCategory, queryParam.getCategory())
            .like(StringUtils.isNotBlank(queryParam.getName()), SysDic::getName, queryParam.getName())
            .eq(queryParam.getStatus() != null, SysDic::getStatus, queryParam.getStatus())
            .orderByAsc(SysDic::getOrderNum);
        return queryChainWrapper.page(iPage).convert(dicConvert::toDTO);
    }

    @Override
    public Boolean saveOrUpdate(DicParam addParam) {
        SysDic sysDic = dicConvert.toEntity(addParam);
        Boolean retVal = this.saveOrUpdate(sysDic);
        dicItemService.remove(new QueryWrapper<SysDicItem>().lambda().eq(SysDicItem::getDicId, sysDic.getId()));
        hashOperations.delete(APIRedisKeyEnum.DIC.getKey(), sysDic.getCategory());

        if (!CollectionUtils.isEmpty(addParam.getItems())) {
            List<SysDicItem> sysDicItems = addParam.getItems().stream().map(o -> {
                SysDicItem item = itemConvert.toEntity(o);
                item.setId(null);
                item.setCategory(sysDic.getCategory());
                item.setDicId(sysDic.getId());
                return item;
            }).collect(Collectors.toList());
            dicItemService.saveBatch(sysDicItems);
            hashOperations.put(APIRedisKeyEnum.DIC.getKey(), sysDic.getCategory(), itemConvert.toDTO(sysDicItems));
        }
        return retVal;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean modifyDic(DicParam modifyParam) {
        LambdaUpdateChainWrapper<SysDicItem> dicItemUpdate =
            new LambdaUpdateChainWrapper<>(dicItemService.getBaseMapper());
        dicItemUpdate.eq(SysDicItem::getDicId, modifyParam.getId()).set(SysDicItem::getStatus, modifyParam.getStatus())
            .update();
        return updateById(dicConvert.toEntity(modifyParam));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean removeBatch(BatchParam removeParam) {
        hashOperations.delete(APIRedisKeyEnum.DIC.getKey(), removeParam.getNames().toArray());
        dicItemService.getBaseMapper().delete(new QueryWrapper<SysDicItem>().lambda()
            .in(!CollectionUtils.isEmpty(removeParam.getId()), SysDicItem::getDicId, removeParam.getId()));
        return removeByIds(removeParam.getId());
    }

    @Override
    public DicDTO currentDetail(Long id) {
        DicDTO retVal = dicConvert.toDTO(getById(id));
        DicItemParam itemParam = new DicItemParam();
        itemParam.setDicId(id);
        List<DicItemDTO> items = dicItemService.queryDicItemByDicId(itemParam);
        retVal.setItems(items);
        return retVal;
    }

    /**
     * 判断字典项是否存在
     *
     * @param dicParam
     * @return
     */
    @Override
    public Boolean validateDicIfNotExist(DicParam dicParam) {
        LambdaQueryChainWrapper<SysDic> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.eq(SysDic::getCategory, dicParam.getCategory()).notIn(dicParam.getId() != null, SysDic::getId,
            dicParam.getId());
        return CollectionUtils.isEmpty(queryChainWrapper.list());
    }

    /**
     * 转换字典项名称
     *
     * @param types 字典项名称
     * @param value 字典值
     * @return 字典名称
     */
    @Override
    public String transform(String types, String value) {
        List<DicItemDTO> dicItemDTOS = hashOperations.get(APIRedisKeyEnum.DIC.getKey(), types);

        Optional<String> optional =
            dicItemDTOS.stream().filter(o -> o.getVal().equals(value)).map(DicItemDTO::getCode).findFirst();
        return optional.orElse("");
    }

    /**
     * 字典名称转换字典值。
     *
     * @param types
     * @param val
     * @return
     */
    @Override
    public String reversal(String types, String val) {

        List<DicItemDTO> dicItemDTOS = hashOperations.get(APIRedisKeyEnum.DIC.getKey(), types);

        Optional<String> optional =
            dicItemDTOS.stream().filter(o -> o.getCode().equals(val)).map(DicItemDTO::getVal).findFirst();
        return optional.orElse("");
    }

    /**
     * 状态切换
     *
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean toggle(List<DicParam> params) {
        List<SysDic> sysDics = params.stream().map(e -> {
            SysDic sysDic = new SysDic();
            sysDic.setId(e.getId());
            sysDic.setStatus(!e.getStatus());
            dicItemService.toggleItemStatus(sysDic.getId(), sysDic.getStatus());
            List<DicItemDTO> dicItemDTOS = hashOperations.get(APIRedisKeyEnum.DIC.getKey(), e.getCategory());
            dicItemDTOS.forEach(o -> {
                o.setStatus(sysDic.getStatus());
            });
            hashOperations.put(APIRedisKeyEnum.DIC.getKey(), e.getCategory(), dicItemDTOS);
            return sysDic;
        }).collect(Collectors.toList());

        return updateBatchById(sysDics);
    }
}
