package com.yhd.admin.api.domain.sys.dto;

import java.io.Serializable;
import java.util.List;

import com.yhd.admin.common.domain.dto.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName SysDicDTO.java @Description 字典表
 * @createTime 2020年05月20日 14:11:00
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class DicDTO extends BaseDTO implements Serializable, Cloneable {
    private Long id;

    /** 字典名称 */
    private String name;
    /** 字典类别 */
    private String category;
    /** 状态 */
    private Boolean status;
    /** 排序 */
    private Long orderNum;

    private List<DicItemDTO> items;
}
