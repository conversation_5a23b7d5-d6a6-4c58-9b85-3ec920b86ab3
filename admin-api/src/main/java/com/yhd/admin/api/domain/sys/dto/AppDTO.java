package com.yhd.admin.api.domain.sys.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AppDTO extends BaseDTO implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * app 图标
     */
    private String appIcon;
    /**
     * app名称
     */
    private String appName;
    /**
     * app访问地址
     */
    private String appUrl;
    /**
     * 角色状态
     */
    private Boolean isEnable;

    /**
     * 排序
     */
    private Integer sorted;
}
