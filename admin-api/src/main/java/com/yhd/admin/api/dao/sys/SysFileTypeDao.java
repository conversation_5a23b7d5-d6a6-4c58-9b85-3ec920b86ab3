package com.yhd.admin.api.dao.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yhd.admin.api.domain.sys.entity.SysFileType;

import java.util.List;

/**
 * 图文类别-数据处理层
 *
 * <AUTHOR>
 * @date 2025/7/29 9:21
 */
public interface SysFileTypeDao extends BaseMapper<SysFileType> {

  /**
   * 根据ID查询子节点
   *
   * @param id ID
   * @return List<SysFileType>
   */
  List<SysFileType> selectChildren(Long id);

  /**
   * 查询 父节点
   *
   * @param id ID
   * @return List<SysFileType>
   */
  List<SysFileType> selectParent(Long id);
}
