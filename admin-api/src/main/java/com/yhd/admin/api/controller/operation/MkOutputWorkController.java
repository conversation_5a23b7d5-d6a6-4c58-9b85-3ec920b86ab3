package com.yhd.admin.api.controller.operation;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.operation.convert.MkOutputWorkConvert;
import com.yhd.admin.api.domain.operation.dto.MkOutputWorkDTO;
import com.yhd.admin.api.domain.operation.query.MkOutputBatchParam;
import com.yhd.admin.api.domain.operation.query.MkOutputWorkParam;
import com.yhd.admin.api.domain.operation.vo.MkOutputWorkVO;
import com.yhd.admin.api.service.operation.MkOutputWorkService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 生产录入-主要工作录入表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@RestController
@RequestMapping("/output/work")
public class MkOutputWorkController {

    @Resource
    private MkOutputWorkConvert convert;

    @Resource
    private MkOutputWorkService service;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkOutputWorkVO> pagingQuery(@RequestBody MkOutputWorkParam queryParam) {
        IPage<MkOutputWorkDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MkOutputWorkParam param) {
        return RespJson.success(convert.toVO(service.getCurrentDetail(param)));
    }

    @PostMapping(
        value = "/batchAddAndModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson batchAddAndModify(@RequestBody MkOutputBatchParam batchParam) {
        Boolean retVal = service.batchAddAndModify(batchParam);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam param) {
        Boolean retVal = service.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }
}
