package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkDrillReviewDTO;
import com.yhd.admin.api.domain.operation.entity.MkDrillReview;
import com.yhd.admin.api.domain.operation.query.MkDrillReviewParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 应急演练-演练审核表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
public interface MkDrillReviewService extends IService<MkDrillReview> {

    IPage<MkDrillReviewDTO> pagingQuery(MkDrillReviewParam queryParam);

     Boolean add(MkDrillReviewParam param);

    Boolean modify(MkDrillReviewParam param);

    Boolean removeBatch(BatchParam param);

    MkDrillReviewDTO getCurrentDetail(MkDrillReviewParam param);
} 