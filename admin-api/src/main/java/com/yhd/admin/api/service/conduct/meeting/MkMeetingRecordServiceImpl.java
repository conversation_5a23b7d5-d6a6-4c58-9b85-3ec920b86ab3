package com.yhd.admin.api.service.conduct.meeting;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.configuration.FastDFSClient;
import com.yhd.admin.api.dao.conduct.meeting.MkMeetingRecordDao;
import com.yhd.admin.api.domain.conduct.convert.meeting.MkMeetingRecordConvert;
import com.yhd.admin.api.domain.conduct.dto.meeting.MkMeetingRecordDTO;
import com.yhd.admin.api.domain.conduct.entity.meeting.MkMeetingRecord;
import com.yhd.admin.api.domain.conduct.query.meeting.MkMeetingRecordParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.FileService;
import com.yhd.admin.api.service.sys.StorageServices;
import com.yhd.admin.common.utils.excel.CellBuilder;
import com.yhd.admin.common.utils.excel.WorkbookBuilder;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 调度会议记录服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-28 10:36:44
 */
@Service
public class MkMeetingRecordServiceImpl extends ServiceImpl<MkMeetingRecordDao, MkMeetingRecord> implements MkMeetingRecordService {

    public final MkMeetingRecordConvert mkMeetingRecordConvert;
    public final FileService fileService;
    public final StorageServices storageServices;
    public final FastDFSClient fastDFSClient;

    public MkMeetingRecordServiceImpl(MkMeetingRecordConvert mkMeetingRecordConvert, FileService fileService, StorageServices storageServices, FastDFSClient fastDFSClient) {
        this.mkMeetingRecordConvert = mkMeetingRecordConvert;
        this.fileService = fileService;
        this.storageServices = storageServices;
        this.fastDFSClient = fastDFSClient;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkMeetingRecordDTO> pagingQuery(MkMeetingRecordParam param) {
        IPage<MkMeetingRecord> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkMeetingRecord> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.ge(Objects.nonNull(param.getStartDate()), MkMeetingRecord::getDate, param.getStartDate());
        wrapper.le(Objects.nonNull(param.getEndDate()), MkMeetingRecord::getDate, param.getEndDate());
        wrapper.eq(Objects.nonNull(param.getTypeId()), MkMeetingRecord::getTypeId, param.getTypeId());
        wrapper.eq(StringUtils.isNotBlank(param.getName()), MkMeetingRecord::getName, param.getName());
        IPage<MkMeetingRecordDTO> resultPage = wrapper.page(page).convert(mkMeetingRecordConvert::toDTO);
        List<MkMeetingRecordDTO> list = resultPage.getRecords();
        if (CollectionUtils.isEmpty(list)) {
            return resultPage;
        }
        for (MkMeetingRecordDTO dto : list) {
            List<String> recordFile = fileService.getFileList(dto.getId(), "调度会议", "会议记录");
            List<String> signUpSheet = fileService.getFileList(dto.getId(), "调度会议", "签到表");
            List<String> pictureFile = fileService.getFileList(dto.getId(), "调度会议", "照片");
            dto.setRecordFile(recordFile);
            dto.setSignUpSheet(signUpSheet);
            dto.setPictureFile(pictureFile);
        }
        return resultPage;
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkMeetingRecordDTO> queryList(MkMeetingRecordParam param) {
        LambdaQueryWrapper<MkMeetingRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(Objects.nonNull(param.getStartDate()), MkMeetingRecord::getDate, param.getStartDate());
        wrapper.le(Objects.nonNull(param.getEndDate()), MkMeetingRecord::getDate, param.getEndDate());
        wrapper.eq(Objects.nonNull(param.getTypeId()), MkMeetingRecord::getTypeId, param.getTypeId());
        wrapper.eq(StringUtils.isNotBlank(param.getName()), MkMeetingRecord::getName, param.getName());
        List<MkMeetingRecordDTO> dtoList = mkMeetingRecordConvert.toDTOList(baseMapper.selectList(wrapper));
        for (MkMeetingRecordDTO dto : dtoList) {
            List<String> recordFile = fileService.getFileList(dto.getId(), "调度会议", "会议记录");
            List<String> signUpSheet = fileService.getFileList(dto.getId(), "调度会议", "签到表");
            List<String> pictureFile = fileService.getFileList(dto.getId(), "调度会议", "照片");
            dto.setRecordFile(recordFile);
            dto.setSignUpSheet(signUpSheet);
            dto.setPictureFile(pictureFile);
        }
        return dtoList;
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkMeetingRecordParam param) {
        MkMeetingRecord entity = mkMeetingRecordConvert.toEntity(param);
        boolean result;
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            result = save(entity);
        } else {
            // 更新操作
            result = updateById(entity);
        }
        if (!result) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(param.getRecordFile()) ||
            CollectionUtils.isNotEmpty(param.getSignUpSheet()) ||
            CollectionUtils.isNotEmpty(param.getPictureFile())) {
            try {
                fileService.insertFile(entity.getId(), "调度会议", "会议记录", param.getRecordFile());
                fileService.insertFile(entity.getId(), "调度会议", "签到表", param.getSignUpSheet());
                fileService.insertFile(entity.getId(), "调度会议", "照片", param.getPictureFile());
            } catch (Exception e) {
                throw new BMSException(ExceptionEnum.FILE_UPLOAD_ERROR);
            }
            entity.setUploadDate(LocalDate.now());
            return updateById(entity);
        }
        return true;
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkMeetingRecordDTO getCurrentDetails(MkMeetingRecordParam param) {
        MkMeetingRecordDTO dto = mkMeetingRecordConvert.toDTO(super.getById(param.getId()));
        if (Objects.nonNull(dto)) {
            return getAttachment(dto);
        } else {
            return null;
        }
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkMeetingRecordParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (removeByIds(param.getIds())) {
            for (Long id : param.getIds()) {
                fileService.removeFile(id, "调度会议", "会议记录");
                fileService.removeFile(id, "调度会议", "签到表");
                fileService.removeFile(id, "调度会议", "照片");
            }
            return true;
        } else {
            return false;
        }
    }

    private MkMeetingRecordDTO getAttachment(MkMeetingRecordDTO dto) {
        List<String> recordFile = fileService.getFileList(dto.getId(), "调度会议", "会议记录");
        List<String> signUpSheet = fileService.getFileList(dto.getId(), "调度会议", "签到表");
        List<String> pictureFile = fileService.getFileList(dto.getId(), "调度会议", "照片");
        dto.setRecordFile(recordFile);
        dto.setSignUpSheet(signUpSheet);
        dto.setPictureFile(pictureFile);
        return dto;
    }

    /**
     * 导出文件
     *
     * @return 文件地址
     */
    @Override
    public String export() {
        LambdaQueryWrapper<MkMeetingRecord> wrapper = new LambdaQueryWrapper<>();
        List<MkMeetingRecord> list = baseMapper.selectList(wrapper);

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String fileName = "调度会议记录-" + timestamp + ".xlsx";


        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 读取模板文件
            File tplFile = ResourceUtils.getFile("classpath:template" + File.separator + "调度会议记录导出模板.xlsx");
            Workbook workbook = WorkbookBuilder.getWorkBook(tplFile);
            Sheet sheet = workbook.getSheetAt(0);
            // 跳过标题行
            int rowNum = 1;
            CellStyle contentStyle = createContentStyle(workbook);

            for (MkMeetingRecord record : list) {
                Row row = sheet.createRow(rowNum++);
                CellBuilder.build(row, 0, record.getDate(), contentStyle);
                CellBuilder.build(row, 1, record.getName(), contentStyle);
                CellBuilder.build(row, 8, record.getCreator(), contentStyle);
                CellBuilder.build(row, 9, record.getUploadDate(), contentStyle);

                List<String> recordFile = fileService.getFileList(record.getId(), "调度会议", "会议记录");
                List<String> signUpSheet = fileService.getFileList(record.getId(), "调度会议", "签到表");
                List<String> pictureFile = fileService.getFileList(record.getId(), "调度会议", "照片");

                processAndBuildCell(recordFile, row, 2, 3, contentStyle);
                processAndBuildCell(signUpSheet, row, 4, 5, contentStyle);
                processAndBuildCell(pictureFile, row, 6, 7, contentStyle);


            }
            workbook.write(outputStream);

            // 上传到文件服务器
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            // 执行上传
            return fastDFSClient.getFullPath(fastDFSClient.uploadFile(inputStream, fileName));

        } catch (Exception e) {
            throw new BMSException(ExceptionEnum.EXPORT_ERROR);
        }
    }

    private CellStyle createContentStyle(Workbook workbook) {
        CellStyle contentStyle = workbook.createCellStyle();
        DataFormat format = workbook.createDataFormat();
        contentStyle.setDataFormat(format.getFormat("yyyy-mm-dd"));
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setAlignment(HorizontalAlignment.CENTER);
        contentStyle.setWrapText(true);
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);
        contentStyle.setBorderTop(BorderStyle.THIN);
        return contentStyle;
    }

    private void processAndBuildCell(List<String> fileList, Row row, int nameColIndex, int urlColIndex, CellStyle style) {
        if (CollectionUtils.isEmpty(fileList)) {
            CellBuilder.build(row, nameColIndex, "", style);
            CellBuilder.build(row, urlColIndex, "", style);
            return;
        }
        StringBuilder names = new StringBuilder();
        StringBuilder urls = new StringBuilder();

        for (String item : fileList) {
            String[] parts = item.split("#@@#");
            if (parts.length == 2) {
                if (!names.isEmpty()) {
                    names.append(",");
                    urls.append(",");
                }
                names.append(parts[1]);
                urls.append(parts[0]);
            }
        }
        CellBuilder.build(row, nameColIndex, names.toString(), style);
        CellBuilder.build(row, urlColIndex, urls.toString(), style);
    }
}
