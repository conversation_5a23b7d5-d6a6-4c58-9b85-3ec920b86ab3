package com.yhd.admin.api.domain.operation.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 运维配置中心-部门管理-部门类型表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class MkUserDeptTypeDTO extends BaseDTO implements Serializable {
	/**
	* Id
	*/
	private Long id;

	/**
	* 部门类型名称
	*/
	private String deptName;

	/**
	* 部门类型分类（0：部门类型:1：子类型）
	*/
	private String deptType;

}
