package com.yhd.admin.api.domain.monitor.fan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.*;

/**
 * 局扇控制系统
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_mk_local_fan_system")
public class TbMkLocalFanSystem extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 局扇系统名称
     */
    @TableField("system_name")
    private String systemName;

    /**
     * 安装位置
     */
    @TableField("location")
    private String location;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;
}
