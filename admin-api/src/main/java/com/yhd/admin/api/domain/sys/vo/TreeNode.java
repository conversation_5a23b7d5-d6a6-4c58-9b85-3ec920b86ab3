package com.yhd.admin.api.domain.sys.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName TreeNode.java @Description TODO 树形节点
 * @createTime 2020年04月14日 11:18:00
 */
@Data
public class TreeNode implements Cloneable, Serializable {

    /** 此项必须设置（其值在整个树范围内唯一） */
    private Object key;

    /** 此项必须设置（其值在整个树范围内唯一） */
    private Object value;
    /** 树节点显示的内容 */
    private String title;
    /** 是否是叶子节点 */
    private Boolean isLeaf = Boolean.FALSE;
    /** 是否禁用 */
    private Boolean disabled = Boolean.FALSE;

    /** 是否可以选择 */
    private Boolean selectable = Boolean.TRUE;
    /** 是否可以选中 */
    private Boolean checkable = Boolean.TRUE;

    /** 子节点 */
    private List<TreeNode> children;

    /**
     * 字段编码
     */
    private String code;
}
