package com.yhd.admin.api.domain.sys.vo;

import com.yhd.admin.common.domain.vo.BaseVO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/17 14:35
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
public class TemplateTableVO extends BaseVO {

    /** 主键 */
    private Long id;
    /** */
    private String tableName;
    /** pro-table 类型 */
    private String type;
    /** 表格标题 */
    private String title;
    /** 空值时的显示，不设置时显示 -， false 可以关闭此功能 */
    private String columnEmptyText;
    /** 是否展示外边框和列边框 */
    private Boolean bordered;
    /** 页面是否加载中 */
    private Boolean loading;
    /** 分页器，参考配置项或 pagination 文档，设为 false 时不展示和进行分页 */
    private String pagination;
    /** 表格行 key 的取值，可以是字符串或一个函数 */
    private String rowKey;
    /** 是否显示表头 */
    private Boolean showHeader;
    /** 表头是否显示下一次排序的 tooltip 提示。当参数类型为对象时，将被设置为 Tooltip 的属性 */
    private Boolean showSorterTooltip;
    /** 表格大小;default | middle | small */
    private String size;
    /** 支持的排序方式，取值为 ascend descend */
    private String sortDirections;
    /**
     * 设置粘性头部和滚动条;boolean | {offsetHeader?: number, offsetScroll?: number, getContainer?: () => HTMLElement}
     */
    private String sticky;
    /** 总结栏 */
    private String summary;
    /** 表格元素的 table-layout 属性，设为 fixed 表示内容不会影响列的布局 */
    private String tableLayout;
    /** 一个获得 dataSource 的方法 */
    private String request;
    /** 渲染操作栏 */
    private String toolBarRender;
    /** 自定义 table 的 alert */
    private String tableAlertRender;
    /** 自定义 table 的 alert 的操作 */
    private String tableAlertOptionRender;
    /** 表格行是否可选择 */
    private String rowSelection;
    /** 可编辑表格的相关配置 */
    private String editable;
    /** able 和 Search 外围 Card 组件的边框 */
    private String cardbordered;
    /** 分页、排序、筛选变化时触发 */
    private String onChange;
    /** 设置头部行属性 */
    private String onHeaderRow;
    /** 设置行属性 */
    private String onRow;
}
