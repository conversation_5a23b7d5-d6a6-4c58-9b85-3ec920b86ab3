package com.yhd.admin.api.domain.monitor.ventilation.convert;

import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkVentilationOperationDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkVentilationOperation;
import com.yhd.admin.api.domain.monitor.ventilation.vo.TbMkVentilationOperationVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkVentilationOperationCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkVentilationOperationUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 通风机整体运行参数
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkVentilationOperationConvert {

    @Mapping(target = "id", ignore = true)
    TbMkVentilationOperation convert(TbMkVentilationOperationCreateVO createVO);

    TbMkVentilationOperation convert(TbMkVentilationOperationUpdateVO updateVO);

    TbMkVentilationOperationVO convert(TbMkVentilationOperation po);

    TbMkVentilationOperationDTO toDTO(TbMkVentilationOperation po);



}
