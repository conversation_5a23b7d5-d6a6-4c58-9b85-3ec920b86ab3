package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkDustSensor.java
 * @Description 粉尘监测传感器表
 * @createTime 2025年08月02日 10:00:00
 */
@Data
@TableName("tb_mk_dust_sensor")
public class MkDustSensor {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 传感器编码
     */
    private String sensorCode;

    /**
     * 传感器名称
     */
    private String sensorName;

    /**
     * 传感器类型
     */
    private String sensorType;

    /**
     * 监测类型：粉尘监测、顶板检测
     */
    private String monitoringType;

    /**
     * 威胁类别
     */
    private String threatCategory;

    /**
     * 所属区域编码
     */
    private String areaCode;

    /**
     * 所属区域名称
     */
    private String areaName;

    /**
     * 安装位置
     */
    private String location;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * X坐标
     */
    private BigDecimal coordinateX;

    /**
     * Y坐标
     */
    private BigDecimal coordinateY;

    /**
     * Z坐标
     */
    private BigDecimal coordinateZ;

    /**
     * 制造商
     */
    private String manufacturer;

    /**
     * 型号
     */
    private String model;

    /**
     * 安装日期
     */
    private LocalDate installDate;

    /**
     * 维护日期
     */
    private LocalDate maintenanceDate;

    /**
     * 下次维护日期
     */
    private LocalDate nextMaintenanceDate;

    /**
     * 校准日期
     */
    private LocalDate calibrationDate;

    /**
     * 下次校准日期
     */
    private LocalDate nextCalibrationDate;

    /**
     * 状态：1-正常，2-异常，3-离线，4-维护中
     */
    private Integer status;

    /**
     * 是否启用：0-禁用，1-启用
     */
    private Boolean isActive;

    /**
     * 负责人
     */
    private String responsiblePerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}