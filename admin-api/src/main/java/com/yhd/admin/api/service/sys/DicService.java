package com.yhd.admin.api.service.sys;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.sys.dto.DicDTO;
import com.yhd.admin.api.domain.sys.entity.SysDic;
import com.yhd.admin.api.domain.sys.query.DicParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName SysDicService.java @Description TODO 字典项服务类
 * @createTime 2020年05月20日 14:22:00
 */
public interface DicService extends IService<SysDic> {

    /**
     * 分页查询
     *
     * @param queryParam 分页查询参数
     * @return {@link IPage< DicDTO >}
     */
    IPage<DicDTO> pagingQuery(DicParam queryParam);

    /**
     * 新增字典类别
     *
     * @param addParam 新增对象
     * @return true 成功，false 失败
     */
    Boolean saveOrUpdate(DicParam addParam);

    /**
     * 修改字典类别
     *
     * @param modifyParam 修改对象
     * @return true 成功，false 失败
     */
    Boolean modifyDic(DicParam modifyParam);

    /**
     * 修改字典类别
     *
     * @param removeParam 修改对象
     * @return true 成功，false 失败
     */
    Boolean removeBatch(BatchParam removeParam);

    /**
     * 根据ID查询详情信息。
     *
     * @param id
     * @return
     */
    DicDTO currentDetail(Long id);

    /**
     * 判断字典项是否存在
     *
     * @param dicParam
     * @return
     */
    Boolean validateDicIfNotExist(DicParam dicParam);

    /**
     * 转换字典项名称
     *
     * @param types 字典项名称
     * @param value 字典值
     * @return 字典名称
     */
    String transform(String types, String value);

    /**
     * 字典名称转换字典值。
     *
     * @param types
     * @param val
     * @return
     */
    String reversal(String types, String val);

    /**
     * 状态切换
     *
     * @param params
     * @return
     */
    Boolean toggle(List<DicParam> params);
}
