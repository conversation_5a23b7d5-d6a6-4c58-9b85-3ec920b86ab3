package com.yhd.admin.api.domain.emergency.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 快照信息实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_mk_snapshot_info")
public class MkSnapshotInfo extends BaseEntity implements Serializable, Cloneable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 快照ID
     */
    private Long snapshotId;

    /**
     * 列表类型(EVENT:事件列表,TIME:时间列表)
     */
    private String listType;

    /**
     * 事件ID字段
     */
    private String eventId;

    /**
     * 事件名称字段
     */
    private String eventName;

    /**
     * 信息类型
     */
    private String infoType;

    /**
     * 信息分类
     */
    private String infoCategory;

    /**
     * 信息名称
     */
    private String infoName;

    /**
     * 信息值
     */
    private String infoValue;

    /**
     * 信息单位
     */
    private String infoUnit;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 采集时间
     */
    private LocalDateTime collectTime;

    /**
     * 位置信息
     */
    private String locationInfo;

    /**
     * 优先级(1:低,2:中,3:高,4:紧急)
     */
    private Integer priorityLevel;

    /**
     * 状态(NORMAL:正常,WARNING:警告,ALARM:报警)
     */
    private String status;

    /**
     * 阈值下限
     */
    private BigDecimal thresholdMin;

    /**
     * 阈值上限
     */
    private BigDecimal thresholdMax;

    /**
     * 备注
     */
    private String remark;
}
