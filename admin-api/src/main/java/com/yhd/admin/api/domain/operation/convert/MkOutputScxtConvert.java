package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkOutputScxtDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputScxt;
import com.yhd.admin.api.domain.operation.query.MkOutputScxtParam;
import com.yhd.admin.api.domain.operation.vo.MkOutputScxtVO;
import org.mapstruct.Mapper;


/**
* 生产录入-生产系统内容表
*
* <AUTHOR>
* @since 1.0.0 2025-07-30
*/
@Mapper(componentModel = "spring")
public interface MkOutputScxtConvert {

    MkOutputScxt toEntity(MkOutputScxtParam param);

    MkOutputScxtVO toVO(MkOutputScxtDTO dto);

    MkOutputScxtDTO toDTO(MkOutputScxt entity);

}
