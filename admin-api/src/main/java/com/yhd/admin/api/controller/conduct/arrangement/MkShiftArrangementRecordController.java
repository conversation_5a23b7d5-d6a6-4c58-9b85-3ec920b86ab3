package com.yhd.admin.api.controller.conduct.arrangement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.arrangement.MkShiftArrangementRecordConvert;
import com.yhd.admin.api.domain.conduct.dto.arrangement.MkShiftArrangementRecordDTO;
import com.yhd.admin.api.domain.conduct.query.arrangement.MkShiftArrangementRecordParam;
import com.yhd.admin.api.domain.conduct.vo.arrangement.MkShiftArrangementRecordVO;
import com.yhd.admin.api.service.conduct.arrangement.MkShiftArrangementRecordService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 调班记录表控制层
 *
 * <AUTHOR>
 * @since 2025-07-29 11:30:39
 */
@RestController
@RequestMapping("/conduct/shift/record")
public class MkShiftArrangementRecordController {

    private final MkShiftArrangementRecordService mkShiftArrangementRecordService;
    private final MkShiftArrangementRecordConvert mkShiftArrangementRecordConvert;

    public MkShiftArrangementRecordController(
        MkShiftArrangementRecordService mkShiftArrangementRecordService,
        MkShiftArrangementRecordConvert mkShiftArrangementRecordConvert
    ) {
        this.mkShiftArrangementRecordService = mkShiftArrangementRecordService;
        this.mkShiftArrangementRecordConvert = mkShiftArrangementRecordConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkShiftArrangementRecordParam param) {
        IPage<MkShiftArrangementRecordDTO> page = mkShiftArrangementRecordService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkShiftArrangementRecordConvert::toVO));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkShiftArrangementRecordVO> getCurrentDetails(@RequestBody MkShiftArrangementRecordParam param) {
        return RespJson.success(mkShiftArrangementRecordConvert.toVO(mkShiftArrangementRecordService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkShiftArrangementRecordParam param) {
        Boolean retVal = mkShiftArrangementRecordService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 审批
     *
     * @param param 主键
     * @return 是否成功
     */
    @PostMapping(
        value = "/handle",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> handle(@RequestBody MkShiftArrangementRecordParam param) {
        Boolean retVal = mkShiftArrangementRecordService.handle(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 导出文件
     *
     * @return 文件地址
     */
    @PostMapping(
        value = "/export",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<String> export(@RequestBody MkShiftArrangementRecordParam param) {
        String url = mkShiftArrangementRecordService.export(param);
        return RespJson.success(url);
    }

}

