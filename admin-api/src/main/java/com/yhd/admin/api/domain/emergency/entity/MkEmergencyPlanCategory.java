package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 应急预案分类表
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tb_mk_emergency_plan_category")
public class MkEmergencyPlanCategory extends BaseEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 父级ID，0表示根节点
     */
    private Integer parentId;

    /**
     * 分类层级
     */
    private Integer categoryLevel;

    /**
     * 分类路径，如：1/2/3
     */
    private String categoryPath;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 分类图标
     */
    private String categoryIcon;

    /**
     * 是否叶子节点：0-否，1-是
     */
    private Boolean isLeaf;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
}
