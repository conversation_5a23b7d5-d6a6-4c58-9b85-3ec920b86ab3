package com.yhd.admin.api.controller.emergency;

import com.yhd.admin.api.domain.emergency.convert.MkDustMonitoringDataConvert;
import com.yhd.admin.api.domain.emergency.entity.MkDustMonitoringData;
import com.yhd.admin.api.domain.emergency.query.MkDustMonitoringDataParam;
import com.yhd.admin.api.domain.emergency.vo.MkDataVOList;
import com.yhd.admin.api.domain.emergency.vo.MkDustMonitoringDataVO;
import com.yhd.admin.api.service.emergency.MkDustMonitoringDataService;
import com.yhd.admin.common.domain.RespJson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkDustMonitoringDataController.java
 * @Description 粉尘监测数据-灾害预警中心Controller
 * @createTime 2025年08月02日 10:00:00
 */
@RestController
@RequestMapping("/open/emergency/monitoring-data")
@Slf4j
public class MkDustMonitoringDataController {

    private final MkDustMonitoringDataService dustMonitoringDataService;
    private final MkDustMonitoringDataConvert convert;

    public MkDustMonitoringDataController(MkDustMonitoringDataService dustMonitoringDataService,
                                          MkDustMonitoringDataConvert convert) {
        this.dustMonitoringDataService = dustMonitoringDataService;
        this.convert = convert;

    }

    /**
     * 查询监测数据列表（支持按监测类型筛选）
     *
     * @param param 查询参数，支持监测类型、时间范围等筛选
     * @return 监测数据列表
     */
    @PostMapping(value = "/queryList", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkDataVOList> queryList(
        @RequestBody(required = false) MkDustMonitoringDataParam param) {
        log.info("查询监测数据列表，参数：{}", param);
        MkDataVOList mkDataVOList = new MkDataVOList();
        List<MkDustMonitoringData> dataList = dustMonitoringDataService.queryList(param);
        mkDataVOList.setMkDustMonitoringData(dataList);
        //过滤isAlarm是报警的数据
        long count = dataList.stream().filter(e -> !e.getIsAlarm()).count();
        mkDataVOList.setBjTotalValue(count);
        //过滤isAlarm是正常的数据
        long count1 = dataList.stream().filter(e -> e.getIsAlarm()).count();
        mkDataVOList.setZcTotalValue(count1);

        return RespJson.success(mkDataVOList);
    }

    /**
     * 获取传感器最新监测数据
     *
     * @param sensorCode 传感器编码
     * @return 最新监测数据
     */
    @PostMapping(value = "/getDetail", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkDustMonitoringDataVO> getDetail(@RequestBody(required = false) MkDustMonitoringDataParam param) {
        if (param == null || !StringUtils.hasText(param.getSensorCode())) {
            return RespJson.failure("编码不能为空");
        }
        MkDustMonitoringData latestData = dustMonitoringDataService.getLatestBySensorCode(param.getSensorCode());
        if (latestData == null) {
            return RespJson.failure("未找到该传感器的监测数据");
        }
        MkDustMonitoringDataVO vo = convert.toVO(latestData);
        return RespJson.success(vo);
    }


}
