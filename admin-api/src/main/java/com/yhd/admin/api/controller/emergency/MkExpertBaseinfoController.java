package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkExpertBaseinfoConvert;
import com.yhd.admin.api.domain.emergency.dto.MkExpertBaseinfoDTO;
import com.yhd.admin.api.domain.emergency.query.MkExpertBaseinfoParam;
import com.yhd.admin.api.domain.emergency.vo.MkExpertBaseinfoVO;
import com.yhd.admin.api.service.emergency.MkExpertBaseinfoService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 应急救援中心-专家库 Controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/emergency/expert")
public class MkExpertBaseinfoController {

    @Resource
    private MkExpertBaseinfoService expertService;

    @Resource
    private MkExpertBaseinfoConvert convert;

    /**
     * 分页查询专家信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkExpertBaseinfoVO> pagingQuery(@RequestBody MkExpertBaseinfoParam queryParam) {
        log.info("分页查询专家信息，参数：{}", queryParam);
        IPage<MkExpertBaseinfoDTO> iPage = expertService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    /**
     * 新增专家信息
     * @param param 专家信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkExpertBaseinfoParam param) {
        log.info("新增专家信息，参数：{}", param);
        Boolean result = expertService.add(param);
        return RespJson.success(result);
    }

    /**
     * 修改专家信息
     * @param param 专家信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> modify(@RequestBody MkExpertBaseinfoParam param) {
        log.info("修改专家信息，参数：{}", param);
        Boolean result = expertService.modify(param);
        return RespJson.success(result);
    }

    /**
     * 获取专家详情
     * @param param 查询参数
     * @return 专家详情
     */
    @PostMapping(value = "/getCurrentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkExpertBaseinfoVO> getCurrentDetail(@RequestBody MkExpertBaseinfoParam param) {
        log.info("获取专家详情，参数：{}", param);
        MkExpertBaseinfoDTO dto = expertService.getCurrentDetail(param);
        return RespJson.success(convert.toVO(dto));
    }

    /**
     * 批量删除专家信息
     * @param param 批量删除参数
     * @return 操作结果
     */
    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody BatchParam param) {
        log.info("批量删除专家信息，参数：{}", param);
        Boolean result = expertService.removeBatch(param);
        return RespJson.success(result);
    }
}
