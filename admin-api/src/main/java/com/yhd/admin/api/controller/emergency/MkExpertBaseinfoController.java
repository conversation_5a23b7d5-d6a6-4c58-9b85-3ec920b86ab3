package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkExpertBaseinfoConvert;
import com.yhd.admin.api.domain.emergency.dto.MkExpertBaseinfoDTO;
import com.yhd.admin.api.domain.emergency.query.MkExpertBaseinfoParam;
import com.yhd.admin.api.domain.emergency.vo.MkExpertBaseinfoVO;
import com.yhd.admin.api.service.emergency.MkExpertBaseinfoService;
import com.yhd.admin.common.domain.PageRespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 应急救援中心-专家库 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/open/emergency/expert")
public class MkExpertBaseinfoController {

    @Resource
    private MkExpertBaseinfoService expertService;

    @Resource
    private MkExpertBaseinfoConvert convert;

    /**
     * 分页查询专家信息
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkExpertBaseinfoVO> pagingQuery(@RequestBody MkExpertBaseinfoParam queryParam) {
        log.info("分页查询专家信息，参数：{}", queryParam);
        IPage<MkExpertBaseinfoDTO> iPage = expertService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }


}
