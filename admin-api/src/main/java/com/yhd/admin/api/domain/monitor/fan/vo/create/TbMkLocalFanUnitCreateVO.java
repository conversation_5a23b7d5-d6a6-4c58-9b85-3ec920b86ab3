package com.yhd.admin.api.domain.monitor.fan.vo.create;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 局扇风机单元
 *
 * <AUTHOR>
 * @since
 */
@Data
public class TbMkLocalFanUnitCreateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 所属局扇系统ID
     */
    private Long systemId;

    /**
     * 风机编号（如：FAN-01）
     */
    private String fanNumber;

    /**
     * 风机型号
     */
    private String model;

    /**
     * 额定功率(kW)
     */
    private BigDecimal ratedPower;

    /**
     * 额定风量(m³/min)
     */
    private BigDecimal ratedAirVolume;

    /**
     * 安装日期
     */
    private LocalDate installationDate;

    /**
     * 状态：1-运行，0-停用
     */
    private Byte status;

}
