package com.yhd.admin.api.service.operation;

import com.yhd.admin.api.dao.operation.MkDisasterIndexDao;
import com.yhd.admin.api.domain.operation.convert.MkDisasterIndexConvert;
import com.yhd.admin.api.domain.operation.dto.MkDisasterIndexDTO;
import com.yhd.admin.api.domain.operation.entity.MkDisasterIndex;
import com.yhd.admin.api.domain.operation.query.MkDisasterIndexParam;
import com.yhd.admin.api.service.sys.DicService;
import com.yhd.admin.common.constant.DicConstant;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;


/**
 * 算法模型-自然灾害评价指标表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@Service
public class MkDisasterIndexServiceImpl extends ServiceImpl<MkDisasterIndexDao, MkDisasterIndex> implements MkDisasterIndexService {

    @Resource
    private MkDisasterIndexConvert convert;

    @Resource
    private MkDisasterIndexService service;

    @Resource
    private DicService dicService;

    @Override
    public IPage<MkDisasterIndexDTO> pagingQuery(MkDisasterIndexParam queryParam) {
        Page<MkDisasterIndex> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkDisasterIndex> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        queryChain.orderByDesc(MkDisasterIndex::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkDisasterIndexParam param) {
        if (StringUtils.isNotBlank(param.getIndexCode())){
            param.setIndexName(dicService.transform(DicConstant.DISASTER_INDEX, param.getIndexCode()));
        }
        MkDisasterIndex entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MkDisasterIndexParam param) {
        if (StringUtils.isNotBlank(param.getIndexCode())){
            param.setIndexName(dicService.transform(DicConstant.DISASTER_INDEX, param.getIndexCode()));
        }
        MkDisasterIndex entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MkDisasterIndexDTO getCurrentDetail(MkDisasterIndexParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

}
