package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkDisasterLevelDTO;
import com.yhd.admin.api.domain.operation.entity.MkDisasterLevel;
import com.yhd.admin.api.domain.operation.query.MkDisasterLevelParam;
import com.yhd.admin.api.domain.operation.vo.MkDisasterLevelVO;
import org.mapstruct.Mapper;


/**
* 算法模型-报警级别表
*
* <AUTHOR>
* @since 1.0.0 2025-08-01
*/
@Mapper(componentModel = "spring")
public interface MkDisasterLevelConvert {

    MkDisasterLevel toEntity(MkDisasterLevelParam param);

    MkDisasterLevelVO toVO(MkDisasterLevelDTO dto);

    MkDisasterLevelDTO toDTO(MkDisasterLevel entity);

}
