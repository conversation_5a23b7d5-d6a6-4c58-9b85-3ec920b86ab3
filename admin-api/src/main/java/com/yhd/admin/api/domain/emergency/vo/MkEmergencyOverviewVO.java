package com.yhd.admin.api.domain.emergency.vo;

import lombok.Data;
import java.util.List;

/**
 * 应急救援中心总览VO
 *
 * <AUTHOR>
 */
@Data
public class MkEmergencyOverviewVO {

    /**
     * 入井领导信息
     */
    private MkEmergencyLeaderInfoVO leaders;

    /**
     * 下井人员统计信息
     */
    private MkEmergencyPersonnelStatsInfoVO personnelStats;

    /**
     * 生产安全总结信息
     */
    private MkEmergencySafetySummaryInfoVO safetySummary;

    /**
     * 矿井工作面信息列表
     */
    private List<MkEmergencyWorkfaceInfoVO> workfaces;

    /**
     * 功能导航信息列表
     */
    private List<MkEmergencyNavigationInfoVO> navigations;

    /**
     * 当前选中导航的列表项目
     */
    private List<MkEmergencyListItemInfoVO> listItems;
}
