package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.sys.dto.ParameterDTO;
import com.yhd.admin.api.domain.sys.entity.SysParameter;
import com.yhd.admin.api.domain.sys.query.ParameterParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 系统参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ParameterService.java
 * @Description TODO
 * @createTime 2020年05月12日 14:54:00
 */
public interface ParameterService extends IService<SysParameter> {

    /**
     * 新增系统参数
     *
     * @param addParam 系统参数
     * @return 【true 成功，false 失败】
     */
    Boolean saveOrUpdate(ParameterParam addParam);

    /**
     * 修改系统参数
     *
     * @param modifyParam 系统参数
     * @return 【true 成功，false 失败】
     */
    Boolean modify(ParameterParam modifyParam);

    /**
     * 批量删除系统参数
     *
     * @param removeParam 系统参数ID集合
     * @return 【t 成功，false 失败】
     */
    Boolean removeBatch(BatchParam removeParam);

    /**
     * 系统参数列表分页查询
     *
     * @param queryParam 系统参数查询参数
     * @return IPage<MenuDTO>
     */
    IPage<ParameterDTO> pagingQuery(ParameterParam queryParam);

    /**
     * @param id
     * @return
     */
    ParameterDTO currentDetail(String id);

    Boolean validateIfNotExist(ParameterParam param);

    /**
     * 根据 KEY 查询参数值。
     *
     * @param key
     * @return
     */
    ParameterDTO getParameterByKey(String key);

    /**
     * 根据 KEY 查询参数值。
     *
     * @param key
     * @return
     */
    String getParameterStrByKey(String key);
}
