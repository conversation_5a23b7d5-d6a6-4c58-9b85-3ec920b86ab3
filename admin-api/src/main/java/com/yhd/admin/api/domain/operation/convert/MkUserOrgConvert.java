package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkUserOrgDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserOrg;
import com.yhd.admin.api.domain.operation.query.MkUserOrgParam;
import com.yhd.admin.api.domain.operation.vo.MkUserOrgVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * 单位管理表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */
@Mapper(componentModel = "spring")
public interface MkUserOrgConvert {

    MkUserOrg toEntity(MkUserOrgParam param);

    MkUserOrgVO toVO(MkUserOrgDTO dto);

    List<MkUserOrgVO> toVOList(List<MkUserOrgDTO> dtos);

    MkUserOrgDTO toDTO(MkUserOrg entity);

    List<MkUserOrgDTO> toDTOList(List<MkUserOrg> entity);

}
