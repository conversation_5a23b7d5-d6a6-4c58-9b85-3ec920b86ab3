package com.yhd.admin.api.domain.monitor.digging.vo.page;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 掘进机基本信息
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkDiggingInfoPageVO extends QueryParam {

    private static final long serialVersionUID = 1L;



        /**
         * 设备编号
         */
        private String deviceNumber;

        /**
         * 设备名称
         */
        private String deviceName;

        /**
         * 备注信息
         */
        private String remarks;
}
