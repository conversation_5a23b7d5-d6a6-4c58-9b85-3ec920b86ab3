package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkUserOrgDTO;
import com.yhd.admin.api.domain.operation.dto.MkUserOrgTypeDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserDeptType;
import com.yhd.admin.api.domain.operation.entity.MkUserOrg;
import com.yhd.admin.api.domain.operation.entity.MkUserOrgType;
import com.yhd.admin.api.domain.operation.query.MkUserOrgTypeParam;
import com.yhd.admin.api.domain.operation.vo.MkUserOrgTypeVO;
import com.yhd.admin.api.domain.operation.vo.MkUserOrgVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
* 单位类型表
*
* <AUTHOR>
* @since 1.0.0 2025-07-28
*/
@Mapper(componentModel = "spring")
public interface MkUserOrgTypeConvert {

    MkUserOrgType toEntity(MkUserOrgTypeParam param);

    MkUserOrgTypeVO toVO(MkUserOrgTypeDTO dto);

    MkUserOrgTypeDTO toDTO(MkUserOrgType entity);

    List<MkUserOrgTypeVO> toVOList(List<MkUserOrgTypeDTO> dtos);

    List<MkUserOrgTypeDTO> toDTOList(List<MkUserOrgType> entity);

}
