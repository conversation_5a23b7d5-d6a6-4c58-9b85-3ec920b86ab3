package com.yhd.admin.api.domain.sys.query;

import java.io.Serializable;

import com.yhd.admin.common.domain.query.Param;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName DicItemParam.java @Description TODO
 * @createTime 2020年06月16日 09:12:00
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class DicItemParam extends Param implements Cloneable, Serializable {
    private Long id;
    /** 字典表主键 */
    private Long dicId;
    /** 字典分类 */
    private String category;
    /** 编码 */
    private String code;
    /** 编码值 */
    private String val;
    /** 状态 */
    private Boolean status;
    /** 排序 */
    private Long orderNum;
}
