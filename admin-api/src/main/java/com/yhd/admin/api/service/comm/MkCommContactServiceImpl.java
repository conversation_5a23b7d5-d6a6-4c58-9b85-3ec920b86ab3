package com.yhd.admin.api.service.comm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.comm.MkCommContactDao;
import com.yhd.admin.api.domain.comm.convert.MkCommContactConvert;
import com.yhd.admin.api.domain.comm.convert.MkCommContactPhoneConvert;
import com.yhd.admin.api.domain.comm.dto.MkCommContactDTO;
import com.yhd.admin.api.domain.comm.entity.MkCommContact;
import com.yhd.admin.api.domain.comm.entity.MkCommContactPhone;
import com.yhd.admin.api.domain.comm.query.MkCommContactParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 调度通讯-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2025/7/25 14:59
 */
@Service
public class MkCommContactServiceImpl extends ServiceImpl<MkCommContactDao, MkCommContact>
    implements MkCommContactService {
  private static final Logger logger = LoggerFactory.getLogger(MkCommContactServiceImpl.class);

  private final MkCommContactConvert contactConvert;
  private final MkCommContactPhoneConvert contactPhoneConvert;
  private final MkCommContactPhoneService contactPhoneService;

  public MkCommContactServiceImpl(
      MkCommContactConvert contactConvert,
      MkCommContactPhoneConvert contactPhoneConvert,
      MkCommContactPhoneService contactPhoneService) {
    this.contactConvert = contactConvert;
    this.contactPhoneConvert = contactPhoneConvert;
    this.contactPhoneService = contactPhoneService;
  }

  /**
   * 新增
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean add(MkCommContactParam param) {
    logger.debug("进入service层，调用【新增通讯信息】接口开始，表单参数：{}", param);
    MkCommContact entity = contactConvert.toEntity(param);
    boolean result = super.save(entity);
    logger.debug("新增通讯信息，通讯信息插入数据库表，结果：{}", result);
    if (result) {
      // 新增成功，添加联系电话信息
      if (CollectionUtils.isNotEmpty(param.getContactPhoneList())) {
        List<MkCommContactPhone> phoneList =
            contactPhoneConvert.toEntity(param.getContactPhoneList());
        phoneList.forEach(v -> v.setContactId(entity.getId()));

        boolean savePhone = contactPhoneService.saveBatch(phoneList);
        logger.debug("新增通讯信息，联系人电话插入数据库表，结果：{}", savePhone);
        result = savePhone;
      }
    }

    logger.debug("调用【新增通讯信息】接口，结果：{}", result);
    return result;
  }

  /**
   * 编辑
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean modify(MkCommContactParam param) {
    logger.debug("进入service层，调用【编辑通讯信息】接口开始，表单参数：{}", param);
    MkCommContact entity = contactConvert.toEntity(param);
    boolean result = super.updateById(entity);
    logger.debug("编辑通讯信息，变更数据库通讯信息，结果：{}", result);
    if (result) {
      // 编辑成功，变更联系电话信息->先删除，再新增
      boolean removePhone = contactPhoneService.removeById(entity.getId());
      if (removePhone) {
        if (CollectionUtils.isNotEmpty(param.getContactPhoneList())) {
          List<MkCommContactPhone> phoneList =
              contactPhoneConvert.toEntity(param.getContactPhoneList());
          phoneList.forEach(v -> v.setContactId(entity.getId()));

          boolean savePhone = contactPhoneService.saveBatch(phoneList);
          logger.debug("编辑通讯信息，变更数据库表联系人电话信息，结果：{}", savePhone);
          result = savePhone;
        }
      }
    }

    return result;
  }

  /**
   * 删除-根据主键
   *
   * @param param id
   * @return true成功，false失败
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean remove(MkCommContactParam param) {
    logger.debug("进入service层，调用【删除通讯信息】接口开始，参数：id={}", param.getIds());
    boolean result = super.removeBatchByIds(param.getIds());
    logger.debug("删除通讯信息，删除通讯信息表数据，结果：{}", result);
    if (result) {
      // 同步删除联系人电话信息
      boolean removePhone = contactPhoneService.removeByIds(param.getIds());
      logger.debug("删除通讯信息，删除联系人电话表数据，结果：{}", removePhone);
      result = removePhone;
    }

    return result;
  }

  /**
   * 查询详情信息
   *
   * @param param 主键id or 编号
   * @return 通讯详情信息
   */
  @Override
  public MkCommContactDTO getCurrentDetail(MkCommContactParam param) {
    logger.debug("进入service层，调用【查询通讯详情信息】接口开始，参数：id={}", param.getId());
    MkCommContactDTO result = null;
    if (Objects.isNull(param.getId())) {
      throw new BMSException(ExceptionEnum.MISS_PARAM_EXCEPTION);
    }
    MkCommContact entity = super.getById(param);
    if (Objects.nonNull(entity)) {
      result = contactConvert.toDTO(entity);
      // 添加联系电话信息
      LambdaQueryWrapper<MkCommContactPhone> wrapper = new LambdaQueryWrapper<>();
      wrapper.eq(MkCommContactPhone::getContactId, param.getId());
      // 排序
      wrapper.orderByAsc(MkCommContactPhone::getPhoneType);
      List<MkCommContactPhone> contactPhoneList = contactPhoneService.list(wrapper);
      if (CollectionUtils.isNotEmpty(contactPhoneList)) {
        result.setContactPhoneList(contactPhoneConvert.toDTO(contactPhoneList));
      }
    }

    return result;
  }

  /**
   * 根据条件查询通讯信息分页列表
   *
   * @param param 查询条件
   * @return 通讯信息分页列表
   */
  @Override
  public IPage<MkCommContactDTO> pagingQuery(MkCommContactParam param) {
    logger.debug("进入service层，调用【根据条件查询通讯信息分页列表】接口开始，参数：{}", param);
    IPage<MkCommContact> page = new Page<>(param.getCurrent(), param.getPageSize());
    LambdaQueryChainWrapper<MkCommContact> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
    // 查询条件
    // 单位编码
    wrapper.eq(
        StringUtils.isNotBlank(param.getOrgCode()), MkCommContact::getOrgCode, param.getOrgCode());
    // 姓名，模糊查询
    wrapper.like(
        StringUtils.isNotBlank(param.getRealName()),
        MkCommContact::getRealName,
        param.getRealName());
    // 类型1：人员 2：地点
    wrapper.eq(StringUtils.isNotBlank(param.getType()), MkCommContact::getType, param.getType());

    // 排序
    wrapper.orderByAsc(MkCommContact::getSortNum);

    IPage<MkCommContact> iPage = wrapper.page(page);

    return iPage.convert(contactConvert::toDTO);
  }
}
