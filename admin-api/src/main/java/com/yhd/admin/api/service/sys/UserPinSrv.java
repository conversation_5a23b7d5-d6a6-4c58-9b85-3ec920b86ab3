package com.yhd.admin.api.service.sys;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.common.domain.dto.UserPinDTO;
import com.yhd.admin.common.domain.entity.SysUserPin;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName UserAccountService.java @Description TODO
 * @createTime 2020年03月30日 11:14:00
 */
public interface UserPinSrv extends IService<SysUserPin> {

    /**
     * @param uid 用户主键
     * @param username 用户登陆名称
     * @return UserPinDTO
     */
    UserPinDTO getUserPin(String uid, String username);

    /**
     * 修改用户登陆密码
     *
     * @param uid 用户ID
     * @param username 用户名字
     * @param password 密码
     * @return BOOLEAN
     */
    Boolean saveOrUpdate(String uid, String username, String password);

    /**
     * 批量删除 账户，根据主键 ID
     *
     * @param list {@link List<String>}
     * @return true 成功，false 失败。
     */
    Boolean removeBatch(List<String> list);

    /**
     * 重置密码。
     *
     * @param uid
     * @param username
     * @return
     */
    Boolean resetPassWord(String uid, String username, String passWord);
}
