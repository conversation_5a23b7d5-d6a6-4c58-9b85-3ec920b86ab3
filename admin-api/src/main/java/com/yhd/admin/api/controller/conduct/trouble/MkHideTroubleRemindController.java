package com.yhd.admin.api.controller.conduct.trouble;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.trouble.MkHideTroubleRemindConvert;
import com.yhd.admin.api.domain.conduct.dto.trouble.MkHideTroubleRemindDTO;
import com.yhd.admin.api.domain.conduct.query.trouble.MkHideTroubleRemindParam;
import com.yhd.admin.api.service.conduct.trouble.MkHideTroubleRemindService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 隐患催办记录表控制层
 *
 * <AUTHOR>
 * @since 2025-08-04 10:28:36
 */
@RestController
@RequestMapping("/conduct/trouble/remind")
public class MkHideTroubleRemindController {

    private final MkHideTroubleRemindService mkHideTroubleRemindService;
    private final MkHideTroubleRemindConvert mkHideTroubleRemindConvert;

    public MkHideTroubleRemindController(
        MkHideTroubleRemindService mkHideTroubleRemindService,
        MkHideTroubleRemindConvert mkHideTroubleRemindConvert
    ) {
        this.mkHideTroubleRemindService = mkHideTroubleRemindService;
        this.mkHideTroubleRemindConvert = mkHideTroubleRemindConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkHideTroubleRemindParam param) {
        IPage<MkHideTroubleRemindDTO> page = mkHideTroubleRemindService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkHideTroubleRemindConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkHideTroubleRemindParam param) {
        return RespJson.success(mkHideTroubleRemindConvert.toVOList(mkHideTroubleRemindService.queryList(param)));
    }

}

