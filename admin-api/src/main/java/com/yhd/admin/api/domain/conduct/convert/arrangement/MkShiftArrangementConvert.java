package com.yhd.admin.api.domain.conduct.convert.arrangement;

import com.yhd.admin.api.domain.conduct.dto.arrangement.MkShiftArrangementDTO;
import com.yhd.admin.api.domain.conduct.entity.arrangement.MkShiftArrangement;
import com.yhd.admin.api.domain.conduct.query.arrangement.MkShiftArrangementParam;
import com.yhd.admin.api.domain.conduct.vo.arrangement.MkShiftArrangementVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 值班带班安排表(MkShiftArrangement)Convert
 *
 * <AUTHOR>
 * @since 2025-07-29 11:30:39
 */
@Mapper(componentModel = "spring")
public interface MkShiftArrangementConvert {
    MkShiftArrangement toEntity(MkShiftArrangementParam param);

    MkShiftArrangement toEntity(MkShiftArrangementDTO dto);

    MkShiftArrangementDTO toDTO(MkShiftArrangement entity);

    MkShiftArrangementVO toVO(MkShiftArrangementDTO dto);

    List<MkShiftArrangementDTO> toDTOList(List<MkShiftArrangement> entityList);

    List<MkShiftArrangementVO> toVOList(List<MkShiftArrangementDTO> dtoList);
}

