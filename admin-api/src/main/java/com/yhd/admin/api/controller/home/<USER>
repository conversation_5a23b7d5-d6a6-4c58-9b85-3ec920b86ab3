package com.yhd.admin.api.controller.home;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.home.dto.MkHomeDTO;
import com.yhd.admin.api.domain.home.query.MkHomeParam;
import com.yhd.admin.api.service.home.MkHomeService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 综合管控大屏
 *
 * <AUTHOR>
 * @date 2025/7/29 09:34
 */
@RestController
@RequestMapping("/home")
public class MkHomeController {
  private final MkHomeService service;

  public MkHomeController(MkHomeService service) {
    this.service = service;
  }

  /**
   * 分页查询专家信息
   *
   * @param queryParam 查询参数
   * @return 分页结果
   */
  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MkHomeDTO> pagingQuery(@RequestBody MkHomeParam queryParam) {
    IPage<MkHomeDTO> iPage = service.pagingQuery(queryParam);
    return new PageRespJson<>(iPage);
  }

  /**
   * 查看详情
   *
   * @param param 查询参数
   * @return 结果
   */
  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<MkHomeDTO> getCurrentDetail(@RequestBody MkHomeParam param) {
    try {
      return RespJson.success(service.getCurrentDetail(param));
    } catch (Exception e) {
      return RespJson.failure(e.toString());
    }
  }

  /**
   * 新增或编辑
   *
   * @param param 查询参数
   * @return 结果
   */
  @PostMapping(
      value = "/addOrModify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> addOrModify(@RequestBody MkHomeParam param) {
    try {
      Boolean retVal = service.addOrModify(param);
      return RespJson.success(retVal);
    } catch (Exception e) {
      return RespJson.failure(e.toString());
    }
  }

  /**
   * 综合管控大屏数据
   *
   * @return 结果
   */
  @PostMapping(
      value = "/getData",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<MkHomeDTO> getData() {
    try {
      return RespJson.success(service.getData());
    } catch (Exception e) {
      return RespJson.failure(e.toString());
    }
  }
}
