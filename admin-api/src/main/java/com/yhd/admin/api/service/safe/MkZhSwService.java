package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.safe.dto.MkZhSwDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhSw;
import com.yhd.admin.api.domain.safe.query.MkZhSwParam;


public interface MkZhSwService extends IService<MkZhSw> {

    Boolean addOrModify(MkZhSwParam param);

    Boolean remove(MkZhSwParam param);

    MkZhSwDTO getCurrentDetail(MkZhSwParam param);

    IPage<MkZhSwDTO> pagingQuery(MkZhSwParam param);

    MkZhSwDTO getData();
}
