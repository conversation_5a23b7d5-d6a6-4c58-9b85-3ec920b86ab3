package com.yhd.admin.api.domain.monitor.fan.vo;

import com.yhd.admin.api.domain.monitor.fan.vo.create.TbMkLocalFanDetailsCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 局扇风机详细监控数据
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkLocalFanDetailsVO extends TbMkLocalFanDetailsCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
