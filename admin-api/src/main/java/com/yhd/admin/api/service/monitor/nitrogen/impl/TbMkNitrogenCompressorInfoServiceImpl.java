package com.yhd.admin.api.service.monitor.nitrogen.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.nitrogen.TbMkNitrogenCompressorInfoDao;
import com.yhd.admin.api.domain.monitor.nitrogen.convert.TbMkNitrogenCompressorInfoConvert;
import com.yhd.admin.api.domain.monitor.nitrogen.dto.TbMkNitrogenCompressorInfoDTO;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenCompressorInfo;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenCompressorInfoCreateVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.page.TbMkNitrogenCompressorInfoPageVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.update.TbMkNitrogenCompressorInfoUpdateVO;
import com.yhd.admin.api.service.monitor.nitrogen.TbMkNitrogenCompressorInfoService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 制氮系统空压机基本信息表 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkNitrogenCompressorInfoServiceImpl extends ServiceImpl<TbMkNitrogenCompressorInfoDao, TbMkNitrogenCompressorInfo> implements TbMkNitrogenCompressorInfoService {

    @Resource
    private TbMkNitrogenCompressorInfoConvert tbMkNitrogenCompressorInfoConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkNitrogenCompressorInfoCreateVO createVO) {
        TbMkNitrogenCompressorInfo tbMkNitrogenCompressorInfo = tbMkNitrogenCompressorInfoConvert.convert(createVO);
        baseMapper.insert(tbMkNitrogenCompressorInfo);
        return tbMkNitrogenCompressorInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkNitrogenCompressorInfoUpdateVO updateVO) {
        TbMkNitrogenCompressorInfo tbMkNitrogenCompressorInfo = tbMkNitrogenCompressorInfoConvert.convert(updateVO);
        return baseMapper.updateById(tbMkNitrogenCompressorInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkNitrogenCompressorInfo get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkNitrogenCompressorInfoDTO> page(TbMkNitrogenCompressorInfoPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkNitrogenCompressorInfo> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkNitrogenCompressorInfo> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.eq(param.getNitrogenSystemId() != null, TbMkNitrogenCompressorInfo::getNitrogenSystemId, param.getNitrogenSystemId());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getCompressorName()), TbMkNitrogenCompressorInfo::getCompressorName, param.getCompressorName());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getCompressorNumber()), TbMkNitrogenCompressorInfo::getCompressorNumber, param.getCompressorNumber());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getRemarks()), TbMkNitrogenCompressorInfo::getRemarks, param.getRemarks());
        queryChainWrapper.orderByDesc(TbMkNitrogenCompressorInfo::getId);
        return queryChainWrapper.page(iPage).convert(tbMkNitrogenCompressorInfoConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkNitrogenCompressorInfoCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 制氮系统空压机基本信息表：传入的列表为空");
            return 0;
        }
        List<TbMkNitrogenCompressorInfo> entities = createVOs.stream()
                .map(tbMkNitrogenCompressorInfoConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 制氮系统空压机基本信息表 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkNitrogenCompressorInfoUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 制氮系统空压机基本信息表：传入的列表为空");
            return 0;
        }
        List<TbMkNitrogenCompressorInfo> entities = updateVOs.stream()
                .map(tbMkNitrogenCompressorInfoConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 制氮系统空压机基本信息表：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 制氮系统空压机基本信息表 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
