package com.yhd.admin.api.service.emergency;

import com.yhd.admin.api.domain.emergency.query.MkEmergencyOverviewParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyOverviewVO;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyListItemInfoVO;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyNavigationInfoVO;

import java.util.List;

/**
 * 应急救援中心总览 Service
 * 
 * <AUTHOR>
 */
public interface MkEmergencyOverviewService {

    /**
     * 获取应急救援中心总览数据
     *
     * @param param 查询参数
     * @return 总览数据
     */
    MkEmergencyOverviewVO getOverviewData(MkEmergencyOverviewParam param);

    /**
     * 根据导航类型获取列表项目
     *
     * @param navType 导航类型
     * @param orgCode 组织机构编码
     * @return 列表项目
     */
    List<MkEmergencyListItemInfoVO> getListItemsByNavType(String navType, String orgCode);

    /**
     * 根据导航类型获取列表项目
     *
     * @param navType 导航类型（可为空，不传查全部）
     * @return 列表项目
     */
    List<MkEmergencyListItemInfoVO> getListItemsByNavType(String navType);

    /**
     * 获取功能导航列表
     *
     * @param orgCode 组织机构编码
     * @return 导航列表
     */
    List<MkEmergencyNavigationInfoVO> getNavigations(String orgCode);
}
