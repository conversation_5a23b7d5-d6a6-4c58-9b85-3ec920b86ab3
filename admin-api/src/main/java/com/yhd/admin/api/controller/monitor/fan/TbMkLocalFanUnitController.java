package com.yhd.admin.api.controller.monitor.fan;

import com.yhd.admin.api.domain.monitor.fan.convert.TbMkLocalFanUnitConvert;
import com.yhd.admin.api.domain.monitor.fan.dto.TbMkLocalFanUnitDTO;
import com.yhd.admin.api.domain.monitor.fan.entity.TbMkLocalFanUnit;
import com.yhd.admin.api.domain.monitor.fan.vo.create.TbMkLocalFanUnitCreateVO;
import com.yhd.admin.api.domain.monitor.fan.vo.page.TbMkLocalFanUnitPageVO;
import com.yhd.admin.api.domain.monitor.fan.vo.update.TbMkLocalFanUnitUpdateVO;
import com.yhd.admin.api.service.monitor.fan.TbMkLocalFanUnitService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 局扇风机单元 Controller
 * <AUTHOR>
 * @since
 */
@Slf4j
@RestController
@RequestMapping("/monitor/fan/tbMkLocalFanUnit")
public class TbMkLocalFanUnitController {

    @Resource
    private TbMkLocalFanUnitService tbMkLocalFanUnitService;

    @Resource
    private TbMkLocalFanUnitConvert tbMkLocalFanUnitConvert;

    /**
     * 分页查询
     *
     * @param pageVO 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<TbMkLocalFanUnitDTO> page(@RequestBody @Valid TbMkLocalFanUnitPageVO pageVO) {
        log.debug("分页查询 局扇风机单元，参数：{}", pageVO);
        return new PageRespJson<>(tbMkLocalFanUnitService.page(pageVO));
    }

    /**
     * 新增
     *
     * @param createVO 创建参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> create(@RequestBody @Valid TbMkLocalFanUnitCreateVO createVO) {
        log.debug("新增 局扇风机单元，参数：{}", createVO);
        return RespJson.success(tbMkLocalFanUnitService.create(createVO));
    }

    /**
     * 更新
     *
     * @param updateVO 更新参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> update(@RequestBody @Valid TbMkLocalFanUnitUpdateVO updateVO) {
        log.debug("更新 局扇风机单元，参数：{}", updateVO);
        return RespJson.success(tbMkLocalFanUnitService.update(updateVO));
    }


    /**
      * 删除
      *
      * @param id 要删除的记录ID
      * @return 操作结果
      */
    @DeleteMapping("/{id}")
    public RespJson<?> delete(@PathVariable Long id) {
        log.debug("删除 局扇风机单元，ID：{}", id);
        return RespJson.success(tbMkLocalFanUnitService.delete(id));
    }

    /**
     * 查看详情
     *
     * @param id 要查看的记录ID
     * @return 详情信息
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<TbMkLocalFanUnitDTO> getDetail(@PathVariable Long id) {
        log.debug("查看详情 局扇风机单元，ID：{}", id);
        TbMkLocalFanUnit entity = tbMkLocalFanUnitService.get(id);
        if (entity == null) {
            return RespJson.failure("未找到对应记录");
        }
        return RespJson.success(tbMkLocalFanUnitConvert.toDTO(entity));
    }
    /**
     * 批量删除
     *
     * @param ids 要删除的记录ID列表
     * @return 操作结果
     */
    @PostMapping(value = "/batchDelete", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchDelete(@RequestBody List<Long> ids) {
        log.debug("批量删除 局扇风机单元，IDs：{}", ids);
        return RespJson.success(tbMkLocalFanUnitService.batchDelete(ids));
    }

    /**
     * 批量新增
     *
     * @param createVOs 新增参数列表
     * @return 新增的记录ID列表
     */
    @PostMapping(value = "/batchAdd", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchCreate(@RequestBody @Valid List<TbMkLocalFanUnitCreateVO> createVOs) {
        log.debug("批量新增 局扇风机单元，数量：{}", createVOs.size());
        return RespJson.success(tbMkLocalFanUnitService.batchCreate(createVOs));
    }

    /**
     * 批量更新
     *
     * @param updateVOs 更新参数列表
     * @return 更新的记录数量
     */
    @PostMapping(value = "/batchModify", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> batchUpdate(@RequestBody @Valid List<TbMkLocalFanUnitUpdateVO> updateVOs) {
        log.debug("批量更新 局扇风机单元，数量：{}", updateVOs.size());
        return RespJson.success(tbMkLocalFanUnitService.batchUpdate(updateVOs));
    }
}
