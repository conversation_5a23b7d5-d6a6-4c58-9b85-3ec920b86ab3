package com.yhd.admin.api.domain.monitor.digging.convert;

import com.yhd.admin.api.domain.monitor.digging.dto.TbMkDiggingOperationDTO;
import com.yhd.admin.api.domain.monitor.digging.entity.TbMkDiggingOperation;
import com.yhd.admin.api.domain.monitor.digging.vo.TbMkDiggingOperationVO;
import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingOperationCreateVO;
import com.yhd.admin.api.domain.monitor.digging.vo.update.TbMkDiggingOperationUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 掘进机运行参数
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkDiggingOperationConvert {

    @Mapping(target = "id", ignore = true)
    TbMkDiggingOperation convert(TbMkDiggingOperationCreateVO createVO);

    TbMkDiggingOperation convert(TbMkDiggingOperationUpdateVO updateVO);

    TbMkDiggingOperationVO convert(TbMkDiggingOperation po);

    TbMkDiggingOperationDTO toDTO(TbMkDiggingOperation po);



}
