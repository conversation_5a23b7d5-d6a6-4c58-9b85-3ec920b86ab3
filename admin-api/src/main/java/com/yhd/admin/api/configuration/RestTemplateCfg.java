package com.yhd.admin.api.configuration;

import java.time.Duration;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateCfg {

  @Bean
  public RestTemplate restTemplate(RestTemplateBuilder builder) {
    builder.setConnectTimeout(Duration.ofMinutes(5)).setReadTimeout(Duration.ofMinutes(5));
    return builder.build();
  }
}
