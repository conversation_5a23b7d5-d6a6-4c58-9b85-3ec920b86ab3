package com.yhd.admin.api.service.conduct.arrangement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.arrangement.MkShiftArrangementRecordDTO;
import com.yhd.admin.api.domain.conduct.entity.arrangement.MkShiftArrangementRecord;
import com.yhd.admin.api.domain.conduct.query.arrangement.MkShiftArrangementRecordParam;

/**
 * 调班记录表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-29 11:30:39
 */
public interface MkShiftArrangementRecordService extends IService<MkShiftArrangementRecord> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkShiftArrangementRecordDTO> pagingQuery(MkShiftArrangementRecordParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkShiftArrangementRecordParam param);

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    MkShiftArrangementRecordDTO getCurrentDetails(MkShiftArrangementRecordParam param);

    /**
     * 审批
     *
     * @param param 主键
     * @return 是否成功
     */
    Boolean handle(MkShiftArrangementRecordParam param);
}
