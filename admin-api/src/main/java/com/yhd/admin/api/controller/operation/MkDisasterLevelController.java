package com.yhd.admin.api.controller.operation;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.operation.convert.MkDisasterLevelConvert;
import com.yhd.admin.api.domain.operation.dto.MkDisasterLevelDTO;
import com.yhd.admin.api.domain.operation.query.MkDisasterLevelParam;
import com.yhd.admin.api.domain.operation.vo.MkDisasterLevelVO;
import com.yhd.admin.api.service.operation.MkDisasterLevelService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 算法模型-报警级别表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@RestController
@RequestMapping("/disaster/level")
public class MkDisasterLevelController {
    @Resource
    private MkDisasterLevelConvert convert;

    @Resource
    private MkDisasterLevelService service;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkDisasterLevelVO> pagingQuery(@RequestBody MkDisasterLevelParam queryParam) {
        IPage<MkDisasterLevelDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MkDisasterLevelParam param) {
        return RespJson.success(convert.toVO(service.getCurrentDetail(param)));
    }

    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson add(@RequestBody MkDisasterLevelParam param) {
        Boolean retVal = service.add(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(
        value = "/modify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody MkDisasterLevelParam param) {
        Boolean retVal = service.modify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam param) {
        Boolean retVal = service.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }
}
