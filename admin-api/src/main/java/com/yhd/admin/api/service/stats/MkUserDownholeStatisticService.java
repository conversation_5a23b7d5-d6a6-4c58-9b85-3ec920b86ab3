package com.yhd.admin.api.service.stats;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.stats.dto.MkUserDownholeStatisticDTO;
import com.yhd.admin.api.domain.stats.entity.MkUserDownholeStatistic;
import com.yhd.admin.api.domain.stats.query.MkUserDownholeStatisticParam;

import java.util.List;

/**
 * 人员下井统计-业务层接口
 *
 * <AUTHOR>
 * @date 2025/7/25 15:06
 */
public interface MkUserDownholeStatisticService extends IService<MkUserDownholeStatistic> {

  /**
   * 获取人员下井记录列表
   *
   * @param param 参数
   * @return 人员下井记录列表
   */
  List<MkUserDownholeStatisticDTO> getUserDownholeRecordList(MkUserDownholeStatisticParam param);

  /**
   * 根据条件查询人员下井统计分页列表
   *
   * @param param 查询条件
   * @return 人员下井统计分页列表
   */
  IPage<MkUserDownholeStatisticDTO> userCountPagingQuery(MkUserDownholeStatisticParam param);
}
