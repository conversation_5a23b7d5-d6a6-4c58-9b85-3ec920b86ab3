package com.yhd.admin.api.domain.emergency.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 生产统计VO
 *
 * <AUTHOR>
 */
@Data
public class MkProductionStatsVO {

    /**
     * 统计日期
     */
    private LocalDate statsDate;

    /**
     * 日产量(吨)
     */
    private BigDecimal dailyProduction;

    /**
     * 日销量(吨)
     */
    private BigDecimal dailySales;

    /**
     * 月累计产量(万吨)
     */
    private BigDecimal monthlyProduction;

    /**
     * 月累计销量(万吨)
     */
    private BigDecimal monthlySales;

    /**
     * 年累计产量(万吨)
     */
    private BigDecimal yearlyProduction;

    /**
     * 年累计销量(万吨)
     */
    private BigDecimal yearlySales;

    /**
     * 安全生产天数
     */
    private Integer safetyDays;

    /**
     * 设计产能(万吨)
     */
    private BigDecimal designCapacity;

    /**
     * 主采煤层
     */
    private String mainCoalSeam;
}