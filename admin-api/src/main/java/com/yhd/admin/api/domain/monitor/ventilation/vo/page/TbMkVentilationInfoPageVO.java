package com.yhd.admin.api.domain.monitor.ventilation.vo.page;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通风机基本信息
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkVentilationInfoPageVO extends QueryParam {

    private static final long serialVersionUID = 1L;



        /**
         * 通风机名称
         */
        private String ventilationName;

        /**
         * 备注信息
         */
        private String remarks;
}
