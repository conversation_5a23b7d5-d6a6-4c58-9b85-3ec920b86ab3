package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencyDeviceHistory.java
 * @Description 应急救援设备历史数据表
 * @createTime 2025年01月20日 12:00:00
 */
@Data
@TableName("tb_mk_emergency_device_history")
public class MkEmergencyDeviceHistory {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 实时值
     */
    private BigDecimal currentValue;

    /**
     * 测量单位
     */
    private String unit;

    /**
     * 设备状态：1-正常，2-异常，3-离线
     */
    private Integer status;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 记录时间
     */
    private LocalDateTime recordTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
}