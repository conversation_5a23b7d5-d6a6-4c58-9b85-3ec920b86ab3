package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.safe.dto.MkZhDbDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhDb;
import com.yhd.admin.api.domain.safe.query.MkZhDbParam;


public interface MkZhDbService extends IService<MkZhDb> {

    Boolean addOrModify(MkZhDbParam param);

    Boolean remove(MkZhDbParam param);

    MkZhDbDTO getCurrentDetail(MkZhDbParam param);

    IPage<MkZhDbDTO> pagingQuery(MkZhDbParam param);

    MkZhDbDTO getData();
}
