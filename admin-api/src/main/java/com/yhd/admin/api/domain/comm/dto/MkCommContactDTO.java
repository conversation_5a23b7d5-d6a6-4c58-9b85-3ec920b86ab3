package com.yhd.admin.api.domain.comm.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 通讯录联系人信息表
 *
 * <AUTHOR>
 * @date 2025/7/25 13:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkCommContactDTO extends BaseDTO implements Serializable {

  private Long id;
  /** 姓名 */
  private String realName;
  /** 组织机构编码 */
  private String orgCode;
  /** 组织机构名称 */
  private String orgName;
  /** 类型 1：人员 2：地点 */
  private String type;
  /** 部门编码 */
  private String departmentCode;
  /** 部门名称 */
  private String departmentName;
  /** 职务编码 */
  private String postCode;
  /** 职务名称 */
  private String postName;
  /** 工种编码 */
  private String jobCode;
  /** 工种名称 */
  private String jobName;
  /** 备注 */
  private String remark;
  /** 排序索引，越小越靠前 */
  private Long sortNum;
  /** 联系人电话列表 */
  private List<MkCommContactPhoneDTO> contactPhoneList;
}
