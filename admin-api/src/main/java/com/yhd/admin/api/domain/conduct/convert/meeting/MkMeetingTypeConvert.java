package com.yhd.admin.api.domain.conduct.convert.meeting;

import com.yhd.admin.api.domain.conduct.dto.meeting.MkMeetingTypeDTO;
import com.yhd.admin.api.domain.conduct.entity.meeting.MkMeetingType;
import com.yhd.admin.api.domain.conduct.query.meeting.MkMeetingTypeParam;
import com.yhd.admin.api.domain.conduct.vo.meeting.MkMeetingTypeVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 会议类型表(MkMeetingType)Convert
 *
 * <AUTHOR>
 * @since 2025-07-28 10:36:44
 */
@Mapper(componentModel = "spring")
public interface MkMeetingTypeConvert {
    MkMeetingType toEntity(MkMeetingTypeParam param);

    MkMeetingTypeDTO toDTO(MkMeetingType entity);

    MkMeetingTypeVO toVO(MkMeetingTypeDTO dto);

    List<MkMeetingTypeDTO> toDTOList(List<MkMeetingType> entityList);

    List<MkMeetingTypeVO> toVOList(List<MkMeetingTypeDTO> dtoList);
}

