package com.yhd.admin.api.service.sys;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.sys.SysOpsLogDao;
import com.yhd.admin.api.domain.sys.convert.SysOpsLogConvert;
import com.yhd.admin.api.domain.sys.dto.SysOpsLogDTO;
import com.yhd.admin.api.domain.sys.entity.SysOpsLog;
import com.yhd.admin.api.domain.sys.query.SysOpsLogParam;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class SysOpsLogServiceImpl extends ServiceImpl<SysOpsLogDao, SysOpsLog> implements SysOpsLogService {

    @Resource
    private SysOpsLogConvert convert;

    @Override
    public IPage<SysOpsLogDTO> pagingQuery(SysOpsLogParam queryParam) {
        IPage<SysOpsLog> iPage = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<SysOpsLog> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper
            .ge(StringUtils.isNotBlank(queryParam.getStartDate()), SysOpsLog::getCreatedTime, queryParam.getStartDate())
            .le(StringUtils.isNotBlank(queryParam.getEndDate()), SysOpsLog::getCreatedTime, queryParam.getEndDate())
            .like(StringUtils.isNotBlank(queryParam.getOpsName()), SysOpsLog::getOpsName, queryParam.getOpsName())
            .like(StringUtils.isNotBlank(queryParam.getClientIp()), SysOpsLog::getClientIp, queryParam.getClientIp())
            .orderByDesc(SysOpsLog::getCreatedTime);
        return queryChainWrapper.page(iPage).convert(convert::toDTO);
    }
}
