package com.yhd.admin.api.service.conduct.meeting;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.meeting.MkMeetingTypeDTO;
import com.yhd.admin.api.domain.conduct.entity.meeting.MkMeetingType;
import com.yhd.admin.api.domain.conduct.query.meeting.MkMeetingTypeParam;

import java.util.List;

/**
 * 会议类型表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28 10:36:44
 */
public interface MkMeetingTypeService extends IService<MkMeetingType> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkMeetingTypeDTO> pagingQuery(MkMeetingTypeParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkMeetingTypeDTO> queryList(MkMeetingTypeParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkMeetingTypeParam param);

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    MkMeetingTypeDTO getCurrentDetails(MkMeetingTypeParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkMeetingTypeParam param);

}
