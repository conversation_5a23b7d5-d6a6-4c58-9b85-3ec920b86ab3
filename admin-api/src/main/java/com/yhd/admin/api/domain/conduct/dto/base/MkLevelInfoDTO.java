package com.yhd.admin.api.domain.conduct.dto.base;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 水平信息表(MkLevelInfo)DTO
 *
 * <AUTHOR>
 * @since 2025-07-28 09:25:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkLevelInfoDTO extends BaseDTO implements Serializable {
    /** Id */
    private Long id;
    /** 所属煤矿 */
    private String orgName;
    /** 组织机构编码 */
    private String orgCode;
    /** 名称 */
    private String name;
    /** 煤层名称 */
    private String coalName;
    /** 煤层 Id */
    private Long coalId;
    /** 可采储量 */
    private BigDecimal validReserves;
    /** 标高上限 */
    private BigDecimal maxElevation;
    /** 标高下限 */
    private BigDecimal minElevation;
    /** 倾角 */
    private BigDecimal dipAngle;
    /** 服务年限 */
    private BigDecimal serviceYears;
}

