package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyPlanDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyPlan;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyPlanParam;
import com.yhd.admin.common.domain.query.BatchParam;

import java.util.List;

/**
 * 应急救援数字预案表 Service
 * <AUTHOR>
 */
public interface MkEmergencyPlanService extends IService<MkEmergencyPlan> {

    /**
     * 分页查询应急救援数字预案信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    IPage<MkEmergencyPlanDTO> pagingQuery(MkEmergencyPlanParam queryParam);

    /**
     * 新增应急救援数字预案信息
     * @param param 应急救援数字预案信息参数
     * @return 是否成功
     */
    Boolean add(MkEmergencyPlanParam param);

    /**
     * 修改应急救援数字预案信息
     * @param param 应急救援数字预案信息参数
     * @return 是否成功
     */
    Boolean modify(MkEmergencyPlanParam param);

    /**
     * 获取应急救援数字预案详情
     * @param param 查询参数
     * @return 应急救援数字预案详情
     */
    MkEmergencyPlanDTO getCurrentDetail(MkEmergencyPlanParam param);

    /**
     * 批量删除应急救援数字预案信息
     * @param param 批量删除参数
     * @return 是否成功
     */
    Boolean removeBatch(BatchParam param);

    /**
     * 根据预案类型查询预案列表
     * @param planType 预案类型
     * @return 预案列表
     */
    List<MkEmergencyPlanDTO> getPlansByType(String planType);

    /**
     * 发布预案
     * @param planId 预案ID
     * @return 是否成功
     */
    Boolean publishPlan(Integer planId);

    /**
     * 废止预案
     * @param planId 预案ID
     * @return 是否成功
     */
    Boolean abolishPlan(Integer planId);

    /**
     * 获取即将到期的预案列表
     * @param orgCode 组织编码
     * @return 即将到期的预案列表
     */
    List<MkEmergencyPlanDTO> getExpiringSoonPlans(String orgCode);

    /**
     * 获取需要演练的预案列表
     * @param orgCode 组织编码
     * @return 需要演练的预案列表
     */
    List<MkEmergencyPlanDTO> getNeedsDrillPlans(String orgCode);

    /**
     * 获取需要评审的预案列表
     * @param orgCode 组织编码
     * @return 需要评审的预案列表
     */
    List<MkEmergencyPlanDTO> getNeedsReviewPlans(String orgCode);

    /**
     * 根据分类ID查询预案列表
     * @param categoryId 分类ID
     * @return 预案列表
     */
    List<MkEmergencyPlanDTO> getPlansByCategoryId(Integer categoryId);
}
