package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.operation.MkNoticeRuleDao;
import com.yhd.admin.api.domain.operation.convert.MkNoticeRuleConvert;
import com.yhd.admin.api.domain.operation.dto.MkNoticeRuleDTO;
import com.yhd.admin.api.domain.operation.entity.MkNoticeRule;
import com.yhd.admin.api.domain.operation.query.MkNoticeRuleParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.UserService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 消息下发规则表服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-08 09:12:47
 */
@Service
public class MkNoticeRuleServiceImpl extends ServiceImpl<MkNoticeRuleDao, MkNoticeRule> implements MkNoticeRuleService {

    private final MkNoticeRuleConvert mkNoticeRuleConvert;
    private final UserService userService;

    public MkNoticeRuleServiceImpl(MkNoticeRuleConvert mkNoticeRuleConvert, UserService userService) {
        this.mkNoticeRuleConvert = mkNoticeRuleConvert;
        this.userService = userService;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkNoticeRuleDTO> pagingQuery(MkNoticeRuleParam param) {
        IPage<MkNoticeRule> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkNoticeRule> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.like(StringUtils.isNotBlank(param.getName()), MkNoticeRule::getName, param.getName());
        wrapper.eq(StringUtils.isNotBlank(param.getTypeCode()), MkNoticeRule::getTypeCode, param.getTypeCode());
        wrapper.eq(StringUtils.isNotBlank(param.getOrgCode()), MkNoticeRule::getOrgCode, param.getOrgCode());
        return wrapper.page(page).convert(mkNoticeRuleConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkNoticeRuleDTO> queryList(MkNoticeRuleParam param) {
        LambdaQueryWrapper<MkNoticeRule> wrapper = new LambdaQueryWrapper<>();
        return mkNoticeRuleConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkNoticeRuleParam param) {
        MkNoticeRule entity = mkNoticeRuleConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            if (save(entity)) {
                String creator = userService.getUserByUsername(entity.getCreatedBy()).getName();
                entity.setCreator(creator);
                return updateById(entity);
            }
            return false;
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkNoticeRuleDTO getCurrentDetails(MkNoticeRuleParam param) {
        return mkNoticeRuleConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkNoticeRuleParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }
}
