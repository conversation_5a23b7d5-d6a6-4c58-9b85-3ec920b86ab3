package com.yhd.admin.api.controller.sys;

import com.yhd.admin.api.domain.sys.convert.SysFileTypeConvert;
import com.yhd.admin.api.domain.sys.dto.SysFileTypeDTO;
import com.yhd.admin.api.domain.sys.query.SysFileTypeParam;
import com.yhd.admin.api.domain.sys.vo.SysFileTypeVO;
import com.yhd.admin.api.service.sys.SysFileTypeService;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 图文类别-控制层
 *
 * <AUTHOR>
 * @date 2025/7/29 16:28
 */
@RestController
@RequestMapping("/sys/fileType")
public class SysFileTypeController {
  private final SysFileTypeService service;
  private final SysFileTypeConvert convert;

  public SysFileTypeController(SysFileTypeService service, SysFileTypeConvert convert) {
    this.service = service;
    this.convert = convert;
  }

  @PostMapping(
      value = "/queryList",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<List<SysFileTypeVO>> pagingQuery(@RequestBody SysFileTypeParam param) {

    List<SysFileTypeDTO> dtoList = service.queryList(param);
    return RespJson.success(convert.toVO(dtoList));
  }

  @PostMapping(
      value = "/add",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> add(@RequestBody SysFileTypeParam param) {
    return RespJson.success(service.add(param));
  }

  @PostMapping(
      value = "/modify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> modify(@RequestBody SysFileTypeParam param) {
    return RespJson.success(service.modify(param));
  }

  @PostMapping(
      value = "/remove",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> remove(@RequestBody SysFileTypeParam param) {
    return RespJson.success(service.remove(param));
  }

  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<SysFileTypeVO> getCurrentDetail(@RequestBody SysFileTypeParam param) {
    SysFileTypeDTO detail = service.getCurrentDetail(param);
    return RespJson.success(convert.toVO(detail));
  }
}
