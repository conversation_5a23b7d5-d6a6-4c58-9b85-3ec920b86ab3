package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencySensorDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencySensor;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySensorParam;
import com.yhd.admin.common.domain.query.BatchParam;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 应急救援监测信息表(传感器表) Service
 * <AUTHOR>
 */
public interface MkEmergencySensorService extends IService<MkEmergencySensor> {

    /**
     * 分页查询应急救援监测信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    IPage<MkEmergencySensorDTO> pagingQuery(MkEmergencySensorParam queryParam);

    /**
     * 新增应急救援监测信息
     * @param param 应急救援监测信息参数
     * @return 是否成功
     */
    Boolean add(MkEmergencySensorParam param);

    /**
     * 修改应急救援监测信息
     * @param param 应急救援监测信息参数
     * @return 是否成功
     */
    Boolean modify(MkEmergencySensorParam param);

    /**
     * 获取应急救援监测信息详情
     * @param param 查询参数
     * @return 应急救援监测信息详情
     */
    MkEmergencySensorDTO getCurrentDetail(MkEmergencySensorParam param);

    /**
     * 批量删除应急救援监测信息
     * @param param 批量删除参数
     * @return 是否成功
     */
    Boolean removeBatch(BatchParam param);

    /**
     * 获取传感器树形结构
     * @param orgCode 组织编码
     * @param parentId 父级ID（可选，为空时查询根节点）
     * @return 树形结构数据
     */
    List<MkEmergencySensorDTO> getSensorTree(String orgCode, Integer parentId);

    /**
     * 获取传感器树形结构（支持条件查询）
     * @param param 查询参数
     * @return 树形结构数据
     */
    List<MkEmergencySensorDTO> getSensorTreeByParam(com.yhd.admin.api.domain.emergency.query.MkEmergencySensorTreeParam param);

    /**
     * 根据传感器编码查询
     * @param sensorCode 传感器编码
     * @return 传感器信息
     */
    MkEmergencySensorDTO getBySensorCode(String sensorCode);

    /**
     * 查询异常传感器
     * @param orgCode 组织编码
     * @return 异常传感器列表
     */
    List<MkEmergencySensorDTO> getAbnormalSensors(String orgCode);

    /**
     * 查询超出阈值的传感器
     * @param orgCode 组织编码
     * @return 超出阈值的传感器列表
     */
    List<MkEmergencySensorDTO> getOutOfRangeSensors(String orgCode);

    /**
     * 根据层级类型查询传感器
     * @param levelType 层级类型
     * @param orgCode 组织编码
     * @return 传感器列表
     */
    List<MkEmergencySensorDTO> getByLevelType(String levelType, String orgCode);

    /**
     * 根据工作面查询传感器
     * @param workFace 工作面
     * @param orgCode 组织编码
     * @return 传感器列表
     */
    List<MkEmergencySensorDTO> getByWorkFace(String workFace, String orgCode);

    /**
     * 根据巷道查询传感器
     * @param tunnel 巷道
     * @param orgCode 组织编码
     * @return 传感器列表
     */
    List<MkEmergencySensorDTO> getByTunnel(String tunnel, String orgCode);

    /**
     * 根据传感器类型查询传感器
     * @param sensorType 传感器类型
     * @param orgCode 组织编码
     * @return 传感器列表
     */
    List<MkEmergencySensorDTO> getBySensorType(String sensorType, String orgCode);

    /**
     * 更新传感器状态
     * @param id 传感器ID
     * @param status 状态
     * @return 是否成功
     */
    Boolean updateStatus(Integer id, Integer status);

    /**
     * 批量更新传感器状态
     * @param ids 传感器ID列表
     * @param status 状态
     * @return 是否成功
     */
    Boolean batchUpdateStatus(List<Integer> ids, Integer status);

    /**
     * 更新传感器当前值
     * @param sensorCode 传感器编码
     * @param currentValue 当前值
     * @return 是否成功
     */
    Boolean updateCurrentValue(String sensorCode, BigDecimal currentValue);

    /**
     * 批量更新传感器当前值
     * @param sensorDataMap 传感器数据映射（传感器编码 -> 当前值）
     * @return 是否成功
     */
    Boolean batchUpdateCurrentValue(Map<String, BigDecimal> sensorDataMap);

    /**
     * 统计各状态传感器数量
     * @param orgCode 组织编码
     * @return 统计结果
     */
    Map<String, Long> countByStatus(String orgCode);

    /**
     * 统计各类型传感器数量
     * @param orgCode 组织编码
     * @return 统计结果
     */
    Map<String, Long> countBySensorType(String orgCode);

    /**
     * 获取传感器监控概览
     * @param orgCode 组织编码
     * @return 监控概览数据
     */
    Map<String, Object> getMonitorOverview(String orgCode);

    /**
     * 校验传感器编码是否唯一
     * @param sensorCode 传感器编码
     * @param excludeId 排除的ID（用于修改时校验）
     * @return 是否唯一
     */
    Boolean checkSensorCodeUnique(String sensorCode, Integer excludeId);
}
