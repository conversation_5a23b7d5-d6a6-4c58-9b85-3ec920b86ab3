package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.operation.MkOutputScxtDao;
import com.yhd.admin.api.domain.operation.convert.MkOutputScxtConvert;
import com.yhd.admin.api.domain.operation.dto.MkOutputScxtDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputScxt;
import com.yhd.admin.api.domain.operation.query.MkOutputScxtParam;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 生产录入-生产系统内容表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@Service
public class MkOutputScxtServiceImpl extends ServiceImpl<MkOutputScxtDao, MkOutputScxt> implements MkOutputScxtService {

    @Resource
    private MkOutputScxtConvert convert;

    @Resource
    private MkOutputScxtService service;

    @Override
    public IPage<MkOutputScxtDTO> pagingQuery(MkOutputScxtParam queryParam) {
        Page<MkOutputScxt> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkOutputScxt> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        queryChain.orderByDesc(MkOutputScxt::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkOutputScxtParam param) {
        MkOutputScxt entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MkOutputScxtParam param) {
        MkOutputScxt entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MkOutputScxtDTO getCurrentDetail(MkOutputScxtParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

}
