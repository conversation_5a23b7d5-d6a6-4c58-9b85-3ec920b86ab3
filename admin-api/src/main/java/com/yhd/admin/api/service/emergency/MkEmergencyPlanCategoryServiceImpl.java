package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.emergency.MkEmergencyPlanCategoryDao;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyPlanCategoryConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyPlanCategoryDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyPlanCategory;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyPlanCategoryParam;
import com.yhd.admin.common.domain.query.BatchParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 应急预案分类表 Service实现类
 * <AUTHOR>
 */
@Service
public class MkEmergencyPlanCategoryServiceImpl extends ServiceImpl<MkEmergencyPlanCategoryDao, MkEmergencyPlanCategory>
        implements MkEmergencyPlanCategoryService {

    @Resource
    private MkEmergencyPlanCategoryConvert convert;

    @Override
    public IPage<MkEmergencyPlanCategoryDTO> pagingQuery(MkEmergencyPlanCategoryParam param) {
        IPage<MkEmergencyPlanCategory> iPage = new Page<>(param.getCurrent(), param.getPageSize());

        LambdaQueryChainWrapper<MkEmergencyPlanCategory> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);

        // 组织编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getOrgCode()),
                MkEmergencyPlanCategory::getOrgCode, param.getOrgCode());

        // 分类编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getCategoryCode()),
                MkEmergencyPlanCategory::getCategoryCode, param.getCategoryCode());

        // 分类名称模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getCategoryName()),
                MkEmergencyPlanCategory::getCategoryName, param.getCategoryName());

        // 父级ID查询
        queryChainWrapper.eq(param.getParentId() != null,
                MkEmergencyPlanCategory::getParentId, param.getParentId());

        // 分类层级查询
        queryChainWrapper.eq(param.getCategoryLevel() != null,
                MkEmergencyPlanCategory::getCategoryLevel, param.getCategoryLevel());

        // 状态查询
        queryChainWrapper.eq(param.getStatus() != null,
                MkEmergencyPlanCategory::getStatus, param.getStatus());

        // 是否叶子节点查询
        queryChainWrapper.eq(param.getIsLeaf() != null,
                MkEmergencyPlanCategory::getIsLeaf, param.getIsLeaf());

        // 排序
        queryChainWrapper.orderByAsc(MkEmergencyPlanCategory::getSortOrder)
                .orderByAsc(MkEmergencyPlanCategory::getId);

        IPage<MkEmergencyPlanCategory> result = queryChainWrapper.page(iPage);
        return result.convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkEmergencyPlanCategoryParam param) {
        // 必填字段校验
        if (StringUtils.isBlank(param.getCategoryCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getCategoryName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        // 设置默认值
        if (param.getParentId() == null) {
            param.setParentId(0);
        }
        if (param.getCategoryLevel() == null) {
            param.setCategoryLevel(1);
        }
        if (param.getStatus() == null) {
            param.setStatus(1);
        }
        if (param.getIsLeaf() == null) {
            param.setIsLeaf(false);
        }
        if (param.getSortOrder() == null) {
            param.setSortOrder(0);
        }

        // 构建分类路径
        if (param.getParentId() > 0) {
            MkEmergencyPlanCategory parent = super.getById(param.getParentId());
            if (parent != null) {
                param.setCategoryLevel(parent.getCategoryLevel() + 1);
                // 先保存获取ID，再更新路径
            }
        }

        // 先保存实体
        MkEmergencyPlanCategory entity = convert.toEntity(param);
        boolean saved = super.save(entity);

        // 保存成功后更新分类路径
        if (saved && param.getParentId() > 0) {
            MkEmergencyPlanCategory parent = super.getById(param.getParentId());
            if (parent != null) {
                entity.setCategoryPath(parent.getCategoryPath() + "/" + entity.getId());
                super.updateById(entity);
            }
        } else if (saved) {
            entity.setCategoryPath(String.valueOf(entity.getId()));
            super.updateById(entity);
        }

        return saved;
    }

    @Override
    public Boolean modify(MkEmergencyPlanCategoryParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        // 必填字段校验
        if (StringUtils.isBlank(param.getCategoryCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getCategoryName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        return super.updateById(convert.toEntity(param));
    }

    @Override
    public MkEmergencyPlanCategoryDTO getCurrentDetail(MkEmergencyPlanCategoryParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        MkEmergencyPlanCategory entity = super.getById(param.getId());
        return convert.toDTO(entity);
    }

    @Override
    public Boolean removeBatch(BatchParam param) {
        if (param.getId() == null || param.getId().isEmpty()) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        return super.removeByIds(param.getId());
    }

    @Override
    public List<MkEmergencyPlanCategoryDTO> getCategoryTree() {
        // 查询所有启用的分类
        List<MkEmergencyPlanCategory> allCategories = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(MkEmergencyPlanCategory::getStatus, 1)
                .orderByAsc(MkEmergencyPlanCategory::getSortOrder)
                .orderByAsc(MkEmergencyPlanCategory::getId)
                .list();

        List<MkEmergencyPlanCategoryDTO> categoryDTOs = convert.toDTO(allCategories);

        // 构建树形结构
        return buildCategoryTree(categoryDTOs, 0);
    }

    @Override
    public List<MkEmergencyPlanCategoryDTO> getCategoriesByParentId(Integer parentId) {
        if (parentId == null) {
            parentId = 0;
        }

        List<MkEmergencyPlanCategory> categories = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(MkEmergencyPlanCategory::getParentId, parentId)
                .eq(MkEmergencyPlanCategory::getStatus, 1)
                .orderByAsc(MkEmergencyPlanCategory::getSortOrder)
                .orderByAsc(MkEmergencyPlanCategory::getId)
                .list();

        return convert.toDTO(categories);
    }

    @Override
    public List<MkEmergencyPlanCategoryDTO> getEnabledCategories() {
        List<MkEmergencyPlanCategory> categories = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(MkEmergencyPlanCategory::getStatus, 1)
                .orderByAsc(MkEmergencyPlanCategory::getSortOrder)
                .orderByAsc(MkEmergencyPlanCategory::getId)
                .list();

        return convert.toDTO(categories);
    }

    /**
     * 构建分类树
     * @param categories 分类列表
     * @param parentId 父级ID
     * @return 树形结构
     */
    private List<MkEmergencyPlanCategoryDTO> buildCategoryTree(List<MkEmergencyPlanCategoryDTO> categories, Integer parentId) {
        List<MkEmergencyPlanCategoryDTO> result = new ArrayList<>();

        for (MkEmergencyPlanCategoryDTO category : categories) {
            if (category.getParentId().equals(parentId)) {
                List<MkEmergencyPlanCategoryDTO> children = buildCategoryTree(categories, category.getId());
                category.setChildren(children);
                result.add(category);
            }
        }

        return result;
    }
}
