package com.yhd.admin.api.domain.operation.query;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;


/**
 * 算法模型-报警级别表查询
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MkDisasterLevelParam extends QueryParam {
    /**
     * Id
     */
    private Long id;

    /**
     * 开始级别code
     */
    private String startCode;

    /**
     * 开始级别name
     */
    private String startName;

    /**
     * 结束级别code
     */
    private String endCode;

    /**
     * 结束级别name
     */
    private String endName;

    /**
     * 报警级别
     */
    private String warnLevel;

    /**
     * 监测名称
     */
    private String monitorName;
}
