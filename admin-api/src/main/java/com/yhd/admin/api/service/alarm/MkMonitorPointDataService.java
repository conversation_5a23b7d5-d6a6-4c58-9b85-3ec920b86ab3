package com.yhd.admin.api.service.alarm;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.alarm.dto.MkMonitorPointDataDTO;
import com.yhd.admin.api.domain.alarm.entity.MkMonitorPointData;
import com.yhd.admin.api.domain.alarm.query.MkMonitorPointDataParam;

/**
 * 测点数据-业务层接口
 *
 * <AUTHOR>
 * @date 2025/8/7 17:01
 */
public interface MkMonitorPointDataService extends IService<MkMonitorPointData> {

  /**
   * 查询详情信息
   *
   * @param param 主键id
   * @return 测点数据
   */
  MkMonitorPointDataDTO getCurrentDetail(MkMonitorPointDataParam param);

  /**
   * 根据条件查询测点数据分页列表
   *
   * @param param 查询条件
   * @return 测点数据分页列表
   */
  IPage<MkMonitorPointDataDTO> pagingQuery(MkMonitorPointDataParam param);
}
