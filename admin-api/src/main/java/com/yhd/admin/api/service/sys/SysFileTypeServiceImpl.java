package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.sys.SysFileTypeDao;
import com.yhd.admin.api.domain.sys.convert.SysFileTypeConvert;
import com.yhd.admin.api.domain.sys.dto.SysFileTypeDTO;
import com.yhd.admin.api.domain.sys.entity.SysFileType;
import com.yhd.admin.api.domain.sys.query.SysFileTypeParam;
import com.yhd.admin.api.exception.BMSException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 图文类别-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2025/7/29 9:31
 */
@Service
public class SysFileTypeServiceImpl extends ServiceImpl<SysFileTypeDao, SysFileType>
    implements SysFileTypeService {
  private static final Logger logger = LoggerFactory.getLogger(SysFileTypeServiceImpl.class);

  private final SysFileTypeConvert convert;

  public SysFileTypeServiceImpl(SysFileTypeConvert convert) {
    this.convert = convert;
  }

  /**
   * 查询图文列表
   *
   * @param param 参数
   * @return List<SysFileTypeDTO>
   */
  @Override
  public List<SysFileTypeDTO> queryList(SysFileTypeParam param) {
    List<SysFileType> typeAll = baseMapper.selectChildren(param.getId());
    List<SysFileTypeDTO> typeDTOList =
        typeAll.stream()
            .map(convert::toDTO)
            .sorted(Comparator.comparing(SysFileTypeDTO::getSortNum))
            .toList();

    Map<Long, List<SysFileTypeDTO>> allChildren =
        typeDTOList.stream()
            .sorted(Comparator.comparing(SysFileTypeDTO::getSortNum))
            .collect(Collectors.groupingBy(SysFileTypeDTO::getParentId));
    typeDTOList.forEach(o -> o.setChildren(allChildren.get(o.getId())));

    return typeDTOList.stream()
        .filter(o -> o.getId().equals(param.getId()))
        .collect(Collectors.toList());
  }

  /**
   * 新增
   *
   * @param param 表单
   * @return true成功，false失败
   */
  @Override
  public Boolean add(SysFileTypeParam param) {
    logger.debug("进入service层，调用【新增图文类别】接口开始，表单参数：{}", param);
    SysFileType entity = convert.toEntity(param);
    // 上级类别为空，为顶级，pid = 0。
    if (Objects.isNull(param.getParentId())) {
      entity.setParentId(0L);
    }
    // 检验本级是否存在相同类别名称
    boolean save = super.save(entity);
    logger.debug("新增图文类别，插入数据库表，结果：{}", save);
    return save;
  }

  /**
   * 编辑
   *
   * @param param 表单
   * @return true成功，false失败
   */
  @Override
  public Boolean modify(SysFileTypeParam param) {
    logger.debug("进入service层，调用【编辑图文类别】接口开始，表单参数：{}", param);
    SysFileType entity = convert.toEntity(param);
    // 上级类别不能为自身
    if (entity.getId().equals(entity.getParentId())) {
      throw new BMSException("error", "上级类别不能为自身");
    }

    boolean update = super.updateById(entity);
    logger.debug("编辑图文类别，更新数据库表，结果：{}", update);

    return update;
  }

  /**
   * 删除
   *
   * @param param 主键id
   * @return true成功，false失败
   */
  @Override
  public Boolean remove(SysFileTypeParam param) {
    logger.debug("进入service层，调用【删除图文类别】接口开始，参数：id={}", param.getId());
    // 判断是否有子类别
    long typeCount =
        count(new LambdaQueryWrapper<SysFileType>().eq(SysFileType::getParentId, param.getId()));
    if (typeCount > 0) {
      throw new BMSException("error", "请先删除子类别");
    }
    // 判断图文类别下面是否存在文件
    boolean remove = super.removeById(param.getId());
    logger.debug("删除图文类别，结果：{}", remove);

    return remove;
  }

  /**
   * 查询图文类别详情
   *
   * @param param 主键id
   * @return 图文类别详情
   */
  @Override
  public SysFileTypeDTO getCurrentDetail(SysFileTypeParam param) {
    logger.debug("进入service层，调用【查询图文类别详情信息】接口开始，参数：id={}", param.getId());
    SysFileType entity = super.getById(param.getId());

    return convert.toDTO(entity);
  }

  /**
   * 根据ID查询父级节点
   *
   * @param id 主键id
   * @return 父级节点
   */
  @Override
  public List<SysFileTypeDTO> listParentById(Long id) {
    return baseMapper.selectParent(id).stream().map(convert::toDTO).collect(Collectors.toList());
  }

  /**
   * 根据ID查询子级
   *
   * @param id 主键id
   * @return 子级列表
   */
  @Override
  public List<SysFileTypeDTO> listChildrenById(Long id) {
    return baseMapper.selectChildren(id).stream().map(convert::toDTO).collect(Collectors.toList());
  }
}
