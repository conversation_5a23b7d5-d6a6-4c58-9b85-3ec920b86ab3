package com.yhd.admin.api.domain.monitor.digging.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 掘进机运行参数
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_mk_digging_operation")
public class TbMkDiggingOperation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联掘进机基本信息表ID
     */
    @TableField("digging_id")
    private Long diggingId;

    /**
     * 运行开始时间
     */
    @TableField("running_start_time")
    private LocalDateTime runningStartTime;

    /**
     * 油泵电流(A)
     */
    @TableField("oil_pump_current")
    private BigDecimal oilPumpCurrent;

    /**
     * 二运电流(A)
     */
    @TableField("secondary_transport_current")
    private BigDecimal secondaryTransportCurrent;

    /**
     * 高速电流(A)
     */
    @TableField("high_speed_current")
    private BigDecimal highSpeedCurrent;

    /**
     * 运行速度(m/s)
     */
    @TableField("operation_speed")
    private BigDecimal operationSpeed;

    /**
     * 低速电机转速(rpm)
     */
    @TableField("low_speed_motor_speed")
    private BigDecimal lowSpeedMotorSpeed;

    /**
     * 高速电机转速(rpm)
     */
    @TableField("high_speed_motor_speed")
    private BigDecimal highSpeedMotorSpeed;

    /**
     * 二运电机转速(rpm)
     */
    @TableField("secondary_transport_motor_speed")
    private BigDecimal secondaryTransportMotorSpeed;

    /**
     * 自动截割深度(m)
     */
    @TableField("automatic_cutting_depth")
    private BigDecimal automaticCuttingDepth;

    /**
     * 自动截割速度(m/min)
     */
    @TableField("automatic_cutting_speed")
    private BigDecimal automaticCuttingSpeed;

    /**
     * 油泵流量(L/min)
     */
    @TableField("oil_pump_flow_rate")
    private BigDecimal oilPumpFlowRate;

    /**
     * 油泵压力(bar)
     */
    @TableField("oil_pump_pressure")
    private BigDecimal oilPumpPressure;

    /**
     * 油泵转速(RPM)
     */
    @TableField("oil_pump_rpm")
    private Integer oilPumpRpm;
}
