package com.yhd.admin.api.domain.sys.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/17 14:35
 */
@Data
public class TemplateTable {

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** */
    private String tableName;
    /** pro-table 类型 */
    private String type;
    /** 表格标题 */
    private String title;
    /** 空值时的显示，不设置时显示 -， false 可以关闭此功能 */
    private String columnEmptyText;
    /** 是否展示外边框和列边框 */
    private Boolean bordered;
    /** 页面是否加载中 */
    private Boolean loading;
    /** 分页器，参考配置项或 pagination 文档，设为 false 时不展示和进行分页 */
    private String pagination;
    /** 表格行 key 的取值，可以是字符串或一个函数 */
    private String rowKey;
    /** 是否显示表头 */
    private Boolean showHeader;
    /** 表头是否显示下一次排序的 tooltip 提示。当参数类型为对象时，将被设置为 Tooltip 的属性 */
    private Boolean showSorterTooltip;
    /** 表格大小;default | middle | small */
    private String size;
    /** 支持的排序方式，取值为 ascend descend */
    private String sortDirections;
    /**
     * 设置粘性头部和滚动条;boolean | {offsetHeader?: number, offsetScroll?: number, getContainer?: () => HTMLElement}
     */
    private String sticky;
    /** 总结栏 */
    private String summary;
    /** 表格元素的 table-layout 属性，设为 fixed 表示内容不会影响列的布局 */
    private String tableLayout;
    /** 一个获得 dataSource 的方法 */
    private String request;
    /** 渲染操作栏 */
    private String toolBarRender;
    /** 自定义 table 的 alert */
    private String tableAlertRender;
    /** 自定义 table 的 alert 的操作 */
    private String tableAlertOptionRender;
    /** 表格行是否可选择 */
    private String rowSelection;
    /** 可编辑表格的相关配置 */
    private String editable;
    /** able 和 Search 外围 Card 组件的边框 */
    private String cardbordered;
    /** 分页、排序、筛选变化时触发 */
    private String onChange;
    /** 设置头部行属性 */
    private String onHeaderRow;
    /** 设置行属性 */
    private String onRow;
    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /** 创建时间 */
    @TableField(updateStrategy = FieldStrategy.NEVER, insertStrategy = FieldStrategy.NEVER)
    private LocalDateTime createdTime;
    /** 更新人 */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /** 更新时间 */
    @TableField(updateStrategy = FieldStrategy.NEVER, insertStrategy = FieldStrategy.NEVER)
    private LocalDateTime updatedTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getColumnEmptyText() {
        return columnEmptyText;
    }

    public void setColumnEmptyText(String columnEmptyText) {
        this.columnEmptyText = columnEmptyText;
    }

    public Boolean getBordered() {
        return bordered;
    }

    public void setBordered(Boolean bordered) {
        this.bordered = bordered;
    }

    public Boolean getLoading() {
        return loading;
    }

    public void setLoading(Boolean loading) {
        this.loading = loading;
    }

    public String getPagination() {
        return pagination;
    }

    public void setPagination(String pagination) {
        this.pagination = pagination;
    }

    public String getRowKey() {
        return rowKey;
    }

    public void setRowKey(String rowKey) {
        this.rowKey = rowKey;
    }

    public Boolean getShowHeader() {
        return showHeader;
    }

    public void setShowHeader(Boolean showHeader) {
        this.showHeader = showHeader;
    }

    public Boolean getShowSorterTooltip() {
        return showSorterTooltip;
    }

    public void setShowSorterTooltip(Boolean showSorterTooltip) {
        this.showSorterTooltip = showSorterTooltip;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getSortDirections() {
        return sortDirections;
    }

    public void setSortDirections(String sortDirections) {
        this.sortDirections = sortDirections;
    }

    public String getSticky() {
        return sticky;
    }

    public void setSticky(String sticky) {
        this.sticky = sticky;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getTableLayout() {
        return tableLayout;
    }

    public void setTableLayout(String tableLayout) {
        this.tableLayout = tableLayout;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getToolBarRender() {
        return toolBarRender;
    }

    public void setToolBarRender(String toolBarRender) {
        this.toolBarRender = toolBarRender;
    }

    public String getTableAlertRender() {
        return tableAlertRender;
    }

    public void setTableAlertRender(String tableAlertRender) {
        this.tableAlertRender = tableAlertRender;
    }

    public String getTableAlertOptionRender() {
        return tableAlertOptionRender;
    }

    public void setTableAlertOptionRender(String tableAlertOptionRender) {
        this.tableAlertOptionRender = tableAlertOptionRender;
    }

    public String getRowSelection() {
        return rowSelection;
    }

    public void setRowSelection(String rowSelection) {
        this.rowSelection = rowSelection;
    }

    public String getEditable() {
        return editable;
    }

    public void setEditable(String editable) {
        this.editable = editable;
    }

    public String getCardbordered() {
        return cardbordered;
    }

    public void setCardbordered(String cardbordered) {
        this.cardbordered = cardbordered;
    }

    public String getOnChange() {
        return onChange;
    }

    public void setOnChange(String onChange) {
        this.onChange = onChange;
    }

    public String getOnHeaderRow() {
        return onHeaderRow;
    }

    public void setOnHeaderRow(String onHeaderRow) {
        this.onHeaderRow = onHeaderRow;
    }

    public String getOnRow() {
        return onRow;
    }

    public void setOnRow(String onRow) {
        this.onRow = onRow;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    public String toString() {
        return "TemplateTable{" + "id=" + id + ", tableName='" + tableName + '\'' + ", type='" + type + '\''
            + ", title='" + title + '\'' + ", columnEmptyText='" + columnEmptyText + '\'' + ", bordered=" + bordered
            + ", loading=" + loading + ", pagination='" + pagination + '\'' + ", rowKey='" + rowKey + '\''
            + ", showHeader=" + showHeader + ", showSorterTooltip=" + showSorterTooltip + ", size='" + size + '\''
            + ", sortDirections='" + sortDirections + '\'' + ", sticky='" + sticky + '\'' + ", summary='" + summary
            + '\'' + ", tableLayout='" + tableLayout + '\'' + ", request='" + request + '\'' + ", toolBarRender='"
            + toolBarRender + '\'' + ", tableAlertRender='" + tableAlertRender + '\'' + ", tableAlertOptionRender='"
            + tableAlertOptionRender + '\'' + ", rowSelection='" + rowSelection + '\'' + ", editable='" + editable
            + '\'' + ", cardbordered='" + cardbordered + '\'' + ", onChange='" + onChange + '\'' + ", onHeaderRow='"
            + onHeaderRow + '\'' + ", onRow='" + onRow + '\'' + ", createdBy='" + createdBy + '\'' + ", createdTime="
            + createdTime + ", updatedBy='" + updatedBy + '\'' + ", updatedTime=" + updatedTime + '}';
    }
}
