package com.yhd.admin.api.controller.conduct.trouble;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.trouble.MkHideTroubleRecordConvert;
import com.yhd.admin.api.domain.conduct.dto.trouble.MkHideTroubleRecordDTO;
import com.yhd.admin.api.domain.conduct.query.trouble.MkHideTroubleRecordParam;
import com.yhd.admin.api.domain.conduct.vo.trouble.MkHideTroubleRecordVO;
import com.yhd.admin.api.service.conduct.trouble.MkHideTroubleRecordService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 隐患记录表控制层
 *
 * <AUTHOR>
 * @since 2025-08-04 10:28:34
 */
@RestController
@RequestMapping("/conduct/trouble")
public class MkHideTroubleRecordController {

    private final MkHideTroubleRecordService mkHideTroubleRecordService;
    private final MkHideTroubleRecordConvert mkHideTroubleRecordConvert;

    public MkHideTroubleRecordController(
        MkHideTroubleRecordService mkHideTroubleRecordService,
        MkHideTroubleRecordConvert mkHideTroubleRecordConvert
    ) {
        this.mkHideTroubleRecordService = mkHideTroubleRecordService;
        this.mkHideTroubleRecordConvert = mkHideTroubleRecordConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkHideTroubleRecordParam param) {
        IPage<MkHideTroubleRecordDTO> page = mkHideTroubleRecordService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkHideTroubleRecordConvert::toVO));
    }

    /**
     * 异常催办-分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQueryOfException",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQueryOfException(@RequestBody MkHideTroubleRecordParam param) {
        IPage<MkHideTroubleRecordDTO> page = mkHideTroubleRecordService.pagingQueryOfException(param);
        return new PageRespJson<>(page.convert(mkHideTroubleRecordConvert::toVO));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkHideTroubleRecordVO> getCurrentDetails(@RequestBody MkHideTroubleRecordParam param) {
        return RespJson.success(mkHideTroubleRecordConvert.toVO(mkHideTroubleRecordService.getCurrentDetails(param)));
    }

    /**
     * 新增
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkHideTroubleRecordParam param) {
        Boolean retVal = mkHideTroubleRecordService.add(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkHideTroubleRecordParam param) {
        Boolean retVal = mkHideTroubleRecordService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 催办
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
        value = "/urge",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> urge(@RequestBody MkHideTroubleRecordParam param) {
        Boolean retVal = mkHideTroubleRecordService.urge(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 整改
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
        value = "/handle",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> handle(@RequestBody MkHideTroubleRecordParam param) {
        Boolean retVal = mkHideTroubleRecordService.handle(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 复查
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
        value = "/review",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> review(@RequestBody MkHideTroubleRecordParam param) {
        Boolean retVal = mkHideTroubleRecordService.review(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 在办隐患-导出文件
     *
     * @return 文件地址
     */
    @PostMapping(
        value = "/export",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<String> export(@RequestBody MkHideTroubleRecordParam param) {
        String url = mkHideTroubleRecordService.export(param);
        return RespJson.success(url);
    }

    /**
     * 历史隐患-导出文件
     *
     * @return 文件地址
     */
    @PostMapping(
        value = "/exportHistory",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<String> exportHistory(@RequestBody MkHideTroubleRecordParam param) {
        String url = mkHideTroubleRecordService.exportHistory(param);
        return RespJson.success(url);
    }

    /**
     * 异常催办-导出文件
     *
     * @return 文件地址
     */
    @PostMapping(
        value = "/exportException",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<String> exportException(@RequestBody MkHideTroubleRecordParam param) {
        String url = mkHideTroubleRecordService.exportException(param);
        return RespJson.success(url);
    }

}

