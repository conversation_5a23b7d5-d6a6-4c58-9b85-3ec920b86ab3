package com.yhd.admin.api.service.conduct.base;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.base.MkMiningTeamInfoDao;
import com.yhd.admin.api.domain.conduct.convert.base.MkMiningTeamInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.base.MkMiningTeamInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkMiningTeamInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkMiningTeamInfoParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 采煤队服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25 14:01:30
 */
@Service
public class MkMiningTeamInfoServiceImpl extends ServiceImpl<MkMiningTeamInfoDao, MkMiningTeamInfo> implements MkMiningTeamInfoService {

    private final MkMiningTeamInfoConvert mkMiningTeamInfoConvert;

    public MkMiningTeamInfoServiceImpl(MkMiningTeamInfoConvert mkMiningTeamInfoConvert) {
        this.mkMiningTeamInfoConvert = mkMiningTeamInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkMiningTeamInfoDTO> pagingQuery(MkMiningTeamInfoParam param) {
        IPage<MkMiningTeamInfo> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkMiningTeamInfo> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.eq(StringUtils.isNotBlank(param.getName()), MkMiningTeamInfo::getName, param.getName());
        wrapper.eq(StringUtils.isNotBlank(param.getWorkFaceName()), MkMiningTeamInfo::getWorkFaceName, param.getWorkFaceName());
        return wrapper.page(page).convert(mkMiningTeamInfoConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkMiningTeamInfoDTO> queryList(MkMiningTeamInfoParam param) {
        LambdaQueryWrapper<MkMiningTeamInfo> wrapper = new LambdaQueryWrapper<>();
        return mkMiningTeamInfoConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param  参数
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkMiningTeamInfoParam param) {
        MkMiningTeamInfo entity = mkMiningTeamInfoConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkMiningTeamInfoDTO getCurrentDetails(MkMiningTeamInfoParam param) {
        return mkMiningTeamInfoConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkMiningTeamInfoParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }
}
