package com.yhd.admin.api.domain.operation.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 运维配置中心-单位管理-单位类型表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class MkUserOrgTypeDTO extends BaseDTO implements Serializable {
	/**
	* Id
	*/
	private Long id;

	/**
	* 类型名称
	*/
	private String name;

}
