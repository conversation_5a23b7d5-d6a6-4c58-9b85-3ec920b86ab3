package com.yhd.admin.api.domain.monitor.fan.convert;

import com.yhd.admin.api.domain.monitor.fan.dto.TbMkLocalFanDetailsDTO;
import com.yhd.admin.api.domain.monitor.fan.entity.TbMkLocalFanDetails;
import com.yhd.admin.api.domain.monitor.fan.vo.TbMkLocalFanDetailsVO;
import com.yhd.admin.api.domain.monitor.fan.vo.create.TbMkLocalFanDetailsCreateVO;
import com.yhd.admin.api.domain.monitor.fan.vo.update.TbMkLocalFanDetailsUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 局扇风机详细监控数据
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkLocalFanDetailsConvert {

    @Mapping(target = "id", ignore = true)
    TbMkLocalFanDetails convert(TbMkLocalFanDetailsCreateVO createVO);

    TbMkLocalFanDetails convert(TbMkLocalFanDetailsUpdateVO updateVO);

    TbMkLocalFanDetailsVO convert(TbMkLocalFanDetails po);

    TbMkLocalFanDetailsDTO toDTO(TbMkLocalFanDetails po);



}
