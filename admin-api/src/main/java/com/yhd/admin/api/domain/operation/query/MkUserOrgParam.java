package com.yhd.admin.api.domain.operation.query;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 运维配置中心-单位管理-单位管理表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class MkUserOrgParam extends QueryParam implements Serializable {
	/**
	* Id
	*/
	private Long id;

	/**
	* 单位编码
	*/
	private String orgCode;

	/**
	* 单位名称
	*/
	private String orgName;

	/**
	* 单位简称
	*/
	private String orgSimpleName;

	/**
	* 上级单位id
	*/
	private Long upId;

	/**
	* 上级单位名称
	*/
	private String upName;

	/**
	* 单位类型id
	*/
	private Long orgTypeId;

	/**
	* 单位类型名称
	*/
	private String orgTypeName;

	/**
	* 单位电话
	*/
	private String phone;

	/**
	* 状态（是否启用，true:是，false:否）
	*/
	private Boolean status;

}
