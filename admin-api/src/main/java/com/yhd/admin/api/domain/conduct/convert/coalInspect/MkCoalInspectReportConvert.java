package com.yhd.admin.api.domain.conduct.convert.coalInspect;

import com.yhd.admin.api.domain.conduct.dto.coalInspect.MkCoalInspectReportDTO;
import com.yhd.admin.api.domain.conduct.entity.coalInspect.MkCoalInspectReport;
import com.yhd.admin.api.domain.conduct.query.coalInspect.MkCoalInspectReportParam;
import com.yhd.admin.api.domain.conduct.vo.coalInspect.MkCoalInspectReportVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 煤质检验报告(MkCoalInspectReport)Convert
 *
 * <AUTHOR>
 * @since 2025-08-05 09:12:06
 */
@Mapper(componentModel = "spring")
public interface MkCoalInspectReportConvert {
    MkCoalInspectReport toEntity(MkCoalInspectReportParam param);

    MkCoalInspectReportDTO toDTO(MkCoalInspectReport entity);

    MkCoalInspectReportVO toVO(MkCoalInspectReportDTO dto);

    List<MkCoalInspectReportDTO> toDTOList(List<MkCoalInspectReport> entityList);

    List<MkCoalInspectReportVO> toVOList(List<MkCoalInspectReportDTO> dtoList);
}

