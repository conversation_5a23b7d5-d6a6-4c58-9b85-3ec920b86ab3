package com.yhd.admin.api.domain.monitor.nitrogen.vo;

import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenSystemMainCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 制氮系统监控数据-主表
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkNitrogenSystemMainVO extends TbMkNitrogenSystemMainCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
