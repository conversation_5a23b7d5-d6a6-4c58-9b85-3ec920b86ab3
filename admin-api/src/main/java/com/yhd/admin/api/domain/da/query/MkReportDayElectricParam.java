package com.yhd.admin.api.domain.da.query;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 电度能耗分析表
 *
 * <AUTHOR>
 * @date 2025/8/1 14:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkReportDayElectricParam extends QueryParam implements Serializable {
  /** 开始日期 */
  private LocalDate startDate;
  /** 结束日期 */
  private LocalDate endDate;
  /** 月份(yyyy-MM) */
  private String month;
  /** 开始月份(yyyy-MM) */
  private String startMonth;
  /** 结束月份(yyyy-MM) */
  private String endMonth;
  /** 年(yyyy) */
  private String year;
  /** 开始年份(yyyy) */
  private String startYear;
  /** 结束年份(yyyy) */
  private String endYear;

  private Long id;

  /** 统计日期 */
  private LocalDate statsDate;
  /** 组织机构编码 */
  private String orgCode;
  /** 组织机构名称 */
  private String orgName;
  /** 峰用电 */
  private Double peakValue;
  /** 谷用电 */
  private Double valleyValue;
  /** 平用电 */
  private Double normalValue;
}
