package com.yhd.admin.api.domain.monitor.ventilation.convert;

import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkMotorOperationDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkMotorOperation;
import com.yhd.admin.api.domain.monitor.ventilation.vo.TbMkMotorOperationVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkMotorOperationCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkMotorOperationUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 电机运行参数
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkMotorOperationConvert {

    @Mapping(target = "id", ignore = true)
    TbMkMotorOperation convert(TbMkMotorOperationCreateVO createVO);

    TbMkMotorOperation convert(TbMkMotorOperationUpdateVO updateVO);

    TbMkMotorOperationVO convert(TbMkMotorOperation po);

    TbMkMotorOperationDTO toDTO(TbMkMotorOperation po);



}
