package com.yhd.admin.api.domain.conduct.entity.coalInspect;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 煤质检验报告(MkCoalInspectReport)实体类
 *
 * <AUTHOR>
 * @since 2025-08-05 09:12:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkCoalInspectReport extends BaseEntity implements Serializable {

    /** id */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 采样日期 */
    private LocalDate sampleDate;
    /** 采样时间 */
    private LocalTime sampleTime;
    /** 采样位置 */
    private String sampleSite;
    /** 煤种名称(1中块 2混块 3粉煤 4五八块 5二五块 6一三块) */
    private String coalTypeName;
    /** 煤种代码 COAL_TYPE */
    private String coalTypeCode;
    /** 热值 */
    private BigDecimal heatValue;
    /** 全水分 */
    private BigDecimal moisture;
    /** 灰分 */
    private BigDecimal ash;
    /** 挥发分 */
    private BigDecimal volatileWater;
    /** 固定碳 */
    private BigDecimal fixCarbon;
    /** 全硫 */
    private BigDecimal sulfur;
    /** 分析水 */
    private BigDecimal analyzeWater;
    /** 填报人 */
    private String creator;
    /** 填报日期 */
    private LocalDate createdDate;
}

