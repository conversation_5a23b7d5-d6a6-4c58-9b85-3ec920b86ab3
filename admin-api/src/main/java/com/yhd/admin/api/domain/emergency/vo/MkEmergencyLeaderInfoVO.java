package com.yhd.admin.api.domain.emergency.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 入井领导信息VO
 *
 * <AUTHOR>
 */
@Data
public class MkEmergencyLeaderInfoVO {
    /**
     * 领导编码
     */
    private String leaderCode;

    /**
     * 领导姓名
     */
    private String leaderName;

    /**
     * 职位
     */
    private String position;

    /**
     * 部门
     */
    private String department;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 是否下井
     */
    private Boolean isUnderground;

    /**
     * 是否下井名称
     */
    private String isUndergroundName;

    /**
     * 入井时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String entryTime;

    /**
     * 当前位置
     */
    private String currentLocation;
}