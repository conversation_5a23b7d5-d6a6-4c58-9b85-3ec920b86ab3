package com.yhd.admin.api.domain.emergency.query;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencySurroundingParam.java
 * @Description 应急救援周边信息查询参数
 * @createTime 2025年01月20日 14:00:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MkEmergencySurroundingParam extends QueryParam {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 项目编码
     */
    private String itemCode;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 项目类型：人员、分站、视频广播、电话、安监
     */
    private String itemType;

    /**
     * 位置信息
     */
    private String location;

    /**
     * 状态：1-正常，2-异常，3-离线
     */
    private Integer status;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * X坐标
     */
    private BigDecimal coordinateX;

    /**
     * Y坐标
     */
    private BigDecimal coordinateY;

    /**
     * Z坐标
     */
    private BigDecimal coordinateZ;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 创建开始时间
     */
    private LocalDateTime createdTimeStart;

    /**
     * 创建结束时间
     */
    private LocalDateTime createdTimeEnd;
}
