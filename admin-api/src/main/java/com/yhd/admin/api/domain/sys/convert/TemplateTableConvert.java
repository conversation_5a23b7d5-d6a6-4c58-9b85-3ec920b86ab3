package com.yhd.admin.api.domain.sys.convert;

import org.mapstruct.Mapper;

import com.yhd.admin.api.domain.sys.dto.TemplateTableDTO;
import com.yhd.admin.api.domain.sys.entity.TemplateTable;
import com.yhd.admin.api.domain.sys.query.TemplateTableParam;
import com.yhd.admin.api.domain.sys.vo.TemplateTableVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/17 14:57
 */
@Mapper(componentModel = "spring")
public interface TemplateTableConvert {

    TemplateTable transformParamToEntity(TemplateTableParam templateTableParam);

    TemplateTableDTO transformEntityToDTO(TemplateTable templateTable);

    TemplateTableVO transformDTOToVO(TemplateTableDTO templateTableDTO);
}
