package com.yhd.admin.api.controller.sys;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.sys.convert.AppConvert;
import com.yhd.admin.api.domain.sys.dto.AppDTO;
import com.yhd.admin.api.domain.sys.query.AppParam;
import com.yhd.admin.api.domain.sys.vo.AppVO;
import com.yhd.admin.api.service.sys.AppSrv;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;

import jakarta.annotation.Resource;

/**
 * 系统集成
 */
@RestController
@RequestMapping("/integration")
public class IntegrationController {

    @Resource
    private AppConvert convert;
    @Resource
    private AppSrv service;

    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<AppVO> pagingQuery(@RequestBody AppParam queryParam) {
        IPage<AppDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody AppParam queryParam) {
        return RespJson.success(service.add(queryParam));
    }

    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> modify(@RequestBody AppParam queryParam) {
        return RespJson.success(service.modify(queryParam));
    }

    @PostMapping(value = "/getCurrentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody AppParam queryParam) {
        return RespJson.success(convert.toVO(service.getCurrentDetail(queryParam)));
    }

    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody BatchParam param) {
        return RespJson.success(service.removeBatch(param));
    }

}
