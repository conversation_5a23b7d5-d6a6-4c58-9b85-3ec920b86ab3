package com.yhd.admin.api.domain.monitor.digging.vo;

import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingNavigationCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 掘进机导航参数
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkDiggingNavigationVO extends TbMkDiggingNavigationCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
