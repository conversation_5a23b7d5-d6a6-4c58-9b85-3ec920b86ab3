package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkDisasterGradeDTO;
import com.yhd.admin.api.domain.operation.entity.MkDisasterGrade;
import com.yhd.admin.api.domain.operation.query.MkDisasterGradeParam;
import com.yhd.admin.api.domain.operation.vo.MkDisasterGradeVO;
import org.mapstruct.Mapper;




/**
* 算法模型-报警次级表
*
* <AUTHOR>
* @since 1.0.0 2025-08-01
*/
@Mapper(componentModel = "spring")
public interface MkDisasterGradeConvert {

    MkDisasterGrade toEntity(MkDisasterGradeParam param);

    MkDisasterGradeVO toVO(MkDisasterGradeDTO dto);

    MkDisasterGradeDTO toDTO(MkDisasterGrade entity);

}
