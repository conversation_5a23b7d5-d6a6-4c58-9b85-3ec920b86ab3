package com.yhd.admin.api.domain.comm.convert;

import com.yhd.admin.api.domain.comm.dto.MkCommContactPhoneDTO;
import com.yhd.admin.api.domain.comm.entity.MkCommContactPhone;
import com.yhd.admin.api.domain.comm.query.MkCommContactPhoneParam;
import com.yhd.admin.api.domain.comm.vo.MkCommContactPhoneVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MkCommContactPhoneConvert {
  MkCommContactPhoneDTO toDTO(MkCommContactPhone entity);

  List<MkCommContactPhoneDTO> toDTO(List<MkCommContactPhone> entityList);

  MkCommContactPhoneVO toVO(MkCommContactPhoneDTO dto);

  MkCommContactPhone toEntity(MkCommContactPhoneParam param);

  List<MkCommContactPhone> toEntity(List<MkCommContactPhoneParam> paramList);
}
