package com.yhd.admin.api.domain.dispatch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 智能调度中心-视频监控
 *
 * <AUTHOR>
 * @date 2025/7/31 09:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkDispatchMonitor extends BaseEntity implements Serializable {

  @TableId(type = IdType.AUTO)
  private Long id;

  /** 视频监控数 */
  private Long monitorNum;

  /** 在线数 */
  private Long onlineNum;

  /** 离线数 */
  private Long offlineNum;

  /** 监控画面url */
  private String monitorUrl;

  /** AI告警信息 */
  private String aiWarn;

  /** 收藏列表 */
  private String collectArea;
}
