package com.yhd.admin.api.service.sys;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.sys.DicItemDao;
import com.yhd.admin.api.domain.sys.convert.DicItemConvert;
import com.yhd.admin.api.domain.sys.dto.DicItemDTO;
import com.yhd.admin.api.domain.sys.entity.SysDicItem;
import com.yhd.admin.api.domain.sys.enums.APIRedisKeyEnum;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.domain.sys.query.DicItemParam;
import com.yhd.admin.api.exception.BMSException;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName DicItemServiceImpl.java @Description TODO
 * @createTime 2020年05月20日 16:47:00
 */
@Service
public class DicItemServiceImpl extends ServiceImpl<DicItemDao, SysDicItem> implements DicItemService {

    private final DicItemConvert convert;

    private final RedisTemplate<String, Object> redisTemplate;

    private final HashOperations<String, String, List<DicItemDTO>> hashOperations;

    public DicItemServiceImpl(DicItemConvert convert, RedisTemplate<String, Object> redisTemplate) {
        this.convert = convert;
        this.redisTemplate = redisTemplate;
        this.hashOperations = redisTemplate.opsForHash();
    }

    @Override
    public Boolean saveOrUpdateBatch(List<DicItemParam> params) {
        return saveOrUpdateBatch(convert.toEntity(params));
    }

    @Override
    public Boolean removeById(Long id) {
        return super.removeById(id);
    }

    @Override
    public List<DicItemDTO> queryDicItemByDicId(DicItemParam param) {
        List<DicItemDTO> retVal = null;
        if (param.getDicId() == null && StringUtils.isBlank(param.getCategory())) {
            throw new BMSException(ExceptionEnum.DIC_ID_CATEGORY_NULL);
        }
        if (StringUtils.isNotBlank(param.getCategory())) {
            retVal = this.hashOperations.get(APIRedisKeyEnum.DIC.getKey(), param.getCategory());
        }
        if (CollectionUtils.isEmpty(retVal)) {
            LambdaQueryChainWrapper<SysDicItem> itemQueryChain = new LambdaQueryChainWrapper<>(baseMapper);
            itemQueryChain.eq(param.getDicId() != null, SysDicItem::getDicId, param.getDicId())
                .eq(StringUtils.isNotBlank(param.getCategory()), SysDicItem::getCategory, param.getCategory())
                .orderByAsc(SysDicItem::getOrderNum);
            retVal = convert.toDTO(itemQueryChain.list());
        }
        return retVal;
    }

    @Override
    public List<DicItemDTO> queryAllDicItem() {
        LambdaQueryChainWrapper<SysDicItem> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return convert.toDTO(queryChainWrapper.list());
    }

    /**
     * 根据字典项ID更新状态
     *
     * @param id
     * @param status
     * @return
     */
    @Override
    public Boolean toggleItemStatus(Long id, Boolean status) {
        LambdaUpdateChainWrapper<SysDicItem> updateChainWrapper = new LambdaUpdateChainWrapper<>(baseMapper);
        updateChainWrapper.eq(SysDicItem::getDicId, id).set(SysDicItem::getStatus, status);
        return updateChainWrapper.update();
    }
}
