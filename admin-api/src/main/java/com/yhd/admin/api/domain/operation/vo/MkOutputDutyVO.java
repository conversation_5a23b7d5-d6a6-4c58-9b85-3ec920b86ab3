package com.yhd.admin.api.domain.operation.vo;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产录入-值班人员表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MkOutputDutyVO extends BaseVO implements Cloneable, Serializable {
    /**
     * Id
     */
    private Long id;

	/**
	* 日期
	*/
	private Date outputDate;

    /**
     * 值班领导人员id
     */
    private String leaderUserNo;

    /**
     * 值班领导人员名称
     */
    private String leaderUserName;

    /**
     * 一班人员id
     */
    private String oneUserNo;

    /**
     * 一班人员名称
     */
    private String oneUserName;

    /**
     * 二班人员id
     */
    private String twoUserNo;

    /**
     * 二班人员名称
     */
    private String twoUserName;

    /**
     * 三班人员id
     */
    private String threeUserNo;

    /**
     * 三班人员名称
     */
    private String threeUserName;

    /**
     * 四班人员id
     */
    private String fourUserNo;

    /**
     * 四班人员名称
     */
    private String fourUserName;

}
