package com.yhd.admin.api.domain.operation.vo;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产录入-值班人员表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MkOutputDutyVO extends BaseVO implements Cloneable, Serializable {
    /**
     * Id
     */
    private Long id;

	/**
	* 日期
	*/
	private Date outputDate;

	/**
	* 班次类型 0：值班领导 1：一班 2：二班 3：三班 4：四班
	*/
	private Integer shiftType;

	/**
	* 值班人员id
	*/
	private String userNo;

	/**
	* 值班人员名称
	*/
	private String userName;

}
