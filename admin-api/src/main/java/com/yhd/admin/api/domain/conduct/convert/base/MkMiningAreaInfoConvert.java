package com.yhd.admin.api.domain.conduct.convert.base;

import com.yhd.admin.api.domain.conduct.dto.base.MkMiningAreaInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkMiningAreaInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkMiningAreaInfoParam;
import com.yhd.admin.api.domain.conduct.vo.base.MkMiningAreaInfoVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 采区信息(MkMiningAreaInfo)Convert
 *
 * <AUTHOR>
 * @since 2025-07-25 10:32:53
 */
@Mapper(componentModel = "spring")
public interface MkMiningAreaInfoConvert {
    MkMiningAreaInfo toEntity(MkMiningAreaInfoParam param);

    MkMiningAreaInfoDTO toDTO(MkMiningAreaInfo entity);

    MkMiningAreaInfoVO toVO(MkMiningAreaInfoDTO dto);

    List<MkMiningAreaInfoDTO> toDTOList(List<MkMiningAreaInfo> entityList);

    List<MkMiningAreaInfoVO> toVOList(List<MkMiningAreaInfoDTO> dtoList);
}

