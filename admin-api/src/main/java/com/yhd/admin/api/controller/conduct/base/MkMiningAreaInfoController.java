package com.yhd.admin.api.controller.conduct.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.base.MkMiningAreaInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.base.MkMiningAreaInfoDTO;
import com.yhd.admin.api.domain.conduct.query.base.MkMiningAreaInfoParam;
import com.yhd.admin.api.domain.conduct.vo.base.MkMiningAreaInfoVO;
import com.yhd.admin.api.service.conduct.base.MkMiningAreaInfoService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 采区信息控制层
 *
 * <AUTHOR>
 * @since 2025-07-25 10:34:53
 * @module 采区基础信息
 */
@RestController
@RequestMapping("/conduct/miningArea")
public class MkMiningAreaInfoController {

    private final MkMiningAreaInfoService mkMiningAreaInfoService;
    private final MkMiningAreaInfoConvert mkMiningAreaInfoConvert;

    public MkMiningAreaInfoController(
        MkMiningAreaInfoService mkMiningAreaInfoService,
        MkMiningAreaInfoConvert mkMiningAreaInfoConvert
    ) {
        this.mkMiningAreaInfoService = mkMiningAreaInfoService;
        this.mkMiningAreaInfoConvert = mkMiningAreaInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkMiningAreaInfoParam param) {
        IPage<MkMiningAreaInfoDTO> page = mkMiningAreaInfoService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkMiningAreaInfoConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkMiningAreaInfoParam param) {
        return RespJson.success(mkMiningAreaInfoConvert.toVOList(mkMiningAreaInfoService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkMiningAreaInfoVO> getCurrentDetails(@RequestBody MkMiningAreaInfoParam param) {
        return RespJson.success(mkMiningAreaInfoConvert.toVO(mkMiningAreaInfoService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkMiningAreaInfoParam param) {
        Boolean retVal = mkMiningAreaInfoService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkMiningAreaInfoParam param) {
        Boolean retVal = mkMiningAreaInfoService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

