package com.yhd.admin.api.domain.video.vo;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class MkVideoRegionVO extends BaseVO {
  private Long id;
  /** 区域名称 */
  private String regionName;
  /** 父级ID */
  private Long parentId;
  /** 父级名称 */
  private String parentName;
  /** 状态 */
  private Boolean status;
  /** 排序，越小越靠前 */
  private Long sortNum;

  /** 子节点 */
  private List<MkVideoRegionVO> children;
}
