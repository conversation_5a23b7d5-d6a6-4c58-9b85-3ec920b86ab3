package com.yhd.admin.api.service.home;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.home.MkHomeDao;
import com.yhd.admin.api.domain.home.convert.MkHomeConvert;
import com.yhd.admin.api.domain.home.dto.MkHomeDTO;
import com.yhd.admin.api.domain.home.dto.MkHomeDataStructureDTO;
import com.yhd.admin.api.domain.home.entity.MkHome;
import com.yhd.admin.api.domain.home.query.MkHomeParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.core.collection.CollectionUtil;
import com.yhd.json.JSONUtil;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 综合管控大屏
 *
 * <AUTHOR>
 * @date 2025/7/29 09:34
 */
@Service
public class MkHomeServiceImpl extends ServiceImpl<MkHomeDao, MkHome> implements MkHomeService {

  private final MkHomeConvert convert;

  public MkHomeServiceImpl(MkHomeConvert convert) {
    this.convert = convert;
  }

  /**
   * 新增
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean addOrModify(MkHomeParam param) {
    MkHome entity = convert.toEntity(param);
    return super.saveOrUpdate(entity);
  }

  /**
   * 删除-根据主键
   *
   * @param param id
   * @return true成功，false失败
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean remove(MkHomeParam param) {
    Long count = this.lambdaQuery().count();
    if (count == 1) {
      throw new BMSException("error", "需要至少保留一条数据，不能全部删除，请检查！");
    }
    return super.removeById(param.getId());
  }

  /**
   * 查询详情信息
   *
   * @param param 主键id or 编号
   * @return 通讯详情信息
   */
  @Override
  public MkHomeDTO getCurrentDetail(MkHomeParam param) {
    MkHomeDTO result = null;
    if (Objects.isNull(param.getId())) {
      throw new BMSException(ExceptionEnum.MISS_PARAM_EXCEPTION);
    }
    MkHome entity = super.getById(param);
    result = convert.toDTO(entity);
    return result;
  }

  /**
   * 根据条件查询通讯信息分页列表
   *
   * @param param 查询条件
   * @return 通讯信息分页列表
   */
  @Override
  public IPage<MkHomeDTO> pagingQuery(MkHomeParam param) {
    IPage<MkHome> page = new Page<>(param.getCurrent(), param.getPageSize());
    LambdaQueryChainWrapper<MkHome> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
    // 排序
    wrapper.orderByAsc(MkHome::getUpdatedTime);
    IPage<MkHome> iPage = wrapper.page(page);
    return iPage.convert(convert::toDTO);
  }

  @Override
  public MkHomeDTO getData() {
    List<MkHome> list =
        this.lambdaQuery().orderByDesc(MkHome::getUpdatedTime).last("limit 1").list();
    MkHomeDTO result = new MkHomeDTO();
    if (CollectionUtil.isEmpty(list)) {
      return result;
    }
    MkHome entity = list.get(0);
    // 安全生产多少天
    if (entity.getDate() != null) {
      // +1是包含当天
      long daysLived = ChronoUnit.DAYS.between(entity.getDate(), LocalDate.now()) + 1;
      if (daysLived >= 0) {
        result.setDay(daysLived);
      }
    }
    // 设计产能
    result.setPlanProduction(new BigDecimal("3000000"));

    // 主采煤层
    result.setMainCoalMining(entity.getMainCoalMining());

    // 带班信息
    List<MkHomeDataStructureDTO> classList = new ArrayList<>();
    classList.add(MkHomeDataStructureDTO.builder().status("早班").name("张三").build());
    classList.add(MkHomeDataStructureDTO.builder().status("中班").name("李四").build());
    classList.add(MkHomeDataStructureDTO.builder().status("晚班").name("王五").build());
    result.setClassList(classList);

    // 年完成率
    result.setYearRate(new BigDecimal("36.37"));

    // 生产数据
    List<MkHomeDataStructureDTO> productionList = new ArrayList<>();
    productionList.add(
        MkHomeDataStructureDTO.builder().name("年完成").value(new BigDecimal("1091116")).build());
    productionList.add(
        MkHomeDataStructureDTO.builder().name("年计划").value(new BigDecimal("3000000")).build());
    productionList.add(
        MkHomeDataStructureDTO.builder().name("月完成").value(new BigDecimal("197288")).build());
    productionList.add(
        MkHomeDataStructureDTO.builder().name("月计划").value(new BigDecimal("250000")).build());
    productionList.add(
        MkHomeDataStructureDTO.builder().name("日完成").value(new BigDecimal("5651.10")).build());
    productionList.add(
        MkHomeDataStructureDTO.builder().name("日计划").value(new BigDecimal("8300")).build());
    result.setProductionList(productionList);

    // 煤仓情况
    if (StringUtils.isNotBlank(entity.getCoalBunker())) {
      result.setCoalBunkerList(
          JSONUtil.toList(entity.getCoalBunker(), MkHomeDataStructureDTO.class));
    }

    // 人员分布
    List<MkHomeDataStructureDTO> peopleList = new ArrayList<>();
    peopleList.add(
        MkHomeDataStructureDTO.builder().name("信息维护中心").value(new BigDecimal("10")).build());
    peopleList.add(
        MkHomeDataStructureDTO.builder().name("地质测量部").value(new BigDecimal("5")).build());
    peopleList.add(
        MkHomeDataStructureDTO.builder().name("信息维护中心1部").value(new BigDecimal("20")).build());
    peopleList.add(
        MkHomeDataStructureDTO.builder().name("信息维护中心2部").value(new BigDecimal("130")).build());
    result.setPeopleList(peopleList);

    // 设备状态
    if (StringUtils.isNotBlank(entity.getEquipmentStatus())) {
      result.setEquipmentStatusList(
          JSONUtil.toList(entity.getEquipmentStatus(), MkHomeDataStructureDTO.class));
    }
    // 安全状态
    if (StringUtils.isNotBlank(entity.getSafeStatus())) {
      result.setSafeStatusList(
          JSONUtil.toList(entity.getSafeStatus(), MkHomeDataStructureDTO.class));
    }
    // 监控画面
    if (StringUtils.isNotBlank(entity.getMonitorUrl())) {
      result.setMonitorList(Arrays.asList(entity.getMonitorUrl().split(",")));
    }

    return result;
  }
}
