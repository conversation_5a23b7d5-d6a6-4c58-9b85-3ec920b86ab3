package com.yhd.admin.api.service.home;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.home.dto.MkHomeDTO;
import com.yhd.admin.api.domain.home.entity.MkHome;
import com.yhd.admin.api.domain.home.query.MkHomeParam;

/**
 * 综合管控大屏
 *
 * <AUTHOR>
 * @date 2025/7/29 09:34
 */
public interface MkHomeService extends IService<MkHome> {

  /**
   * 新增
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  Boolean addOrModify(MkHomeParam param);

  /**
   * 删除-根据主键
   *
   * @param param id
   * @return true成功，false失败
   */
  Boolean remove(MkHomeParam param);

  /**
   * 查询详情信息
   *
   * @param param 主键id or 编号
   * @return 详情信息
   */
  MkHomeDTO getCurrentDetail(MkHomeParam param);

  /**
   * 根据条件查询通讯信息分页列表
   *
   * @param param 查询条件
   * @return 分页列表
   */
  IPage<MkHomeDTO> pagingQuery(MkHomeParam param);

  /**
   * 查询综合管控大屏
   *
   * @return 详情信息
   */
  MkHomeDTO getData();
}
