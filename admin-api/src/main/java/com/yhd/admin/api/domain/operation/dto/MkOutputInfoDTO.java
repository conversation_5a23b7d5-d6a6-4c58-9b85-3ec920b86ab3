package com.yhd.admin.api.domain.operation.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产录入-产量信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MkOutputInfoDTO extends BaseDTO implements Cloneable, Serializable {
    /**
     * Id
     */
    private Long id;

	/**
	* 日期
	*/
	private Date outputDate;

	/**
	* 产量类型 1：原煤产量 2：掘进进尺
	*/
	private Integer outputType;

	/**
	* 施工单位
	*/
	private String constructionTeam;

	/**
	* 工程名称
	*/
	private String projectName;

	/**
	* 工程月计划量
	*/
	private BigDecimal monthPlanNum;

	/**
	* 日计划产量
	*/
	private BigDecimal dayPlanOutput;

	/**
	* 日产量
	*/
	private BigDecimal dayOutput;

	/**
	* 日超欠量
	*/
	private BigDecimal dayExceedOweNum;

	/**
	* 月超欠量
	*/
	private BigDecimal monthExceedOweNum;

	/**
	* 月累计
	*/
	private BigDecimal monthOutput;

	/**
	* 年累计
	*/
	private BigDecimal yearOutput;

	/**
	* 备注
	*/
	private String remark;

}
