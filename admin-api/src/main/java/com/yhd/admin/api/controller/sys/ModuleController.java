package com.yhd.admin.api.controller.sys;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yhd.admin.api.domain.sys.convert.ClientConvert;
import com.yhd.admin.api.domain.sys.dto.ClientDTO;
import com.yhd.admin.api.domain.sys.query.ClientQueryParam;
import com.yhd.admin.api.domain.sys.vo.ClientVO;
import com.yhd.admin.api.service.sys.ClientService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 客户端/资源模块管理
 *
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/4 09:12
 */
@RequestMapping("/module")
@RestController
public class ModuleController {

    private final ClientConvert convert;

    private final ClientService clientService;

    private final ObjectMapper objectMapper;

    public ModuleController(ClientConvert convert, ClientService clientService, ObjectMapper objectMapper) {
        this.convert = convert;
        this.clientService = clientService;
        this.objectMapper = objectMapper;
    }

    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<ClientVO> pagingQuery(@RequestBody ClientQueryParam queryParam) {
        IPage<ClientDTO> iPage = clientService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(value = "/saveOrUpdate", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson add(@RequestBody ClientQueryParam addParam) {
        clientService.saveOrUpdate(addParam);
        return RespJson.success();
    }

    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody ClientQueryParam modifyParam) {
        clientService.modifyClient(modifyParam);
        return RespJson.success();
    }

    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam batchParam) {
        clientService.removeBatch(batchParam);
        return RespJson.success();
    }

    @PostMapping(value = "/currentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<ClientVO> currentDetail(@RequestBody ClientQueryParam queryParam) {
        return RespJson.success(convert.toVO(clientService.currentDetail(queryParam)));
    }

    @PostMapping(value = "/queryList", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<ClientVO>> queryList(@RequestBody ClientQueryParam queryParam) {
        List<ClientVO> retVal =
            clientService.queryList(queryParam, Boolean.FALSE).stream().map(convert::toVO).collect(Collectors.toList());
        return RespJson.success(retVal);
    }

    @PostMapping(value = "/validateIfNotExist", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> checkIfNotExist(@RequestBody ClientQueryParam queryParam) {
        Boolean ifExit = clientService.checkIfNotExist(queryParam);
        return RespJson.success(ifExit);
    }
}
