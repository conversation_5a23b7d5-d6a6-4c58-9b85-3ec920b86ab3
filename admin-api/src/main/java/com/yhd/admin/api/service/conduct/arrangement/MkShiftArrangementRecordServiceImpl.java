package com.yhd.admin.api.service.conduct.arrangement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.arrangement.MkShiftArrangementRecordDao;
import com.yhd.admin.api.domain.conduct.convert.arrangement.MkShiftArrangementRecordConvert;
import com.yhd.admin.api.domain.conduct.dto.arrangement.MkShiftArrangementDTO;
import com.yhd.admin.api.domain.conduct.dto.arrangement.MkShiftArrangementRecordDTO;
import com.yhd.admin.api.domain.conduct.entity.arrangement.MkShiftArrangement;
import com.yhd.admin.api.domain.conduct.entity.arrangement.MkShiftArrangementRecord;
import com.yhd.admin.api.domain.conduct.query.arrangement.MkShiftArrangementParam;
import com.yhd.admin.api.domain.conduct.query.arrangement.MkShiftArrangementRecordParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.DicService;
import com.yhd.admin.common.constant.DicConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 调班记录表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29 11:30:39
 */
@Service
public class MkShiftArrangementRecordServiceImpl extends ServiceImpl<MkShiftArrangementRecordDao, MkShiftArrangementRecord> implements MkShiftArrangementRecordService {

    public final MkShiftArrangementRecordConvert mkShiftArrangementRecordConvert;
    public final MkShiftArrangementService mkShiftArrangementService;
    public final DicService dicService;

    public MkShiftArrangementRecordServiceImpl(MkShiftArrangementRecordConvert mkShiftArrangementRecordConvert, MkShiftArrangementService mkShiftArrangementService, DicService dicService) {
        this.mkShiftArrangementRecordConvert = mkShiftArrangementRecordConvert;
        this.mkShiftArrangementService = mkShiftArrangementService;
        this.dicService = dicService;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkShiftArrangementRecordDTO> pagingQuery(MkShiftArrangementRecordParam param) {
        IPage<MkShiftArrangementRecord> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkShiftArrangementRecord> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        // 查询条件 年、月 查询当月调班记录
        if (StringUtils.isNotBlank(param.getYear()) || StringUtils.isNotBlank(param.getMonth())) {
            String year = param.getYear();
            String month = param.getMonth();
            LocalDate startDate;
            startDate = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 1);
            LocalDate endDate = startDate.plusMonths(1).minusDays(1);
            wrapper.ge(MkShiftArrangementRecord::getShiftDate, startDate);
            wrapper.le(MkShiftArrangementRecord::getShiftDate, endDate);
        }
        // 审核状态
        wrapper.eq(StringUtils.isNotBlank(param.getStatusCode()), MkShiftArrangementRecord::getStatusCode, param.getStatusCode());
        // 班次类型
        wrapper.eq(StringUtils.isNotBlank(param.getShiftTypeCode()), MkShiftArrangementRecord::getShiftTypeCode, param.getShiftTypeCode());
        return wrapper.page(page).convert(mkShiftArrangementRecordConvert::toDTO);
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkShiftArrangementRecordParam param) {
        // 获取排班表id
        MkShiftArrangementRecord entity = mkShiftArrangementRecordConvert.toEntity(param);
        MkShiftArrangementParam mkShiftArrangementParam = new MkShiftArrangementParam();
        mkShiftArrangementParam.setShiftDate(entity.getShiftDate());
        List<MkShiftArrangementDTO> arrangementList = mkShiftArrangementService.queryList(mkShiftArrangementParam);

        if (CollectionUtils.isNotEmpty(arrangementList)){
            MkShiftArrangementDTO dto = arrangementList.get(0);
            entity.setArrangementId(dto.getId());
        } else {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        entity.setCreatedDate(LocalDate.now());
        entity.setStatusCode(DicConstant.RECORD_APPROVE_STATUS_ONE);
        entity.setStatusName(dicService.transform(DicConstant.RECORD_APPROVE_STATUS, entity.getStatusCode()));
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkShiftArrangementRecordDTO getCurrentDetails(MkShiftArrangementRecordParam param) {
        return mkShiftArrangementRecordConvert.toDTO(super.getById(param.getId()));
    }

    /**
     * 审批
     *
     * @param param 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public Boolean handle(MkShiftArrangementRecordParam param) {
        if (Objects.isNull(param.getId()) || Objects.isNull(param.getStatusCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        MkShiftArrangementRecord entity = getById(param.getId());
        if (Objects.isNull(entity)) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        entity.setStatusCode(param.getStatusCode());
        entity.setStatusName(dicService.transform(DicConstant.RECORD_APPROVE_STATUS, entity.getStatusCode()));
        if (updateById(entity)) {
            if (entity.getStatusCode().equals(DicConstant.RECORD_APPROVE_STATUS_TWO)) {
                // 审批状态为 通过 修改排班表
                MkShiftArrangement arrangement = mkShiftArrangementService.getById(param.getArrangementId());
                if (Objects.isNull(arrangement)) {
                    throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
                }
                arrangement.setName(entity.getName());
                arrangement.setAccount(entity.getAccount());
                return mkShiftArrangementService.updateById(arrangement);
            }
            return true;
        } else {
            return false;
        }
    }
}
