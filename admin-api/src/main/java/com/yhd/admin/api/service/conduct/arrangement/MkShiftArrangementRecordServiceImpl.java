package com.yhd.admin.api.service.conduct.arrangement;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.configuration.FastDFSClient;
import com.yhd.admin.api.dao.conduct.arrangement.MkShiftArrangementRecordDao;
import com.yhd.admin.api.domain.conduct.convert.arrangement.MkShiftArrangementRecordConvert;
import com.yhd.admin.api.domain.conduct.dto.arrangement.MkShiftArrangementRecordDTO;
import com.yhd.admin.api.domain.conduct.entity.arrangement.MkShiftArrangement;
import com.yhd.admin.api.domain.conduct.entity.arrangement.MkShiftArrangementRecord;
import com.yhd.admin.api.domain.conduct.query.arrangement.MkShiftArrangementRecordParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.DicService;
import com.yhd.admin.api.service.sys.UserService;
import com.yhd.admin.common.constant.DicConstant;
import com.yhd.admin.common.domain.dto.UserDTO;
import com.yhd.admin.common.utils.excel.CellBuilder;
import com.yhd.admin.common.utils.excel.WorkbookBuilder;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 调班记录表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29 11:30:39
 */
@Service
public class MkShiftArrangementRecordServiceImpl extends ServiceImpl<MkShiftArrangementRecordDao, MkShiftArrangementRecord> implements MkShiftArrangementRecordService {

    private final MkShiftArrangementRecordConvert mkShiftArrangementRecordConvert;
    private final MkShiftArrangementService mkShiftArrangementService;
    private final DicService dicService;
    private final UserService userService;
    private final FastDFSClient fastDFSClient;

    public MkShiftArrangementRecordServiceImpl(MkShiftArrangementRecordConvert mkShiftArrangementRecordConvert, MkShiftArrangementService mkShiftArrangementService, DicService dicService, UserService userService, FastDFSClient fastDFSClient) {
        this.mkShiftArrangementRecordConvert = mkShiftArrangementRecordConvert;
        this.mkShiftArrangementService = mkShiftArrangementService;
        this.dicService = dicService;
        this.userService = userService;
        this.fastDFSClient = fastDFSClient;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkShiftArrangementRecordDTO> pagingQuery(MkShiftArrangementRecordParam param) {
        IPage<MkShiftArrangementRecord> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkShiftArrangementRecord> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        // 查询条件 年、月 查询当月调班记录
        if (Objects.nonNull(param.getYearMonth())) {
            YearMonth yearMonth = param.getYearMonth();
            LocalDate startDate = yearMonth.atDay(1);
            LocalDate endDate = yearMonth.atEndOfMonth();
            wrapper.ge(MkShiftArrangementRecord::getShiftDate, startDate);
            wrapper.le(MkShiftArrangementRecord::getShiftDate, endDate);
        }
        // 时间 账号
        wrapper.eq(StringUtils.isNotBlank(param.getAccount()), MkShiftArrangementRecord::getAccount, param.getAccount());
        wrapper.eq(Objects.nonNull(param.getShiftDate()), MkShiftArrangementRecord::getShiftDate, param.getShiftDate());
        // 审核状态
        wrapper.eq(StringUtils.isNotBlank(param.getStatusCode()), MkShiftArrangementRecord::getStatusCode, param.getStatusCode());
        // 班次类型
        wrapper.eq(StringUtils.isNotBlank(param.getShiftTypeName()), MkShiftArrangementRecord::getShiftTypeName,
            param.getShiftTypeName());
        //姓名
        if (StringUtils.isNotBlank(param.getName())) {
            wrapper.and(w -> w.eq(MkShiftArrangementRecord::getName, param.getName())
                .or().eq(MkShiftArrangementRecord::getPlanName, param.getName()));
        }
        return wrapper.page(page).convert(mkShiftArrangementRecordConvert::toDTO);
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkShiftArrangementRecordParam param) {
        // 获取排班表id
        LocalDate shiftDate = param.getShiftDate();
        String shiftTypeName = param.getShiftTypeName();
        String name = param.getName();
        String planAccount = param.getPlanAccount();
        MkShiftArrangementRecord entity = mkShiftArrangementRecordConvert.toEntity(param);
        param.setShiftDate(shiftDate);
        param.setShiftTypeName(shiftTypeName);
        LambdaQueryWrapper<MkShiftArrangement> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkShiftArrangement::getShiftDate, shiftDate);
        wrapper.eq(MkShiftArrangement::getShiftTypeName, shiftTypeName);
        List<MkShiftArrangement> arrangementList = mkShiftArrangementService.list(wrapper);

        if (CollectionUtils.isEmpty(arrangementList)) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        MkShiftArrangement dto = arrangementList.get(0);
        entity.setArrangementId(dto.getId());
        entity.setShiftDate(dto.getShiftDate());
        entity.setShiftTypeName(dto.getShiftTypeName());
        entity.setShiftTypeCode(dto.getShiftTypeCode());
        entity.setPlanName(userService.getUserByUsername(planAccount).getName());
        entity.setPlanAccount(planAccount);
        UserParam userParam = new UserParam();
        param.setName(name);
        List<UserDTO> userDTOList= userService.queryUsers(userParam);
        if (CollectionUtils.isEmpty(userDTOList)) {
            throw new BMSException("error", "调班人员不存在");
        }
        entity.setAccount(userDTOList.get(0).getUsername());
        entity.setCreatedDate(LocalDate.now());
        entity.setStatusCode(DicConstant.RECORD_APPROVE_STATUS_ONE);
        entity.setStatusName(dicService.transform(DicConstant.RECORD_APPROVE_STATUS, entity.getStatusCode()));
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            if (save(entity)) {
                String creator = userService.getUserByUsername(entity.getCreatedBy()).getName();
                entity.setCreator(creator);
                return updateById(entity);
            }
            return false;
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkShiftArrangementRecordDTO getCurrentDetails(MkShiftArrangementRecordParam param) {
        return mkShiftArrangementRecordConvert.toDTO(super.getById(param.getId()));
    }

    /**
     * 审批
     *
     * @param param 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public Boolean handle(MkShiftArrangementRecordParam param) {
        if (Objects.isNull(param.getId()) || Objects.isNull(param.getStatusCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        MkShiftArrangementRecord entity = getById(param.getId());
        if (Objects.isNull(entity)) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        entity.setStatusCode(param.getStatusCode());
        entity.setStatusName(dicService.transform(DicConstant.RECORD_APPROVE_STATUS, entity.getStatusCode()));
        if (updateById(entity)) {
            if (entity.getStatusCode().equals(DicConstant.RECORD_APPROVE_STATUS_TWO)) {
                // 审批状态为 通过 修改排班表
                MkShiftArrangement arrangement = mkShiftArrangementService.getById(param.getArrangementId());
                if (Objects.isNull(arrangement)) {
                    throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
                }
                arrangement.setName(entity.getName());
                arrangement.setAccount(entity.getAccount());
                if (!arrangement.getAccount().equals(arrangement.getPlanAccount())) {
                    arrangement.setPlanFlag(1);
                }
                return mkShiftArrangementService.updateById(arrangement);
            }
            return true;
        } else {
            return false;
        }
    }

    @Override
    public String export(MkShiftArrangementRecordParam param) {
        LambdaQueryWrapper<MkShiftArrangementRecord> wrapper = new LambdaQueryWrapper<>();
        List<MkShiftArrangementRecord> list = baseMapper.selectList(wrapper);

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String fileName = "调班审核记录-" + timestamp + ".xlsx";

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 读取模板文件
            File tplFile = ResourceUtils.getFile("classpath:template" + File.separator + "调班审核导出模板.xlsx");
            Workbook workbook = WorkbookBuilder.getWorkBook(tplFile);
            Sheet sheet = workbook.getSheetAt(0);
            // 跳过标题行
            int rowNum = 1;
            CellStyle contentStyle = createContentStyle(workbook);

            for (MkShiftArrangementRecord record : list) {
                Row row = sheet.createRow(rowNum++);
                CellBuilder.build(row, 0, record.getShiftDate(), contentStyle);
                CellBuilder.build(row, 1, record.getShiftTypeName(), contentStyle);
                CellBuilder.build(row, 2, record.getPlanName(), contentStyle);
                CellBuilder.build(row, 3, record.getName(), contentStyle);
                CellBuilder.build(row, 4, record.getCreator(), contentStyle);
                CellBuilder.build(row, 5, record.getCreatedDate(), contentStyle);
                CellBuilder.build(row, 6, StringUtils.isNotBlank(record.getRemark()) ? record.getRemark() : "", contentStyle);
                CellBuilder.build(row, 7, record.getStatusName(), contentStyle);
            }
            workbook.write(outputStream);

            // 上传到文件服务器
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            // 执行上传
            return fastDFSClient.getFullPath(fastDFSClient.uploadFile(inputStream, fileName));

        } catch (Exception e) {
            throw new BMSException(ExceptionEnum.EXPORT_ERROR);
        }
    }

    private CellStyle createContentStyle(Workbook workbook) {
        CellStyle contentStyle = workbook.createCellStyle();
        DataFormat format = workbook.createDataFormat();
        contentStyle.setDataFormat(format.getFormat("yyyy-mm-dd"));
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setAlignment(HorizontalAlignment.CENTER);
        contentStyle.setWrapText(true);
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);
        contentStyle.setBorderTop(BorderStyle.THIN);
        return contentStyle;
    }
}
