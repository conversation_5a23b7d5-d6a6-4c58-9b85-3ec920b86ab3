package com.yhd.admin.api.domain.emergency.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 应急快照VO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
public class MkEmergencySnapshotVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 快照名称
     */
    private String snapshotName;

    /**
     * 快照类型
     */
    private String snapshotType;

    /**
     * 矿井ID
     */
    private Long mineId;

    /**
     * 矿井名称
     */
    private String mineName;

    /**
     * 快照时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime snapshotTime;

    /**
     * 设置时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime setTime;

    /**
     * 回溯时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime backtrackTime;

    /**
     * 快照状态(ACTIVE:有效,INACTIVE:无效)
     */
    private String snapshotStatus;

    /**
     * 快照描述
     */
    private String description;

    /**
     * 快照文件路径
     */
    private String filePath;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;


}
