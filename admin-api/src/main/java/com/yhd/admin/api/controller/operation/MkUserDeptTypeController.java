package com.yhd.admin.api.controller.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.operation.convert.MkUserDeptTypeConvert;
import com.yhd.admin.api.domain.operation.dto.MkUserDeptTypeDTO;
import com.yhd.admin.api.domain.operation.query.MkUserDeptTypeParam;
import com.yhd.admin.api.domain.operation.vo.MkUserDeptTypeVO;
import com.yhd.admin.api.service.operation.MkUserDeptTypeService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 运维配置中心-部门类型-控制层
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */
@RestController
@RequestMapping("/user/dept/type")
public class MkUserDeptTypeController {
    @Resource
    private MkUserDeptTypeConvert convert;

    @Resource
    private MkUserDeptTypeService service;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkUserDeptTypeVO> pagingQuery(@RequestBody MkUserDeptTypeParam queryParam) {
        IPage<MkUserDeptTypeDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MkUserDeptTypeParam param) {
        return RespJson.success(convert.toVO(service.getCurrentDetail(param)));
    }

    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson add(@RequestBody MkUserDeptTypeParam param) {
        Boolean retVal = service.add(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(
        value = "/modify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody MkUserDeptTypeParam param) {
        Boolean retVal = service.modify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(
        value = "/getList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson getList(@RequestBody MkUserDeptTypeParam param) {
        return  RespJson.success(convert.toVOList(service.getList(param)));
    }

    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam param) {
        Boolean retVal = service.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }
}
