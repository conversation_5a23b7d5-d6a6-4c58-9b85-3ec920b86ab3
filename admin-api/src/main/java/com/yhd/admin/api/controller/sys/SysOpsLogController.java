package com.yhd.admin.api.controller.sys;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.sys.convert.SysOpsLogConvert;
import com.yhd.admin.api.domain.sys.dto.SysOpsLogDTO;
import com.yhd.admin.api.domain.sys.query.SysOpsLogParam;
import com.yhd.admin.api.domain.sys.vo.SysOpsLogVO;
import com.yhd.admin.api.service.sys.SysOpsLogService;
import com.yhd.admin.common.domain.PageRespJson;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/log")
public class SysOpsLogController {

    @Resource
    private SysOpsLogConvert convert;

    @Resource
    private SysOpsLogService sysOpsLogService;

    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<SysOpsLogVO> pagingQuery(@RequestBody SysOpsLogParam queryParam) {
        IPage<SysOpsLogDTO> iPage = sysOpsLogService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

}
