package com.yhd.admin.api.domain.operation.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 生产录入-主要工作录入表批量操作参数
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@Data
public class MkOutputBatchParam implements Serializable {

    /**
     * 值班人员
     */
    private List<MkOutputDutyParam> dutyList;

    /**
     * 主要工作录入
     */
    private List<MkOutputWorkParam> workList;

    /**
     * 产量信息
     */
    private List<MkOutputInfoParam> infoList;

    /**
     * 产量销售库存
     */
    private List<MkOutputSaleInfoParam> saleInfoList;

}
