package com.yhd.admin.api.service.monitor.ventilation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkMotorInfoDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkMotorInfo;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkMotorInfoCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.page.TbMkMotorInfoPageVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkMotorInfoUpdateVO;

import java.util.List;

/**
 * 电机基本信息 服务类
 *
 * <AUTHOR>
 * @since
 */
public interface TbMkMotorInfoService  extends IService<TbMkMotorInfo> {

    /**
     * 新建
     *
     * @param createVO 新建VO
     * @return id
     */
    Long create(TbMkMotorInfoCreateVO createVO);

    /**
     * 更新
     *
     * @param updateVO 更新VO
     */
    int update(TbMkMotorInfoUpdateVO updateVO);

    /**
     * 删除
     *
     * @param id id
     */
    int delete(Long id);

    /**
     * 查询单个
     *
     * @param id id
     * @return po
     */
    TbMkMotorInfo get(Long id);

    /**
     * 分页查询
     *
     * @param pageVO 分页VO
     * @return pageResult
     */
    IPage<TbMkMotorInfoDTO> page(TbMkMotorInfoPageVO pageVO);

    /**
      * 批量删除
      *
      * @param ids 要删除的记录ID列表
      * @return 受影响的行数
      */
    int batchDelete(List<Long> ids);

    /**
     * 批量新增
     *
     * @param createVOs 新增VO列表
     * @return 新增的记录ID列表
     */
    int batchCreate(List<TbMkMotorInfoCreateVO> createVOs);

    /**
     * 批量更新
     *
     * @param updateVOs 更新VO列表
     * @return 受影响的行数
     */
    int batchUpdate(List<TbMkMotorInfoUpdateVO> updateVOs);
}
