package com.yhd.admin.api.domain.monitor.fan.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class TbMkFanSystemDetailDTO {
    /**
     * 风机Id
     */
    private Long fanId;
    /**
     * 风机编号（如：FAN-01）
     */
    private String fanNumber;

    /**
     * 风机风量(m³/min)
     */
    private BigDecimal airVolume;

    /**
     * 垂直振动(mm/s)
     */
    private BigDecimal verticalVibration;

    /**
     * 水平振动(mm/s)
     */
    private BigDecimal horizontalVibration;

    /**
     * 瓦斯浓度T1(%)
     */
    private BigDecimal gasConcentrationT1;

    /**
     * 瓦斯浓度T2(%)
     */
    private BigDecimal gasConcentrationT2;

    /**
     * 瓦斯浓度T3(%)
     */
    private BigDecimal gasConcentrationT3;

    /**
     * I级电机电压(V)
     */
    private BigDecimal stageIMotorVoltage;

    /**
     * I级电机电流(A)
     */
    private BigDecimal stageIMotorCurrent;

    /**
     * I级前机电流(A)
     */
    private BigDecimal stageIFrontMotorCurrent;

    /**
     * I级后机电流(A)
     */
    private BigDecimal stageIRearMotorCurrent;

    /**
     * 前级前轴温度(°C)
     */
    private BigDecimal stageIFrontBearingTemperature;

    /**
     * 前级后轴温度(°C)
     */
    private BigDecimal stageIRearBearingTemperature;

    /**
     * 前级绕组温度(°C)
     */
    private BigDecimal stageIWindingTemperature;

    /**
     * 后级前轴温度(°C)
     */
    private BigDecimal stageIiFrontBearingTemperature;

    /**
     * 后级后轴温度(°C)
     */
    private BigDecimal stageIiRearBearingTemperature;

    /**
     * 后级绕组温度(°C)
     */
    private BigDecimal stageIiWindingTemperature;

}
