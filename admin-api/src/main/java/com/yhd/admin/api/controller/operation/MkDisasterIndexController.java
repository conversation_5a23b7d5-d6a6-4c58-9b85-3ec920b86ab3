package com.yhd.admin.api.controller.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.operation.convert.MkDisasterIndexConvert;
import com.yhd.admin.api.domain.operation.dto.MkDisasterIndexDTO;
import com.yhd.admin.api.domain.operation.query.MkDisasterIndexParam;
import com.yhd.admin.api.domain.operation.vo.MkDisasterIndexVO;
import com.yhd.admin.api.service.operation.MkDisasterIndexService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 算法模型-自然灾害评价指标表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@RestController
@RequestMapping("/disaster/index")
public class MkDisasterIndexController {
    @Resource
    private MkDisasterIndexConvert convert;

    @Resource
    private MkDisasterIndexService service;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkDisasterIndexVO> pagingQuery(@RequestBody MkDisasterIndexParam queryParam) {
        IPage<MkDisasterIndexDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MkDisasterIndexParam param) {
        return RespJson.success(convert.toVO(service.getCurrentDetail(param)));
    }

    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson add(@RequestBody MkDisasterIndexParam param) {
        Boolean retVal = service.add(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(
        value = "/modify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody MkDisasterIndexParam param) {
        Boolean retVal = service.modify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam param) {
        Boolean retVal = service.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }
}
