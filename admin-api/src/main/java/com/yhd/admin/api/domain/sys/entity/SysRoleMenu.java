package com.yhd.admin.api.domain.sys.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName SysRoleMenu.java @Description TODO
 * @createTime 2020年03月30日 14:49:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysRoleMenu extends BaseEntity implements Serializable, Cloneable {
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 角色ID */
    private Long roleId;
    /** 菜单ID */
    private Long menuId;
}
