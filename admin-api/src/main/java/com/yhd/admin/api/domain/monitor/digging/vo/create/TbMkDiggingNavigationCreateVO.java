package com.yhd.admin.api.domain.monitor.digging.vo.create;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 掘进机导航参数
 *
 * <AUTHOR>
 * @since
 */
@Data
public class TbMkDiggingNavigationCreateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联掘进机基本信息表ID
     */
    private Long diggingId;

    /**
     * 俯仰角(°)
     */
    private BigDecimal pitchAngle;

    /**
     * 翻滚角(°)
     */
    private BigDecimal rollAngle;

    /**
     * 航向角(°)
     */
    private BigDecimal yawAngle;

}
