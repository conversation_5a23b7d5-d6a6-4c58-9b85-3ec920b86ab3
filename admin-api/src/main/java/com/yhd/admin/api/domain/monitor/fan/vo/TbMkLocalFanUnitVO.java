package com.yhd.admin.api.domain.monitor.fan.vo;

import com.yhd.admin.api.domain.monitor.fan.vo.create.TbMkLocalFanUnitCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 局扇风机单元
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkLocalFanUnitVO extends TbMkLocalFanUnitCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
