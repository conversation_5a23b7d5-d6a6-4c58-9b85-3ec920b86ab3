package com.yhd.admin.api.advice;

import java.nio.file.AccessDeniedException;
import java.time.LocalDateTime;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.common.domain.RespJson;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/2/26 15:18
 */
@ControllerAdvice
@Slf4j
public class ExceptionControllerAdvice {

    private final String prefix = ">>";

    @ExceptionHandler(value = BMSException.class)
    @ResponseBody
    public ResponseEntity<RespJson<String>> exceptionHandler(BMSException e) {

        if (log.isErrorEnabled()) {
            log.error("{}", e.getCause());
        }
        return ResponseEntity.ok().header("Last-Modified-Time", String.valueOf(LocalDateTime.now().getSecond()))
            .body(RespJson.failure(e.getDesc()));
    }

    @ExceptionHandler(value = AccessDeniedException.class)
    public void accessDeniedException(AccessDeniedException e) throws AccessDeniedException {
        throw e;
    }

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public ResponseEntity<RespJson<String>> exceptionHandler(Exception e) {

        if (log.isErrorEnabled()) {
            log.debug(prefix + "{}", e.getCause());
        }
        return ResponseEntity.ok().header("Last-Modified-Time", String.valueOf(LocalDateTime.now().getSecond()))
            .body(RespJson.failure(e.getMessage()));
    }
}
