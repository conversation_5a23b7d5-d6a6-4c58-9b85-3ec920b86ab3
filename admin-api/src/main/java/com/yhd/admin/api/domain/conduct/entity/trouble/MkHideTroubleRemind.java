package com.yhd.admin.api.domain.conduct.entity.trouble;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 隐患催办记录表(MkHideTroubleRemind)实体类
 *
 * <AUTHOR>
 * @since 2025-08-04 10:28:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkHideTroubleRemind extends BaseEntity implements Serializable {

    /** id */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 隐患id */
    private Long troubleId;
    /** 催办描述 */
    private String remindDesc;
    /** 检查班次名称 */
    private String shiftName;
    /** 检查班次代码 */
    private String shiftCode;
}

