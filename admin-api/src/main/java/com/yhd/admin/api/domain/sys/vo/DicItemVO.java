package com.yhd.admin.api.domain.sys.vo;

import java.io.Serializable;

import com.yhd.admin.common.domain.vo.BaseVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName SysDicItemDTO.java @Description 字典表项
 * @createTime 2020年05月20日 14:15:00
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class DicItemVO extends BaseVO implements Serializable, Cloneable {
    private String id;
    /** 字典表主键 */
    private String dicId;
    /** 字典分类 */
    private String category;
    /** 编码 */
    private String code;
    /** 编码值 */
    private String val;
    /** 状态 */
    private Boolean status;

    /** 排序 */
    private Long orderNum;
}
