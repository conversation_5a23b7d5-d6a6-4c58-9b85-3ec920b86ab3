package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.sys.OrgDao;
import com.yhd.admin.api.domain.sys.convert.OrgConvert;
import com.yhd.admin.api.domain.sys.dto.OrgDTO;
import com.yhd.admin.api.domain.sys.entity.SysOrg;
import com.yhd.admin.api.domain.sys.query.OrgParam;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/1/9 10:54
 */
@Service
public class OrgServiceImpl extends ServiceImpl<OrgDao, SysOrg> implements OrgService {

    private final OrgConvert orgConvert;

    public OrgServiceImpl(OrgConvert orgConvert) {
        this.orgConvert = orgConvert;
    }

    @Override
    public List<OrgDTO> queryList(Long id) {
        List<SysOrg> orgAll = baseMapper.selectChildren(id);
        List<OrgDTO> orgDTOList = orgAll.stream().map(orgConvert::toDTO)
            .sorted(Comparator.comparing(OrgDTO::getSortNum)).collect(Collectors.toList());

        Map<Long, List<OrgDTO>> allChildren = orgDTOList.stream().sorted(Comparator.comparing(OrgDTO::getSortNum))
            .collect(Collectors.groupingBy(OrgDTO::getParentId));
        orgDTOList.forEach(o -> o.setChildren(allChildren.get(o.getId())));
        return orgDTOList.stream().filter(o -> o.getId().equals(id)).collect(Collectors.toList());
    }

    @Override
    public Boolean addOrModifyOrg(OrgParam param) {

        return saveOrUpdate(orgConvert.toEntity(param));
    }

    @Override
    public Boolean removeBatch(List<Long> ids) {
        return removeByIds(ids);
    }

    @Override
    public OrgDTO currentDetail(Long id) {
        return orgConvert.toDTO(getById(id));
    }

    @Override
    public List<OrgDTO> listParentOrgById(Long id) {
        return baseMapper.selectParent(id).stream().map(orgConvert::toDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrgDTO> listChildrenOrgById(Long id) {
        return baseMapper.selectChildren(id).stream().map(orgConvert::toDTO).collect(Collectors.toList());
    }

    @Override
    public String joinOrgName(Long id) {
        List<SysOrg> parents = baseMapper.selectParent(id);
        return parents.stream().map(SysOrg::getOrgName).collect(Collectors.joining(">"));
    }
}
