package com.yhd.admin.api.controller.emergency;

import com.yhd.admin.api.domain.emergency.query.MkComprehensiveDecisionParam;
import com.yhd.admin.api.domain.emergency.vo.MkComprehensiveDecisionVO;
import com.yhd.admin.api.service.emergency.MkComprehensiveDecisionService;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 综合决策大屏 Controller
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/emergency/comprehensive-decision")
public class MkComprehensiveDecisionController {

    @Resource
    private MkComprehensiveDecisionService comprehensiveDecisionService;

    /**
     * 获取综合决策大屏数据
     *
     * @param param 查询参数
     * @return 综合决策大屏数据
     */
    @PostMapping(value = "/getDashboardData", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkComprehensiveDecisionVO> getDashboardData(
            @RequestBody(required = false) MkComprehensiveDecisionParam param) {
        log.info("获取综合决策大屏数据，参数：{}", param);

        if (param == null) {
            param = new MkComprehensiveDecisionParam();
        }

        MkComprehensiveDecisionVO result = comprehensiveDecisionService.getDashboardData(param);
        return RespJson.success(result);
    }
}