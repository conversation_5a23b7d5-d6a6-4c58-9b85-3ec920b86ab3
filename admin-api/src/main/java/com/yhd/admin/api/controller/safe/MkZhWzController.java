package com.yhd.admin.api.controller.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.safe.dto.MkZhWzDTO;
import com.yhd.admin.api.domain.safe.query.MkZhWzParam;
import com.yhd.admin.api.service.safe.MkZhWzService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/open/safe/wz")
public class MkZhWzController {
    private final MkZhWzService service;

    public MkZhWzController(MkZhWzService service) {
        this.service = service;
    }


    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkZhWzDTO> pagingQuery(@RequestBody MkZhWzParam queryParam) {
        IPage<MkZhWzDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage);
    }


    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkZhWzDTO> getCurrentDetail(@RequestBody MkZhWzParam param) {
        try {
            return RespJson.success(service.getCurrentDetail(param));
        } catch (Exception e) {
            return RespJson.failure(e.toString());
        }
    }

    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> addOrModify(@RequestBody MkZhWzParam param) {
        try {
            Boolean retVal = service.addOrModify(param);
            return RespJson.success(retVal);
        } catch (Exception e) {
            return RespJson.failure(e.toString());
        }
    }


    @PostMapping(
        value = "/getData",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkZhWzDTO> getData() {
        try {
            return RespJson.success(service.getData());
        } catch (Exception e) {
            return RespJson.failure(e.toString());
        }
    }
}
