package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.safe.MkZhDbDao;
import com.yhd.admin.api.domain.safe.convert.MkZhDbConvert;
import com.yhd.admin.api.domain.safe.dto.MkZhDbDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhDb;
import com.yhd.admin.api.domain.safe.query.MkZhDbParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;


@Service
public class MkZhDbServiceImpl extends ServiceImpl<MkZhDbDao, MkZhDb> implements MkZhDbService {

    private final MkZhDbConvert convert;

    public MkZhDbServiceImpl(MkZhDbConvert convert) {
        this.convert = convert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addOrModify(MkZhDbParam param) {
        MkZhDb entity = convert.toEntity(param);
        return super.saveOrUpdate(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(MkZhDbParam param) {
        return super.removeById(param.getId());
    }

    @Override
    public MkZhDbDTO getCurrentDetail(MkZhDbParam param) {
        MkZhDbDTO result;
        if (Objects.isNull(param.getId())) {
            throw new BMSException(ExceptionEnum.MISS_PARAM_EXCEPTION);
        }
        MkZhDb entity = super.getById(param);
        result = convert.toDTO(entity);
        return result;
    }

    @Override
    public IPage<MkZhDbDTO> pagingQuery(MkZhDbParam param) {
        IPage<MkZhDb> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkZhDb> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        // 排序
        wrapper.orderByAsc(MkZhDb::getUpdatedTime);
        IPage<MkZhDb> iPage = wrapper.page(page);
        return iPage.convert(convert::toDTO);
    }

    @Override
    public MkZhDbDTO getData() {
        LambdaQueryChainWrapper<MkZhDb> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return convert.toDTO(wrapper.list().get(0));
    }
}
