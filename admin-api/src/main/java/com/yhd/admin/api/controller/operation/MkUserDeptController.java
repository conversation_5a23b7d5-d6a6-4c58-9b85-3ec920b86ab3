package com.yhd.admin.api.controller.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.operation.convert.MkUserDeptConvert;
import com.yhd.admin.api.domain.operation.dto.MkUserDeptDTO;
import com.yhd.admin.api.domain.operation.query.MkUserDeptParam;
import com.yhd.admin.api.domain.operation.vo.MkUserDeptVO;
import com.yhd.admin.api.service.operation.MkUserDeptService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 运维配置中心-部门管理-控制层
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */
@RestController
@RequestMapping("/user/dept")
public class MkUserDeptController {
    @Resource
    private MkUserDeptConvert convert;

    @Resource
    private MkUserDeptService service;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkUserDeptVO> pagingQuery(@RequestBody MkUserDeptParam queryParam) {
        IPage<MkUserDeptDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MkUserDeptParam param) {
        return RespJson.success(convert.toVO(service.getCurrentDetail(param)));
    }

    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson addOrModify(@RequestBody MkUserDeptParam param) {
        Boolean retVal = service.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(
        value = "/getDeptList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson getDeptList(@RequestBody MkUserDeptParam param) {
        return  RespJson.success(convert.toVOList(service.getDeptList(param)));
    }

    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam param) {
        Boolean retVal = service.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }
}
