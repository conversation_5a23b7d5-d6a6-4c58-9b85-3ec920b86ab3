package com.yhd.admin.api.service.monitor.digging.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.digging.TbMkDiggingOperationDao;
import com.yhd.admin.api.domain.monitor.digging.convert.TbMkDiggingOperationConvert;
import com.yhd.admin.api.domain.monitor.digging.dto.TbMkDiggingOperationDTO;
import com.yhd.admin.api.domain.monitor.digging.entity.TbMkDiggingOperation;
import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingOperationCreateVO;
import com.yhd.admin.api.domain.monitor.digging.vo.page.TbMkDiggingOperationPageVO;
import com.yhd.admin.api.domain.monitor.digging.vo.update.TbMkDiggingOperationUpdateVO;
import com.yhd.admin.api.service.monitor.digging.TbMkDiggingOperationService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 掘进机运行参数 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkDiggingOperationServiceImpl extends ServiceImpl<TbMkDiggingOperationDao, TbMkDiggingOperation> implements TbMkDiggingOperationService {

    @Resource
    private TbMkDiggingOperationConvert tbMkDiggingOperationConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkDiggingOperationCreateVO createVO) {
        TbMkDiggingOperation tbMkDiggingOperation = tbMkDiggingOperationConvert.convert(createVO);
        baseMapper.insert(tbMkDiggingOperation);
        return tbMkDiggingOperation.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkDiggingOperationUpdateVO updateVO) {
        TbMkDiggingOperation tbMkDiggingOperation = tbMkDiggingOperationConvert.convert(updateVO);
        return baseMapper.updateById(tbMkDiggingOperation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkDiggingOperation get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkDiggingOperationDTO> page(TbMkDiggingOperationPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkDiggingOperation> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkDiggingOperation> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.eq(param.getDiggingId() != null, TbMkDiggingOperation::getDiggingId, param.getDiggingId());
        queryChainWrapper.eq(param.getOilPumpRpm() != null, TbMkDiggingOperation::getOilPumpRpm, param.getOilPumpRpm());
        queryChainWrapper.orderByDesc(TbMkDiggingOperation::getId);
        return queryChainWrapper.page(iPage).convert(tbMkDiggingOperationConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkDiggingOperationCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 掘进机运行参数：传入的列表为空");
            return 0;
        }
        List<TbMkDiggingOperation> entities = createVOs.stream()
                .map(tbMkDiggingOperationConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 掘进机运行参数 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkDiggingOperationUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 掘进机运行参数：传入的列表为空");
            return 0;
        }
        List<TbMkDiggingOperation> entities = updateVOs.stream()
                .map(tbMkDiggingOperationConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 掘进机运行参数：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 掘进机运行参数 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
