package com.yhd.admin.api.domain.conduct.entity.transfer;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 收发文部门表(MkTransferOrg)实体类
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkTransferOrg extends BaseEntity implements Serializable {

    /** 收发文部门id */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 组织机构编码 */
    private String orgCode;
    /** 收发文部门名称 */
    private String orgName;
    /** 收发文部门简称 */
    private String orgSubName;
}

