package com.yhd.admin.api.domain.conduct.entity.coalInspect;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 煤质看板环比曲线
 * <AUTHOR>
 */
@Data
public class MkCoalCurve {

    /** 采样日期 */
    private LocalDate sampleDate;
    /** 发热量 */
    private BigDecimal heatValue;
    /** 发热量环比 */
    private BigDecimal heatRatio;
    /** 硫分 */
    private BigDecimal sulfurValue;
    /** 硫分环比 */
    private BigDecimal sulfurRatio;
    /** 水分 */
    private BigDecimal moistureValue;
    /** 水分环比 */
    private BigDecimal moistureRatio;
    /** 灰分 */
    private BigDecimal ashValue;
    /** 灰分环比 */
    private BigDecimal ashRatio;

}
