package com.yhd.admin.api.service.sys;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName StorageServices.java @Description TODO 对象存储服务
 * @createTime 2020年04月23日 14:32:00
 */
public interface StorageServices {

    /**
     * 上传文件
     *
     * @param bucketName 分区
     * @param objectName 对象文件
     * @param stream 文件流
     * @param size 文件大小
     * @param contentType 文件类型 {@link org.springframework.http.MediaType}
     * @return void
     * @description TODO 上传文件
     * <AUTHOR>
     * @date 2020/4/23
     */
    void uploadObject(String bucketName, String objectName, InputStream stream, String contentType);

    /**
     * 根据存储对象获取访问地址
     *
     * @param bucketName 存储分区
     * @param objectName 存储对象
     * @return 访问地址。
     */
    String getStorageUrl(String bucketName, String objectName);
}
