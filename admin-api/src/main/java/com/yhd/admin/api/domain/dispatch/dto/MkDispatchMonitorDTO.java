package com.yhd.admin.api.domain.dispatch.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 智能调度中心-视频监控
 *
 * <AUTHOR>
 * @date 2025/7/31 09:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkDispatchMonitorDTO extends BaseDTO implements Serializable {

  private Long id;

  /** 视频监控数 */
  private Long monitorNum;

  /** 在线数 */
  private Long onlineNum;

  /** 离线数 */
  private Long offlineNum;

  /** 监控画面url */
  private List<String> monitorUrlList = new ArrayList<>();

  /** AI告警信息 */
  private List<String> aiWarnList = new ArrayList<>();

  /** 视频列表 */
  private List<MkDispatchMonitorStructureDTO> videoList = new ArrayList<>();

  /** 收藏列表 */
  private List<MkDispatchMonitorStructureDTO> collectAreaList = new ArrayList<>();
}
