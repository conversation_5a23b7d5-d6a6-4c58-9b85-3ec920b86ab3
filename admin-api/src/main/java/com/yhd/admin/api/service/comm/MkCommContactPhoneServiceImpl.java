package com.yhd.admin.api.service.comm;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.comm.MkCommContactPhoneDao;
import com.yhd.admin.api.domain.comm.entity.MkCommContactPhone;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 联系人电话-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2025/7/25 15:07
 */
@Service
public class MkCommContactPhoneServiceImpl
    extends ServiceImpl<MkCommContactPhoneDao, MkCommContactPhone>
    implements MkCommContactPhoneService {
  private static final Logger logger = LoggerFactory.getLogger(MkCommContactPhoneServiceImpl.class);
}
