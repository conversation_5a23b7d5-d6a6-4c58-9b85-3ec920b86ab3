package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.safe.MkZhWzDao;
import com.yhd.admin.api.domain.safe.convert.MkZhWzConvert;
import com.yhd.admin.api.domain.safe.dto.MkZhWzDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhWz;
import com.yhd.admin.api.domain.safe.query.MkZhWzParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;


@Service
public class MkZhWzServiceImpl extends ServiceImpl<MkZhWzDao, MkZhWz> implements MkZhWzService {

    private final MkZhWzConvert convert;

    public MkZhWzServiceImpl(MkZhWzConvert convert) {
        this.convert = convert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addOrModify(MkZhWzParam param) {
        MkZhWz entity = convert.toEntity(param);
        return super.saveOrUpdate(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(MkZhWzParam param) {
        return super.removeById(param.getId());
    }

    @Override
    public MkZhWzDTO getCurrentDetail(MkZhWzParam param) {
        MkZhWzDTO result;
        if (Objects.isNull(param.getId())) {
            throw new BMSException(ExceptionEnum.MISS_PARAM_EXCEPTION);
        }
        MkZhWz entity = super.getById(param);
        result = convert.toDTO(entity);
        return result;
    }

    @Override
    public IPage<MkZhWzDTO> pagingQuery(MkZhWzParam param) {
        IPage<MkZhWz> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkZhWz> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.like(StringUtils.isNotBlank(param.getWz()), MkZhWz::getWz, param.getWz());
        // 排序
        wrapper.orderByAsc(MkZhWz::getXh);
        IPage<MkZhWz> iPage = wrapper.page(page);
        return iPage.convert(convert::toDTO);
    }

    @Override
    public MkZhWzDTO getData() {
        LambdaQueryChainWrapper<MkZhWz> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return convert.toDTO(wrapper.list().get(0));
    }
}
