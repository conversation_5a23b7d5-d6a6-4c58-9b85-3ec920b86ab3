package com.yhd.admin.api.domain.operation.vo;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 消息策略表(MkNoticeStrategy)VO
 *
 * <AUTHOR>
 * @since 2025-08-08 09:12:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkNoticeStrategyVO extends BaseVO implements Serializable {
    /** id */
    private Integer id;
    /** 策略名称 */
    private String name;
    /** 策略编码 */
    private String code;
    /** 单位名称 */
    private String orgName;
    /** 单位代码 */
    private String orgCode;
    /** 策略状态：0-禁用，1-启用 */
    private String status;
    /** 策略描述 */
    private String description;
    /** 消息通道 */
    private String channel;
    /** 消息通道 */
    private Map<String, List<Map<String, Object>>> channels;
}

