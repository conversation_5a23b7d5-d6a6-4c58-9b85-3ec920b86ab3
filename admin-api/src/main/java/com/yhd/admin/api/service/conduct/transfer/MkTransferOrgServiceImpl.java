package com.yhd.admin.api.service.conduct.transfer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.configuration.FastDFSClient;
import com.yhd.admin.api.dao.conduct.transfer.MkTransferOrgDao;
import com.yhd.admin.api.domain.conduct.convert.transfer.MkTransferOrgConvert;
import com.yhd.admin.api.domain.conduct.dto.transfer.MkTransferOrgDTO;
import com.yhd.admin.api.domain.conduct.entity.transfer.MkTransferOrg;
import com.yhd.admin.api.domain.conduct.query.transfer.MkTransferOrgParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.common.utils.excel.CellBuilder;
import com.yhd.admin.common.utils.excel.WorkbookBuilder;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 收发文部门表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
@Service
public class MkTransferOrgServiceImpl extends ServiceImpl<MkTransferOrgDao, MkTransferOrg> implements MkTransferOrgService {

    public final MkTransferOrgConvert mkTransferOrgConvert;
    public final FastDFSClient fastDFSClient;

    public MkTransferOrgServiceImpl(MkTransferOrgConvert mkTransferOrgConvert, FastDFSClient fastDFSClient) {
        this.mkTransferOrgConvert = mkTransferOrgConvert;
        this.fastDFSClient = fastDFSClient;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkTransferOrgDTO> pagingQuery(MkTransferOrgParam param) {
        IPage<MkTransferOrg> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkTransferOrg> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return wrapper.page(page).convert(mkTransferOrgConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkTransferOrgDTO> queryList(MkTransferOrgParam param) {
        LambdaQueryWrapper<MkTransferOrg> wrapper = new LambdaQueryWrapper<>();
        return mkTransferOrgConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkTransferOrgParam param) {
        MkTransferOrg entity = mkTransferOrgConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkTransferOrgDTO getCurrentDetails(MkTransferOrgParam param) {
        return mkTransferOrgConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkTransferOrgParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }

    /**
     * 导出文件
     *
     * @return 文件地址
     */
    @Override
    public String export(MkTransferOrgParam param) {
        List<MkTransferOrgDTO> dtoList = queryList(param);

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String fileName = "收发部门导出-" + timestamp + ".xlsx";
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 读取模板文件
            File tplFile = ResourceUtils.getFile("classpath:template" + File.separator + "收发部门导出模板.xlsx");
            Workbook workbook = WorkbookBuilder.getWorkBook(tplFile);
            Sheet sheet = workbook.getSheetAt(0);
            // 跳过标题行
            int rowNum = 1;
            CellStyle contentStyle = createContentStyle(workbook);

            for (MkTransferOrgDTO dto : dtoList) {
                Row row = sheet.createRow(rowNum++);
                CellBuilder.build(row, 0, dto.getOrgName(), contentStyle);
                CellBuilder.build(row, 1, dto.getOrgSubName(), contentStyle);
            }
            workbook.write(outputStream);

            // 上传到文件服务器
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            // 执行上传
            return fastDFSClient.getFullPath(fastDFSClient.uploadFile(inputStream, fileName));

        } catch (Exception e) {
            throw new BMSException(ExceptionEnum.EXPORT_ERROR);
        }
    }

    private CellStyle createContentStyle(Workbook workbook) {
        CellStyle contentStyle = workbook.createCellStyle();
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setAlignment(HorizontalAlignment.CENTER);
        contentStyle.setWrapText(true);
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);
        contentStyle.setBorderTop(BorderStyle.THIN);
        return contentStyle;
    }
}
