package com.yhd.admin.api.service.conduct.transfer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.transfer.MkTransferOrgDao;
import com.yhd.admin.api.domain.conduct.convert.transfer.MkTransferOrgConvert;
import com.yhd.admin.api.domain.conduct.dto.transfer.MkTransferOrgDTO;
import com.yhd.admin.api.domain.conduct.entity.transfer.MkTransferOrg;
import com.yhd.admin.api.domain.conduct.query.transfer.MkTransferOrgParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 收发文部门表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
@Service
public class MkTransferOrgServiceImpl extends ServiceImpl<MkTransferOrgDao, MkTransferOrg> implements MkTransferOrgService {

    public final MkTransferOrgConvert mkTransferOrgConvert;

    public MkTransferOrgServiceImpl(MkTransferOrgConvert mkTransferOrgConvert) {
        this.mkTransferOrgConvert = mkTransferOrgConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkTransferOrgDTO> pagingQuery(MkTransferOrgParam param) {
        IPage<MkTransferOrg> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkTransferOrg> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return wrapper.page(page).convert(mkTransferOrgConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkTransferOrgDTO> queryList(MkTransferOrgParam param) {
        LambdaQueryWrapper<MkTransferOrg> wrapper = new LambdaQueryWrapper<>();
        return mkTransferOrgConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkTransferOrgParam param) {
        MkTransferOrg entity = mkTransferOrgConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkTransferOrgDTO getCurrentDetails(MkTransferOrgParam param) {
        return mkTransferOrgConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkTransferOrgParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }
}
