package com.yhd.admin.api.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/** 目前只用到了 trackerServer。 */
@Component
@ConfigurationProperties(prefix = "dfs")
@Data
public class FastDFSCfg {
    private String charset;
    private Long connectTimeout;
    private ConnectionPool connectionPool;
    private Http http;

    private Long networkTimeout;
    private String trackerServer;
    private String nginx;

    @Data
    class ConnectionPool {
        private Boolean enabled;
        private Long maxCountPerEntry;
        private Long maxIdleTime;
        private Long maxWaitTimeInMs;
    }

    @Data
    class Http {
        private String antiStealToken;
        private String secretKey;
        private Long trackerHttpPort;
    }
}
