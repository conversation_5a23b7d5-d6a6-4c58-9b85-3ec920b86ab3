package com.yhd.admin.api.controller.monitor.ventilation;

import com.yhd.admin.api.domain.monitor.ventilation.convert.TbMkVentilationOperationConvert;
import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkVentilationOperationDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkVentilationOperation;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkVentilationOperationCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.page.TbMkVentilationOperationPageVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkVentilationOperationUpdateVO;
import com.yhd.admin.api.service.monitor.ventilation.TbMkVentilationOperationService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通风机整体运行参数 Controller
 * <AUTHOR>
 * @since
 */
@Slf4j
@RestController
@RequestMapping("/monitor/ventilation/tbMkVentilationOperation")
public class TbMkVentilationOperationController {

    @Resource
    private TbMkVentilationOperationService tbMkVentilationOperationService;

    @Resource
    private TbMkVentilationOperationConvert tbMkVentilationOperationConvert;

    /**
     * 分页查询
     *
     * @param pageVO 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<TbMkVentilationOperationDTO> page(@RequestBody @Valid TbMkVentilationOperationPageVO pageVO) {
        log.debug("分页查询 通风机整体运行参数，参数：{}", pageVO);
        return new PageRespJson<>(tbMkVentilationOperationService.page(pageVO));
    }

    /**
     * 新增
     *
     * @param createVO 创建参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> create(@RequestBody @Valid TbMkVentilationOperationCreateVO createVO) {
        log.debug("新增 通风机整体运行参数，参数：{}", createVO);
        return RespJson.success(tbMkVentilationOperationService.create(createVO));
    }

    /**
     * 更新
     *
     * @param updateVO 更新参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> update(@RequestBody @Valid TbMkVentilationOperationUpdateVO updateVO) {
        log.debug("更新 通风机整体运行参数，参数：{}", updateVO);
        return RespJson.success(tbMkVentilationOperationService.update(updateVO));
    }


    /**
      * 删除
      *
      * @param id 要删除的记录ID
      * @return 操作结果
      */
    @DeleteMapping("/{id}")
    public RespJson<?> delete(@PathVariable Long id) {
        log.debug("删除 通风机整体运行参数，ID：{}", id);
        return RespJson.success(tbMkVentilationOperationService.delete(id));
    }

    /**
     * 查看详情
     *
     * @param id 要查看的记录ID
     * @return 详情信息
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<TbMkVentilationOperationDTO> getDetail(@PathVariable Long id) {
        log.debug("查看详情 通风机整体运行参数，ID：{}", id);
        TbMkVentilationOperation entity = tbMkVentilationOperationService.get(id);
        if (entity == null) {
            return RespJson.failure("未找到对应记录");
        }
        return RespJson.success(tbMkVentilationOperationConvert.toDTO(entity));
    }
    /**
     * 批量删除
     *
     * @param ids 要删除的记录ID列表
     * @return 操作结果
     */
    @PostMapping(value = "/batchDelete", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchDelete(@RequestBody List<Long> ids) {
        log.debug("批量删除 通风机整体运行参数，IDs：{}", ids);
        return RespJson.success(tbMkVentilationOperationService.batchDelete(ids));
    }

    /**
     * 批量新增
     *
     * @param createVOs 新增参数列表
     * @return 新增的记录ID列表
     */
    @PostMapping(value = "/batchAdd", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchCreate(@RequestBody @Valid List<TbMkVentilationOperationCreateVO> createVOs) {
        log.debug("批量新增 通风机整体运行参数，数量：{}", createVOs.size());
        return RespJson.success(tbMkVentilationOperationService.batchCreate(createVOs));
    }

    /**
     * 批量更新
     *
     * @param updateVOs 更新参数列表
     * @return 更新的记录数量
     */
    @PostMapping(value = "/batchModify", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> batchUpdate(@RequestBody @Valid List<TbMkVentilationOperationUpdateVO> updateVOs) {
        log.debug("批量更新 通风机整体运行参数，数量：{}", updateVOs.size());
        return RespJson.success(tbMkVentilationOperationService.batchUpdate(updateVOs));
    }
}
