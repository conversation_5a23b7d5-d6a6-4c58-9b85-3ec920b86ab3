package com.yhd.admin.api.controller.emergency;

import com.yhd.admin.api.domain.emergency.query.MkSafetyDecisionParam;
import com.yhd.admin.api.domain.emergency.vo.MkSafetyDecisionVO;
import com.yhd.admin.api.service.emergency.MkSafetyDecisionService;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 安全决策大屏 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/open/emergency/safety-decision")
public class MkSafetyDecisionController {

    @Resource
    private MkSafetyDecisionService safetyDecisionService;

    /**
     * 获取安全决策大屏数据
     *
     * @param param 查询参数
     * @return 安全决策大屏数据
     */
    @PostMapping(value = "/getDashboardData", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkSafetyDecisionVO> getDashboardData(
        @RequestBody(required = false) MkSafetyDecisionParam param) {
        log.info("获取安全决策大屏数据，参数：{}", param);

        if (param == null) {
            param = new MkSafetyDecisionParam();
        }

        MkSafetyDecisionVO result = safetyDecisionService.getSafetyDecisionData(param);
        return RespJson.success(result);
    }


}
