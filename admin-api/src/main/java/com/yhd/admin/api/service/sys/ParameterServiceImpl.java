package com.yhd.admin.api.service.sys;

import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.sys.ParameterDao;
import com.yhd.admin.api.domain.sys.convert.ParameterConvert;
import com.yhd.admin.api.domain.sys.dto.ParameterDTO;
import com.yhd.admin.api.domain.sys.entity.SysParameter;
import com.yhd.admin.api.domain.sys.query.ParameterParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName ParameterServiceImpl.java @Description TODO
 * @createTime 2020年05月12日 15:02:00
 */
@Service
public class ParameterServiceImpl extends ServiceImpl<ParameterDao, SysParameter> implements ParameterService {

    private final ParameterConvert convert;

    public ParameterServiceImpl(ParameterConvert convert) {
        this.convert = convert;
    }

    @Override
    public Boolean saveOrUpdate(ParameterParam addParam) {

        return this.saveOrUpdate(convert.toEntity(addParam));
    }

    @Override
    public Boolean modify(ParameterParam modifyParam) {
        return this.updateById(convert.toEntity(modifyParam));
    }

    @Override
    public Boolean removeBatch(BatchParam removeParam) {
        return this.removeByIds(removeParam.getId());
    }

    @Override
    public IPage<ParameterDTO> pagingQuery(ParameterParam queryParam) {
        IPage<SysParameter> iPage = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<SysParameter> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper
            .like(StringUtils.isNotBlank(queryParam.getParamKey()), SysParameter::getParamKey, queryParam.getParamKey())
            .like(StringUtils.isNotBlank(queryParam.getParamVal()), SysParameter::getParamVal, queryParam.getParamVal())
            .eq(queryParam.getIsEnable() != null, SysParameter::getIsEnable, queryParam.getIsEnable());
        return queryChainWrapper.page(iPage).convert(convert::toDTO);
    }

    @Override
    public ParameterDTO currentDetail(String id) {
        return convert.toDTO(this.getById(id));
    }

    @Override
    public Boolean validateIfNotExist(ParameterParam param) {
        LambdaQueryChainWrapper<SysParameter> validateChain = new LambdaQueryChainWrapper<>(baseMapper);
        validateChain.notIn(StringUtils.isNotBlank(param.getId()), SysParameter::getId, param.getId())
            .eq(SysParameter::getParamKey, param.getParamKey());
        return CollectionUtils.isEmpty(validateChain.list());
    }

    /**
     * 根据 KEY 查询参数值。
     *
     * @param key
     * @return
     */
    @Override
    public ParameterDTO getParameterByKey(String key) {
        ParameterDTO parameterDTO = convert.toDTO(
            baseMapper.selectOne(new QueryWrapper<SysParameter>().lambda().eq(SysParameter::getParamKey, key), false));
        return parameterDTO;
    }

    /**
     * 根据 KEY 查询参数值。
     *
     * @param key
     * @return
     */
    @Override
    public String getParameterStrByKey(String key) {
        ParameterDTO dto = getParameterByKey(key);
        return Optional.of(dto.getParamVal()).orElse("");
    }
}
