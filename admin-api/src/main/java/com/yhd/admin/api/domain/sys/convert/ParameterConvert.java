package com.yhd.admin.api.domain.sys.convert;

import org.mapstruct.Mapper;

import com.yhd.admin.api.domain.sys.dto.ParameterDTO;
import com.yhd.admin.api.domain.sys.entity.SysParameter;
import com.yhd.admin.api.domain.sys.query.ParameterParam;
import com.yhd.admin.api.domain.sys.vo.ParameterVO;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName ParameterConvert.java @Description TODO
 * @createTime 2020年05月12日 15:05:00
 */
@Mapper(componentModel = "spring")
public interface ParameterConvert {

    ParameterDTO toDTO(SysParameter parameter);

    ParameterDTO toDTO(ParameterParam parameter);

    SysParameter toEntity(ParameterDTO parameterDTO);

    SysParameter toEntity(ParameterParam parameterDTO);

    ParameterVO toVO(ParameterDTO parameterDTO);
}
