package com.yhd.admin.api.service.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.dispatch.MkDispatchMonitorDao;
import com.yhd.admin.api.domain.dispatch.convert.MkDispatchMonitorConvert;
import com.yhd.admin.api.domain.dispatch.dto.MkDispatchMonitorDTO;
import com.yhd.admin.api.domain.dispatch.dto.MkDispatchMonitorStructureDTO;
import com.yhd.admin.api.domain.dispatch.entity.MkDispatchMonitor;
import com.yhd.admin.api.domain.dispatch.query.MkDispatchMonitorParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.core.collection.CollectionUtil;
import com.yhd.json.JSONUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 智能调度中心-视频监控
 *
 * <AUTHOR>
 * @date 2025/7/31 09:34
 */
@Service
public class MkDispatchMonitorServiceImpl
    extends ServiceImpl<MkDispatchMonitorDao, MkDispatchMonitor>
    implements MkDispatchMonitorService {

  private final MkDispatchMonitorConvert convert;

  public MkDispatchMonitorServiceImpl(MkDispatchMonitorConvert convert) {
    this.convert = convert;
  }

  /**
   * 新增
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean addOrModify(MkDispatchMonitorParam param) {
    MkDispatchMonitor entity = convert.toEntity(param);
    return super.saveOrUpdate(entity);
  }

  /**
   * 删除-根据主键
   *
   * @param param id
   * @return true成功，false失败
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean remove(MkDispatchMonitorParam param) {
    Long count = this.lambdaQuery().count();
    if (count == 1) {
      throw new BMSException("error", "需要至少保留一条数据，不能全部删除，请检查！");
    }
    return super.removeById(param.getId());
  }

  /**
   * 查询详情信息
   *
   * @param param 主键id or 编号
   * @return 通讯详情信息
   */
  @Override
  public MkDispatchMonitorDTO getCurrentDetail(MkDispatchMonitorParam param) {
    MkDispatchMonitorDTO result = null;
    if (Objects.isNull(param.getId())) {
      throw new BMSException(ExceptionEnum.MISS_PARAM_EXCEPTION);
    }
    MkDispatchMonitor entity = super.getById(param);
    result = convert.toDTO(entity);
    return result;
  }

  /**
   * 根据条件查询通讯信息分页列表
   *
   * @param param 查询条件
   * @return 通讯信息分页列表
   */
  @Override
  public IPage<MkDispatchMonitorDTO> pagingQuery(MkDispatchMonitorParam param) {
    IPage<MkDispatchMonitor> page = new Page<>(param.getCurrent(), param.getPageSize());
    LambdaQueryChainWrapper<MkDispatchMonitor> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
    // 排序
    wrapper.orderByAsc(MkDispatchMonitor::getUpdatedTime);
    IPage<MkDispatchMonitor> iPage = wrapper.page(page);
    return iPage.convert(convert::toDTO);
  }

  @Override
  public MkDispatchMonitorDTO getData() {
    List<MkDispatchMonitor> list =
        this.lambdaQuery().orderByDesc(MkDispatchMonitor::getUpdatedTime).last("limit 1").list();
    MkDispatchMonitorDTO result = new MkDispatchMonitorDTO();
    if (CollectionUtil.isEmpty(list)) {
      return result;
    }
    MkDispatchMonitor entity = list.get(0);

    result = convert.toDTO(entity);

    // 监控画面
    if (StringUtils.isNotBlank(entity.getMonitorUrl())) {
      result.setMonitorUrlList(Arrays.asList(entity.getMonitorUrl().split(",")));
    }
    // AI告警信息
    if (StringUtils.isNotBlank(entity.getAiWarn())) {
      result.setAiWarnList(Arrays.asList(entity.getAiWarn().split(",")));
    }
    // 收藏列表
    if (StringUtils.isNotBlank(entity.getCollectArea())) {
      result.setCollectAreaList(
          JSONUtil.toList(entity.getCollectArea(), MkDispatchMonitorStructureDTO.class));
    }
    // 视频列表
    List<MkDispatchMonitorStructureDTO> videoList = new ArrayList<>();
    videoList.add(
        MkDispatchMonitorStructureDTO.builder()
            .name("2采区2煤辅运大巷1120米")
            .value(new BigDecimal("1"))
            .build());
    videoList.add(
        MkDispatchMonitorStructureDTO.builder()
            .name("3206运顺掘进工作面")
            .value(new BigDecimal("2"))
            .build());
    videoList.add(
        MkDispatchMonitorStructureDTO.builder()
            .name("3206辅运1870米避难硐室")
            .value(new BigDecimal("3"))
            .build());
    videoList.add(
        MkDispatchMonitorStructureDTO.builder().name("二采区变电所").value(new BigDecimal("4")).build());
    videoList.add(
        MkDispatchMonitorStructureDTO.builder()
            .name("2煤辅运大巷3380")
            .value(new BigDecimal("5"))
            .build());
    result.setVideoList(videoList);

    return result;
  }
}
