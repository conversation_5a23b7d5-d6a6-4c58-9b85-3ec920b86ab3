package com.yhd.admin.api.controller.conduct.meeting;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.meeting.MkMeetingRecordConvert;
import com.yhd.admin.api.domain.conduct.dto.meeting.MkMeetingRecordDTO;
import com.yhd.admin.api.domain.conduct.query.meeting.MkMeetingRecordParam;
import com.yhd.admin.api.domain.conduct.vo.meeting.MkMeetingRecordVO;
import com.yhd.admin.api.service.conduct.meeting.MkMeetingRecordService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 调度会议记录控制层
 *
 * <AUTHOR>
 * @since 2025-07-28 10:36:44
 */
@RestController
@RequestMapping("/conduct/meeting/record")
public class MkMeetingRecordController {

    public final MkMeetingRecordService mkMeetingRecordService;
    public final MkMeetingRecordConvert mkMeetingRecordConvert;

    public MkMeetingRecordController(
        MkMeetingRecordService mkMeetingRecordService,
        MkMeetingRecordConvert mkMeetingRecordConvert
    ) {
        this.mkMeetingRecordService = mkMeetingRecordService;
        this.mkMeetingRecordConvert = mkMeetingRecordConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkMeetingRecordParam param) {
        IPage<MkMeetingRecordDTO> page = mkMeetingRecordService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkMeetingRecordConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkMeetingRecordParam param) {
        return RespJson.success(mkMeetingRecordConvert.toVOList(mkMeetingRecordService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkMeetingRecordVO> getCurrentDetails(@RequestBody MkMeetingRecordParam param) {
        return RespJson.success(mkMeetingRecordConvert.toVO(mkMeetingRecordService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkMeetingRecordParam param) {
        Boolean retVal = mkMeetingRecordService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkMeetingRecordParam param) {
        Boolean retVal = mkMeetingRecordService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 导出文件
     *
     * @return 文件地址
     */
    @PostMapping(
        value = "/export",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<String> export() {
        String url = mkMeetingRecordService.export();
        return RespJson.success(url);
    }

}

