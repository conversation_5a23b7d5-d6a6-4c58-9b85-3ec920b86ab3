package com.yhd.admin.api.service.monitor.ventilation.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.ventilation.TbMkMotorInfoDao;
import com.yhd.admin.api.domain.monitor.ventilation.convert.TbMkMotorInfoConvert;
import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkMotorInfoDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkMotorInfo;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkMotorInfoCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.page.TbMkMotorInfoPageVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkMotorInfoUpdateVO;
import com.yhd.admin.api.service.monitor.ventilation.TbMkMotorInfoService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 电机基本信息 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkMotorInfoServiceImpl extends ServiceImpl<TbMkMotorInfoDao, TbMkMotorInfo> implements TbMkMotorInfoService {

    @Resource
    private TbMkMotorInfoConvert tbMkMotorInfoConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkMotorInfoCreateVO createVO) {
        TbMkMotorInfo tbMkMotorInfo = tbMkMotorInfoConvert.convert(createVO);
        baseMapper.insert(tbMkMotorInfo);
        return tbMkMotorInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkMotorInfoUpdateVO updateVO) {
        TbMkMotorInfo tbMkMotorInfo = tbMkMotorInfoConvert.convert(updateVO);
        return baseMapper.updateById(tbMkMotorInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkMotorInfo get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkMotorInfoDTO> page(TbMkMotorInfoPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkMotorInfo> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkMotorInfo> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.eq(param.getVentilationId() != null, TbMkMotorInfo::getVentilationId, param.getVentilationId());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getMotorName()), TbMkMotorInfo::getMotorName, param.getMotorName());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getMotorNumber()), TbMkMotorInfo::getMotorNumber, param.getMotorNumber());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getRemarks()), TbMkMotorInfo::getRemarks, param.getRemarks());
        queryChainWrapper.orderByDesc(TbMkMotorInfo::getId);
        return queryChainWrapper.page(iPage).convert(tbMkMotorInfoConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkMotorInfoCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 电机基本信息：传入的列表为空");
            return 0;
        }
        List<TbMkMotorInfo> entities = createVOs.stream()
                .map(tbMkMotorInfoConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 电机基本信息 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkMotorInfoUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 电机基本信息：传入的列表为空");
            return 0;
        }
        List<TbMkMotorInfo> entities = updateVOs.stream()
                .map(tbMkMotorInfoConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 电机基本信息：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 电机基本信息 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
