package com.yhd.admin.api.domain.monitor.nitrogen.vo.page;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 制氮系统参数表
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkNitrogenParametersPageVO extends QueryParam {

    private static final long serialVersionUID = 1L;



        /**
         * 关联制氮系统主表ID
         */
        private Long nitrogenSystemId;

        /**
         * 氮气压力(MPa)
         */
        private BigDecimal nitrogenPressure;

        /**
         * 氮气流量(Nm³/h)
         */
        private BigDecimal nitrogenFlowRate;

        /**
         * 氮气纯度(%)
         */
        private BigDecimal nitrogenPurity;
}
