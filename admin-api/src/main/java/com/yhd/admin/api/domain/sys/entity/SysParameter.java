package com.yhd.admin.api.domain.sys.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName SysParameter.java @Description TODO
 * @createTime 2020年05月12日 14:38:00
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class SysParameter extends BaseEntity implements Serializable, Cloneable {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /** 参数KEY */
    private String paramKey;
    /** 参数值 */
    private String paramVal;
    /** 状态;0否1是 */
    private Boolean isEnable;

    /** 备注 */
    private String remark;
}
