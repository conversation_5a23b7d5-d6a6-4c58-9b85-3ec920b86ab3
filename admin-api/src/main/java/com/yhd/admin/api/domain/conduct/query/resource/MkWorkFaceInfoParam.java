package com.yhd.admin.api.domain.conduct.query.resource;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 工作面信息(MkWorkFaceInfo)Param
 *
 * <AUTHOR>
 * @since 2025-07-28 09:25:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkWorkFaceInfoParam extends QueryParam implements Serializable {

    /** 主键列表ids */
    private List<Long> ids;
    /** id */
    private Long id;
    /** 工作面编码 */
    private String workFaceCode;
    /** 工作面名称 */
    private String workFaceName;
    /** 工作面类型 1：综采 2：掘进 */
    private String workFaceTypeName;
    /** 工作面类型 1：综采 2：掘进 */
    private String workFaceTypeCode;
    /** 工作面状态 1：待采 2：在采 3：停用 */
    private String statusName;
    /** 工作面状态 1：待采 2：在采 3：停用 */
    private String statusCode;
    /** 井田名称 */
    private String mineName;
    /** 矿井id*/
    private Long mineId;
    /** 所属煤层 名称 */
    private String coalInfoName;
    /** 所属煤层 id */
    private Long coalInfoId;
    /** 所属采区 名称 */
    private String miningAreaName;
    /** 所属采区 id */
    private Long miningAreaId;
    /** 走向长度 */
    private BigDecimal length;
    /** 倾向长度 */
    private BigDecimal width;
    /** 平均厚度 */
    private BigDecimal averageThickness;
    /** 总储量 */
    private BigDecimal totalReserves;
    /** 可采储量 */
    private BigDecimal validReserves;
    /** 设计采高 */
    private BigDecimal heightDesign;
    /** 硬度系数 */
    private BigDecimal hardness;
    /** 容量 */
    private BigDecimal capacity;
    /** 停采日期 */
    private LocalDate dateStop;
}

