package com.yhd.admin.api.domain.conduct.convert.base;

import com.yhd.admin.api.domain.conduct.dto.base.MkMineInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkMineInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkMineInfoParam;
import com.yhd.admin.api.domain.conduct.vo.base.MkMineInfoVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 矿井基础信息表(MkMineInfo)Convert
 *
 * <AUTHOR>
 * @since 2025-07-25 10:32:53
 */
@Mapper(componentModel = "spring")
public interface MkMineInfoConvert {
    MkMineInfo toEntity(MkMineInfoParam param);

    MkMineInfoDTO toDTO(MkMineInfo entity);

    MkMineInfoVO toVO(MkMineInfoDTO dto);

    List<MkMineInfoDTO> toDTOList(List<MkMineInfo> entityList);

    List<MkMineInfoVO> toVOList(List<MkMineInfoDTO> dtoList);
}

