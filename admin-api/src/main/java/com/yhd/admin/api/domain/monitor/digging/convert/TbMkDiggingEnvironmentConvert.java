package com.yhd.admin.api.domain.monitor.digging.convert;

import com.yhd.admin.api.domain.monitor.digging.dto.TbMkDiggingEnvironmentDTO;
import com.yhd.admin.api.domain.monitor.digging.entity.TbMkDiggingEnvironment;
import com.yhd.admin.api.domain.monitor.digging.vo.TbMkDiggingEnvironmentVO;
import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingEnvironmentCreateVO;
import com.yhd.admin.api.domain.monitor.digging.vo.update.TbMkDiggingEnvironmentUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 掘进机环境参数
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkDiggingEnvironmentConvert {

    @Mapping(target = "id", ignore = true)
    TbMkDiggingEnvironment convert(TbMkDiggingEnvironmentCreateVO createVO);

    TbMkDiggingEnvironment convert(TbMkDiggingEnvironmentUpdateVO updateVO);

    TbMkDiggingEnvironmentVO convert(TbMkDiggingEnvironment po);

    TbMkDiggingEnvironmentDTO toDTO(TbMkDiggingEnvironment po);



}
