package com.yhd.admin.api.service.conduct.coalInspect;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.configuration.FastDFSClient;
import com.yhd.admin.api.dao.conduct.coalInspect.MkCoalInspectReportDao;
import com.yhd.admin.api.domain.conduct.convert.coalInspect.MkCoalInspectReportConvert;
import com.yhd.admin.api.domain.conduct.dto.coalInspect.MkCoalInspectReportDTO;
import com.yhd.admin.api.domain.conduct.entity.coalInspect.*;
import com.yhd.admin.api.domain.conduct.query.coalInspect.MkCoalInspectReportParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.UserService;
import com.yhd.admin.common.utils.ConvertUtil;
import com.yhd.admin.common.utils.excel.CellBuilder;
import com.yhd.admin.common.utils.excel.WorkbookBuilder;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 煤质检验报告服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-05 09:12:06
 */
@Service
public class MkCoalInspectReportServiceImpl extends ServiceImpl<MkCoalInspectReportDao, MkCoalInspectReport> implements MkCoalInspectReportService {

    public final MkCoalInspectReportConvert mkCoalInspectReportConvert;
    public final UserService userService;
    public final FastDFSClient fastDFSClient;

    public MkCoalInspectReportServiceImpl(MkCoalInspectReportConvert mkCoalInspectReportConvert, UserService userService, FastDFSClient fastDFSClient) {
        this.mkCoalInspectReportConvert = mkCoalInspectReportConvert;
        this.userService = userService;
        this.fastDFSClient = fastDFSClient;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkCoalInspectReportDTO> pagingQuery(MkCoalInspectReportParam param) {
        IPage<MkCoalInspectReport> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkCoalInspectReport> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        if (Objects.nonNull(param.getStartDate()) && Objects.nonNull(param.getEndDate())) {
            wrapper.ge(MkCoalInspectReport::getCreatedDate, param.getStartDate());
            wrapper.le(MkCoalInspectReport::getCreatedDate, param.getEndDate());
        }
        wrapper.like(StringUtils.isNotBlank(param.getCoalTypeName()), MkCoalInspectReport::getCoalTypeName,
            param.getCoalTypeName());
        wrapper.like(StringUtils.isNotBlank(param.getSampleSite()), MkCoalInspectReport::getSampleSite, param.getSampleSite());
        return wrapper.page(page).convert(mkCoalInspectReportConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkCoalInspectReportDTO> queryList(MkCoalInspectReportParam param) {
        LambdaQueryWrapper<MkCoalInspectReport> wrapper = new LambdaQueryWrapper<>();
        return mkCoalInspectReportConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkCoalInspectReportParam param) {
        MkCoalInspectReport entity = mkCoalInspectReportConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            if (save(entity)) {
                entity = getById(entity.getId());
                String creator = userService.getUserByUsername(entity.getCreatedBy()).getName();
                LocalDate createdDate = LocalDate.from((entity.getCreatedTime()));
                entity.setCreator(creator);
                entity.setCreatedDate(createdDate);
                return updateById(entity);
            }
            return false;
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkCoalInspectReportDTO getCurrentDetails(MkCoalInspectReportParam param) {
        return mkCoalInspectReportConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkCoalInspectReportParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }

    /**
     * 导出文件
     *
     * @return 文件地址
     */
    @Override
    public String export(MkCoalInspectReportParam param) {
        LambdaQueryWrapper<MkCoalInspectReport> wrapper = new LambdaQueryWrapper<>();
        List<MkCoalInspectReport> entityList = baseMapper.selectList(wrapper);

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String fileName = "煤质检验记录导出-" + timestamp + ".xlsx";
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 读取模板文件
            File tplFile = ResourceUtils.getFile("classpath:template" + File.separator + "煤质检验记录导出模板.xlsx");
            Workbook workbook = WorkbookBuilder.getWorkBook(tplFile);
            Sheet sheet = workbook.getSheetAt(0);
            // 跳过标题行
            int rowNum = 1;
            CellStyle contentStyle = createContentStyle(workbook);
            CellStyle timeStyle = createContentStyle(workbook);
            DataFormat format = workbook.createDataFormat();
            timeStyle.setDataFormat(format.getFormat("HH:mm:ss"));
            CellStyle dateStyle = createContentStyle(workbook);
            dateStyle.setDataFormat(format.getFormat("yyyy-mm-dd"));

            for (MkCoalInspectReport entity : entityList) {
                Row row = sheet.createRow(rowNum++);
                CellBuilder.build(row, 0, entity.getSampleDate(), dateStyle);
                CellBuilder.build(row, 1, ConvertUtil.toStr(entity.getSampleTime(), ""), timeStyle);
                CellBuilder.build(row, 2, entity.getSampleSite(), contentStyle);
                CellBuilder.build(row, 3, entity.getCoalTypeName(), contentStyle);
                CellBuilder.build(row, 4, ConvertUtil.toStr(entity.getHeatValue(), ""), contentStyle);
                CellBuilder.build(row, 5, ConvertUtil.toStr(entity.getMoisture(), ""), contentStyle);
                CellBuilder.build(row, 6, ConvertUtil.toStr(entity.getAsh(), ""), contentStyle);
                CellBuilder.build(row, 7, ConvertUtil.toStr(entity.getVolatileWater(), ""), contentStyle);
                CellBuilder.build(row, 8, ConvertUtil.toStr(entity.getFixCarbon(), ""), contentStyle);
                CellBuilder.build(row, 9, ConvertUtil.toStr(entity.getSulfur(), ""), contentStyle);
                CellBuilder.build(row, 10, ConvertUtil.toStr(entity.getAnalyzeWater(), ""), contentStyle);
            }

            workbook.write(outputStream);

            // 上传到文件服务器
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            // 执行上传
            return fastDFSClient.getFullPath(fastDFSClient.uploadFile(inputStream, fileName));

        } catch (Exception e) {
            throw new BMSException(ExceptionEnum.EXPORT_ERROR);
        }
    }

    private CellStyle createContentStyle(Workbook workbook) {
        CellStyle contentStyle = workbook.createCellStyle();
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setAlignment(HorizontalAlignment.CENTER);
        contentStyle.setWrapText(true);
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);
        contentStyle.setBorderTop(BorderStyle.THIN);
        return contentStyle;
    }

    /**
     * 获取煤质数据
     * @param param 查询参数（包含煤种名称）
     * @return 煤质数据（包含近10天、近30天和近12个月数据曲线）
     */
    @Override
    public MkCoalData getData(MkCoalInspectReportParam param) {
        if (StringUtils.isBlank(param.getCoalTypeName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        MkCoalData result = new MkCoalData();
        LocalDate today = LocalDate.now();
        String coalTypeName = param.getCoalTypeName();

        // 生成近10天数据
        List<MkCoalCurve> tenDayCurve = generateDailyCurves(coalTypeName, today, 11, 10);

        // 生成近30天数据
        List<MkCoalCurve> thirtyDayCurve = generateDailyCurves(coalTypeName, today, 31, 30);

        // 生成近12个月数据
        List<MkCoalCurve> monthlyCurve = generateMonthlyCurves(coalTypeName, today);

        // 设置结果数据
        result.setTenDayCurve(tenDayCurve);
        result.setThirtyDayCurve(thirtyDayCurve);
        result.setMonthlyCurve(monthlyCurve);

        return result;
    }

    /**
     * 生成日度数据曲线
     * @param coalTypeName 煤种名称
     * @param today 今天日期
     * @param queryDays 查询天数
     * @param resultDays 结果天数
     * @return 数据曲线列表
     */
    private List<MkCoalCurve> generateDailyCurves(String coalTypeName, LocalDate today, int queryDays, int resultDays) {
        // 查询数据
        LambdaQueryWrapper<MkCoalInspectReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkCoalInspectReport::getCoalTypeName, coalTypeName);
        wrapper.le(MkCoalInspectReport::getSampleDate, today);
        wrapper.ge(MkCoalInspectReport::getSampleDate, today.minusDays(queryDays));
        wrapper.orderByDesc(MkCoalInspectReport::getSampleDate);
        List<MkCoalInspectReport> entityList = baseMapper.selectList(wrapper);

        // 构建日期到报告的映射
        Map<LocalDate, MkCoalInspectReport> reportMap = entityList.stream()
            .filter(report -> report.getSampleDate() != null)
            .collect(Collectors.toMap(MkCoalInspectReport::getSampleDate, Function.identity(), (a, b) -> a));

        // 生成数据曲线
        List<MkCoalCurve> curves = new ArrayList<>();
        for (int i = 0; i < resultDays; i++) {
            LocalDate currentDate = today.minusDays(i);
            LocalDate previousDate = currentDate.minusDays(1);

            MkCoalCurve curve = createEmptyCoalCurve(currentDate);

            // 设置当前日期数据
            MkCoalInspectReport currentReport = reportMap.get(currentDate);
            setCurveValues(curve, currentReport);

            // 设置环比值
            MkCoalInspectReport previousReport = reportMap.get(previousDate);
            setCurveRatios(curve, previousReport);

            curves.add(curve);
        }

        return curves;
    }

    /**
     * 生成月度数据曲线
     *
     * @param coalTypeName 煤种名称
     * @param today        今天日期
     * @return 数据曲线列表
     */
    private List<MkCoalCurve> generateMonthlyCurves(String coalTypeName, LocalDate today) {
        // 查询数据
        LocalDate queryStartDate = today.minusMonths(13);
        LambdaQueryWrapper<MkCoalInspectReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkCoalInspectReport::getCoalTypeName, coalTypeName);
        wrapper.le(MkCoalInspectReport::getSampleDate, today);
        wrapper.ge(MkCoalInspectReport::getSampleDate, queryStartDate);
        wrapper.orderByDesc(MkCoalInspectReport::getSampleDate);
        List<MkCoalInspectReport> entityList = baseMapper.selectList(wrapper);

        // 构建日期到报告的映射
        Map<LocalDate, MkCoalInspectReport> reportMap = entityList.stream()
            .filter(report -> report.getSampleDate() != null)
            .collect(Collectors.toMap(MkCoalInspectReport::getSampleDate, Function.identity(), (a, b) -> a));

        // 生成数据曲线
        List<MkCoalCurve> curves = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            LocalDate currentDate = today.minusMonths(i);
            LocalDate previousDate = today.minusMonths(i + 1);

            MkCoalCurve curve = createEmptyCoalCurve(currentDate);

            // 获取当前日期所在月份的最新报告
            MkCoalInspectReport currentReport = findLatestReportByMonth(reportMap, currentDate);
            setCurveValues(curve, currentReport);

            // 获取前一个月对应日期所在月份的最新报告用于环比
            MkCoalInspectReport previousReport = findLatestReportByMonth(reportMap, previousDate);
            setCurveRatios(curve, previousReport);

            curves.add(curve);
        }

        return curves;
    }

    /**
     * 设置曲线的值
     * @param curve 曲线对象
     * @param report 报告对象
     */
    private void setCurveValues(MkCoalCurve curve, MkCoalInspectReport report) {
        if (Objects.nonNull(report)) {
            curve.setHeatValue(report.getHeatValue() != null ? report.getHeatValue() : BigDecimal.ZERO);
            curve.setSulfurValue(report.getSulfur() != null ? report.getSulfur() : BigDecimal.ZERO);
            curve.setMoistureValue(report.getMoisture() != null ? report.getMoisture() : BigDecimal.ZERO);
            curve.setAshValue(report.getAsh() != null ? report.getAsh() : BigDecimal.ZERO);
        }
    }

    /**
     * 设置曲线的环比值
     * @param curve 曲线对象
     * @param report 报告对象
     */
    private void setCurveRatios(MkCoalCurve curve, MkCoalInspectReport report) {
        if (Objects.nonNull(report)) {
            curve.setHeatRatio(report.getHeatValue() != null ? report.getHeatValue() : BigDecimal.ZERO);
            curve.setSulfurRatio(report.getSulfur() != null ? report.getSulfur() : BigDecimal.ZERO);
            curve.setMoistureRatio(report.getMoisture() != null ? report.getMoisture() : BigDecimal.ZERO);
            curve.setAshRatio(report.getAsh() != null ? report.getAsh() : BigDecimal.ZERO);
        }
    }

    /**
     * 查找某个日期所在月份的最新报告
     * @param reportMap 日期到报告的映射
     * @param targetDate 目标日期
     * @return 目标日期的报告，如果没有则返回目标日期所在月份的最新报告，如果仍没有则返回null
     */
    private MkCoalInspectReport findLatestReportByMonth(Map<LocalDate, MkCoalInspectReport> reportMap, LocalDate targetDate) {
        // 首先检查目标日期是否有数据
        if (reportMap.containsKey(targetDate)) {
            return reportMap.get(targetDate);
        }

        // 获取目标日期所在月份的第一天和最后一天
        LocalDate firstDayOfMonth = targetDate.withDayOfMonth(1);
        LocalDate lastDayOfMonth = targetDate.withDayOfMonth(targetDate.lengthOfMonth());

        // 筛选出目标月份内的所有报告，并按日期降序排序
        return reportMap.keySet().stream()
                .filter(date -> !date.isBefore(firstDayOfMonth) && !date.isAfter(lastDayOfMonth))
                .max(Comparator.naturalOrder())
                .map(reportMap::get)
                .orElse(null);
    }


    /**
     * 创建空数据的MkCoalCurve对象
     * @param sampleDate 采样日期
     * @return 初始化后的MkCoalCurve对象
     */
    private MkCoalCurve createEmptyCoalCurve(LocalDate sampleDate) {
        MkCoalCurve mkCoalCurve = new MkCoalCurve();
        mkCoalCurve.setSampleDate(sampleDate);
        mkCoalCurve.setHeatValue(BigDecimal.ZERO);
        mkCoalCurve.setSulfurValue(BigDecimal.ZERO);
        mkCoalCurve.setMoistureValue(BigDecimal.ZERO);
        mkCoalCurve.setAshValue(BigDecimal.ZERO);
        mkCoalCurve.setHeatRatio(BigDecimal.ZERO);
        mkCoalCurve.setSulfurRatio(BigDecimal.ZERO);
        mkCoalCurve.setMoistureRatio(BigDecimal.ZERO);
        mkCoalCurve.setAshRatio(BigDecimal.ZERO);
        return mkCoalCurve;
    }

    /**
     * 获取出块率曲线数据
     * 生成包含最近10天、30天和12个月的出块率数据曲线，并计算各种平均值
     *
     * @return MkCoalBlock 包含三类出块率曲线数据及各种平均值的对象
     */
    @Override
    public MkCoalBlock getDataOfBlock() {
        MkCoalBlock result = new MkCoalBlock();
        LocalDate today = LocalDate.now();
        List<MkCoalBlockRate> tenDayList = new ArrayList<>();
        List<MkCoalBlockRate> thirtyDayList = new ArrayList<>();
        List<MkCoalBlockRate> monthlyList = new ArrayList<>();

        // 查询最近13个月的数据（包含12个月曲线所需的所有可能日期）
        LambdaQueryWrapper<MkCoalInspectReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.le(MkCoalInspectReport::getSampleDate, today);
        wrapper.ge(MkCoalInspectReport::getSampleDate, today.minusMonths(12));
        wrapper.orderByDesc(MkCoalInspectReport::getSampleDate);
        List<MkCoalInspectReport> entityList = baseMapper.selectList(wrapper);

        // 按日期分组
        Map<LocalDate, List<MkCoalInspectReport>> reportsByDate = entityList.stream()
                .collect(Collectors.groupingBy(MkCoalInspectReport::getSampleDate));

        // 生成10天数据（最近10天）
        for (int i = 0; i < 10; i++) {
            LocalDate currentDate = today.minusDays(i);
            tenDayList.add(createBlockData(currentDate, reportsByDate));
        }

        // 生成30天数据（最近30天）
        for (int i = 0; i < 30; i++) {
            LocalDate currentDate = today.minusDays(i);
            thirtyDayList.add(createBlockData(currentDate, reportsByDate));
        }

        // 生成12个月数据（每个月的今天）
        for (int i = 0; i < 12; i++) {
            LocalDate currentDate = today.minusMonths(i);
            // 处理月末日期不存在的情况（例如3月31日减去一个月应该是2月28/29日）
            currentDate = adjustForEndOfMonth(currentDate);
            monthlyList.add(createBlockData(currentDate, reportsByDate));
        }

        result.setTenDayBlockList(tenDayList);
        result.setThirtyDayBlockList(thirtyDayList);
        result.setMonthlyBlockList(monthlyList);

        // 计算10日均出块率
        BigDecimal tenDayAvg = calculateAverage(tenDayList);
        result.setTenDayRate(tenDayAvg);

        // 计算月均出块率（30日出块率平均数）
        BigDecimal thirtyDayAvg = calculateAverage(thirtyDayList);
        result.setThirtyDayRate(thirtyDayAvg);

        // 计算年均出块率（12月出块率平均数）
        BigDecimal yearlyAvg = calculateAverage(monthlyList);
        result.setMonthlyRate(yearlyAvg);

        // 计算昨日出块率（tenDayList中索引1的元素）
        BigDecimal yesterdayBlockRate = tenDayList.size() > 1 ? tenDayList.get(1).getBlockValue() : BigDecimal.ZERO;
        result.setYesterdayRate(yesterdayBlockRate);

        return result;
    }

    /**
     * 计算单日块率数据
     * 根据指定日期和按日期分组的报告数据，计算该日的出块率
     *
     * @param date 指定日期
     * @param reportsByDate 按日期分组的煤质检测报告数据
     * @return MkCoalBlock 包含指定日期出块率数据的对象
     */
    private MkCoalBlockRate createBlockData(LocalDate date, Map<LocalDate, List<MkCoalInspectReport>> reportsByDate) {
        MkCoalBlockRate blockDataRate = new MkCoalBlockRate();
        blockDataRate.setSampleDate(date);

        List<MkCoalInspectReport> reports = reportsByDate.getOrDefault(date, Collections.emptyList());
        int totalCount = reports.size();

        if (totalCount == 0) {
            blockDataRate.setBlockValue(BigDecimal.ZERO);
            return blockDataRate;
        }

        // 计算带'块'的煤种数量
        long blockCount = reports.stream()
                .filter(report -> report.getCoalTypeName() != null && report.getCoalTypeName().contains("块"))
                .count();

        // 计算出块率 (blockCount / totalCount) * 100
        BigDecimal blockRate = BigDecimal.valueOf(blockCount)
                .divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));

        blockDataRate.setBlockValue(blockRate);
        return blockDataRate;
    }

    /**
     * 计算列表中出块率的平均值
     *
     * @param blockRates 出块率列表
     * @return BigDecimal 平均值（保留4位小数）
     */
    private BigDecimal calculateAverage(List<MkCoalBlockRate> blockRates) {
        if (blockRates.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal sum = blockRates.stream()
                .map(MkCoalBlockRate::getBlockValue)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return sum.divide(BigDecimal.valueOf(blockRates.size()), 4, RoundingMode.HALF_UP);
    }

    /**
     * 调整日期，处理月末日期不存在的情况
     * 当指定日期在目标月份不存在时（例如3月31日减去一个月），
     * 自动调整为目标月份的最后一天（例如2月28/29日）
     *
     * @param date 需要调整的日期
     * @return LocalDate 调整后的有效日期
     */
    private LocalDate adjustForEndOfMonth(LocalDate date) {
        int dayOfMonth = date.getDayOfMonth();
        LocalDate adjustedDate;

        // 尝试获取相同日期，如果该月没有这一天，则获取该月的最后一天
        try {
            adjustedDate = date.withDayOfMonth(dayOfMonth);
        } catch (DateTimeException e) {
            adjustedDate = date.withDayOfMonth(date.lengthOfMonth());
        }

        return adjustedDate;
    }

}
