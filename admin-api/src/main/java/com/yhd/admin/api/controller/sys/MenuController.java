package com.yhd.admin.api.controller.sys;

import java.security.Principal;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.sys.convert.MenuConvert;
import com.yhd.admin.api.domain.sys.dto.MenuDTO;
import com.yhd.admin.api.domain.sys.query.MenuParam;
import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.api.domain.sys.vo.MenuVO;
import com.yhd.admin.api.service.sys.MenuService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName MenuController.java @Description TODO 菜单查询服务类。
 * @createTime 2020年03月31日 10:20:00
 */
@RestController
@RequestMapping(value = "/menu")
@Slf4j
public class MenuController {

    private final MenuService menuService;

    private final MenuConvert convert;

    public MenuController(MenuService menuService, MenuConvert convert) {
        this.menuService = menuService;
        this.convert = convert;
    }

    @PostMapping(value = "/obtainCurrentUserMenu/{clientId}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MenuVO>> obtainCurrentUserMenu(@PathVariable String clientId, Principal principal) {
        UserParam userParam = UserParam.builder().username(principal.getName()).clientId(clientId).build();
        List<MenuDTO> menuDTOS = menuService.queryMenuByUserWithCache(userParam);
        return RespJson.success(menuDTOS.stream().map(convert::toVo).collect(Collectors.toList()));
    }

    @PostMapping(value = "/queryMenuList", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MenuVO> queryMenuList(@RequestBody MenuParam queryParam) {
        queryParam.setId(0L);
        IPage<MenuDTO> menuPage = menuService.pagingQuery(queryParam);
        return new PageRespJson<>(menuPage.convert(convert::toVo));
    }

    @PostMapping(value = "/addOrUpdate", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson addOrUpdate(@RequestBody MenuParam queryParam, Principal principal) {
        queryParam.setCreatedBy(principal.getName());
        menuService.addOrUpdate(queryParam);
        return RespJson.success();
    }

    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody MenuParam queryParam, Principal principal) {
        queryParam.setUpdatedBy(principal.getName());
        menuService.modify(queryParam);
        return RespJson.success();
    }

    @PostMapping(value = "/remove", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson remove(@RequestBody BatchParam batchParam) {
        menuService.removeBatch(batchParam);
        return RespJson.success();
    }

    @PostMapping(value = "/obtainDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson obtainDetail(@RequestBody MenuParam queryParam) {
        return RespJson.success(convert.toVo(menuService.obtainDetailById(queryParam.getId())));
    }

    @PostMapping(value = "/querySubMenuIncludeSelf", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson querySubMenuIncludeSelf(@RequestBody MenuParam queryParam) {
        queryParam.setId(0L);
        return RespJson.success(convert.toTreeNode(menuService.querySubMenu(queryParam)));
    }

    @PostMapping(value = "/validateIfNotExist", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> validateIfNotExist(@RequestBody MenuParam param) {
        return RespJson.success(menuService.validateIfNotExist(param));
    }
}
