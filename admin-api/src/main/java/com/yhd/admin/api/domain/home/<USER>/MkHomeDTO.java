package com.yhd.admin.api.domain.home.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 综合管控大屏
 *
 * <AUTHOR>
 * @date 2025/7/29 09:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkHomeDTO extends BaseDTO implements Serializable {

  private Long id;

  /** 安全生产日期 */
  private LocalDate date;

  /** 安全生产天数 */
  private long day;

  /** 设计产能 */
  private BigDecimal planProduction;

  /** 主采煤层 */
  private String mainCoalMining;

  /** 带班信息 */
  private List<MkHomeDataStructureDTO> classList = new ArrayList<>();

  /** 年完成率 */
  private BigDecimal yearRate;

  /** 生产数据 */
  private List<MkHomeDataStructureDTO> productionList = new ArrayList<>();

  /** 煤仓情况 */
  private List<MkHomeDataStructureDTO> coalBunkerList = new ArrayList<>();

  /** 人员分布 */
  private List<MkHomeDataStructureDTO> peopleList = new ArrayList<>();

  /** 设备状态 */
  private List<MkHomeDataStructureDTO> equipmentStatusList = new ArrayList<>();

  /** 安全状态 */
  private List<MkHomeDataStructureDTO> safeStatusList = new ArrayList<>();

  /** 监控画面 */
  private List<String> monitorList = new ArrayList<>();
}
