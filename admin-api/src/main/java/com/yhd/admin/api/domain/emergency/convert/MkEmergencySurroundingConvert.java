package com.yhd.admin.api.domain.emergency.convert;

import com.yhd.admin.api.domain.emergency.dto.MkEmergencySurroundingDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencySurrounding;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySurroundingParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencySurroundingVO;
import org.mapstruct.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencySurroundingConvert.java
 * @Description 应急救援周边信息转换器
 * @createTime 2025年01月20日 14:00:00
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface MkEmergencySurroundingConvert {

    /**
     * 将实体转换为视图对象
     *
     * @param entity 实体
     * @return 视图对象
     */
    @Mapping(source = "status", target = "statusName", qualifiedByName = "statusToStatusName")
    MkEmergencySurroundingVO toVO(MkEmergencySurrounding entity);

    /**
     * 将实体转换为DTO对象
     *
     * @param entity 实体
     * @return DTO对象
     */
    MkEmergencySurroundingDTO toDTO(MkEmergencySurrounding entity);

    /**
     * 将DTO对象转换为实体
     *
     * @param dto DTO对象
     * @return 实体
     */
    MkEmergencySurrounding toEntity(MkEmergencySurroundingDTO dto);

    /**
     * 将参数对象转换为实体
     *
     * @param param 参数对象
     * @return 实体
     */
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "remark", ignore = true)
    MkEmergencySurrounding toEntity(MkEmergencySurroundingParam param);

    /**
     * 更新实体
     *
     * @param param  参数对象
     * @param entity 实体对象
     */
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "remark", ignore = true)
    void updateEntity(MkEmergencySurroundingParam param, @MappingTarget MkEmergencySurrounding entity);

    /**
     * 状态转换为状态名称
     *
     * @param status 状态
     * @return 状态名称
     */
    @Named("statusToStatusName")
    default String statusToStatusName(Integer status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case 1:
                return "正常";
            case 2:
                return "异常";
            case 3:
                return "离线";
            default:
                return "未知状态";
        }
    }
}