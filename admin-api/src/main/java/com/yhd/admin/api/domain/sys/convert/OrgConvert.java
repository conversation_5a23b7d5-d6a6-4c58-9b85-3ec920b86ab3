package com.yhd.admin.api.domain.sys.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import com.yhd.admin.api.domain.sys.dto.OrgDTO;
import com.yhd.admin.api.domain.sys.entity.SysOrg;
import com.yhd.admin.api.domain.sys.query.OrgParam;
import com.yhd.admin.api.domain.sys.vo.OrgVO;

/** Org 转换类 */
@Mapper(componentModel = "spring")
public interface OrgConvert {
    SysOrg toEntity(OrgParam param);

    @Mappings({@Mapping(target = "children", ignore = true)})
    OrgDTO toDTO(SysOrg sysOrg);

    OrgVO toVO(OrgDTO orgVO);

    List<OrgVO> toVO(List<OrgDTO> orgVO);
}
