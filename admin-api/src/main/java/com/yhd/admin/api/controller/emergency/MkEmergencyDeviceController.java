package com.yhd.admin.api.controller.emergency;

import com.yhd.admin.api.domain.emergency.convert.MkEmergencyDeviceConvert;
import com.yhd.admin.api.domain.emergency.query.DeviceRealtimeChartParam;
import com.yhd.admin.api.domain.emergency.vo.DeviceRealtimeChartVO;
import com.yhd.admin.api.service.emergency.MkEmergencyDeviceService;
import com.yhd.admin.common.domain.RespJson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencyDeviceController.java
 * @Description 应急救援设备信息-矿井快照Controller
 * @createTime 2025年07月30日 17:35:00
 */
@RestController
@RequestMapping("/emergency/device")
@Slf4j
public class MkEmergencyDeviceController {

    private final MkEmergencyDeviceService deviceService;
    private final MkEmergencyDeviceConvert convert;

    public MkEmergencyDeviceController(MkEmergencyDeviceService deviceService, MkEmergencyDeviceConvert convert) {
        this.deviceService = deviceService;
        this.convert = convert;
    }


    /**
     * 获取设备实时值曲线数据
     *
     * @param param 查询参数
     * @return 曲线数据
     */
    @PostMapping(value = "/getRealtimeChart", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<DeviceRealtimeChartVO> getRealtimeChart(@RequestBody DeviceRealtimeChartParam param) {
        log.info("获取设备实时值曲线数据，参数：{}", param);
        DeviceRealtimeChartVO result = deviceService.getRealtimeChart(param);
        return RespJson.success(result);
    }
}
