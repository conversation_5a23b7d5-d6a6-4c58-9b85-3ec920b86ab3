package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyDeviceConvert;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyDevice;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyDeviceParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyDeviceVO;
import com.yhd.admin.api.service.emergency.MkEmergencyDeviceService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencyDeviceController.java
 * @Description 应急救援设备信息Controller
 * @createTime 2025年07月30日 17:35:00
 */
@RestController
@RequestMapping("/emergency/device")
@Slf4j
public class MkEmergencyDeviceController {

    private final MkEmergencyDeviceService deviceService;
    private final MkEmergencyDeviceConvert convert;

    public MkEmergencyDeviceController(MkEmergencyDeviceService deviceService, MkEmergencyDeviceConvert convert) {
        this.deviceService = deviceService;
        this.convert = convert;
    }

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkEmergencyDeviceVO> pagingQuery(@RequestBody MkEmergencyDeviceParam param) {
        IPage<MkEmergencyDevice> iPage = deviceService.pagingQuery(param);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    /**
     * 根据ID查询
     *
     * @param param 参数
     * @return 设备信息
     */
    @PostMapping(value = "/getById", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkEmergencyDeviceVO> getById(@RequestBody MkEmergencyDeviceParam param) {
        MkEmergencyDevice device = deviceService.getById(param.getId());
        return RespJson.success(convert.toVO(device));
    }

    /**
     * 新增
     *
     * @param param     参数
     * @param principal 当前用户
     * @return 结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> add(@RequestBody MkEmergencyDeviceParam param, Principal principal) {
        param.setCreatedBy(principal.getName());
        boolean result = deviceService.add(param);
        return RespJson.success(result);
    }

    /**
     * 修改
     *
     * @param param     参数
     * @param principal 当前用户
     * @return 结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> modify(@RequestBody MkEmergencyDeviceParam param, Principal principal) {
        param.setUpdatedBy(principal.getName());
        boolean result = deviceService.modify(param);
        return RespJson.success(result);
    }

    /**
     * 删除
     *
     * @param param 参数
     * @return 结果
     */
    @PostMapping(value = "/remove", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> remove(@RequestBody MkEmergencyDeviceParam param) {
        boolean result = deviceService.remove(param.getId());
        return RespJson.success(result);
    }

    /**
     * 批量删除
     *
     * @param param 参数
     * @return 结果
     */
    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> removeBatch(@RequestBody BatchParam param) {
        // 将Long类型的id转换为Integer类型
        List<Integer> ids = new ArrayList<>();
        if (param.getId() != null && !param.getId().isEmpty()) {
            ids = param.getId().stream().map(Long::intValue).collect(Collectors.toList());
        }
        boolean result = deviceService.removeBatch(ids);
        return RespJson.success(result);
    }
}
