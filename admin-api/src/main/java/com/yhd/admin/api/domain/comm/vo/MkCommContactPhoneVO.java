package com.yhd.admin.api.domain.comm.vo;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 联系人电话表
 *
 * <AUTHOR>
 * @date 2025/7/25 13:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkCommContactPhoneVO extends BaseVO implements Serializable {

  /** 联系人ID */
  private Long contactId;
  /** 电话类型 1：井下调度电话 2：地面调度电话 3：主移动电话 4：副移动电话 5：固定电话 */
  private String phoneType;
  /** 电话号码 */
  private String phoneNumber;
  /** 备注 */
  private String remark;
}
