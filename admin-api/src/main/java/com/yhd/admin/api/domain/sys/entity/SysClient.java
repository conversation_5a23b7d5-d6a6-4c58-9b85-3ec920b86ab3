package com.yhd.admin.api.domain.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/3 10:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysClient extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String clientName;
    /** 模块名称 */
    private String clientId;
    /** 资源名称 */
    private String resourceId;
}
