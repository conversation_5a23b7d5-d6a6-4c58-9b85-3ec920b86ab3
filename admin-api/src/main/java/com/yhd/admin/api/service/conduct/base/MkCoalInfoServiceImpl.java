package com.yhd.admin.api.service.conduct.base;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.base.MkCoalInfoDao;
import com.yhd.admin.api.domain.conduct.convert.base.MkCoalInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.base.MkCoalInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkCoalInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkCoalInfoParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 煤层信息服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25 10:32:52
 */
@Service
public class MkCoalInfoServiceImpl extends ServiceImpl<MkCoalInfoDao, MkCoalInfo> implements MkCoalInfoService {

    private final MkCoalInfoConvert mkCoalInfoConvert;

    public MkCoalInfoServiceImpl(MkCoalInfoConvert mkCoalInfoConvert) {
        this.mkCoalInfoConvert = mkCoalInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkCoalInfoDTO> pagingQuery(MkCoalInfoParam param) {
        IPage<MkCoalInfo> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkCoalInfo> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return wrapper.page(page).convert(mkCoalInfoConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkCoalInfoDTO> queryList(MkCoalInfoParam param) {
        LambdaQueryWrapper<MkCoalInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(param.getMineId()), MkCoalInfo::getMineId, param.getMineId());
        return mkCoalInfoConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param  数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkCoalInfoParam param) {
        MkCoalInfo entity = mkCoalInfoConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return MkCoalInfoDTO
     */
    @Override
    public MkCoalInfoDTO getCurrentDetails(MkCoalInfoParam param) {
        return mkCoalInfoConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkCoalInfoParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }
}
