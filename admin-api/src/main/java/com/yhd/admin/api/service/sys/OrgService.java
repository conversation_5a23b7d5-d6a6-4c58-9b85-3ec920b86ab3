package com.yhd.admin.api.service.sys;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.sys.dto.OrgDTO;
import com.yhd.admin.api.domain.sys.entity.SysOrg;
import com.yhd.admin.api.domain.sys.query.OrgParam;

/**
 * <一句话描述>: 组织部门管理 <详细功能>:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/1/8 16:51
 */
public interface OrgService extends IService<SysOrg> {

    /**
     * <一句话描述> <详细功能>
     *
     * @param *
     * @param: id 主键
     * @return java.util.List<com.yhd.admin.bms.domain.dto.OrgDTO>
     * <AUTHOR>
     * @date 2021/1/8 16:51
     */
    List<OrgDTO> queryList(Long id);

    /**
     * <一句话描述> 新增组织部门管理 <详细功能>
     *
     * @param *
     * @param: param
     * @return java.lang.Boolean
     * @description
     * <AUTHOR>
     * @date 2021/1/8 16:49
     */
    Boolean addOrModifyOrg(OrgParam param);

    /**
     * <一句话描述> <详细功能>
     *
     * @return java.lang.Boolean
     * @param: [ids 主键数组]
     * <AUTHOR>
     * @date 2021/1/8 17:00
     */
    Boolean removeBatch(List<Long> ids);

    /**
     * <一句话简介>: 查询组织部门详情 <详细描述>:
     *
     * @param: id
     * @return: com.yhd.admin.bms.domain.dto.OrgDTO
     * @author: jiangZhengHao
     * @date: 2021/1/9
     */
    OrgDTO currentDetail(Long id);

    /**
     * 根据ID查询父级部门
     *
     * @param id
     * @return
     */
    List<OrgDTO> listParentOrgById(Long id);

    /**
     * 根据ID查询子级部门
     *
     * @param id
     * @return
     */
    List<OrgDTO> listChildrenOrgById(Long id);

    /**
     *
     * @param id
     * @return
     */
    String joinOrgName(Long id);
}
