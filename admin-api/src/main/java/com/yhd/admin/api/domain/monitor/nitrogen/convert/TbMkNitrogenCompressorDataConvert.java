package com.yhd.admin.api.domain.monitor.nitrogen.convert;

import com.yhd.admin.api.domain.monitor.nitrogen.dto.TbMkNitrogenCompressorDataDTO;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenCompressorData;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.TbMkNitrogenCompressorDataVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenCompressorDataCreateVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.update.TbMkNitrogenCompressorDataUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 制氮空压机详情表
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkNitrogenCompressorDataConvert {

    @Mapping(target = "id", ignore = true)
    TbMkNitrogenCompressorData convert(TbMkNitrogenCompressorDataCreateVO createVO);

    TbMkNitrogenCompressorData convert(TbMkNitrogenCompressorDataUpdateVO updateVO);

    TbMkNitrogenCompressorDataVO convert(TbMkNitrogenCompressorData po);

    TbMkNitrogenCompressorDataDTO toDTO(TbMkNitrogenCompressorData po);



}
