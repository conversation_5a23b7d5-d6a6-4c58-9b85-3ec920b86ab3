package com.yhd.admin.api.service.monitor.fan.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.fan.TbMkLocalFanDetailsDao;
import com.yhd.admin.api.domain.monitor.fan.convert.TbMkLocalFanDetailsConvert;
import com.yhd.admin.api.domain.monitor.fan.dto.TbMkLocalFanDetailsDTO;
import com.yhd.admin.api.domain.monitor.fan.entity.TbMkLocalFanDetails;
import com.yhd.admin.api.domain.monitor.fan.vo.create.TbMkLocalFanDetailsCreateVO;
import com.yhd.admin.api.domain.monitor.fan.vo.page.TbMkLocalFanDetailsPageVO;
import com.yhd.admin.api.domain.monitor.fan.vo.update.TbMkLocalFanDetailsUpdateVO;
import com.yhd.admin.api.service.monitor.fan.TbMkLocalFanDetailsService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 局扇风机详细监控数据 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkLocalFanDetailsServiceImpl extends ServiceImpl<TbMkLocalFanDetailsDao, TbMkLocalFanDetails> implements TbMkLocalFanDetailsService {

    @Resource
    private TbMkLocalFanDetailsConvert tbMkLocalFanDetailsConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkLocalFanDetailsCreateVO createVO) {
        TbMkLocalFanDetails tbMkLocalFanDetails = tbMkLocalFanDetailsConvert.convert(createVO);
        baseMapper.insert(tbMkLocalFanDetails);
        return tbMkLocalFanDetails.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkLocalFanDetailsUpdateVO updateVO) {
        TbMkLocalFanDetails tbMkLocalFanDetails = tbMkLocalFanDetailsConvert.convert(updateVO);
        return baseMapper.updateById(tbMkLocalFanDetails);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkLocalFanDetails get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkLocalFanDetailsDTO> page(TbMkLocalFanDetailsPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkLocalFanDetails> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkLocalFanDetails> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.eq(param.getFanUnitId() != null, TbMkLocalFanDetails::getFanUnitId, param.getFanUnitId());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getStatus()), TbMkLocalFanDetails::getStatus, param.getStatus());
        queryChainWrapper.orderByDesc(TbMkLocalFanDetails::getId);
        return queryChainWrapper.page(iPage).convert(tbMkLocalFanDetailsConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkLocalFanDetailsCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 局扇风机详细监控数据：传入的列表为空");
            return 0;
        }
        List<TbMkLocalFanDetails> entities = createVOs.stream()
                .map(tbMkLocalFanDetailsConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 局扇风机详细监控数据 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkLocalFanDetailsUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 局扇风机详细监控数据：传入的列表为空");
            return 0;
        }
        List<TbMkLocalFanDetails> entities = updateVOs.stream()
                .map(tbMkLocalFanDetailsConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 局扇风机详细监控数据：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 局扇风机详细监控数据 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
