package com.yhd.admin.api.service.conduct.meeting;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.meeting.MkMeetingRecordDTO;
import com.yhd.admin.api.domain.conduct.entity.meeting.MkMeetingRecord;
import com.yhd.admin.api.domain.conduct.query.meeting.MkMeetingRecordParam;

import java.util.List;

/**
 * 调度会议记录服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28 10:36:44
 */
public interface MkMeetingRecordService extends IService<MkMeetingRecord> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkMeetingRecordDTO> pagingQuery(MkMeetingRecordParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkMeetingRecordDTO> queryList(MkMeetingRecordParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkMeetingRecordParam param);

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    MkMeetingRecordDTO getCurrentDetails(MkMeetingRecordParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkMeetingRecordParam param);

    /**
     * 导出文件
     *
     * @return 文件地址
     */
    String export();
}
