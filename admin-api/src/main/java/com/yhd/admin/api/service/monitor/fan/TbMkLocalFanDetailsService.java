package com.yhd.admin.api.service.monitor.fan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.monitor.fan.dto.TbMkLocalFanDetailsDTO;
import com.yhd.admin.api.domain.monitor.fan.entity.TbMkLocalFanDetails;
import com.yhd.admin.api.domain.monitor.fan.vo.create.TbMkLocalFanDetailsCreateVO;
import com.yhd.admin.api.domain.monitor.fan.vo.page.TbMkLocalFanDetailsPageVO;
import com.yhd.admin.api.domain.monitor.fan.vo.update.TbMkLocalFanDetailsUpdateVO;

import java.util.List;

/**
 * 局扇风机详细监控数据 服务类
 *
 * <AUTHOR>
 * @since
 */
public interface TbMkLocalFanDetailsService  extends IService<TbMkLocalFanDetails> {

    /**
     * 新建
     *
     * @param createVO 新建VO
     * @return id
     */
    Long create(TbMkLocalFanDetailsCreateVO createVO);

    /**
     * 更新
     *
     * @param updateVO 更新VO
     */
    int update(TbMkLocalFanDetailsUpdateVO updateVO);

    /**
     * 删除
     *
     * @param id id
     */
    int delete(Long id);

    /**
     * 查询单个
     *
     * @param id id
     * @return po
     */
    TbMkLocalFanDetails get(Long id);

    /**
     * 分页查询
     *
     * @param pageVO 分页VO
     * @return pageResult
     */
    IPage<TbMkLocalFanDetailsDTO> page(TbMkLocalFanDetailsPageVO pageVO);

    /**
      * 批量删除
      *
      * @param ids 要删除的记录ID列表
      * @return 受影响的行数
      */
    int batchDelete(List<Long> ids);

    /**
     * 批量新增
     *
     * @param createVOs 新增VO列表
     * @return 新增的记录ID列表
     */
    int batchCreate(List<TbMkLocalFanDetailsCreateVO> createVOs);

    /**
     * 批量更新
     *
     * @param updateVOs 更新VO列表
     * @return 受影响的行数
     */
    int batchUpdate(List<TbMkLocalFanDetailsUpdateVO> updateVOs);
}
