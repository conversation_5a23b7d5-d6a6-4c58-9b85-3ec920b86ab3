package com.yhd.admin.api.controller.monitor.ventilation;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yhd.admin.api.dao.monitor.ventilation.TbMkMotorInfoDao;
import com.yhd.admin.api.dao.monitor.ventilation.TbMkMotorOperationDao;
import com.yhd.admin.api.dao.monitor.ventilation.TbMkVentilationInfoDao;
import com.yhd.admin.api.dao.monitor.ventilation.TbMkVentilationOperationDao;
import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkVentilationDTO;
import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkVentilationDetailDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkMotorInfo;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkMotorOperation;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkVentilationInfo;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkVentilationOperation;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/monitor/ventilation/screen")
public class TbMkVentilationController {

    @Resource
    private TbMkVentilationInfoDao tbMkVentilationInfoDao;

    @Resource
    private TbMkMotorInfoDao tbMkMotorInfooDao;

    @Resource
    private TbMkMotorOperationDao tbMkMotorOperationDao;

    @Resource
    private TbMkVentilationOperationDao tbMkVentilationOperationDao;


    /**
     * 获取通风机及其电机的运行参数
     *
     * @return 运行参数列表
     */
    @GetMapping(value = "/info", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<TbMkVentilationDTO>> geInfo() {
        log.debug("获取通风机及其电机的运行参数");
        List<TbMkVentilationDTO> result = new ArrayList<>();
        // 查询所有通风机信息
        List<TbMkVentilationInfo> ventilationInfos = tbMkVentilationInfoDao.selectList(null);
        List<TbMkVentilationDetailDTO> motorDetails;
        TbMkVentilationDTO tbMkVentilationDTO;
        for (TbMkVentilationInfo ventilationInfo : ventilationInfos) {
            // 查询该通风机的运行参数
            QueryWrapper<TbMkVentilationOperation> ventilationOperationQuery = new QueryWrapper<>();
            ventilationOperationQuery.eq("ventilation_id", ventilationInfo.getId());
            ventilationOperationQuery.orderByDesc("updated_time");
            TbMkVentilationOperation ventilationOperation = tbMkVentilationOperationDao.selectOne(ventilationOperationQuery);
            tbMkVentilationDTO = new TbMkVentilationDTO();
            BeanUtils.copyProperties(ventilationOperation, tbMkVentilationDTO);
            tbMkVentilationDTO.setVentilationId(ventilationInfo.getId());
            tbMkVentilationDTO.setVentilationName(ventilationInfo.getVentilationName());

            // 查询该通风机对应的电机信息
            QueryWrapper<TbMkMotorInfo> motorQuery = new QueryWrapper<>();
            motorQuery.eq("ventilation_id", ventilationInfo.getId());
            List<TbMkMotorInfo> motorInfos = tbMkMotorInfooDao.selectList(motorQuery);
            motorDetails = new ArrayList<>();
            for (TbMkMotorInfo motorInfo : motorInfos) {
                // 查询该电机的运行参数
                QueryWrapper<TbMkMotorOperation> motorOperationQuery = new QueryWrapper<>();
                motorOperationQuery.eq("motor_id", motorInfo.getId());
                motorOperationQuery.orderByDesc("updated_time");
                TbMkMotorOperation motorOperation = tbMkMotorOperationDao.selectOne(motorOperationQuery);
                // 封装结果
                TbMkVentilationDetailDTO dto = new TbMkVentilationDetailDTO();
                BeanUtils.copyProperties(motorOperation, dto);
                dto.setMotorName(motorInfo.getMotorName());
                dto.setMotorNumber(motorInfo.getMotorNumber());
                motorDetails.add(dto);
            }
            tbMkVentilationDTO.setMotorDetails(motorDetails);
            result.add(tbMkVentilationDTO);
        }
        return RespJson.success(result);
    }
}
