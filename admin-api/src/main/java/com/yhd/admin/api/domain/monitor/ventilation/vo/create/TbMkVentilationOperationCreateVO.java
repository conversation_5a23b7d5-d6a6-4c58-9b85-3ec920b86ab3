package com.yhd.admin.api.domain.monitor.ventilation.vo.create;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 通风机整体运行参数
 *
 * <AUTHOR>
 * @since
 */
@Data
public class TbMkVentilationOperationCreateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联通风机基本信息表ID
     */
    private Long ventilationId;

    /**
     * 蝶阀状态
     */
    private String valveStatus;

    /**
     * 风速(m/s)
     */
    private BigDecimal windSpeed;

}
