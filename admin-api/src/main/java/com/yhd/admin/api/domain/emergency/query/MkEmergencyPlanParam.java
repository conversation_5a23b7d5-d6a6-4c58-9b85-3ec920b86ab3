package com.yhd.admin.api.domain.emergency.query;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.yhd.admin.common.domain.query.QueryParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 应急救援数字预案表查询参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
public class MkEmergencyPlanParam extends QueryParam implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 预案编码
     */
    private String planCode;

    /**
     * 预案名称（模糊查询）
     */
    private String planName;

    /**
     * 预案分类ID
     */
    private Integer categoryId;

    /**
     * 预案类型
     */
    private String planType;

    /**
     * 预案级别
     */
    private String planLevel;

    /**
     * 适用范围
     */
    private String applicableScope;

    /**
     * 预案内容
     */
    private String planContent;

    /**
     * 流程图数据
     */
    private String flowChart;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 预案状态：1-草稿，2-审核中，3-已发布，4-已废止
     */
    private Integer planStatus;

    /**
     * 版本号
     */
    private String version;

    /**
     * 生效日期
     */
    private LocalDateTime effectiveDate;

    /**
     * 失效日期
     */
    private LocalDateTime expiryDate;

    /**
     * 责任人（模糊查询）
     */
    private String responsiblePerson;

    /**
     * 责任部门（模糊查询）
     */
    private String responsibleDepartment;

    /**
     * 应急联系人
     */
    private String emergencyContacts;

    /**
     * 所需资源
     */
    private String requiredResources;

    /**
     * 培训要求
     */
    private String trainingRequirements;

    /**
     * 演练频次
     */
    private String drillFrequency;

    /**
     * 最后演练日期
     */
    private LocalDateTime lastDrillDate;

    /**
     * 下次演练日期
     */
    private LocalDateTime nextDrillDate;

    /**
     * 评审周期（月）
     */
    private Integer reviewCycle;

    /**
     * 最后评审日期
     */
    private LocalDateTime lastReviewDate;

    /**
     * 下次评审日期
     */
    private LocalDateTime nextReviewDate;

    /**
     * 附件地址
     */
    private String attachmentUrls;

    /**
     * 关键词（模糊查询）
     */
    private String keywords;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 生效日期范围查询-开始日期
     */
    private LocalDate effectiveDateBegin;

    /**
     * 生效日期范围查询-结束日期
     */
    private LocalDate effectiveDateEnd;

    /**
     * 失效日期范围查询-开始日期
     */
    private LocalDate expiryDateBegin;

    /**
     * 失效日期范围查询-结束日期
     */
    private LocalDate expiryDateEnd;
}
