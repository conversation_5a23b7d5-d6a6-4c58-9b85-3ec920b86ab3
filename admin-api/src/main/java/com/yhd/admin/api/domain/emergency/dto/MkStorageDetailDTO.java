package com.yhd.admin.api.domain.emergency.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 应急物资记录表 DTO
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkStorageDetailDTO extends BaseDTO implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 物资系统编码
     */
    private String materialSystemCode;

    /**
     * 物资编码
     */
    private String materialCode;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 仓库标识
     */
    private String storageId;

    /**
     * 物资名称
     */
    private String materialName;

    /**
     * 物资大类
     */
    private String catalogType;

    /**
     * 物资分类
     */
    private String materialType;

    /**
     * 规格型号
     */
    private String materialModel;

    /**
     * 主要技术参数
     */
    private String materialSpecification;

    /**
     * 供货厂家
     */
    private String materialManufacture;

    /**
     * 计量单位
     */
    private String unitOfMeasure;

    /**
     * 库存数量
     */
    private Double qtyInStock;

    /**
     * 在修数量
     */
    private Double qtyInRepair;

    /**
     * 使用数量
     */
    private Double qtyInUse;

    /**
     * 报废数量
     */
    private Double qtyInScrap;

    /**
     * 总数量
     */
    private Double totalQty;

    /**
     * 状态
     */
    private String materialStatus;

    /**
     * 使用情况
     */
    private String usageSituation;

    /**
     * 采购到货情况
     */
    private String purchaseSituation;

    /**
     * 最后检测时间
     */
    private LocalDate checkDate;

    /**
     * 检测情况
     */
    private String checkSituation;

    /**
     * 到期时间
     */
    private LocalDate dqsjDate;
}
