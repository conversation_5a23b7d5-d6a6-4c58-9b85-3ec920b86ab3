package com.yhd.admin.api.service.emergency;

import com.yhd.admin.api.domain.emergency.query.MkComprehensiveDecisionParam;
import com.yhd.admin.api.domain.emergency.vo.MkComprehensiveDecisionVO;

/**
 * 综合决策大屏 Service
 * 
 * <AUTHOR>
 */
public interface MkComprehensiveDecisionService {

    /**
     * 获取综合决策大屏数据
     *
     * @param param 查询参数
     * @return 综合决策大屏数据
     */
    MkComprehensiveDecisionVO getDashboardData(MkComprehensiveDecisionParam param);
}