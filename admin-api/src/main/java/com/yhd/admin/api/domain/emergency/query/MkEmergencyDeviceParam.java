package com.yhd.admin.api.domain.emergency.query;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencyDeviceParam.java
 * @Description 应急救援设备信息查询参数
 * @createTime 2025年07月30日 17:10:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MkEmergencyDeviceParam extends QueryParam {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备位置
     */
    private String location;

    /**
     * 设备状态：1-正常，2-异常，3-离线
     */
    private Integer status;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 当前实时值
     */
    private BigDecimal currentValue;

    /**
     * 最小阈值
     */
    private BigDecimal thresholdMin;

    /**
     * 最大阈值
     */
    private BigDecimal thresholdMax;

    /**
     * 测量单位
     */
    private String unit;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 安装开始时间
     */
    private LocalDateTime installTimeStart;

    /**
     * 安装结束时间
     */
    private LocalDateTime installTimeEnd;

    /**
     * 设备型号
     */
    private String model;

    /**
     * 制造商
     */
    private String manufacturer;
}
