package com.yhd.admin.api.service.monitor.nitrogen.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.nitrogen.TbMkNitrogenCompressorDataDao;
import com.yhd.admin.api.domain.monitor.nitrogen.convert.TbMkNitrogenCompressorDataConvert;
import com.yhd.admin.api.domain.monitor.nitrogen.dto.TbMkNitrogenCompressorDataDTO;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenCompressorData;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenCompressorDataCreateVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.page.TbMkNitrogenCompressorDataPageVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.update.TbMkNitrogenCompressorDataUpdateVO;
import com.yhd.admin.api.service.monitor.nitrogen.TbMkNitrogenCompressorDataService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 制氮空压机详情表 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkNitrogenCompressorDataServiceImpl extends ServiceImpl<TbMkNitrogenCompressorDataDao, TbMkNitrogenCompressorData> implements TbMkNitrogenCompressorDataService {

    @Resource
    private TbMkNitrogenCompressorDataConvert tbMkNitrogenCompressorDataConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkNitrogenCompressorDataCreateVO createVO) {
        TbMkNitrogenCompressorData tbMkNitrogenCompressorData = tbMkNitrogenCompressorDataConvert.convert(createVO);
        baseMapper.insert(tbMkNitrogenCompressorData);
        return tbMkNitrogenCompressorData.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkNitrogenCompressorDataUpdateVO updateVO) {
        TbMkNitrogenCompressorData tbMkNitrogenCompressorData = tbMkNitrogenCompressorDataConvert.convert(updateVO);
        return baseMapper.updateById(tbMkNitrogenCompressorData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkNitrogenCompressorData get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkNitrogenCompressorDataDTO> page(TbMkNitrogenCompressorDataPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkNitrogenCompressorData> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkNitrogenCompressorData> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.eq(param.getNitrogenCompressorInfoId() != null, TbMkNitrogenCompressorData::getNitrogenCompressorInfoId, param.getNitrogenCompressorInfoId());
        queryChainWrapper.eq(param.getStatus() != null, TbMkNitrogenCompressorData::getStatus, param.getStatus());
        queryChainWrapper.eq(param.getRuntimeHours() != null, TbMkNitrogenCompressorData::getRuntimeHours, param.getRuntimeHours());
        queryChainWrapper.orderByDesc(TbMkNitrogenCompressorData::getId);
        return queryChainWrapper.page(iPage).convert(tbMkNitrogenCompressorDataConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkNitrogenCompressorDataCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 制氮空压机详情表：传入的列表为空");
            return 0;
        }
        List<TbMkNitrogenCompressorData> entities = createVOs.stream()
                .map(tbMkNitrogenCompressorDataConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 制氮空压机详情表 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkNitrogenCompressorDataUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 制氮空压机详情表：传入的列表为空");
            return 0;
        }
        List<TbMkNitrogenCompressorData> entities = updateVOs.stream()
                .map(tbMkNitrogenCompressorDataConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 制氮空压机详情表：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 制氮空压机详情表 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
