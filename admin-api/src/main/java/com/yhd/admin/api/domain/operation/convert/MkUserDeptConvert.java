package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkUserDeptDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserDept;
import com.yhd.admin.api.domain.operation.query.MkUserDeptParam;
import com.yhd.admin.api.domain.operation.vo.MkUserDeptVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
* 部门管理表
*
* <AUTHOR>
* @since 1.0.0 2025-07-28
*/
@Mapper(componentModel = "spring")
public interface MkUserDeptConvert {

    MkUserDept toEntity(MkUserDeptParam param);

    MkUserDeptVO toVO(MkUserDeptDTO dto);

    List<MkUserDeptVO> toVOList(List<MkUserDeptDTO> dtos);

    MkUserDeptDTO toDTO(MkUserDept entity);

    List<MkUserDeptDTO> toDTOList(List<MkUserDept> entitys);

}
