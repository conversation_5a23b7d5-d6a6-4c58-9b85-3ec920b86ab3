package com.yhd.admin.api.domain.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 运维配置中心-单位管理-单位类型表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class MkUserOrgType extends BaseEntity implements Serializable {
	/**
	* Id
	*/
    @TableId(type = IdType.AUTO)
	private Long id;

	/**
	* 类型名称
	*/
	private String name;

}
