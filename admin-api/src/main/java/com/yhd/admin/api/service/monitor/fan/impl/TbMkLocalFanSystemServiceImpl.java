package com.yhd.admin.api.service.monitor.fan.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.fan.TbMkLocalFanSystemDao;
import com.yhd.admin.api.domain.monitor.fan.convert.TbMkLocalFanSystemConvert;
import com.yhd.admin.api.domain.monitor.fan.dto.TbMkLocalFanSystemDTO;
import com.yhd.admin.api.domain.monitor.fan.entity.TbMkLocalFanSystem;
import com.yhd.admin.api.domain.monitor.fan.vo.create.TbMkLocalFanSystemCreateVO;
import com.yhd.admin.api.domain.monitor.fan.vo.page.TbMkLocalFanSystemPageVO;
import com.yhd.admin.api.domain.monitor.fan.vo.update.TbMkLocalFanSystemUpdateVO;
import com.yhd.admin.api.service.monitor.fan.TbMkLocalFanSystemService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 局扇控制系统 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkLocalFanSystemServiceImpl extends ServiceImpl<TbMkLocalFanSystemDao, TbMkLocalFanSystem> implements TbMkLocalFanSystemService {

    @Resource
    private TbMkLocalFanSystemConvert tbMkLocalFanSystemConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkLocalFanSystemCreateVO createVO) {
        TbMkLocalFanSystem tbMkLocalFanSystem = tbMkLocalFanSystemConvert.convert(createVO);
        baseMapper.insert(tbMkLocalFanSystem);
        return tbMkLocalFanSystem.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkLocalFanSystemUpdateVO updateVO) {
        TbMkLocalFanSystem tbMkLocalFanSystem = tbMkLocalFanSystemConvert.convert(updateVO);
        return baseMapper.updateById(tbMkLocalFanSystem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkLocalFanSystem get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkLocalFanSystemDTO> page(TbMkLocalFanSystemPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkLocalFanSystem> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkLocalFanSystem> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.like(StringUtils.isNotBlank(param.getSystemName()), TbMkLocalFanSystem::getSystemName, param.getSystemName());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getLocation()), TbMkLocalFanSystem::getLocation, param.getLocation());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getRemarks()), TbMkLocalFanSystem::getRemarks, param.getRemarks());
        queryChainWrapper.orderByDesc(TbMkLocalFanSystem::getId);
        return queryChainWrapper.page(iPage).convert(tbMkLocalFanSystemConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkLocalFanSystemCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 局扇控制系统：传入的列表为空");
            return 0;
        }
        List<TbMkLocalFanSystem> entities = createVOs.stream()
                .map(tbMkLocalFanSystemConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 局扇控制系统 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkLocalFanSystemUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 局扇控制系统：传入的列表为空");
            return 0;
        }
        List<TbMkLocalFanSystem> entities = updateVOs.stream()
                .map(tbMkLocalFanSystemConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 局扇控制系统：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 局扇控制系统 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
