package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkUserOrgTypeDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserOrgType;
import com.yhd.admin.api.domain.operation.query.MkUserOrgTypeParam;
import com.yhd.admin.common.domain.query.BatchParam;

import java.util.List;

/**
 * 运维配置中心-单位类型-Service
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */
public interface MkUserOrgTypeService extends IService<MkUserOrgType> {

    IPage<MkUserOrgTypeDTO> pagingQuery(MkUserOrgTypeParam queryParam);

     Boolean add(MkUserOrgTypeParam param);

    Boolean modify(MkUserOrgTypeParam param);

    Boolean removeBatch(BatchParam param);

    MkUserOrgTypeDTO getCurrentDetail(MkUserOrgTypeParam param);

    List<MkUserOrgTypeDTO> getList(MkUserOrgTypeParam param);
}
