package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.emergency.MkEmergencyHistoryDao;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyHistoryConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyHistoryDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyHistory;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyHistoryParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 应急历史事件记录表 Service实现类
 * <AUTHOR>
 */
@Service
public class MkEmergencyHistoryServiceImpl extends ServiceImpl<MkEmergencyHistoryDao, MkEmergencyHistory>
        implements MkEmergencyHistoryService {

    @Resource
    private MkEmergencyHistoryConvert convert;

    @Override
    public IPage<MkEmergencyHistoryDTO> pagingQuery(MkEmergencyHistoryParam param) {
        IPage<MkEmergencyHistory> iPage = new Page<>(param.getCurrent(), param.getPageSize());

        LambdaQueryChainWrapper<MkEmergencyHistory> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);

        // 组织编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getOrgCode()),
                MkEmergencyHistory::getOrgCode, param.getOrgCode());

        // 事件编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getEventCode()),
                MkEmergencyHistory::getEventCode, param.getEventCode());

        // 事件名称模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getEventName()),
                MkEmergencyHistory::getEventName, param.getEventName());

        // 事件位置模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getEventLocation()),
                MkEmergencyHistory::getEventLocation, param.getEventLocation());

        // 事件类型查询
        queryChainWrapper.eq(param.getEventType() != null,
                MkEmergencyHistory::getEventType, param.getEventType());

        // 事件状态查询
        queryChainWrapper.eq(param.getEventStatus() != null,
                MkEmergencyHistory::getEventStatus, param.getEventStatus());

        // 事件等级查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getEventLevel()),
                MkEmergencyHistory::getEventLevel, param.getEventLevel());

        // 处理人模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getHandler()),
                MkEmergencyHistory::getHandler, param.getHandler());

        // 联系电话查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getContactPhone()),
                MkEmergencyHistory::getContactPhone, param.getContactPhone());

        // 事件日期范围查询（基于事件时间）
        if (param.getEventDateBegin() != null) {
            queryChainWrapper.ge(MkEmergencyHistory::getEventTime,
                    param.getEventDateBegin().atStartOfDay());
        }
        if (param.getEventDateEnd() != null) {
            queryChainWrapper.le(MkEmergencyHistory::getEventTime,
                    param.getEventDateEnd().atTime(23, 59, 59));
        }

        // 排序：按事件时间倒序
        queryChainWrapper.orderByDesc(MkEmergencyHistory::getEventTime);

        return queryChainWrapper.page(iPage).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkEmergencyHistoryParam param) {
        // 必填字段校验
        if (StringUtils.isBlank(param.getEventCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getEventName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getEventLocation())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (param.getEventType() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (param.getEventTime() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        return super.save(convert.toEntity(param));
    }

    @Override
    public Boolean modify(MkEmergencyHistoryParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        // 必填字段校验
        if (StringUtils.isBlank(param.getEventCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getEventName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getEventLocation())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (param.getEventType() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (param.getEventTime() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        return super.updateById(convert.toEntity(param));
    }

    @Override
    public MkEmergencyHistoryDTO getCurrentDetail(MkEmergencyHistoryParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        MkEmergencyHistory entity = super.getById(param.getId());
        return convert.toDTO(entity);
    }

    @Override
    public Boolean removeBatch(BatchParam param) {
        if (param.getId() == null || param.getId().isEmpty()) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        return super.removeByIds(param.getId());
    }
}
