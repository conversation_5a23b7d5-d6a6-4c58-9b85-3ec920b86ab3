package com.yhd.admin.api.domain.emergency.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencyDeviceDTO.java
 * @Description 应急救援设备信息传输对象
 * @createTime 2025年07月30日 17:45:00
 */
@Data
public class MkEmergencyDeviceDTO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备位置
     */
    private String location;

    /**
     * 设备状态：1-正常，2-异常，3-离线
     */
    private Integer status;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 当前实时值
     */
    private BigDecimal currentValue;

    /**
     * 最小阈值
     */
    private BigDecimal thresholdMin;

    /**
     * 最大阈值
     */
    private BigDecimal thresholdMax;

    /**
     * 测量单位
     */
    private String unit;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 安装时间
     */
    private LocalDateTime installTime;

    /**
     * 最后检修时间
     */
    private LocalDateTime lastMaintenanceTime;

    /**
     * 设备型号
     */
    private String model;

    /**
     * 制造商
     */
    private String manufacturer;

    /**
     * 设备描述
     */
    private String description;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
