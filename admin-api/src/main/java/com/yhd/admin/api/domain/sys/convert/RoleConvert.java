package com.yhd.admin.api.domain.sys.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.yhd.admin.api.domain.sys.dto.RoleDTO;
import com.yhd.admin.api.domain.sys.entity.SysRole;
import com.yhd.admin.api.domain.sys.query.RoleParam;
import com.yhd.admin.api.domain.sys.vo.RoleVO;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName RoleConvert.java @Description TODO
 * @createTime 2020年03月30日 16:49:00
 */
@Mapper(componentModel = "spring")
public interface RoleConvert {

    @Mapping(target = "authority", ignore = true)
    RoleDTO toDTO(SysRole role);

    RoleVO toVO(RoleDTO roleDTO);

    List<RoleVO> toVO(List<RoleDTO> roleDTO);

    List<RoleDTO> toDTO(List<SysRole> roleList);

    SysRole toEntity(RoleDTO roleDTO);

    SysRole toEntity(RoleParam roleParam);
}
