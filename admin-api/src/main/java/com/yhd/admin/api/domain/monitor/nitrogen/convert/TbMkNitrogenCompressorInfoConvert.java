package com.yhd.admin.api.domain.monitor.nitrogen.convert;

import com.yhd.admin.api.domain.monitor.nitrogen.dto.TbMkNitrogenCompressorInfoDTO;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenCompressorInfo;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.TbMkNitrogenCompressorInfoVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenCompressorInfoCreateVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.update.TbMkNitrogenCompressorInfoUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 制氮系统空压机基本信息表
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkNitrogenCompressorInfoConvert {

    @Mapping(target = "id", ignore = true)
    TbMkNitrogenCompressorInfo convert(TbMkNitrogenCompressorInfoCreateVO createVO);

    TbMkNitrogenCompressorInfo convert(TbMkNitrogenCompressorInfoUpdateVO updateVO);

    TbMkNitrogenCompressorInfoVO convert(TbMkNitrogenCompressorInfo po);

    TbMkNitrogenCompressorInfoDTO toDTO(TbMkNitrogenCompressorInfo po);



}
