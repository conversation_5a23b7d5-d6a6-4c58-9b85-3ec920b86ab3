package com.yhd.admin.api.domain.sys.vo;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import com.yhd.admin.common.domain.vo.BaseVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class UserAccountVo extends BaseVO implements Serializable, Cloneable {

    private String uid;

    /** 登录名称 */
    private String username;
    /** 密码 */
    @JsonProperty(access = Access.WRITE_ONLY)
    private String password;
    /** 账户未过期 */
    private Boolean isAccountNonExpired;
    /** 账户未锁定 */
    private Boolean isAccountNonLocked;
    /** 密码未过期 */
    private Boolean isCredentialsNonExpired;

    /** 账户状态 */
    private Boolean isEnable;

    /** 是否绑定人员信息 */
    private Boolean isBind;

    /** 角色ID */
    private List<Long> role;
}
