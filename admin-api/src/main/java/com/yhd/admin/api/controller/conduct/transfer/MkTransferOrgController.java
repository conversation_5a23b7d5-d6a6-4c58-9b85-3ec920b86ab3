package com.yhd.admin.api.controller.conduct.transfer;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.transfer.MkTransferOrgConvert;
import com.yhd.admin.api.domain.conduct.dto.transfer.MkTransferOrgDTO;
import com.yhd.admin.api.domain.conduct.query.transfer.MkTransferOrgParam;
import com.yhd.admin.api.service.conduct.transfer.MkTransferOrgService;
import com.yhd.admin.api.domain.conduct.vo.transfer.MkTransferOrgVO;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 收发文部门表控制层
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
@RestController
@RequestMapping("/conduct/transfer/org")
public class MkTransferOrgController {

    public final MkTransferOrgService mkTransferOrgService;
    public final MkTransferOrgConvert mkTransferOrgConvert;

    public MkTransferOrgController(
        MkTransferOrgService mkTransferOrgService,
        MkTransferOrgConvert mkTransferOrgConvert
    ) {
        this.mkTransferOrgService = mkTransferOrgService;
        this.mkTransferOrgConvert = mkTransferOrgConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkTransferOrgParam param) {
        IPage<MkTransferOrgDTO> page = mkTransferOrgService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkTransferOrgConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkTransferOrgParam param) {
        return RespJson.success(mkTransferOrgConvert.toVOList(mkTransferOrgService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkTransferOrgVO> getCurrentDetails(@RequestBody MkTransferOrgParam param) {
        return RespJson.success(mkTransferOrgConvert.toVO(mkTransferOrgService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkTransferOrgParam param) {
        Boolean retVal = mkTransferOrgService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkTransferOrgParam param) {
        Boolean retVal = mkTransferOrgService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

