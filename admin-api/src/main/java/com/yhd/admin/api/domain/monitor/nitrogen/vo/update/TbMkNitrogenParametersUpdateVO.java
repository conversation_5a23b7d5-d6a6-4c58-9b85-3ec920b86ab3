package com.yhd.admin.api.domain.monitor.nitrogen.vo.update;

import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenParametersCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 制氮系统参数表
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkNitrogenParametersUpdateVO extends TbMkNitrogenParametersCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
