package com.yhd.admin.api.domain.da.convert;

import com.yhd.admin.api.domain.da.dto.MkReportDayElectricDTO;
import com.yhd.admin.api.domain.da.entity.MkReportDayElectric;
import com.yhd.admin.api.domain.da.query.MkReportDayElectricParam;
import com.yhd.admin.api.domain.da.vo.MkReportDayElectricVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MkReportDayElectricConvert {
  MkReportDayElectricDTO toDTO(MkReportDayElectric entity);

  MkReportDayElectricVO toVO(MkReportDayElectricDTO dto);

  List<MkReportDayElectricVO> toVO(List<MkReportDayElectricDTO> dtoList);

  MkReportDayElectric toEntity(MkReportDayElectricParam param);
}
