package com.yhd.admin.api.domain.conduct.dto.base;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 采煤队(MkMiningTeamInfo)DTO
 *
 * <AUTHOR>
 * @since 2025-07-28 09:25:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkMiningTeamInfoDTO extends BaseDTO implements Serializable {
    /** Id */
    private Long id;
    /** 所属煤矿 */
    private String orgName;
    /** 组织机构编码 */
    private String orgCode;
    /** 名称 */
    private String name;
    /** 所属单位 */
    private String company;
    /** 队组类型名称 */
    private String typeName;
    /** 队组类型代码 */
    private String typeCode;
    /** 成员人数 */
    private Integer memberNumber;
    /** 管理人数 */
    private Integer managementNumber;
    /** 队长姓名 */
    private String leaderName;
    /** 队长账号 */
    private String leaderAccount;
    /** 技术员姓名 */
    private String technicianName;
    /** 技术员账号 */
    private String technicianAccount;
    /** 工作面名称 */
    private String workFaceName;
    /** 工作面 Id */
    private Long workFaceId;
}

