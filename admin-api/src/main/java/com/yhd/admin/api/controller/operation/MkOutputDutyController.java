package com.yhd.admin.api.controller.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.operation.convert.MkOutputDutyConvert;
import com.yhd.admin.api.domain.operation.dto.MkOutputDutyDTO;
import com.yhd.admin.api.domain.operation.query.MkOutputDutyParam;
import com.yhd.admin.api.domain.operation.vo.MkOutputDutyVO;
import com.yhd.admin.api.service.operation.MkOutputDutyService;
import com.yhd.admin.common.domain.query.BatchParam;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 生产录入-值班人员表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@RestController
@RequestMapping("/output/duty")
public class MkOutputDutyController {

    @Resource
    private MkOutputDutyConvert convert;

    @Resource
    private MkOutputDutyService service;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkOutputDutyVO> pagingQuery(@RequestBody MkOutputDutyParam queryParam) {
        IPage<MkOutputDutyDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MkOutputDutyParam param) {
        return RespJson.success(convert.toVO(service.getCurrentDetail(param)));
    }

    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson add(@RequestBody MkOutputDutyParam param) {
        Boolean retVal = service.add(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(
        value = "/modify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody MkOutputDutyParam param) {
        Boolean retVal = service.modify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam param) {
        Boolean retVal = service.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }
}
