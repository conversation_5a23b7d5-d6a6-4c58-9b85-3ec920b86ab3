package com.yhd.admin.api.controller.conduct.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.query.base.MkLevelInfoParam;
import com.yhd.admin.api.service.conduct.base.MkLevelInfoService;
import com.yhd.admin.api.domain.conduct.convert.base.MkLevelInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.base.MkLevelInfoDTO;
import com.yhd.admin.api.domain.conduct.vo.base.MkLevelInfoVO;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 水平信息表控制层
 *
 * <AUTHOR>
 * @since 2025-07-25 10:34:53
 * @module 水平基础信息
 */
@RestController
@RequestMapping("/conduct/level")
public class MkLevelInfoController {

    public final MkLevelInfoService mkLevelInfoService;
    public final MkLevelInfoConvert mkLevelInfoConvert;

    public MkLevelInfoController(
        MkLevelInfoService mkLevelInfoService,
        MkLevelInfoConvert mkLevelInfoConvert
    ) {
        this.mkLevelInfoService = mkLevelInfoService;
        this.mkLevelInfoConvert = mkLevelInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkLevelInfoParam param) {
        IPage<MkLevelInfoDTO> page = mkLevelInfoService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkLevelInfoConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkLevelInfoParam param) {
        return RespJson.success(mkLevelInfoConvert.toVOList(mkLevelInfoService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkLevelInfoVO> getCurrentDetails(@RequestBody MkLevelInfoParam param) {
        return RespJson.success(mkLevelInfoConvert.toVO(mkLevelInfoService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkLevelInfoParam param) {
        Boolean retVal = mkLevelInfoService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkLevelInfoParam param) {
        Boolean retVal = mkLevelInfoService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

