package com.yhd.admin.api.domain.conduct.convert.resource;

import com.yhd.admin.api.domain.conduct.dto.resource.MkWorkFaceInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.resource.MkWorkFaceInfo;
import com.yhd.admin.api.domain.conduct.query.resource.MkWorkFaceInfoParam;
import com.yhd.admin.api.domain.conduct.vo.resource.MkWorkFaceInfoVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 工作面信息(MkWorkFaceInfo)Convert
 *
 * <AUTHOR>
 * @since 2025-07-28 08:21:47
 */
@Mapper(componentModel = "spring")
public interface MkWorkFaceInfoConvert {
    MkWorkFaceInfo toEntity(MkWorkFaceInfoParam param);

    MkWorkFaceInfoDTO toDTO(MkWorkFaceInfo entity);

    MkWorkFaceInfoVO toVO(MkWorkFaceInfoDTO dto);

    List<MkWorkFaceInfoDTO> toDTOList(List<MkWorkFaceInfo> entityList);

    List<MkWorkFaceInfoVO> toVOList(List<MkWorkFaceInfoDTO> dtoList);
}

