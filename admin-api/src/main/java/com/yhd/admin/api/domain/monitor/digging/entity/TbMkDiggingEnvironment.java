package com.yhd.admin.api.domain.monitor.digging.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 掘进机环境参数
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_mk_digging_environment")
public class TbMkDiggingEnvironment extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联掘进机基本信息表ID
     */
    @TableField("digging_id")
    private Long diggingId;

    /**
     * 人员数量
     */
    @TableField("personnel_count")
    private Integer personnelCount;

    /**
     * 粉尘浓度(mg/m³)
     */
    @TableField("dust_concentration")
    private BigDecimal dustConcentration;

    /**
     * 瓦斯浓度(ppm)
     */
    @TableField("methane_concentration")
    private BigDecimal methaneConcentration;

    /**
     * 温度(℃)
     */
    @TableField("temperature")
    private BigDecimal temperature;

    /**
     * 风筒风速(m/s)
     */
    @TableField("wind_speed")
    private BigDecimal windSpeed;
}
