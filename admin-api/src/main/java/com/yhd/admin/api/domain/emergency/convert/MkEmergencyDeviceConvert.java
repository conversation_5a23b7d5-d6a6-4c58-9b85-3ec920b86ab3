package com.yhd.admin.api.domain.emergency.convert;

import com.yhd.admin.api.domain.emergency.dto.MkEmergencyDeviceDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyDevice;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyDeviceParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyDeviceVO;
import org.mapstruct.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencyDeviceConvert.java
 * @Description 应急救援设备信息转换器
 * @createTime 2025年07月30日 17:15:00
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface MkEmergencyDeviceConvert {

    /**
     * 将实体转换为视图对象
     *
     * @param entity 实体
     * @return 视图对象
     */
    @Mapping(source = "status", target = "statusName", qualifiedByName = "statusToStatusName")
    MkEmergencyDeviceVO toVO(MkEmergencyDevice entity);

    /**
     * 将实体转换为DTO对象
     *
     * @param entity 实体
     * @return DTO对象
     */
    MkEmergencyDeviceDTO toDTO(MkEmergencyDevice entity);

    /**
     * 将DTO对象转换为实体
     *
     * @param dto DTO对象
     * @return 实体
     */
    MkEmergencyDevice toEntity(MkEmergencyDeviceDTO dto);

    /**
     * 将参数对象转换为实体
     *
     * @param param 参数对象
     * @return 实体
     */
    @Mapping(target = "installTime", ignore = true)
    @Mapping(target = "lastMaintenanceTime", ignore = true)
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "remark", ignore = true)
    MkEmergencyDevice toEntity(MkEmergencyDeviceParam param);

    /**
     * 更新实体
     *
     * @param param  参数对象
     * @param entity 实体对象
     */
    @Mapping(target = "installTime", ignore = true)
    @Mapping(target = "lastMaintenanceTime", ignore = true)
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "remark", ignore = true)
    void updateEntity(MkEmergencyDeviceParam param, @MappingTarget MkEmergencyDevice entity);

    /**
     * 状态转换为状态名称
     *
     * @param status 状态
     * @return 状态名称
     */
    @Named("statusToStatusName")
    default String statusToStatusName(Integer status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case 1:
                return "正常";
            case 2:
                return "异常";
            case 3:
                return "离线";
            default:
                return "未知状态";
        }
    }
}
