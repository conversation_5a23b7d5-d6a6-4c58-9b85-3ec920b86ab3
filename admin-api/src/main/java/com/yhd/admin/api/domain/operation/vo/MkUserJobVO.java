package com.yhd.admin.api.domain.operation.vo;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 运维配置中心-职务/工种管理-职务/工种管理表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkUserJobVO extends BaseVO implements Serializable {
    /**
     * Id
     */
    private Long id;

    /**
     * 职务/工种编码
     */
    private String jobCode;

    /**
     * 职务/工种名称
     */
    private String jobName;

    /**
     * 单位ID
     */
    private String orgId;

    /**
     * 单位名称
     */
    private String orgName;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;
}
