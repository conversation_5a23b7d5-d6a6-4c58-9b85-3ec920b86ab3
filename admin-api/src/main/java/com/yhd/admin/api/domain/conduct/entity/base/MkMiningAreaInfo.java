package com.yhd.admin.api.domain.conduct.entity.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采区信息(MkMiningAreaInfo)实体类
 *
 * <AUTHOR>
 * @since 2025-07-28 09:25:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkMiningAreaInfo extends BaseEntity implements Serializable {

    /** Id */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 所属煤矿 */
    private String orgName;
    /** 组织机构编码 */
    private String orgCode;
    /** 采区名称 */
    private String name;
    /** 煤层名称 */
    private String coalName;
    /** 煤层 Id */
    private Long coalId;
    /** 水平名称 */
    private String levelName;
    /** 水平 id */
    private Long levelId;
    /** 可采储量 */
    private BigDecimal validReserves;
    /** 采高 */
    private BigDecimal miningHeight;
}

