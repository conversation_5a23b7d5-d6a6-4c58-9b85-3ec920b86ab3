package com.yhd.admin.api.service.operation;

import com.yhd.admin.api.dao.operation.MkOutputInfoDao;
import com.yhd.admin.api.domain.operation.convert.MkOutputInfoConvert;
import com.yhd.admin.api.domain.operation.dto.MkOutputInfoDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputInfo;
import com.yhd.admin.api.domain.operation.query.MkOutputInfoParam;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;


import java.util.List;

/**
 * 生产录入-产量信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@Service
public class MkOutputInfoServiceImpl extends ServiceImpl<MkOutputInfoDao, MkOutputInfo> implements MkOutputInfoService {

    @Resource
    private MkOutputInfoConvert convert;

    @Resource
    private MkOutputInfoService service;

    @Override
    public IPage<MkOutputInfoDTO> pagingQuery(MkOutputInfoParam queryParam) {
        Page<MkOutputInfo> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkOutputInfo> queryChain = new LambdaQueryChainWrapper<>(baseMapper);

        queryChain.orderByDesc(MkOutputInfo::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkOutputInfoParam param) {
        MkOutputInfo entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MkOutputInfoParam param) {
        MkOutputInfo entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MkOutputInfoDTO getCurrentDetail(MkOutputInfoParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

}
