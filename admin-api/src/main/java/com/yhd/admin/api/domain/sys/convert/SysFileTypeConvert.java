package com.yhd.admin.api.domain.sys.convert;

import com.yhd.admin.api.domain.sys.dto.SysFileTypeDTO;
import com.yhd.admin.api.domain.sys.entity.SysFileType;
import com.yhd.admin.api.domain.sys.query.SysFileTypeParam;
import com.yhd.admin.api.domain.sys.vo.SysFileTypeVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/** FileType 转换类 */
@Mapper(componentModel = "spring")
public interface SysFileTypeConvert {
  SysFileType toEntity(SysFileTypeParam param);

  @Mappings({@Mapping(target = "children", ignore = true)})
  SysFileTypeDTO toDTO(SysFileType entity);

  SysFileTypeVO toVO(SysFileTypeDTO dto);

  List<SysFileTypeVO> toVO(List<SysFileTypeDTO> dtoList);
}
