package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 综合决策风险管控清单表
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_mk_risk_management_list")
public class MkRiskManagementList {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 清单编码
     */
    private String itemCode;

    /**
     * 风险描述
     */
    private String riskDescription;

    /**
     * 风险等级
     */
    private String riskLevel;

    /**
     * 风险等级颜色
     */
    private String riskLevelColor;

    /**
     * 责任人
     */
    private String responsiblePerson;

    /**
     * 部门
     */
    private String department;

    /**
     * 管控措施
     */
    private String controlMeasures;

    /**
     * 完成期限
     */
    private LocalDate dueDate;

    /**
     * 完成状态
     */
    private String completionStatus;

    /**
     * 录入时间
     */
    private LocalDateTime entryTime;

    /**
     * 组织机构编码
     */
    private String orgCode;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}