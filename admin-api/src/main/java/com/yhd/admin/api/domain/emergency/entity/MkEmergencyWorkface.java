package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 应急救援矿井工作面信息表
 * 
 * <AUTHOR>
 */
@Data
@TableName("tb_mk_emergency_workface")
public class MkEmergencyWorkface implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 工作面编码
     */
    private String workfaceCode;

    /**
     * 工作面名称
     */
    private String workfaceName;

    /**
     * 工作面类型：采空区、回采工作面等
     */
    private String workfaceType;

    /**
     * 区域层级
     */
    private Integer areaLevel;

    /**
     * X坐标
     */
    private BigDecimal coordinateX;

    /**
     * Y坐标
     */
    private BigDecimal coordinateY;

    /**
     * Z坐标（高度）
     */
    private BigDecimal coordinateZ;

    /**
     * 宽度
     */
    private BigDecimal width;

    /**
     * 长度
     */
    private BigDecimal length;

    /**
     * 高度
     */
    private BigDecimal height;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 层级路径
     */
    private String levelPath;

    /**
     * 是否活跃：0-非活跃，1-活跃
     */
    private Boolean isActive;

    /**
     * 安全状态：0-危险，1-正常，2-警告
     */
    private Integer safetyStatus;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 组织机构编码
     */
    private String orgCode;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}