package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.safe.MkZhSwDao;
import com.yhd.admin.api.domain.safe.convert.MkZhSwConvert;
import com.yhd.admin.api.domain.safe.dto.MkZhSwDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhSw;
import com.yhd.admin.api.domain.safe.query.MkZhSwParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;


@Service
public class MkZhSwServiceImpl extends ServiceImpl<MkZhSwDao, MkZhSw> implements MkZhSwService {

    private final MkZhSwConvert convert;

    public MkZhSwServiceImpl(MkZhSwConvert convert) {
        this.convert = convert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addOrModify(MkZhSwParam param) {
        MkZhSw entity = convert.toEntity(param);
        return super.saveOrUpdate(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(MkZhSwParam param) {
        return super.removeById(param.getId());
    }

    @Override
    public MkZhSwDTO getCurrentDetail(MkZhSwParam param) {
        MkZhSwDTO result;
        if (Objects.isNull(param.getId())) {
            throw new BMSException(ExceptionEnum.MISS_PARAM_EXCEPTION);
        }
        MkZhSw entity = super.getById(param);
        result = convert.toDTO(entity);
        return result;
    }

    @Override
    public IPage<MkZhSwDTO> pagingQuery(MkZhSwParam param) {
        IPage<MkZhSw> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkZhSw> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        // 排序
        wrapper.orderByAsc(MkZhSw::getUpdatedTime);
        IPage<MkZhSw> iPage = wrapper.page(page);
        return iPage.convert(convert::toDTO);
    }

    @Override
    public MkZhSwDTO getData() {
        LambdaQueryChainWrapper<MkZhSw> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return convert.toDTO(wrapper.list().get(0));
    }
}
