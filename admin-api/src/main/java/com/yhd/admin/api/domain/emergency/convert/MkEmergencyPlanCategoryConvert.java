package com.yhd.admin.api.domain.emergency.convert;

import com.yhd.admin.api.domain.emergency.dto.MkEmergencyPlanCategoryDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyPlanCategory;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyPlanCategoryParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyPlanCategoryVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 应急预案分类表转换器
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
@Component
public interface MkEmergencyPlanCategoryConvert {

    /**
     * Entity转DTO
     * @param entity 实体
     * @return DTO
     */
    @Mapping(target = "children", ignore = true)
    MkEmergencyPlanCategoryDTO toDTO(MkEmergencyPlanCategory entity);

    /**
     * Entity列表转DTO列表
     * @param entityList 实体列表
     * @return DTO列表
     */
    List<MkEmergencyPlanCategoryDTO> toDTO(List<MkEmergencyPlanCategory> entityList);

    /**
     * DTO转VO
     * @param dto DTO
     * @return VO
     */
    @Mapping(target = "statusName", expression = "java(getStatusName(dto.getStatus()))")
    MkEmergencyPlanCategoryVO toVO(MkEmergencyPlanCategoryDTO dto);

    /**
     * DTO列表转VO列表
     * @param dtoList DTO列表
     * @return VO列表
     */
    List<MkEmergencyPlanCategoryVO> toVO(List<MkEmergencyPlanCategoryDTO> dtoList);

    /**
     * Param转Entity
     * @param param 参数
     * @return 实体
     */
    MkEmergencyPlanCategory toEntity(MkEmergencyPlanCategoryParam param);

    /**
     * 获取状态名称
     * @param status 状态
     * @return 状态名称
     */
    default String getStatusName(Integer status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 1:
                return "启用";
            case 0:
                return "禁用";
            default:
                return "未知";
        }
    }
}
