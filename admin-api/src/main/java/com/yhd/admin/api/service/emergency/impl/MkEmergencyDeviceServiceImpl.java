package com.yhd.admin.api.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.emergency.MkEmergencyDeviceDao;
import com.yhd.admin.api.dao.emergency.MkEmergencyDeviceHistoryDao;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyDeviceConvert;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyDevice;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyDeviceHistory;
import com.yhd.admin.api.domain.emergency.query.DeviceRealtimeChartParam;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyDeviceParam;
import com.yhd.admin.api.domain.emergency.vo.DeviceRealtimeChartVO;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.emergency.MkEmergencyDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencyDeviceServiceImpl.java
 * @Description 应急救援设备信息Service实现类
 * @createTime 2025年07月30日 17:30:00
 */
@Slf4j
@Service
public class MkEmergencyDeviceServiceImpl extends ServiceImpl<MkEmergencyDeviceDao, MkEmergencyDevice>
        implements MkEmergencyDeviceService {

    private final MkEmergencyDeviceConvert convert;
    private final MkEmergencyDeviceHistoryDao historyDao;

    public MkEmergencyDeviceServiceImpl(MkEmergencyDeviceConvert convert, MkEmergencyDeviceHistoryDao historyDao) {
        this.convert = convert;
        this.historyDao = historyDao;
    }

    @Override
    public IPage<MkEmergencyDevice> pagingQuery(MkEmergencyDeviceParam param) {
        LambdaQueryWrapper<MkEmergencyDevice> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        if (StringUtils.isNotBlank(param.getDeviceCode())) {
            queryWrapper.like(MkEmergencyDevice::getDeviceCode, param.getDeviceCode());
        }

        if (StringUtils.isNotBlank(param.getDeviceName())) {
            queryWrapper.like(MkEmergencyDevice::getDeviceName, param.getDeviceName());
        }

        if (StringUtils.isNotBlank(param.getDeviceType())) {
            queryWrapper.eq(MkEmergencyDevice::getDeviceType, param.getDeviceType());
        }

        if (StringUtils.isNotBlank(param.getLocation())) {
            queryWrapper.like(MkEmergencyDevice::getLocation, param.getLocation());
        }

        if (param.getStatus() != null) {
            queryWrapper.eq(MkEmergencyDevice::getStatus, param.getStatus());
        }

        if (StringUtils.isNotBlank(param.getOrgCode())) {
            queryWrapper.eq(MkEmergencyDevice::getOrgCode, param.getOrgCode());
        }

        if (param.getInstallTimeStart() != null) {
            queryWrapper.ge(MkEmergencyDevice::getInstallTime, param.getInstallTimeStart());
        }

        if (param.getInstallTimeEnd() != null) {
            queryWrapper.le(MkEmergencyDevice::getInstallTime, param.getInstallTimeEnd());
        }

        if (StringUtils.isNotBlank(param.getModel())) {
            queryWrapper.like(MkEmergencyDevice::getModel, param.getModel());
        }

        if (StringUtils.isNotBlank(param.getManufacturer())) {
            queryWrapper.like(MkEmergencyDevice::getManufacturer, param.getManufacturer());
        }

        // 排序
        queryWrapper.orderByDesc(MkEmergencyDevice::getCreatedTime);

        // 分页
        Page<MkEmergencyDevice> page = new Page<>(param.getCurrent(), param.getPageSize());

        return page(page, queryWrapper);
    }

    @Override
    public MkEmergencyDevice getById(Integer id) {
        if (id == null) {
            throw new BMSException("0002", "设备ID不能为空");
        }
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(MkEmergencyDeviceParam param) {
        // 参数校验
        if (StringUtils.isBlank(param.getDeviceCode())) {
            throw new BMSException("0002", "设备编码不能为空");
        }
        if (StringUtils.isBlank(param.getDeviceName())) {
            throw new BMSException("0002", "设备名称不能为空");
        }
        if (StringUtils.isBlank(param.getDeviceType())) {
            throw new BMSException("0002", "设备类型不能为空");
        }
        if (StringUtils.isBlank(param.getLocation())) {
            throw new BMSException("0002", "设备位置不能为空");
        }
        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException("0002", "组织编码不能为空");
        }

        // 检查设备编码是否存在
        LambdaQueryWrapper<MkEmergencyDevice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkEmergencyDevice::getDeviceCode, param.getDeviceCode());
        if (count(queryWrapper) > 0) {
            throw new BMSException("0005", "设备编码已存在");
        }

        // 转换为实体
        MkEmergencyDevice entity = convert.toEntity(param);

        // 设置默认值
        if (entity.getStatus() == null) {
            entity.setStatus(1);
        }

        return save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean modify(MkEmergencyDeviceParam param) {
        // 参数校验
        if (param.getId() == null) {
            throw new BMSException("0002", "设备ID不能为空");
        }

        // 检查ID是否存在
        MkEmergencyDevice existDevice = getById(param.getId());
        if (existDevice == null) {
            throw new BMSException("0002", "设备不存在");
        }

        // 检查设备编码是否存在
        if (StringUtils.isNotBlank(param.getDeviceCode())) {
            LambdaQueryWrapper<MkEmergencyDevice> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MkEmergencyDevice::getDeviceCode, param.getDeviceCode())
                    .ne(MkEmergencyDevice::getId, param.getId());
            if (count(queryWrapper) > 0) {
                throw new BMSException("0005", "设备编码已存在");
            }
        }

        // 转换为实体
        MkEmergencyDevice entity = convert.toEntity(param);

        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(Integer id) {
        if (id == null) {
            throw new BMSException("0002", "设备ID不能为空");
        }

        // 检查ID是否存在
        MkEmergencyDevice existDevice = getById(id);
        if (existDevice == null) {
            throw new BMSException("0002", "设备不存在");
        }

        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatch(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BMSException("0002", "设备ID不能为空");
        }

        return removeByIds(ids);
    }

    @Override
    public DeviceRealtimeChartVO getRealtimeChart(DeviceRealtimeChartParam param) {
        // 参数校验
        if (StringUtils.isBlank(param.getDeviceCode())) {
            throw new BMSException("0002", "设备编码不能为空");
        }

        // 获取设备信息
        LambdaQueryWrapper<MkEmergencyDevice> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.eq(MkEmergencyDevice::getDeviceCode, param.getDeviceCode());
        if (StringUtils.isNotBlank(param.getOrgCode())) {
            deviceWrapper.eq(MkEmergencyDevice::getOrgCode, param.getOrgCode());
        }
        MkEmergencyDevice device = getOne(deviceWrapper);
        if (device == null) {
            throw new BMSException("0002", "设备不存在");
        }

        // 计算时间范围
        LocalDateTime startTime = param.getStartTime();
        LocalDateTime endTime = param.getEndTime();

        if (param.getTimeRangeType() != null && param.getTimeRangeType() != 5) {
            endTime = LocalDateTime.now();
            switch (param.getTimeRangeType()) {
                case 1: // 最近1小时
                    startTime = endTime.minusHours(1);
                    break;
                case 2: // 最近6小时
                    startTime = endTime.minusHours(6);
                    break;
                case 3: // 最近24小时
                    startTime = endTime.minusHours(24);
                    break;
                case 4: // 最近7天
                    startTime = endTime.minusDays(7);
                    break;
                default:
                    startTime = endTime.minusHours(24); // 默认24小时
            }
        }

        // 如果没有指定时间范围，默认查询最近24小时
        if (startTime == null || endTime == null) {
            endTime = LocalDateTime.now();
            startTime = endTime.minusHours(24);
        }

        // 查询历史数据
        LambdaQueryWrapper<MkEmergencyDeviceHistory> historyWrapper = new LambdaQueryWrapper<>();
        historyWrapper.eq(MkEmergencyDeviceHistory::getDeviceCode, param.getDeviceCode());
        historyWrapper.ge(MkEmergencyDeviceHistory::getRecordTime, startTime);
        historyWrapper.le(MkEmergencyDeviceHistory::getRecordTime, endTime);
        historyWrapper.orderByAsc(MkEmergencyDeviceHistory::getRecordTime);

        // 限制数据点数量
        if (param.getLimitCount() != null && param.getLimitCount() > 0) {
            historyWrapper.last("LIMIT " + param.getLimitCount());
        }

        List<MkEmergencyDeviceHistory> historyList = historyDao.selectList(historyWrapper);

        // 构建返回结果
        DeviceRealtimeChartVO result = new DeviceRealtimeChartVO();
        result.setDeviceCode(device.getDeviceCode());
        result.setDeviceName(device.getDeviceName());
        result.setUnit(device.getUnit());
        result.setCurrentValue(device.getCurrentValue());
        result.setThresholdMin(device.getThresholdMin());
        result.setThresholdMax(device.getThresholdMax());

        // 转换历史数据点
        List<DeviceRealtimeChartVO.ChartDataPoint> dataPoints = historyList.stream()
                .map(history -> {
                    DeviceRealtimeChartVO.ChartDataPoint point = new DeviceRealtimeChartVO.ChartDataPoint();
                    point.setTime(history.getRecordTime());
                    point.setValue(history.getCurrentValue());
                    point.setStatus(history.getStatus());
                    point.setStatusName(getStatusName(history.getStatus()));
                    return point;
                })
                .collect(Collectors.toList());

        result.setDataPoints(dataPoints);

        return result;
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return "未知状态";
        }
        switch (status) {
            case 1:
                return "正常";
            case 2:
                return "异常";
            case 3:
                return "离线";
            default:
                return "未知状态";
        }
    }
}
