package com.yhd.admin.api.domain.monitor.ventilation.vo.create;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电机运行参数
 *
 * <AUTHOR>
 * @since
 */
@Data
public class TbMkMotorOperationCreateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联电机基本信息表ID
     */
    private Long motorId;

    /**
     * 电压Uab(KV)
     */
    private BigDecimal voltageUab;

    /**
     * 电压Ubc(KV)
     */
    private BigDecimal voltageUbc;

    /**
     * 电压Uca(KV)
     */
    private BigDecimal voltageUca;

    /**
     * 电流Ia(A)
     */
    private BigDecimal currentIa;

    /**
     * 电流Ib(A)
     */
    private BigDecimal currentIb;

    /**
     * 电流Ic(A)
     */
    private BigDecimal currentIc;

    /**
     * 水平振动(mm/s)
     */
    private BigDecimal horizontalVibration;

    /**
     * 垂直振动(mm/s)
     */
    private BigDecimal verticalVibration;

    /**
     * 前轴温度(°C)
     */
    private BigDecimal frontBearingTemperature;

    /**
     * 后轴温度(°C)
     */
    private BigDecimal rearBearingTemperature;

    /**
     * 母线电压(V)
     */
    private BigDecimal busVoltage;

}
