package com.yhd.admin.api.service.sys;

import java.io.InputStream;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.common.domain.dto.UserDTO;
import com.yhd.admin.common.domain.entity.SysUser;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName UserService.java @Description TODO 用户服务类
 * @createTime 2020年04月01日 14:43:00
 */
public interface UserService extends IService<SysUser> {

    /**
     * 根据ID查询用户信息。
     *
     * @param uid
     * @return {@link UserDTO}
     */
    UserDTO getUserByUid(String uid);

    /**
     * 根据username 查询用户信息。
     *
     * @param username
     * @return
     */
    UserDTO getUserByUsername(String username);

    /**
     * 用户信息分页查询
     *
     * @param queryParam {@link UserParam}
     * @return {@link IPage<UserDTO>}
     */
    IPage<UserDTO> pagingQuery(UserParam queryParam);

    /**
     * 新增用户信息
     *
     * @param userParam {@link UserParam}
     * @return 成功 true 失败 false
     */
    Boolean add(UserParam userParam);

    /**
     * 根据ID主键修改用户信息
     *
     * @param userParam {@link UserParam}
     * @return 成功 true 失败 false
     */
    Boolean modify(UserParam userParam);

    /**
     * 批量删除用户 根据ID
     *
     * @param batchParam [id] {@link BatchParam}
     * @return 成功 true 失败 false
     */
    Boolean removeBatch(BatchParam batchParam);

    /**
     * 根据ID 查询用户详情
     *
     * @param queryParam {@link UserParam}
     * @return com.yhd.admin.bms.domain.dto.UserDTO
     * @description 根据ID 查询用户详情
     * <AUTHOR>
     * @date 2020/4/22
     */
    UserDTO obtainCurrentDetail(UserParam queryParam);

    /**
     * 校验用户名是否被占用
     *
     * @param queryParam username,uid;
     * @return Boolean
     */
    Boolean checkUsernameIfNotExist(UserParam queryParam);

    /***
     * 导出用户
     *
     * @param exportParam
     * @return
     */
    String exportUser(UserParam exportParam);

    /**
     * 生成用户导入模板。
     *
     * @return 模板下载地址。
     */
    String generationUserTemplate();

    /**
     * 导入用户
     *
     * @param inputStream
     * @return
     */
    String importUser(InputStream inputStream, Boolean isGranted, String userName);

    /**
     * 查询用户列表
     *
     * @param queryParam 查询条件
     * @return 用户列表
     */
    List<UserDTO> queryUsers(UserParam queryParam);
}
