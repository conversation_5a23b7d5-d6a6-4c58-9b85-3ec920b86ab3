package com.yhd.admin.api.domain.operation.query;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 应急演练-演练审核表查询
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MkDrillReviewParam extends QueryParam {
    
    /**
     * Id
     */
    private Long id;

    /**
     * 演练计划ID
     */
    private Long planId;

    /**
     * 计划演练时间
     */
    private LocalDateTime planTime;

    /**
     * 演练名称
     */
    private String drillName;

    /**
     * 地点
     */
    private String location;

    /**
     * 演练方式
     */
    private String method;

    /**
     * 负责人账号
     */
    private String leaderNo;

    /**
     * 负责人名称
     */
    private String leaderName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 审核状态
     */
    private Integer reviewStatus;

    /**
     * 审核人
     */
    private String reviewBy;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 审核意见
     */
    private String reviewOpinion;

    private LocalDateTime startTime;

    private LocalDateTime endTime;
} 