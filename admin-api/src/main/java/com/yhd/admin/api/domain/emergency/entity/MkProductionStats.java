package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 综合决策生产统计表
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_mk_production_stats")
public class MkProductionStats {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 统计编码
     */
    private String statsCode;

    /**
     * 统计日期
     */
    private LocalDate statsDate;

    /**
     * 日产量(吨)
     */
    private BigDecimal dailyProduction;

    /**
     * 日销量(吨)
     */
    private BigDecimal dailySales;

    /**
     * 月累计产量(万吨)
     */
    private BigDecimal monthlyProduction;

    /**
     * 月累计销量(万吨)
     */
    private BigDecimal monthlySales;

    /**
     * 年累计产量(万吨)
     */
    private BigDecimal yearlyProduction;

    /**
     * 年累计销量(万吨)
     */
    private BigDecimal yearlySales;

    /**
     * 安全生产天数
     */
    private Integer safetyDays;

    /**
     * 设计产能(万吨)
     */
    private BigDecimal designCapacity;

    /**
     * 主采煤层
     */
    private String mainCoalSeam;

    /**
     * 组织机构编码
     */
    private String orgCode;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}