package com.yhd.admin.api.domain.conduct.vo.trouble;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 隐患记录表(MkHideTroubleRecord)VO
 *
 * <AUTHOR>
 * @since 2025-08-04 10:28:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkHideTroubleRecordVO extends BaseVO implements Serializable {
    /** 隐患id */
    private Long id;
    /** 隐患名称 */
    private String name;
    /** 隐患描述 */
    private String description;
    /** 检查日期 */
    private LocalDate checkDate;
    /** 检查班次名称 */
    private String shiftName;
    /** 检查班次代码 */
    private String shiftCode;
    /** 检查部门名称 */
    private String checkDeptName;
    /** 检查部门代码 */
    private String checkDeptCode;
    /** 检查人姓名 */
    private String checkUserName;
    /** 检查人账号 */
    private String checkUserAccount;
    /** 隐患等级名称 1：重大隐患 2：较大隐患 3：中等隐患 4：一般隐患 */
    private String troubleLevelName;
    /** 隐患等级代码 TROUBLE_LEVEL */
    private String troubleLevelCode;
    /** 隐患类别名称 1：水文 2：粉尘 3：火灾 4：顶板 5：瓦斯 6：通风 */
    private String troubleTypeName;
    /** 隐患类别代码 TROUBLE_TYPE */
    private String troubleTypeCode;
    /** 责任部门名称 */
    private String respDeptName;
    /** 责任部门代码 */
    private String respDeptCode;
    /** 责任人姓名 */
    private String respUserName;
    /** 责任人账号 */
    private String respUserAccount;
    /** 整改截止日期 */
    private LocalDate respDeadline;
    /** 督办人姓名 */
    private String superviserName;
    /** 督办人账号 */
    private String superviserAccount;
    /** 催办描述 */
    private String remindDesc;
    /** 整改状态名称 1：未整改 2：已整改 3：驳回 4：整改完成 */
    private String respStatusName;
    /** 整改状态代码 TROUBLE_RESP_STATUS */
    private String respStatusCode;
    /** 整改日期 */
    private LocalDate respDate;
    /** 整改内容 */
    private String respContent;
    /** 复查意见 */
    private String reviewComment;
    /** 复查人姓名 */
    private String reviewerName;
    /** 复查人账号 */
    private String reviewerAccount;
}

