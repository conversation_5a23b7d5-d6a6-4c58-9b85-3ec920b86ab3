package com.yhd.admin.api.domain.monitor.digging.convert;

import com.yhd.admin.api.domain.monitor.digging.dto.TbMkDiggingNavigationDTO;
import com.yhd.admin.api.domain.monitor.digging.entity.TbMkDiggingNavigation;
import com.yhd.admin.api.domain.monitor.digging.vo.TbMkDiggingNavigationVO;
import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingNavigationCreateVO;
import com.yhd.admin.api.domain.monitor.digging.vo.update.TbMkDiggingNavigationUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 掘进机导航参数
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkDiggingNavigationConvert {

    @Mapping(target = "id", ignore = true)
    TbMkDiggingNavigation convert(TbMkDiggingNavigationCreateVO createVO);

    TbMkDiggingNavigation convert(TbMkDiggingNavigationUpdateVO updateVO);

    TbMkDiggingNavigationVO convert(TbMkDiggingNavigation po);

    TbMkDiggingNavigationDTO toDTO(TbMkDiggingNavigation po);



}
