package com.yhd.admin.api.domain.emergency.query;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.yhd.admin.common.domain.query.QueryParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 应急预案分类表查询参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
public class MkEmergencyPlanCategoryParam extends QueryParam implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 分类名称（模糊查询）
     */
    private String categoryName;

    /**
     * 父级ID，0表示根节点
     */
    private Integer parentId;

    /**
     * 分类层级
     */
    private Integer categoryLevel;

    /**
     * 分类路径，如：1/2/3
     */
    private String categoryPath;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 分类图标
     */
    private String categoryIcon;

    /**
     * 是否叶子节点：0-否，1-是
     */
    private Boolean isLeaf;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
}
