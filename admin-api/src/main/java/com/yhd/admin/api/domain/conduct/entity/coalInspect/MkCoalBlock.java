package com.yhd.admin.api.domain.conduct.entity.coalInspect;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 出块率曲线
 * <AUTHOR>
 */
@Data
public class MkCoalBlock {
    /** 10日出块率曲线 */
    private List<MkCoalBlockRate> tenDayBlockList;
    /** 30日出块率曲线 */
    private List<MkCoalBlockRate> thirtyDayBlockList;
    /** 12月出块率曲线 */
    private List<MkCoalBlockRate> monthlyBlockList;
    /** 昨日出块率 */
    private BigDecimal yesterdayRate;
    /** 10日均出块率 */
    private BigDecimal tenDayRate;
    /** 月均出块率 */
    private BigDecimal thirtyDayRate;
    /** 12月均出块率 */
    private BigDecimal monthlyRate;
}
