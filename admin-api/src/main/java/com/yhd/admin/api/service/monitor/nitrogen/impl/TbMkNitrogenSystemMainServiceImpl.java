package com.yhd.admin.api.service.monitor.nitrogen.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.nitrogen.TbMkNitrogenSystemMainDao;
import com.yhd.admin.api.domain.monitor.nitrogen.convert.TbMkNitrogenSystemMainConvert;
import com.yhd.admin.api.domain.monitor.nitrogen.dto.TbMkNitrogenSystemMainDTO;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenSystemMain;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenSystemMainCreateVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.page.TbMkNitrogenSystemMainPageVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.update.TbMkNitrogenSystemMainUpdateVO;
import com.yhd.admin.api.service.monitor.nitrogen.TbMkNitrogenSystemMainService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 制氮系统监控数据-主表 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkNitrogenSystemMainServiceImpl extends ServiceImpl<TbMkNitrogenSystemMainDao, TbMkNitrogenSystemMain> implements TbMkNitrogenSystemMainService {

    @Resource
    private TbMkNitrogenSystemMainConvert tbMkNitrogenSystemMainConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkNitrogenSystemMainCreateVO createVO) {
        TbMkNitrogenSystemMain tbMkNitrogenSystemMain = tbMkNitrogenSystemMainConvert.convert(createVO);
        baseMapper.insert(tbMkNitrogenSystemMain);
        return tbMkNitrogenSystemMain.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkNitrogenSystemMainUpdateVO updateVO) {
        TbMkNitrogenSystemMain tbMkNitrogenSystemMain = tbMkNitrogenSystemMainConvert.convert(updateVO);
        return baseMapper.updateById(tbMkNitrogenSystemMain);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkNitrogenSystemMain get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkNitrogenSystemMainDTO> page(TbMkNitrogenSystemMainPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkNitrogenSystemMain> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkNitrogenSystemMain> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.like(StringUtils.isNotBlank(param.getSystemName()), TbMkNitrogenSystemMain::getSystemName, param.getSystemName());
        queryChainWrapper.eq(param.getControlBoxStatus() != null, TbMkNitrogenSystemMain::getControlBoxStatus, param.getControlBoxStatus());
        queryChainWrapper.orderByDesc(TbMkNitrogenSystemMain::getId);
        return queryChainWrapper.page(iPage).convert(tbMkNitrogenSystemMainConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkNitrogenSystemMainCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 制氮系统监控数据-主表：传入的列表为空");
            return 0;
        }
        List<TbMkNitrogenSystemMain> entities = createVOs.stream()
                .map(tbMkNitrogenSystemMainConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 制氮系统监控数据-主表 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkNitrogenSystemMainUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 制氮系统监控数据-主表：传入的列表为空");
            return 0;
        }
        List<TbMkNitrogenSystemMain> entities = updateVOs.stream()
                .map(tbMkNitrogenSystemMainConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 制氮系统监控数据-主表：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 制氮系统监控数据-主表 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
