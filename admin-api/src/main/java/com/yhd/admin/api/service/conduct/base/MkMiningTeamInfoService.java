package com.yhd.admin.api.service.conduct.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.base.MkMiningTeamInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkMiningTeamInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkMiningTeamInfoParam;

import java.util.List;

/**
 * 采煤队服务接口
 *
 * <AUTHOR>
 * @since 2025-07-25 10:32:53
 */
public interface MkMiningTeamInfoService extends IService<MkMiningTeamInfo> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkMiningTeamInfoDTO> pagingQuery(MkMiningTeamInfoParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkMiningTeamInfoDTO> queryList(MkMiningTeamInfoParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkMiningTeamInfoParam param);

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    MkMiningTeamInfoDTO getCurrentDetails(MkMiningTeamInfoParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkMiningTeamInfoParam param);

}
