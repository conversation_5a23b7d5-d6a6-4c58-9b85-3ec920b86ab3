package com.yhd.admin.api.service.operation;

import com.yhd.admin.api.dao.operation.MkUserOrgDao;
import com.yhd.admin.api.domain.operation.convert.MkUserOrgConvert;
import com.yhd.admin.api.domain.operation.dto.MkUserOrgDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserOrg;
import com.yhd.admin.api.domain.operation.query.MkUserDeptParam;
import com.yhd.admin.api.domain.operation.query.MkUserOrgParam;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 运维配置中心-单位管理-Service
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */
@Service
public class MkUserOrgServiceImpl extends ServiceImpl<MkUserOrgDao, MkUserOrg> implements MkUserOrgService {
    @Resource
    private MkUserOrgConvert convert;
    @Resource
    private MkUserOrgTypeService typeService;

    @Override
    public IPage<MkUserOrgDTO> pagingQuery(MkUserOrgParam queryParam) {
        Page<MkUserOrg> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkUserOrg> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        //单位编码
        if (StringUtils.isNotBlank(queryParam.getOrgCode())) {
            queryChain.like(MkUserOrg::getOrgCode, queryParam.getOrgCode());
        }
        //单位名称
        if (StringUtils.isNotBlank(queryParam.getOrgName())) {
            queryChain.like(MkUserOrg::getOrgName, queryParam.getOrgName());
        }
        //单位简称
        if (StringUtils.isNotBlank(queryParam.getOrgSimpleName())) {
            queryChain.like(MkUserOrg::getOrgSimpleName, queryParam.getOrgSimpleName());
        }
        //单位类型
        if (queryParam.getOrgTypeId() != null) {
            queryChain.eq(MkUserOrg::getOrgTypeId, queryParam.getOrgTypeId());
        }
        queryChain.orderByDesc(MkUserOrg::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public Boolean addOrModify(MkUserOrgParam param) {
        MkUserOrg entity = convert.toEntity(param);
        //上级单位
        Optional.ofNullable(entity.getUpId()).ifPresent(upId ->
            entity.setUpName(this.getById(upId).getOrgName()));
        //单位类型
        Optional.ofNullable(entity.getOrgTypeId()).ifPresent(deptTypeId ->
            entity.setOrgTypeName(typeService.getById(deptTypeId).getName()));
        return super.save(entity);
    }

    @Override
    public MkUserOrgDTO getCurrentDetail(MkUserOrgParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    public List<MkUserOrgDTO> getOrgList(MkUserDeptParam param) {
        List<MkUserOrg> orgs = this.list();
        //编辑时，上级单位去除本身
        if (param.getId() != null){
            orgs = orgs.stream()
                .filter(item -> item.getId().equals(param.getId()))
                .filter(item -> item.getStatus().equals(true))
                .collect(Collectors.toList());
        }
        return convert.toDTOList(orgs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

}
