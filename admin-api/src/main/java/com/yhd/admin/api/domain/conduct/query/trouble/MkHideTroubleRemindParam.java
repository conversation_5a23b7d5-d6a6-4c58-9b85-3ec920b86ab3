package com.yhd.admin.api.domain.conduct.query.trouble;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 隐患催办记录表(MkHideTroubleRemind)Param
 *
 * <AUTHOR>
 * @since 2025-08-04 10:28:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkHideTroubleRemindParam extends QueryParam implements Serializable {

    /** 主键列表ids */
    private List<Long> ids;
    /** 催办id */
    private Long id;
    /** 隐患id */
    private Long troubleId;
    /** 催办描述 */
    private String remindDesc;
    /** 检查班次名称 */
    private String shiftName;
    /** 检查班次代码 */
    private String shiftCode;
}

