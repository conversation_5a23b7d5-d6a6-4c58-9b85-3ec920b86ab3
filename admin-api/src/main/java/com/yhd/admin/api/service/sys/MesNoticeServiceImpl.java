package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yhd.admin.api.dao.sys.MesNoticeDao;
import com.yhd.admin.api.domain.sys.convert.MesNoticeConvert;
import com.yhd.admin.api.domain.sys.dto.DicItemDTO;
import com.yhd.admin.api.domain.sys.dto.MesNoticeDTO;
import com.yhd.admin.api.domain.sys.entity.MesNotice;
import com.yhd.admin.api.domain.sys.entity.SysDic;
import com.yhd.admin.api.domain.sys.query.DicItemParam;
import com.yhd.admin.api.domain.sys.query.MesNoticeParam;
import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.api.domain.sys.vo.MesNoticeVO;
import com.yhd.admin.api.domain.sys.vo.TreeNode;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.common.domain.dto.UserDTO;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class MesNoticeServiceImpl extends ServiceImpl<MesNoticeDao, MesNotice> implements MesNoticeService {

    private final MesNoticeConvert convert;

    private final DicItemService dicItemService;

    private final DicService dicService;
    private final MesNoticeDao noticeDao;
    @Resource
    private UserService userService;

    public MesNoticeServiceImpl(MesNoticeConvert convert, DicItemService dicItemService, DicService dicService,
                                MesNoticeDao noticeDao) {
        this.convert = convert;
        this.dicItemService = dicItemService;
        this.dicService = dicService;
        this.noticeDao = noticeDao;
    }

    /**
     * 分页查询
     *
     * @param queryParam {@link MesNoticeParam}
     * @return IPage<MesNoticeDTO>
     */
    @Override
    public IPage<MesNoticeDTO> pagingQuery(MesNoticeParam queryParam) {
        Page<MesNotice> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MesNotice> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        queryChain.eq(StringUtils.isNoneBlank(queryParam.getReceiverAccount()), MesNotice::getReceiverAccount,
            queryParam.getReceiverAccount());
        queryChain.eq(!org.springframework.util.StringUtils.isEmpty(queryParam.getReadState()), MesNotice::getReadState,
            queryParam.getReadState());
        queryChain.eq(StringUtils.isNoneBlank(queryParam.getTargetType()), MesNotice::getTargetType,
            queryParam.getTargetType());
        queryChain.like(StringUtils.isNoneBlank(queryParam.getTitle()), MesNotice::getTitle, queryParam.getTitle());
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        queryChain.eq(MesNotice::getReceiverAccount, authentication.getName());
        queryChain.orderByDesc(MesNotice::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }

    /**
     * 根据ID进行新增或者修改
     *
     * @param queryParam {@link MesNoticeParam}
     * @return true 成功，false 失败
     */
    @Override
    public Boolean addOrModify(MesNoticeParam queryParam) {
        MesNotice entity = convert.toEntity(queryParam);
        return saveOrUpdate(entity);
    }

    /**
     * 批量删除
     *
     * @param param {@link BatchParam}
     * @return true 成功，false 失败
     */
    @Override
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

    /**
     * 根据ID查询详情
     *
     * @param queryParam {@link MesNoticeParam}
     * @return {@link MesNoticeDTO}
     */
    @Override
    public MesNoticeDTO getCurrentDetail(MesNoticeParam queryParam) {
        return convert.toDTO(super.getById(queryParam.getId()));
    }

    @Override
    public List<TreeNode> getNoticeDictList(DicItemParam queryParam) {
        LambdaQueryWrapper<SysDic> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDic::getCategory, queryParam.getCategory());
        SysDic dic = dicService.getOne(wrapper);
        if (dic == null) {
            throw new BMSException("error", "找不到字典项" + queryParam.getCategory());
        }
        List<DicItemDTO> dicItemDTOList = dicItemService.queryDicItemByDicId(queryParam);
        List<TreeNode> list = new ArrayList<>();
        TreeNode parant = new TreeNode();
        parant.setDisabled(false);
        parant.setIsLeaf(false);
        parant.setKey(dic.getId());
        parant.setTitle(dic.getName());
        parant.setValue(dic.getId());
        parant.setCode("");

        if (!CollectionUtils.isEmpty(dicItemDTOList)) {
            List<TreeNode> children = new ArrayList<>();
            TreeNode childrenNode = null;
            for (int i = 0; i < dicItemDTOList.size(); i++) {
                DicItemDTO item = dicItemDTOList.get(i);
                childrenNode = new TreeNode();
                childrenNode.setDisabled(false);
                childrenNode.setIsLeaf(true);
                childrenNode.setKey(item.getId());
                childrenNode.setTitle(item.getCode());
                childrenNode.setValue(item.getId());
                childrenNode.setCode(item.getVal());
                children.add(childrenNode);
            }
            parant.setChildren(children);
        }
        list.add(parant);
        return list;
    }

    @Override
    public Boolean saveMesNotice(String... params) {
        if (params == null)
            throw new BMSException("error", "保存消息通知参数不能为空");
        int length = params.length;

        MesNotice po = new MesNotice();
        for (int i = 0; i < length; i++) {
            if (i == 0) {
                po.setReceiverAccount(params[0]);
                // 查询用户名称
                UserParam userParam = new UserParam();
                userParam.setUsername(params[0]);
                UserDTO userDTO = userService.getUserByUsername(params[0]);
                if (userDTO != null) {
                    po.setReceiverAccountName(userDTO.getName());
                }
            } else if (i == 1) {
                po.setTargetType(params[1]);
                po.setTargetTypeName(dicService.transform("NOTICE_TYPE", params[1]));
            } else if (i == 2)
                po.setTargetId(params[2]);
                // 紧急程度
            else if (i == 3)
                po.setPriority("1");
        }
        // 读取状态;1-未读
        po.setReadState(false);
        po.setPushState("1");
        po.setTitle("您有一条" + po.getTargetTypeName() + "通知信息，请查看！");
        int result = noticeDao.insert(po);
        return true;
    }

    @Override
    public Integer getCurrentNoticeCount(MesNoticeParam queryParam) {
        LambdaQueryChainWrapper<MesNotice> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        queryChain.eq(StringUtils.isNoneBlank(queryParam.getReceiverAccount()), MesNotice::getReceiverAccount,
            queryParam.getReceiverAccount());
        queryChain.eq(!org.springframework.util.StringUtils.isEmpty(queryParam.getReadState()), MesNotice::getReadState,
            queryParam.getReadState());
        queryChain.eq(StringUtils.isNoneBlank(queryParam.getTargetType()), MesNotice::getTargetType,
            queryParam.getTargetType());
        return Math.toIntExact(queryChain.count());
    }

    @Override
    public Boolean readBatch(BatchParam param) {
        List<MesNotice> notices = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(param.getId())) {
            param.getId().stream().forEach(id -> {
                MesNotice notice = new MesNotice();
                notice.setId(id);
                notice.setReadState(true);
                notices.add(notice);
            });
        }
        return super.updateBatchById(notices);
    }

    @Override
    public Boolean readtype(String type, String name) {
        QueryWrapper<MesNotice> noticeQueryWrapper = new QueryWrapper<>();
        noticeQueryWrapper.eq("receiver_account", name);
        noticeQueryWrapper.eq("type", type);
        MesNotice notification = new MesNotice();
        notification.setReadState(true);
        return super.baseMapper.update(notification, noticeQueryWrapper) > 0;
    }

    @Override
    public Boolean read(MesNotice notify) {
        return super.baseMapper.updateById(notify) > 0;
    }

    @Override
    public List<MesNoticeVO> queryList(String name) {
        List<MesNoticeVO> resList = Lists.newArrayList();
        LambdaQueryChainWrapper<MesNotice> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        queryChain.eq(MesNotice::getReceiverAccount, name);
        queryChain.eq(MesNotice::getReadState, 0);
        queryChain.orderByDesc(MesNotice::getCreatedTime);
        List<MesNotice> list = queryChain.list();
        Optional.ofNullable(list).ifPresent(e -> {
            e.forEach(item -> {
                resList.add(convert.toVO(convert.toDTO(item)));
            });
        });
        return resList;
    }
}
