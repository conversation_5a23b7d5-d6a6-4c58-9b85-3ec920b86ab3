package com.yhd.admin.api.service.conduct.trouble;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.configuration.FastDFSClient;
import com.yhd.admin.api.dao.conduct.trouble.MkHideTroubleRecordDao;
import com.yhd.admin.api.domain.conduct.convert.trouble.MkHideTroubleRecordConvert;
import com.yhd.admin.api.domain.conduct.dto.trouble.MkHideTroubleRecordDTO;
import com.yhd.admin.api.domain.conduct.entity.trouble.MkHideTroubleRecord;
import com.yhd.admin.api.domain.conduct.query.trouble.MkHideTroubleRecordParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.DicService;
import com.yhd.admin.api.service.sys.UserService;
import com.yhd.admin.common.constant.DicConstant;
import com.yhd.admin.common.utils.excel.CellBuilder;
import com.yhd.admin.common.utils.excel.WorkbookBuilder;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 隐患记录表服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-04 10:28:36
 */
@Service
public class MkHideTroubleRecordServiceImpl extends ServiceImpl<MkHideTroubleRecordDao, MkHideTroubleRecord> implements MkHideTroubleRecordService {

    private final MkHideTroubleRecordConvert mkHideTroubleRecordConvert;
    private final MkHideTroubleRemindService mkHideTroubleRemindService;
    private final DicService dicService;
    private final FastDFSClient fastDFSClient;
    private final UserService userService;

    public MkHideTroubleRecordServiceImpl(MkHideTroubleRecordConvert mkHideTroubleRecordConvert, MkHideTroubleRemindServiceImpl mkHideTroubleRemindService, DicService dicService, FastDFSClient fastDFSClient, UserService userService) {
        this.mkHideTroubleRecordConvert = mkHideTroubleRecordConvert;
        this.mkHideTroubleRemindService = mkHideTroubleRemindService;
        this.dicService = dicService;
        this.fastDFSClient = fastDFSClient;
        this.userService = userService;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkHideTroubleRecordDTO> pagingQuery(MkHideTroubleRecordParam param) {
        IPage<MkHideTroubleRecord> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkHideTroubleRecord> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        if (Objects.nonNull(param.getStartDate()) && Objects.nonNull(param.getEndDate())) {
            wrapper.ge(MkHideTroubleRecord::getCheckDate, param.getStartDate());
            wrapper.le(MkHideTroubleRecord::getCheckDate, param.getEndDate());
        }
        // 区分 在办隐患 历史隐患
        if (StringUtils.isNotBlank(param.getRespStatusCode())){
            wrapper.eq(MkHideTroubleRecord::getRespStatusCode, param.getRespStatusCode());
        } else {
            // 默认查找 在办隐患 整改状态!= 整改完成
            wrapper.ne(MkHideTroubleRecord::getRespStatusCode, DicConstant.TROUBLE_RESP_STATUS_FOUR);
        }
        // 查询条件 隐患类别 隐患等级 检查人 责任部门 隐患名称
        wrapper.eq(StringUtils.isNotBlank(param.getTroubleTypeCode()), MkHideTroubleRecord::getTroubleTypeCode,
            param.getTroubleTypeCode());
        wrapper.eq(StringUtils.isNotBlank(param.getTroubleLevelCode()), MkHideTroubleRecord::getTroubleLevelCode,
            param.getTroubleLevelCode());
        wrapper.eq(StringUtils.isNotBlank(param.getCheckUserName()), MkHideTroubleRecord::getCheckUserName, param.getCheckUserName());
        wrapper.eq(StringUtils.isNotBlank(param.getRespDeptName()), MkHideTroubleRecord::getRespDeptName, param.getRespDeptName());
        wrapper.like(StringUtils.isNotBlank(param.getName()), MkHideTroubleRecord::getName, param.getName());
        return wrapper.page(page).convert(mkHideTroubleRecordConvert::toDTO);
    }

    /**
     * 异常催办-分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkHideTroubleRecordDTO> pagingQueryOfException(MkHideTroubleRecordParam param) {
        IPage<MkHideTroubleRecord> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkHideTroubleRecord> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.lt(MkHideTroubleRecord::getRespDeadline, LocalDate.now());
        if (Objects.nonNull(param.getStartDate()) && Objects.nonNull(param.getEndDate())) {
            wrapper.ge(MkHideTroubleRecord::getCheckDate, param.getStartDate());
            wrapper.le(MkHideTroubleRecord::getCheckDate, param.getEndDate());
        }
        // 查询条件 整改状态 当班人员 隐患名称
        wrapper.eq(StringUtils.isNotBlank(param.getRespStatusCode()), MkHideTroubleRecord::getRespStatusCode,
            param.getRespStatusCode());
        wrapper.like(StringUtils.isNotBlank(param.getRespUserName()), MkHideTroubleRecord::getRespUserName,
            param.getRespUserName());
        wrapper.like(StringUtils.isNotBlank(param.getName()), MkHideTroubleRecord::getName, param.getName());
        return wrapper.page(page).convert(mkHideTroubleRecordConvert::toDTO);
    }

    private List<MkHideTroubleRecord> queryList(MkHideTroubleRecordParam param) {
        LambdaQueryWrapper<MkHideTroubleRecord> wrapper = new LambdaQueryWrapper<>();
        if (DicConstant.TROUBLE_RESP_STATUS_FOUR.equals(param.getRespStatusCode())) {
            wrapper.eq(MkHideTroubleRecord::getRespStatusCode, DicConstant.TROUBLE_RESP_STATUS_FOUR);
        } else {
            wrapper.ne(MkHideTroubleRecord::getRespStatusCode, DicConstant.TROUBLE_RESP_STATUS_FOUR);
        }
        return baseMapper.selectList(wrapper);
    }

    /**
     * 新增
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    @Transactional
    public Boolean add(MkHideTroubleRecordParam param) {
        MkHideTroubleRecord entity = mkHideTroubleRecordConvert.toEntity(param);
        entity.setRespStatusCode(DicConstant.TROUBLE_RESP_STATUS_ONE);
        entity.setRespStatusName(dicService.transform(DicConstant.TROUBLE_RESP_STATUS, entity.getRespStatusCode()));
        if (save(entity)) {
            mkHideTroubleRemindService.add(entity);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkHideTroubleRecordDTO getCurrentDetails(MkHideTroubleRecordParam param) {
        return mkHideTroubleRecordConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkHideTroubleRecordParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }

    /**
     * 催办
     *
     * @param param 主键
     * @return 是否成功
     */
    @Override
    public Boolean urge(MkHideTroubleRecordParam param) {
        MkHideTroubleRecord entity = getById(param.getId());
        return mkHideTroubleRemindService.add(entity);
    }

    /**
     * 整改
     *
     * @param param 主键
     * @return 是否成功
     */
    @Override
    public Boolean handle(MkHideTroubleRecordParam param) {
        MkHideTroubleRecord entity = getById(param.getId());
        entity.setRespContent(param.getRespContent());
        entity.setRespDate(LocalDate.now());
        entity.setRespStatusCode(DicConstant.TROUBLE_RESP_STATUS_TWO);
        entity.setRespStatusName(dicService.transform(DicConstant.TROUBLE_RESP_STATUS, DicConstant.TROUBLE_RESP_STATUS_TWO));
        return updateById(entity);
    }

    /**
     * 审核
     *
     * @param param 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public Boolean review(MkHideTroubleRecordParam param) {
        if (DicConstant.TROUBLE_RESP_STATUS_ONE.equals(param.getRespStatusCode()) || DicConstant.TROUBLE_RESP_STATUS_TWO.equals(param.getRespStatusCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        MkHideTroubleRecord entity = getById(param.getId());
        entity.setReviewComment(param.getReviewComment());
        entity.setRespStatusCode(param.getRespStatusCode());
        entity.setRespStatusName(dicService.transform(DicConstant.TROUBLE_RESP_STATUS, entity.getRespStatusCode()));
        if (updateById(entity)) {
            String reviewerAccount = entity.getUpdatedBy();
            entity.setRespUserAccount(reviewerAccount);
            entity.setReviewerName(userService.getUserByUsername(reviewerAccount).getName());
            return updateById(entity);
        } else {
            return false;
        }
    }

    /**
     * 在办隐患-导出文件
     *
     * @return 文件地址
     */
    @Override
    public String export(MkHideTroubleRecordParam param) {
        List<MkHideTroubleRecord> recordList = queryList(param);

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String fileName = "在办隐患导出-" + timestamp + ".xlsx";
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 读取模板文件
            File tplFile = ResourceUtils.getFile("classpath:template" + File.separator + "在办隐患导出模板.xlsx");
            Workbook workbook = WorkbookBuilder.getWorkBook(tplFile);
            Sheet sheet = workbook.getSheetAt(0);
            // 跳过标题行
            int rowNum = 1;
            CellStyle contentStyle = createContentStyle(workbook);

            for (MkHideTroubleRecord record : recordList) {
                Row row = sheet.createRow(rowNum++);
                CellBuilder.build(row, 0, record.getName(), contentStyle);
                CellBuilder.build(row, 1, record.getTroubleLevelName(), contentStyle);
                CellBuilder.build(row, 2, record.getTroubleTypeName(), contentStyle);
                CellBuilder.build(row, 3, record.getCheckDate(), contentStyle);
                CellBuilder.build(row, 4, record.getShiftName(), contentStyle);
                CellBuilder.build(row, 5, record.getCheckUserName(), contentStyle);
                CellBuilder.build(row, 6, record.getRespDeptName(), contentStyle);
                CellBuilder.build(row, 7, record.getRespUserName(), contentStyle);
                CellBuilder.build(row, 8, record.getRespDeadline(), contentStyle);
                CellBuilder.build(row, 9, record.getRespStatusName(), contentStyle);
                CellBuilder.build(row, 10, record.getSuperviserName(), contentStyle);
            }
            workbook.write(outputStream);

            // 上传到文件服务器
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            // 执行上传
            return fastDFSClient.getFullPath(fastDFSClient.uploadFile(inputStream, fileName));

        } catch (Exception e) {
            throw new BMSException(ExceptionEnum.EXPORT_ERROR);
        }
    }

    /**
     * 历史隐患-导出文件
     *
     * @return 文件地址
     */
    @Override
    public String exportHistory(MkHideTroubleRecordParam param) {
        param.setRespStatusCode(DicConstant.TROUBLE_RESP_STATUS_FOUR);
        List<MkHideTroubleRecord> recordList = queryList(param);

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String fileName = "历史隐患导出-" + timestamp + ".xlsx";
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 读取模板文件
            File tplFile = ResourceUtils.getFile("classpath:template" + File.separator + "历史隐患导出模板.xlsx");
            Workbook workbook = WorkbookBuilder.getWorkBook(tplFile);
            Sheet sheet = workbook.getSheetAt(0);
            // 跳过标题行
            int rowNum = 1;
            CellStyle contentStyle = createContentStyle(workbook);

            for (MkHideTroubleRecord record : recordList) {
                Row row = sheet.createRow(rowNum++);
                CellBuilder.build(row, 0, record.getName(), contentStyle);
                CellBuilder.build(row, 1, record.getTroubleLevelName(), contentStyle);
                CellBuilder.build(row, 2, record.getTroubleTypeName(), contentStyle);
                CellBuilder.build(row, 3, record.getCheckDate(), contentStyle);
                CellBuilder.build(row, 4, record.getShiftName(), contentStyle);
                CellBuilder.build(row, 5, record.getCheckUserName(), contentStyle);
                CellBuilder.build(row, 6, record.getRespDeptName(), contentStyle);
                CellBuilder.build(row, 7, record.getRespUserName(), contentStyle);
                CellBuilder.build(row, 8, record.getRespDeadline(), contentStyle);
                CellBuilder.build(row, 9, record.getReviewerName(), contentStyle);
                CellBuilder.build(row, 10, record.getRespDate(), contentStyle);
                CellBuilder.build(row, 11, record.getSuperviserName(), contentStyle);
            }
            workbook.write(outputStream);

            // 上传到文件服务器
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            // 执行上传
            return fastDFSClient.getFullPath(fastDFSClient.uploadFile(inputStream, fileName));

        } catch (Exception e) {
            throw new BMSException(ExceptionEnum.EXPORT_ERROR);
        }
    }

    /**
     * 异常催办-导出文件
     *
     * @return 文件地址
     */
    @Override
    public String exportException(MkHideTroubleRecordParam param) {
        LambdaQueryWrapper<MkHideTroubleRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(MkHideTroubleRecord::getRespDeadline, LocalDate.now());
        List<MkHideTroubleRecord> recordList = baseMapper.selectList(wrapper);

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String fileName = "异常催办导出-" + timestamp + ".xlsx";
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 读取模板文件
            File tplFile = ResourceUtils.getFile("classpath:template" + File.separator + "异常催办导出模板.xlsx");
            Workbook workbook = WorkbookBuilder.getWorkBook(tplFile);
            Sheet sheet = workbook.getSheetAt(0);
            // 跳过标题行
            int rowNum = 1;
            CellStyle contentStyle = createContentStyle(workbook);

            for (MkHideTroubleRecord record : recordList) {
                Row row = sheet.createRow(rowNum++);
                CellBuilder.build(row, 0, record.getName(), contentStyle);
                CellBuilder.build(row, 1, record.getCheckDate(), contentStyle);
                CellBuilder.build(row, 2, record.getRespStatusName(), contentStyle);
                CellBuilder.build(row, 3, record.getShiftName(), contentStyle);
                CellBuilder.build(row, 4, record.getRespStatusName(), contentStyle);
            }
            workbook.write(outputStream);

            // 上传到文件服务器
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            // 执行上传
            return fastDFSClient.getFullPath(fastDFSClient.uploadFile(inputStream, fileName));

        } catch (Exception e) {
            throw new BMSException(ExceptionEnum.EXPORT_ERROR);
        }
    }

    private CellStyle createContentStyle(Workbook workbook) {
        CellStyle contentStyle = workbook.createCellStyle();
        DataFormat format = workbook.createDataFormat();
        contentStyle.setDataFormat(format.getFormat("yyyy-mm-dd"));
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setAlignment(HorizontalAlignment.CENTER);
        contentStyle.setWrapText(true);
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);
        contentStyle.setBorderTop(BorderStyle.THIN);
        return contentStyle;
    }
}
