package com.yhd.admin.api.domain.sys.query;

import java.io.Serializable;
import java.util.List;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName RoleParam.java @Description TODO
 * @createTime 2020年03月30日 14:57:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RoleParam extends QueryParam implements Cloneable, Serializable {

    /** 角色ID */
    private Long id;

    /** 角色名称 */
    private String roleName;
    /** 角色编码 */
    private String roleCode;
    /** 角色状态 */
    private Boolean isEnable;

    /** 权限 */
    private List<Long> authority;
}
