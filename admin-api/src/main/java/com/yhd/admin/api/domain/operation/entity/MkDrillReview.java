package com.yhd.admin.api.domain.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 应急演练-演练审核表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MkDrillReview extends BaseEntity implements Cloneable, Serializable {
    /**
     * Id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 演练计划ID
     */
    private Long planId;

    /**
     * 计划演练时间
     */
    private LocalDateTime planTime;

    /**
     * 演练名称
     */
    private String drillName;

    /**
     * 地点
     */
    private String location;

    /**
     * 演练方式
     */
    private String method;

    /**
     * 负责人账号
     */
    private String leaderNo;

    /**
     * 负责人名称
     */
    private String leaderName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 审核状态
     */
    private Integer reviewStatus;

    /**
     * 审核人
     */
    private String reviewBy;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 审核意见
     */
    private String reviewOpinion;
} 