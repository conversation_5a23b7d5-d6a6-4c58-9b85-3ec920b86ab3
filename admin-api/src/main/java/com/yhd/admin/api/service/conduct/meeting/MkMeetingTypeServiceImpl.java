package com.yhd.admin.api.service.conduct.meeting;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.meeting.MkMeetingTypeDao;
import com.yhd.admin.api.domain.conduct.convert.meeting.MkMeetingTypeConvert;
import com.yhd.admin.api.domain.conduct.dto.meeting.MkMeetingTypeDTO;
import com.yhd.admin.api.domain.conduct.entity.meeting.MkMeetingType;
import com.yhd.admin.api.domain.conduct.query.meeting.MkMeetingTypeParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 会议类型表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-28 10:36:44
 */
@Service
public class MkMeetingTypeServiceImpl extends ServiceImpl<MkMeetingTypeDao, MkMeetingType> implements MkMeetingTypeService {

    private final MkMeetingTypeConvert mkMeetingTypeConvert;


    public MkMeetingTypeServiceImpl(MkMeetingTypeConvert mkMeetingTypeConvert) {
        this.mkMeetingTypeConvert = mkMeetingTypeConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkMeetingTypeDTO> pagingQuery(MkMeetingTypeParam param) {
        IPage<MkMeetingType> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkMeetingType> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return wrapper.page(page).convert(mkMeetingTypeConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkMeetingTypeDTO> queryList(MkMeetingTypeParam param) {
        LambdaQueryWrapper<MkMeetingType> wrapper = new LambdaQueryWrapper<>();
        return mkMeetingTypeConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkMeetingTypeParam param) {
        MkMeetingType entity = mkMeetingTypeConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkMeetingTypeDTO getCurrentDetails(MkMeetingTypeParam param) {
        return mkMeetingTypeConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkMeetingTypeParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }
}
