package com.yhd.admin.api.controller.conduct.arrangement;

import com.yhd.admin.api.domain.conduct.convert.arrangement.MkShiftArrangementConvert;
import com.yhd.admin.api.domain.conduct.query.arrangement.MkShiftArrangementParam;
import com.yhd.admin.api.service.conduct.arrangement.MkShiftArrangementService;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 值班带班安排表控制层
 *
 * <AUTHOR>
 * @since 2025-07-29 11:30:39
 */
@RestController
@RequestMapping("/conduct/shiftArrangement")
public class MkShiftArrangementController {

    public final MkShiftArrangementService mkShiftArrangementService;
    public final MkShiftArrangementConvert mkShiftArrangementConvert;
    private static final String TEMPLATE = "值班带班安排导入模板.xls";

    public MkShiftArrangementController(
        MkShiftArrangementService mkShiftArrangementService,
        MkShiftArrangementConvert mkShiftArrangementConvert
    ) {
        this.mkShiftArrangementService = mkShiftArrangementService;
        this.mkShiftArrangementConvert = mkShiftArrangementConvert;
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkShiftArrangementParam param) {
        return RespJson.success(mkShiftArrangementConvert.toVOList(mkShiftArrangementService.queryList(param)));
    }

}

