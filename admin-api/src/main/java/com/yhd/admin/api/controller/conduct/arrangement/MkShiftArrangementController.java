package com.yhd.admin.api.controller.conduct.arrangement;

import com.yhd.admin.api.domain.conduct.convert.arrangement.MkShiftArrangementConvert;
import com.yhd.admin.api.domain.conduct.query.arrangement.MkShiftArrangementParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.domain.sys.enums.ResultStateEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.conduct.arrangement.MkShiftArrangementService;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 值班带班安排表控制层
 *
 * <AUTHOR>
 * @since 2025-07-29 11:30:39
 */
@RestController
@RequestMapping("/conduct/shift")
public class MkShiftArrangementController {

    private final MkShiftArrangementService mkShiftArrangementService;
    private final MkShiftArrangementConvert mkShiftArrangementConvert;

    public MkShiftArrangementController(
        MkShiftArrangementService mkShiftArrangementService,
        MkShiftArrangementConvert mkShiftArrangementConvert
    ) {
        this.mkShiftArrangementService = mkShiftArrangementService;
        this.mkShiftArrangementConvert = mkShiftArrangementConvert;
    }

    /**
     * 排班查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryArrange",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryArrange(@RequestBody MkShiftArrangementParam param) {
        return RespJson.success(mkShiftArrangementService.queryArrange(param));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkShiftArrangementParam param) {
        return RespJson.success(mkShiftArrangementConvert.toVOList(mkShiftArrangementService.queryList(param)));
    }

    /**
     * 导入文件
     *
     * @return 文件地址
     */
    @PostMapping(value = "/importExcel")
    public RespJson<Boolean> importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return RespJson.failure(ResultStateEnum.FAIL.getCode(), "文件不存在或者为空！");
        }
        return RespJson.success(mkShiftArrangementService.importExcel(file));
    }

    /**
     * 导出文件
     *
     * @param param 筛选条件
     * @return 文件地址
     */
    @PostMapping(
        value = "/export",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> export(@RequestBody MkShiftArrangementParam param) {
        try {
            return RespJson.success(mkShiftArrangementService.export(param));
        } catch (Exception e) {
            return RespJson.failure(e.getMessage());
        }
    }

    /**
     * 下载模版
     *
     * @param param 筛选条件
     * @return 文件地址
     */
    @PostMapping(
        value = "/getTemplate",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> downloadTemplate(@RequestBody MkShiftArrangementParam param) {
        try {
            return RespJson.success(mkShiftArrangementService.getTemplate(param));
        } catch (Exception e) {
            throw new BMSException(ExceptionEnum.DOWNLOAD_TEMPLATE_ERROR);
        }
    }

}

