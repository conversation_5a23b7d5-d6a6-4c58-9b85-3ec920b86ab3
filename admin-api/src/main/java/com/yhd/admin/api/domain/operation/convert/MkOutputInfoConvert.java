package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkOutputInfoDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputInfo;
import com.yhd.admin.api.domain.operation.query.MkOutputInfoParam;
import com.yhd.admin.api.domain.operation.vo.MkOutputInfoVO;
import org.mapstruct.Mapper;


/**
* 生产录入-产量信息表
*
* <AUTHOR>
* @since 1.0.0 2025-07-30
*/
@Mapper(componentModel = "spring")
public interface MkOutputInfoConvert {

    MkOutputInfo toEntity(MkOutputInfoParam param);

    MkOutputInfoVO toVO(MkOutputInfoDTO dto);

    MkOutputInfoDTO toDTO(MkOutputInfo entity);

}
