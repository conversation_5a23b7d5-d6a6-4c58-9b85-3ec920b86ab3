package com.yhd.admin.api.service.monitor.compressor.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.compressor.TbMkAirCompressorInfoDao;
import com.yhd.admin.api.domain.monitor.compressor.convert.TbMkAirCompressorInfoConvert;
import com.yhd.admin.api.domain.monitor.compressor.dto.TbMkAirCompressorInfoDTO;
import com.yhd.admin.api.domain.monitor.compressor.entity.TbMkAirCompressorInfo;
import com.yhd.admin.api.domain.monitor.compressor.vo.create.TbMkAirCompressorInfoCreateVO;
import com.yhd.admin.api.domain.monitor.compressor.vo.page.TbMkAirCompressorInfoPageVO;
import com.yhd.admin.api.domain.monitor.compressor.vo.update.TbMkAirCompressorInfoUpdateVO;
import com.yhd.admin.api.service.monitor.compressor.TbMkAirCompressorInfoService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 空压机基本信息 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkAirCompressorInfoServiceImpl extends ServiceImpl<TbMkAirCompressorInfoDao, TbMkAirCompressorInfo> implements TbMkAirCompressorInfoService {

    @Resource
    private TbMkAirCompressorInfoConvert tbMkAirCompressorInfoConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkAirCompressorInfoCreateVO createVO) {
        TbMkAirCompressorInfo tbMkAirCompressorInfo = tbMkAirCompressorInfoConvert.convert(createVO);
        baseMapper.insert(tbMkAirCompressorInfo);
        return tbMkAirCompressorInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkAirCompressorInfoUpdateVO updateVO) {
        TbMkAirCompressorInfo tbMkAirCompressorInfo = tbMkAirCompressorInfoConvert.convert(updateVO);
        return baseMapper.updateById(tbMkAirCompressorInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkAirCompressorInfo get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkAirCompressorInfoDTO> page(TbMkAirCompressorInfoPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkAirCompressorInfo> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkAirCompressorInfo> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.like(StringUtils.isNotBlank(param.getCompressorName()), TbMkAirCompressorInfo::getCompressorName, param.getCompressorName());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getDeviceNumber()), TbMkAirCompressorInfo::getDeviceNumber, param.getDeviceNumber());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getRemarks()), TbMkAirCompressorInfo::getRemarks, param.getRemarks());
        queryChainWrapper.orderByDesc(TbMkAirCompressorInfo::getId);
        return queryChainWrapper.page(iPage).convert(tbMkAirCompressorInfoConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkAirCompressorInfoCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 空压机基本信息：传入的列表为空");
            return 0;
        }
        List<TbMkAirCompressorInfo> entities = createVOs.stream()
                .map(tbMkAirCompressorInfoConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 空压机基本信息 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkAirCompressorInfoUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 空压机基本信息：传入的列表为空");
            return 0;
        }
        List<TbMkAirCompressorInfo> entities = updateVOs.stream()
                .map(tbMkAirCompressorInfoConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 空压机基本信息：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 空压机基本信息 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
