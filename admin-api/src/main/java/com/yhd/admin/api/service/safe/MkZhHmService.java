package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.safe.dto.MkZhHmDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhHm;
import com.yhd.admin.api.domain.safe.query.MkZhHmParam;


public interface MkZhHmService extends IService<MkZhHm> {

    Boolean addOrModify(MkZhHmParam param);

    Boolean remove(MkZhHmParam param);

    MkZhHmDTO getCurrentDetail(MkZhHmParam param);

    IPage<MkZhHmDTO> pagingQuery(MkZhHmParam param);

    MkZhHmDTO getData();
}
