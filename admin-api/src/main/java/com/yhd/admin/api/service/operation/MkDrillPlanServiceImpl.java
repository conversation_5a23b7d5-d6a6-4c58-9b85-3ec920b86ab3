package com.yhd.admin.api.service.operation;

import com.yhd.admin.api.dao.operation.MkDrillPlanDao;
import com.yhd.admin.api.domain.operation.convert.MkDrillPlanConvert;
import com.yhd.admin.api.domain.operation.dto.MkDrillPlanDTO;
import com.yhd.admin.api.domain.operation.entity.MkDrillPlan;
import com.yhd.admin.api.domain.operation.query.MkDrillPlanParam;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;

/**
 * 应急演练-演练计划表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@Service
public class MkDrillPlanServiceImpl extends ServiceImpl<MkDrillPlanDao, MkDrillPlan> implements MkDrillPlanService {

    @Resource
    private MkDrillPlanConvert convert;

    @Resource
    private MkDrillPlanService service;

    @Override
    public IPage<MkDrillPlanDTO> pagingQuery(MkDrillPlanParam queryParam) {
        Page<MkDrillPlan> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkDrillPlan> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        
        // 添加查询条件
        if (queryParam.getDrillName() != null && !queryParam.getDrillName().isEmpty()) {
            queryChain.like(MkDrillPlan::getDrillName, queryParam.getDrillName());
        }
        if (queryParam.getMethod() != null && !queryParam.getMethod().isEmpty()) {
            queryChain.eq(MkDrillPlan::getMethod, queryParam.getMethod());
        }
        if (queryParam.getLeaderName() != null && !queryParam.getLeaderName().isEmpty()) {
            queryChain.like(MkDrillPlan::getLeaderName, queryParam.getLeaderName());
        }
        if (queryParam.getStatus() != null) {
            queryChain.eq(MkDrillPlan::getStatus, queryParam.getStatus());
        }
        if (queryParam.getStartTime() != null) {
            queryChain.ge(MkDrillPlan::getPlanTime, queryParam.getStartTime());
        }
        if (queryParam.getEndTime() != null) {
            queryChain.le(MkDrillPlan::getPlanTime, queryParam.getEndTime());
        }
        
        queryChain.orderByDesc(MkDrillPlan::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkDrillPlanParam param) {
        MkDrillPlan entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MkDrillPlanParam param) {
        MkDrillPlan entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MkDrillPlanDTO getCurrentDetail(MkDrillPlanParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

} 