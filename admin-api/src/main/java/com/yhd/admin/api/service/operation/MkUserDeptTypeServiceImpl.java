package com.yhd.admin.api.service.operation;

import com.yhd.admin.api.dao.operation.MkUserDeptTypeDao;
import com.yhd.admin.api.domain.operation.convert.MkUserDeptTypeConvert;
import com.yhd.admin.api.domain.operation.dto.MkUserDeptTypeDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserDeptType;
import com.yhd.admin.api.domain.operation.query.MkUserDeptTypeParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 运维配置中心-部门类型-Service
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */
@Service
public class MkUserDeptTypeServiceImpl extends ServiceImpl<MkUserDeptTypeDao, MkUserDeptType> implements MkUserDeptTypeService {

    @Resource
    private MkUserDeptTypeConvert convert;

    @Override
    public IPage<MkUserDeptTypeDTO> pagingQuery(MkUserDeptTypeParam queryParam) {
        Page<MkUserDeptType> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkUserDeptType> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        queryChain.orderByAsc(MkUserDeptType::getId);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkUserDeptTypeParam param) {
        MkUserDeptType entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MkUserDeptTypeParam param) {
        MkUserDeptType entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MkUserDeptTypeDTO getCurrentDetail(MkUserDeptTypeParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    public List<MkUserDeptTypeDTO> getList(MkUserDeptTypeParam param) {
        if (StringUtils.isBlank(param.getDeptType())){
            throw new BMSException("error","请选择部门类型还是子类型");
        }
        List<MkUserDeptType> deptTypes = super.list().stream()
            .filter(item -> item.getDeptType().equals(param.getDeptType()))
            .collect(Collectors.toList());
        return convert.toDTOList(deptTypes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

}
