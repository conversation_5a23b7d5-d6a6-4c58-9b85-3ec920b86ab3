package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.configuration.OssServerCfg;
import com.yhd.admin.api.dao.sys.FileDao;
import com.yhd.admin.api.domain.sys.entity.FileManager;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName FileService.java @Description TODO 附件
 * @createTime 2022年05月20日 14:22:00
 */
@Service
public class FileServiceImpl extends ServiceImpl<FileDao, FileManager> implements FileService {

    @Resource
    private OssServerCfg ossServerCfg;

    @Override
    public boolean insertFile(Long relationId, String businessType, String fileType, List<String> fileList) {
        // 删除后在新增
        removeFile(relationId, businessType, fileType);
        // 处理文件属性
        return translateFile(relationId, businessType, fileType, fileList);
    }

    @Override
    public List<FileManager> queryFileListByRelationId(
        Long relationId, String businessType, String fileType) {
        LambdaQueryChainWrapper<FileManager> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        queryChain.eq(FileManager::getRelationId, relationId);
        if (StringUtils.isNotBlank(businessType)) {
            queryChain.eq(FileManager::getBusinessType, businessType);
        }
        if (StringUtils.isNotBlank(fileType)) {
            queryChain.eq(FileManager::getFileType, fileType);
        }
        queryChain.orderByDesc(FileManager::getCreatedTime);
        return queryChain.list();
    }

    @Override
    public List<String> getFileList(Long relationId, String businessType, String fileType) {
        List<FileManager> fileList = queryFileListByRelationId(relationId, businessType, fileType);
        List<String> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(fileList)) {
            int fileSize = fileList.size();
            for (int i = 0; i < fileSize; i++) {
                FileManager item = fileList.get(i);
                StringBuilder urlOrName = new StringBuilder();
                urlOrName.append(item.getFileUrl()).append("#@@#").append(item.getFileName());
                list.add(String.valueOf(urlOrName));
            }
        }
        return list;
    }

    @Override
    public boolean removeFile(Long relationId, String businessType, String fileType) {
        List<FileManager> FileManagers = queryFileListByRelationId(relationId, businessType, fileType);
        List<Long> id = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(FileManagers)) {
            FileManagers.stream()
                .forEach(
                    e -> {
                        id.add(e.getId());
                    });
        }
        return this.removeByIds(id);
    }

    @Override
    public int removeFile(Long relationId, String businessType) {
        LambdaQueryWrapper<FileManager> queryChain = new LambdaQueryWrapper<>();
        queryChain.eq(FileManager::getRelationId, relationId);
        queryChain.eq(FileManager::getBusinessType, businessType);
        return this.baseMapper.delete(queryChain);
    }

    @Override
    public List<String> queryFilePictureByRelationId(
        Long relationId, String businessType, String fileType) {
        List<String> result = new ArrayList<>();
        List<FileManager> fileList = queryFileListByRelationId(relationId, businessType, fileType);
        if (CollectionUtils.isNotEmpty(fileList)) {
            StringBuffer url =
                new StringBuffer(ossServerCfg.getEndpoint())
                    .append(":")
                    .append(ossServerCfg.getPort())
                    .append("/")
                    .append(ossServerCfg.getBucketName())
                    .append("/");
            result =
                fileList.stream()
                    .filter(file -> Objects.nonNull(file))
                    .filter(
                        file ->
                            (StringUtils.isNotBlank(file.getFileUrl())
                                && (file.getFileUrl().endsWith(".bmp")
                                || file.getFileUrl().endsWith(".jpg")
                                || file.getFileUrl().endsWith(".png")
                                || file.getFileUrl().endsWith(".gif")
                                || file.getFileUrl().endsWith(".jpeg"))))
                    .map(file -> url.toString() + file.getFileUrl())
                    .collect(Collectors.toList());
        }
        return result;
    }

    /**
     * @param relationId
     * @param businessType
     * @param fileType
     * @param fileList
     */
    private boolean translateFile(
        Long relationId, String businessType, String fileType, List<String> fileList) {
        List<FileManager> FileManagers = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(fileList)) {
            for (int i = 0; i < fileList.size(); i++) {
                FileManager fileEntity = new FileManager();
                fileEntity.setBusinessType(businessType);
                String urlOrName = fileList.get(i);
                if (StringUtils.isNotBlank(urlOrName)) {
                    if (urlOrName.contains("#@@#")) {
                        String[] urlOrNames = urlOrName.split("#@@#");
                        fileEntity.setFileUrl(urlOrNames[0]);
                        fileEntity.setFileName(urlOrNames[1]);
                    } else {
                        fileEntity.setFileUrl(urlOrName);
                    }
                }
                fileEntity.setFileType(fileType);
                fileEntity.setRelationId(relationId);
                FileManagers.add(fileEntity);
            }
        }
        return this.saveBatch(FileManagers);
    }
}
