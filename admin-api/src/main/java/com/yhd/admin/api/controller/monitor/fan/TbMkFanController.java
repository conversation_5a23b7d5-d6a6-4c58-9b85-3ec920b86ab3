package com.yhd.admin.api.controller.monitor.fan;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yhd.admin.api.dao.monitor.fan.TbMkLocalFanDetailsDao;
import com.yhd.admin.api.dao.monitor.fan.TbMkLocalFanSystemDao;
import com.yhd.admin.api.dao.monitor.fan.TbMkLocalFanUnitDao;
import com.yhd.admin.api.domain.monitor.fan.dto.TbMkFanDTO;
import com.yhd.admin.api.domain.monitor.fan.dto.TbMkFanSystemDTO;
import com.yhd.admin.api.domain.monitor.fan.dto.TbMkFanSystemDetailDTO;
import com.yhd.admin.api.domain.monitor.fan.entity.TbMkLocalFanDetails;
import com.yhd.admin.api.domain.monitor.fan.entity.TbMkLocalFanSystem;
import com.yhd.admin.api.domain.monitor.fan.entity.TbMkLocalFanUnit;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 局扇控制系统监控控制器
 */
@Slf4j
@RestController
@RequestMapping("/monitor/fan/screen")
public class TbMkFanController {

    @Resource
    private TbMkLocalFanSystemDao systemDao;

    @Resource
    private TbMkLocalFanUnitDao unitDao;

    @Resource
    private TbMkLocalFanDetailsDao detailsDao;


    /**
     * 获取所有局扇列表
     *
     * @return 包含局扇系统信息的列表
     */
    @GetMapping(value = "/list", produces = org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<TbMkFanDTO>> list() {
        log.debug("获取局扇控制系统列表");
        List<TbMkFanDTO> result = new ArrayList<>();
        try {
            List<TbMkLocalFanSystem> systems = systemDao.selectList(null);
            for (TbMkLocalFanSystem system : systems) {
                TbMkFanDTO dto = new TbMkFanDTO();
                BeanUtils.copyProperties(system, dto);
                result.add(dto);
            }
            return RespJson.success(result);
        } catch (Exception e) {
            log.error("查询局扇控制系统信息时发生异常", e);
            return RespJson.failure("查询失败");
        }
    }


    /**
     * 获取指定ID的局扇控制系统及其风机最新运行数据
     *
     * @param ids 可选：局扇系统ID列表，为空时返回所有
     * @return RespJson<List < TbMkFanDTO>>
     */
    @PostMapping(value = "/info", produces = org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<TbMkFanSystemDTO>> getLocalFanInfo(@RequestBody(required = false) List<Long> ids) {
        log.debug("获取指定ID的局扇控制系统及其风机运行数据, IDs: {}", ids);

        List<TbMkFanSystemDTO> result = new ArrayList<>();

        try {
            QueryWrapper<TbMkLocalFanSystem> systemQuery = new QueryWrapper<>();
            if (ids != null && !ids.isEmpty()) {
                systemQuery.in("id", ids);
            }
            List<TbMkLocalFanSystem> systems = systemDao.selectList(systemQuery);
            if (systems.isEmpty()) {
                return RespJson.success(result);
            }
            for (TbMkLocalFanSystem system : systems) {
                TbMkFanSystemDTO dto = new TbMkFanSystemDTO();
                BeanUtils.copyProperties(system, dto);
                dto.setSystemId(system.getId());
                List<TbMkFanSystemDetailDTO>  fanDetailList = new ArrayList<>();
                QueryWrapper<TbMkLocalFanUnit> paramQuery = new QueryWrapper<>();
                paramQuery.eq("system_id", system.getId());
                List<TbMkLocalFanUnit> parameters = unitDao.selectList(paramQuery);
                for (TbMkLocalFanUnit parameter : parameters) {
                    TbMkFanSystemDetailDTO tbMkFanSystemDetailDTO = new TbMkFanSystemDetailDTO();
                    BeanUtils.copyProperties(parameter, tbMkFanSystemDetailDTO);
                    tbMkFanSystemDetailDTO.setFanId(parameter.getId());

                    TbMkLocalFanDetails runtime = detailsDao.selectOne(
                        new QueryWrapper<TbMkLocalFanDetails>()
                            .lambda()
                            .eq(TbMkLocalFanDetails::getFanUnitId,parameter.getId())
                            .orderByDesc(TbMkLocalFanDetails::getUpdatedTime)
                            .last("limit 1")
                    );
                    if (runtime != null) {
                        BeanUtils.copyProperties(runtime, tbMkFanSystemDetailDTO);
                    }
                    fanDetailList.add(tbMkFanSystemDetailDTO);
                }
                dto.setFanDetailList(fanDetailList);
                result.add(dto);
            }
            return RespJson.success(result);

        } catch (Exception e) {
            log.error("查询局扇系统信息时发生异常", e);
            return RespJson.failure("查询失败：" + e.getMessage());
        }
    }
}
