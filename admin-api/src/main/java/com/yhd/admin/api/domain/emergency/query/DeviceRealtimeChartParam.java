package com.yhd.admin.api.domain.emergency.query;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DeviceRealtimeChartParam.java
 * @Description 设备实时值曲线查询参数
 * @createTime 2025年01月20日 12:00:00
 */
@Data
public class DeviceRealtimeChartParam {

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 时间范围类型：1-最近1小时，2-最近6小时，3-最近24小时，4-最近7天，5-自定义
     */
    private Integer timeRangeType;

    /**
     * 数据点数量限制（默认100）
     */
    private Integer limitCount = 100;
}