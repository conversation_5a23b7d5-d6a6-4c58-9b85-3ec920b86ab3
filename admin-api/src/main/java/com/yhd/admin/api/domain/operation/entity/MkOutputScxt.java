package com.yhd.admin.api.domain.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产录入-生产系统内容表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MkOutputScxt extends BaseEntity implements Cloneable, Serializable {
    /**
     * Id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
	/**
	* 日期
	*/
	private Date outputDate;

	/**
	* 系统描述
	*/
	private String description;
}
