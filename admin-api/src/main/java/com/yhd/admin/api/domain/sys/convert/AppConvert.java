package com.yhd.admin.api.domain.sys.convert;

import com.yhd.admin.api.domain.sys.dto.AppDTO;
import com.yhd.admin.api.domain.sys.entity.SysApp;
import com.yhd.admin.api.domain.sys.query.AppParam;
import com.yhd.admin.api.domain.sys.vo.AppVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AppConvert {

    AppDTO toDTO(SysApp sysApp);

    List<AppDTO> toDTO(List<SysApp> apps);

    AppVO toVO(AppDTO sysApp);

    List<AppVO> toVO(List<AppDTO> apps);

    SysApp toEntity(AppParam param);
}
