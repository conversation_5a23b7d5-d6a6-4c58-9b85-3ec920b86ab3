package com.yhd.admin.api.controller.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.operation.convert.MkUserJobConvert;
import com.yhd.admin.api.domain.operation.dto.MkUserJobDTO;
import com.yhd.admin.api.domain.operation.query.MkUserJobParam;
import com.yhd.admin.api.domain.operation.vo.MkUserJobVO;
import com.yhd.admin.api.service.operation.MkUserJobService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 运维配置中心-职务/工种管理-控制层
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-27
 */
@RestController
@RequestMapping("/user/job")
public class MkUserJobController {
    @Resource
    private MkUserJobConvert convert;

    @Resource
    private MkUserJobService service;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkUserJobVO> pagingQuery(@RequestBody MkUserJobParam queryParam) {
        IPage<MkUserJobDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MkUserJobParam param) {
        return RespJson.success(convert.toVO(service.getCurrentDetail(param)));
    }

    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson addOrModify(@RequestBody MkUserJobParam param) {
        Boolean retVal = service.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(
        value = "/getJobList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson getJobList(@RequestBody MkUserJobParam param) {
        List<MkUserJobDTO> jobList = service.getJobList(param);
        return RespJson.success(convert.toVOList(jobList));
    }

    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam param) {
        Boolean retVal = service.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }
} 