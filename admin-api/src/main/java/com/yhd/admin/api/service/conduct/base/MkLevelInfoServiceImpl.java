package com.yhd.admin.api.service.conduct.base;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.base.MkLevelInfoDao;
import com.yhd.admin.api.domain.conduct.convert.base.MkLevelInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.base.MkLevelInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkLevelInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkLevelInfoParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 水平信息表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25 14:01:30
 */
@Service
public class MkLevelInfoServiceImpl extends ServiceImpl<MkLevelInfoDao, MkLevelInfo> implements MkLevelInfoService {

    public final MkLevelInfoConvert mkLevelInfoConvert;

    public MkLevelInfoServiceImpl(MkLevelInfoConvert mkLevelInfoConvert) {
        this.mkLevelInfoConvert = mkLevelInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkLevelInfoDTO> pagingQuery(MkLevelInfoParam param) {
        IPage<MkLevelInfo> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkLevelInfo> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return wrapper.page(page).convert(mkLevelInfoConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkLevelInfoDTO> queryList(MkLevelInfoParam param) {
        LambdaQueryWrapper<MkLevelInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(param.getOrgCode()), MkLevelInfo::getOrgCode, param.getOrgCode());
        return mkLevelInfoConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param  数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkLevelInfoParam param) {
        MkLevelInfo entity = mkLevelInfoConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkLevelInfoDTO getCurrentDetails(MkLevelInfoParam param) {
        return mkLevelInfoConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkLevelInfoParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }
}
