package com.yhd.admin.api.domain.monitor.nitrogen.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 制氮系统监控数据-主表
 *
 * <AUTHOR>
 * @since
 */
@Data
public class TbMkNitrogenSystemMainDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 制氮控制箱状态: 0-停止, 1-运行, 2-故障, 3-待机
     */
    private Byte controlBoxStatus;
}
