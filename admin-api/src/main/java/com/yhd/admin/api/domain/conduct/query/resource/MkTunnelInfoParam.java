package com.yhd.admin.api.domain.conduct.query.resource;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 巷道信息(MkTunnelInfo)Param
 *
 * <AUTHOR>
 * @since 2025-07-28 09:25:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkTunnelInfoParam extends QueryParam implements Serializable {

    /** 主键列表ids */
    private List<Long> ids;
    /** id */
    private Long id;
    /** 巷道名称 */
    private String name;
    /** 巷道状态名称 1：规划 2：掘进 3：完成 4：停用 */
    private String statusName;
    /** 巷道状态代码 1：规划 2：掘进 3：完成 4：停用 */
    private String statusCode;
    /** 巷道性质名称 1：准备 2：开拓 3：回采 */
    private String natureName;
    /** 巷道性质代码 1：准备 2：开拓 3：回采 */
    private String natureCode;
    /** 巷道类型名称 1：煤巷 2：岩巷 3：半煤岩巷 */
    private String typeName;
    /** 巷道类型代码 1：煤巷 2：岩巷 3：半煤岩巷 */
    private String typeCode;
    /** 巷道形状名称 1：矩形 2：拱形 3：梯形 */
    private String shapeName;
    /** 巷道形状代码 1：矩形 2：拱形 3：梯形 */
    private String shapeCode;
    /** 断面面积 */
    private BigDecimal sectionalArea;
    /** 巷道长度 */
    private BigDecimal length;
    /** 巷道宽度 */
    private BigDecimal width;
    /** 巷道高度 */
    private BigDecimal height;
    /** 默认父节点名称 1：矿井 2：煤层 3：采区 4：工作面 5：巷道 */
    private String parentNodeName;
    /** 默认父节点代码 1：矿井 2：煤层 3：采区 4：工作面 5：巷道 */
    private String parentNodeCode;
    /** 矿井名称 */
    private String mineName;
    /** 矿井 id */
    private Long mineId;
    /** 所属煤层 名称 */
    private String coalInfoName;
    /** 所属煤层 id */
    private Long coalInfoId;
    /** 所属盘区 名称 */
    private String miningAreaName;
    /** 所属盘区 id */
    private Long miningAreaId;
    /** 工作面 名称(巷道为回采巷道填写) */
    private String workFaceName;
    /** 工作面 id(巷道为回采巷道填写) */
    private Long workFaceId;
    /** 工作面前置 0：否 1：是 */
    private Long workFaceUp;
    /** 成巷日期 */
    private LocalDate foundDate;
    /** 停用日期 */
    private LocalDate stopDate;
}

