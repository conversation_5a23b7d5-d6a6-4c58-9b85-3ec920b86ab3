package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.emergency.MkEmergencyContactDao;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyContactConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyContactDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyContact;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyContactParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 应急救援通讯录表 Service实现类
 * <AUTHOR>
 */
@Service
public class MkEmergencyContactServiceImpl extends ServiceImpl<MkEmergencyContactDao, MkEmergencyContact> 
        implements MkEmergencyContactService {

    @Resource
    private MkEmergencyContactConvert convert;

    @Override
    public IPage<MkEmergencyContactDTO> pagingQuery(MkEmergencyContactParam param) {
        IPage<MkEmergencyContact> iPage = new Page<>(param.getCurrent(), param.getPageSize());
        
        LambdaQueryChainWrapper<MkEmergencyContact> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        
        // 组织编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getOrgCode()), 
                MkEmergencyContact::getOrgCode, param.getOrgCode());
        
        // 联系人编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getContactCode()), 
                MkEmergencyContact::getContactCode, param.getContactCode());
        
        // 联系人姓名模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getContactName()), 
                MkEmergencyContact::getContactName, param.getContactName());
        
        // 部门模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getDepartment()), 
                MkEmergencyContact::getDepartment, param.getDepartment());
        
        // 职位模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getPosition()), 
                MkEmergencyContact::getPosition, param.getPosition());
        
        // 联系电话查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getPhone()), 
                MkEmergencyContact::getPhone, param.getPhone());
        
        // 手机号码查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getMobile()), 
                MkEmergencyContact::getMobile, param.getMobile());
        
        // 联系人类型查询
        queryChainWrapper.eq(param.getContactType() != null, 
                MkEmergencyContact::getContactType, param.getContactType());
        
        // 应急角色模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getEmergencyRole()), 
                MkEmergencyContact::getEmergencyRole, param.getEmergencyRole());
        
        // 专业特长模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getSpeciality()), 
                MkEmergencyContact::getSpeciality, param.getSpeciality());
        
        // 在职状态查询
        queryChainWrapper.eq(param.getDutyStatus() != null, 
                MkEmergencyContact::getDutyStatus, param.getDutyStatus());
        
        // 是否应急负责人查询
        queryChainWrapper.eq(param.getIsEmergencyLeader() != null, 
                MkEmergencyContact::getIsEmergencyLeader, param.getIsEmergencyLeader());
        
        // 排序：按排序号升序，创建时间倒序
        queryChainWrapper.orderByAsc(MkEmergencyContact::getSortOrder)
                         .orderByDesc(MkEmergencyContact::getCreatedTime);
        
        return queryChainWrapper.page(iPage).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkEmergencyContactParam param) {
        // 必填字段校验
        if (StringUtils.isBlank(param.getContactCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getContactName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getDepartment())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getPhone())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        return super.save(convert.toEntity(param));
    }

    @Override
    public Boolean modify(MkEmergencyContactParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        // 必填字段校验
        if (StringUtils.isBlank(param.getContactCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getContactName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getDepartment())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getPhone())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        return super.updateById(convert.toEntity(param));
    }

    @Override
    public MkEmergencyContactDTO getCurrentDetail(MkEmergencyContactParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        MkEmergencyContact entity = super.getById(param.getId());
        return convert.toDTO(entity);
    }

    @Override
    public Boolean removeBatch(BatchParam param) {
        if (param.getId() == null || param.getId().isEmpty()) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        return super.removeByIds(param.getId());
    }

    @Override
    public List<MkEmergencyContactDTO> getContactsByDepartment(String department) {
        if (StringUtils.isBlank(department)) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        List<MkEmergencyContact> entityList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(MkEmergencyContact::getDepartment, department)
                .eq(MkEmergencyContact::getDutyStatus, 1) // 在职状态
                .orderByAsc(MkEmergencyContact::getSortOrder)
                .list();
        
        return convert.toDTO(entityList);
    }

    @Override
    public List<MkEmergencyContactDTO> getEmergencyLeaders(String orgCode) {
        if (StringUtils.isBlank(orgCode)) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        
        List<MkEmergencyContact> entityList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(MkEmergencyContact::getOrgCode, orgCode)
                .eq(MkEmergencyContact::getIsEmergencyLeader, true)
                .eq(MkEmergencyContact::getDutyStatus, 1) // 在职状态
                .orderByAsc(MkEmergencyContact::getSortOrder)
                .list();
        
        return convert.toDTO(entityList);
    }
}
