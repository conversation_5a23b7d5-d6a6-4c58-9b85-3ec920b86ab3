package com.yhd.admin.api.domain.sys.enums;

import lombok.Getter;

/**
 * @desc 枚举返回状态
 */
@Getter
public enum ResultStateEnum {
    /**
     * FAIL
     */
    FAIL("error", "失败"),
    /**
     * SUCCESS
     */
    SUCCESS("ok", "成功");

    private final String code;
    private final String desc;

    /**
     *
     */
    ResultStateEnum(String code, String description) {
        this.code = code;
        this.desc = description;
    }

    /**
     * 通过code获取枚举
     */
    public static ResultStateEnum getByCode(String code) {
        for (ResultStateEnum yesNo : values()) {
            if (yesNo.code.equals(code)) {
                return yesNo;
            }
        }
        return null;
    }

}
