package com.yhd.admin.api.service.operation;

import com.yhd.admin.api.dao.operation.MkUserJobDao;
import com.yhd.admin.api.domain.operation.convert.MkUserJobConvert;
import com.yhd.admin.api.domain.operation.dto.MkUserJobDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserJob;
import com.yhd.admin.api.domain.operation.query.MkUserJobParam;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 运维配置中心-职务/工种管理-Service
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-27
 */
@Service
public class MkUserJobServiceImpl extends ServiceImpl<MkUserJobDao, MkUserJob> implements MkUserJobService {
    @Resource
    private MkUserJobConvert convert;
    @Resource
    private MkUserOrgService orgService;
    @Resource
    private MkUserDeptService deptService;

    @Override
    public IPage<MkUserJobDTO> pagingQuery(MkUserJobParam queryParam) {
        Page<MkUserJob> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkUserJob> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        //职务/工种编码
        if (StringUtils.isNotBlank(queryParam.getJobCode())) {
            queryChain.like(MkUserJob::getJobCode, queryParam.getJobCode());
        }
        //职务/工种名称
        if (StringUtils.isNotBlank(queryParam.getJobName())) {
            queryChain.like(MkUserJob::getJobName, queryParam.getJobName());
        }
        //单位ID
        if (queryParam.getOrgId() != null) {
            queryChain.eq(MkUserJob::getOrgId, queryParam.getOrgId());
        }
        //单位名称
        if (StringUtils.isNotBlank(queryParam.getOrgName())) {
            queryChain.like(MkUserJob::getOrgName, queryParam.getOrgName());
        }
        //部门ID
        if (queryParam.getDeptId() != null) {
            queryChain.eq(MkUserJob::getDeptId, queryParam.getDeptId());
        }
        //部门名称
        if (StringUtils.isNotBlank(queryParam.getDeptName())) {
            queryChain.like(MkUserJob::getDeptName, queryParam.getDeptName());
        }
        queryChain.orderByDesc(MkUserJob::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public Boolean addOrModify(MkUserJobParam param) {
        if (param.getOrgId() != null) {
            param.setOrgName(orgService.getById(param.getOrgId()).getOrgName());
        }
        if (param.getDeptId() != null) {
            param.setDeptName(deptService.getById(param.getDeptId()).getDeptName());
        }
        MkUserJob entity = convert.toEntity(param);
        return super.saveOrUpdate(entity);
    }

    @Override
    public MkUserJobDTO getCurrentDetail(MkUserJobParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    public List<MkUserJobDTO> getJobList(MkUserJobParam param) {
        LambdaQueryChainWrapper<MkUserJob> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        //职务/工种编码
        if (StringUtils.isNotBlank(param.getJobCode())) {
            queryChain.like(MkUserJob::getJobCode, param.getJobCode());
        }
        //职务/工种名称
        if (StringUtils.isNotBlank(param.getJobName())) {
            queryChain.like(MkUserJob::getJobName, param.getJobName());
        }
        //单位ID
        if (param.getOrgId() != null) {
            queryChain.eq(MkUserJob::getOrgId, param.getOrgId());
        }
        //部门ID
        if (param.getDeptId() != null) {
            queryChain.eq(MkUserJob::getDeptId, param.getDeptId());
        }
        queryChain.orderByDesc(MkUserJob::getCreatedTime);
        return convert.toDTOList(queryChain.list());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }
}
