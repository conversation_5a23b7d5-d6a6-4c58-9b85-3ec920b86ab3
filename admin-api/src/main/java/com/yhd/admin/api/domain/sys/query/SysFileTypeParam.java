package com.yhd.admin.api.domain.sys.query;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class SysFileTypeParam extends QueryParam implements Serializable {

  private Long id;
  /** 类型名称 */
  private String typeName;
  /** 父级ID */
  private Long parentId;
  /** 父级名称 */
  private String parentName;
  /** 状态 */
  private Boolean status;
  /** 排序，越小越靠前 */
  private Long sortNum;
}
