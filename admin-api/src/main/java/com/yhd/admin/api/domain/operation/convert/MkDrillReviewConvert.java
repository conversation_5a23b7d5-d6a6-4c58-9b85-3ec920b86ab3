package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkDrillReviewDTO;
import com.yhd.admin.api.domain.operation.entity.MkDrillReview;
import com.yhd.admin.api.domain.operation.query.MkDrillReviewParam;
import com.yhd.admin.api.domain.operation.vo.MkDrillReviewVO;
import org.mapstruct.Mapper;

/**
* 应急演练-演练审核表
*
* <AUTHOR>
* @since 1.0.0 2025-08-01
*/
@Mapper(componentModel = "spring")
public interface MkDrillReviewConvert {

    MkDrillReview toEntity(MkDrillReviewParam param);

    MkDrillReviewVO toVO(MkDrillReviewDTO dto);

    MkDrillReviewDTO toDTO(MkDrillReview entity);

} 