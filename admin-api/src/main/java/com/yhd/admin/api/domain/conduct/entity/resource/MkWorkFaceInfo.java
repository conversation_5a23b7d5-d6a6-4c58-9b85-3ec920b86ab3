package com.yhd.admin.api.domain.conduct.entity.resource;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 工作面信息(MkWorkFaceInfo)实体类
 *
 * <AUTHOR>
 * @since 2025-07-28 09:25:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkWorkFaceInfo extends BaseEntity implements Serializable {

    /** id */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 工作面编码 */
    private String workFaceCode;
    /** 工作面名称 */
    private String workFaceName;
    /** 工作面类型 1：综采 2：掘进 */
    private String workFaceTypeName;
    /** 工作面类型 1：综采 2：掘进 */
    private String workFaceTypeCode;
    /** 工作面状态名称 1：待采 2：在采 3：停用 */
    private String statusName;
    /** 工作面状态代码 1：待采 2：在采 3：停用 */
    private String statusCode;
    /** 井田名称 */
    private String mineName;
    /** 所属煤层 名称 */
    private String coalInfoName;
    /** 所属煤层 id */
    private Long coalInfoId;
    /** 所属采区 名称 */
    private String miningAreaName;
    /** 所属采区 id */
    private Long miningAreaId;
    /** 走向长度 */
    private BigDecimal length;
    /** 倾向长度 */
    private BigDecimal width;
    /** 平均厚度 */
    private BigDecimal averageThickness;
    /** 总储量 */
    private BigDecimal totalReserves;
    /** 可采储量 */
    private BigDecimal validReserves;
    /** 设计采高 */
    private BigDecimal heightDesign;
    /** 硬度系数 */
    private BigDecimal hardness;
    /** 停采日期 */
    private LocalDate dateStop;
}

