package com.yhd.admin.api.domain.monitor.ventilation.convert;

import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkVentilationInfoDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkVentilationInfo;
import com.yhd.admin.api.domain.monitor.ventilation.vo.TbMkVentilationInfoVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkVentilationInfoCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkVentilationInfoUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 通风机基本信息
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkVentilationInfoConvert {

    @Mapping(target = "id", ignore = true)
    TbMkVentilationInfo convert(TbMkVentilationInfoCreateVO createVO);

    TbMkVentilationInfo convert(TbMkVentilationInfoUpdateVO updateVO);

    TbMkVentilationInfoVO convert(TbMkVentilationInfo po);

    TbMkVentilationInfoDTO toDTO(TbMkVentilationInfo po);



}
