package com.yhd.admin.api.domain.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 消息策略表(MkNoticeStrategy)实体类
 *
 * <AUTHOR>
 * @since 2025-08-08 09:12:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkNoticeStrategy extends BaseEntity implements Serializable {

    /** id */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 策略名称 */
    private String name;
    /** 策略编码 */
    private String code;
    /** 单位名称 */
    private String orgName;
    /** 单位代码 */
    private String orgCode;
    /** 策略状态：0-禁用，1-启用 */
    private String status;
    /** 策略描述 */
    private String description;
    /** 消息通道 */
    private String channel;
}

