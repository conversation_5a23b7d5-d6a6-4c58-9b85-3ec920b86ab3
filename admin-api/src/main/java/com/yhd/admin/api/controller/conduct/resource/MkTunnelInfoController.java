package com.yhd.admin.api.controller.conduct.resource;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.resource.MkTunnelInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.resource.MkTunnelInfoDTO;
import com.yhd.admin.api.domain.conduct.query.resource.MkTunnelInfoParam;
import com.yhd.admin.api.service.conduct.resource.MkTunnelInfoService;
import com.yhd.admin.api.domain.conduct.vo.resource.MkTunnelInfoVO;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 巷道信息控制层
 *
 * <AUTHOR>
 * @since 2025-07-28 08:21:47
 */
@RestController
@RequestMapping("/conduct/tunnel")
public class MkTunnelInfoController {

    public final MkTunnelInfoService mkTunnelInfoService;
    public final MkTunnelInfoConvert mkTunnelInfoConvert;

    public MkTunnelInfoController(
        MkTunnelInfoService mkTunnelInfoService,
        MkTunnelInfoConvert mkTunnelInfoConvert
    ) {
        this.mkTunnelInfoService = mkTunnelInfoService;
        this.mkTunnelInfoConvert = mkTunnelInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkTunnelInfoParam param) {
        IPage<MkTunnelInfoDTO> page = mkTunnelInfoService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkTunnelInfoConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkTunnelInfoParam param) {
        return RespJson.success(mkTunnelInfoConvert.toVOList(mkTunnelInfoService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkTunnelInfoVO> getCurrentDetails(@RequestBody MkTunnelInfoParam param) {
        return RespJson.success(mkTunnelInfoConvert.toVO(mkTunnelInfoService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkTunnelInfoParam param) {
        Boolean retVal = mkTunnelInfoService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkTunnelInfoParam param) {
        Boolean retVal = mkTunnelInfoService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

