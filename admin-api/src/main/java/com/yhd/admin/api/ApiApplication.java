package com.yhd.admin.api;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 */
@EnableTransactionManagement
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@SpringBootApplication
@ServletComponentScan
@MapperScan("com.yhd.admin.api.**.dao")
@EnableConfigurationProperties
public class ApiApplication {

    public static void main(String[] args) {
        SpringApplication bms = new SpringApplication(ApiApplication.class);
        bms.setBannerMode(Banner.Mode.OFF);
        bms.run(args);
    }
}
