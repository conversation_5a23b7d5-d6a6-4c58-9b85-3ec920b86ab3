package com.yhd.admin.api.service.monitor.ventilation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkVentilationOperationDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkVentilationOperation;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkVentilationOperationCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.page.TbMkVentilationOperationPageVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkVentilationOperationUpdateVO;

import java.util.List;

/**
 * 通风机整体运行参数 服务类
 *
 * <AUTHOR>
 * @since
 */
public interface TbMkVentilationOperationService  extends IService<TbMkVentilationOperation> {

    /**
     * 新建
     *
     * @param createVO 新建VO
     * @return id
     */
    Long create(TbMkVentilationOperationCreateVO createVO);

    /**
     * 更新
     *
     * @param updateVO 更新VO
     */
    int update(TbMkVentilationOperationUpdateVO updateVO);

    /**
     * 删除
     *
     * @param id id
     */
    int delete(Long id);

    /**
     * 查询单个
     *
     * @param id id
     * @return po
     */
    TbMkVentilationOperation get(Long id);

    /**
     * 分页查询
     *
     * @param pageVO 分页VO
     * @return pageResult
     */
    IPage<TbMkVentilationOperationDTO> page(TbMkVentilationOperationPageVO pageVO);

    /**
      * 批量删除
      *
      * @param ids 要删除的记录ID列表
      * @return 受影响的行数
      */
    int batchDelete(List<Long> ids);

    /**
     * 批量新增
     *
     * @param createVOs 新增VO列表
     * @return 新增的记录ID列表
     */
    int batchCreate(List<TbMkVentilationOperationCreateVO> createVOs);

    /**
     * 批量更新
     *
     * @param updateVOs 更新VO列表
     * @return 受影响的行数
     */
    int batchUpdate(List<TbMkVentilationOperationUpdateVO> updateVOs);
}
