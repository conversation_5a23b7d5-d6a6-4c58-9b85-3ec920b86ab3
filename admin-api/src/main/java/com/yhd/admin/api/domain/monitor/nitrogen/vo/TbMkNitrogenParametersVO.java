package com.yhd.admin.api.domain.monitor.nitrogen.vo;

import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenParametersCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 制氮系统参数表
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkNitrogenParametersVO extends TbMkNitrogenParametersCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
