package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.sys.AppDao;
import com.yhd.admin.api.domain.sys.convert.AppConvert;
import com.yhd.admin.api.domain.sys.dto.AppDTO;
import com.yhd.admin.api.domain.sys.entity.SysApp;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.domain.sys.query.AppParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 系统集成 service
 */
@Component
public class AppSrvImpl extends ServiceImpl<AppDao, SysApp> implements AppSrv {

    @Resource
    private AppConvert convert;

    @Override
    public IPage<AppDTO> pagingQuery(AppParam param) {
        IPage<SysApp> iPage = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<SysApp> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.like(StringUtils.isNotBlank(param.getAppName()), SysApp::getAppName, param.getAppName());
        queryChainWrapper.orderByAsc(SysApp::getSorted).orderByAsc(SysApp::getCreatedTime);
        return queryChainWrapper.page(iPage).convert(convert::toDTO);
    }

    @Override
    public Boolean add(AppParam param) {
        if (StringUtils.isBlank(param.getAppName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return super.save(convert.toEntity(param));
    }

    @Override
    public Boolean modify(AppParam param) {
        if (StringUtils.isBlank(param.getAppName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return super.updateById(convert.toEntity(param));
    }

    @Override
    public AppDTO getCurrentDetail(AppParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }
}
