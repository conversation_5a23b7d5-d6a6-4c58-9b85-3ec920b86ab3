package com.yhd.admin.api.domain.emergency.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.time.LocalDate;

/**
 * 生产安全总结信息VO
 *
 * <AUTHOR>
 */
@Data
public class MkEmergencySafetySummaryInfoVO {
    /**
     * 总结日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate summaryDate;

    /**
     * 总结标题
     */
    private String summaryTitle;

    /**
     * 总结内容
     */
    private String summaryContent;

    /**
     * 是否有内容
     */
    private Boolean hasContent;

    /**
     * 警告级别：0-正常，1-提醒，2-警告，3-严重
     */
    private Integer warningLevel;

    /**
     * 警告级别名称
     */
    private String warningLevelName;

    /**
     * 警告信息
     */
    private String warningMessage;

    /**
     * 负责人
     */
    private String responsiblePerson;

    /**
     * 负责部门
     */
    private String department;
}