package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyDevice;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyDeviceParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencyDeviceService.java
 * @Description 应急救援设备信息Service接口
 * @createTime 2025年07月30日 17:25:00
 */
public interface MkEmergencyDeviceService {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<MkEmergencyDevice> pagingQuery(MkEmergencyDeviceParam param);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return 设备信息
     */
    MkEmergencyDevice getById(Integer id);

    /**
     * 新增
     *
     * @param param 参数
     * @return 是否成功
     */
    boolean add(MkEmergencyDeviceParam param);

    /**
     * 修改
     *
     * @param param 参数
     * @return 是否成功
     */
    boolean modify(MkEmergencyDeviceParam param);

    /**
     * 删除
     *
     * @param id ID
     * @return 是否成功
     */
    boolean remove(Integer id);

    /**
     * 批量删除
     *
     * @param ids ID列表
     * @return 是否成功
     */
    boolean removeBatch(List<Integer> ids);
}
