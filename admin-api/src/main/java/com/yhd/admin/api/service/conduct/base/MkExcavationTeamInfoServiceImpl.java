package com.yhd.admin.api.service.conduct.base;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.base.MkExcavationTeamInfoDao;
import com.yhd.admin.api.domain.conduct.convert.base.MkExcavationTeamInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.base.MkExcavationTeamInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkExcavationTeamInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkExcavationTeamInfoParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 掘进队信息服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25 14:01:29
 */
@Service
public class MkExcavationTeamInfoServiceImpl extends ServiceImpl<MkExcavationTeamInfoDao, MkExcavationTeamInfo> implements MkExcavationTeamInfoService {

    private final MkExcavationTeamInfoConvert mkExcavationTeamInfoConvert;

    public MkExcavationTeamInfoServiceImpl(MkExcavationTeamInfoConvert mkExcavationTeamInfoConvert) {
        this.mkExcavationTeamInfoConvert = mkExcavationTeamInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkExcavationTeamInfoDTO> pagingQuery(MkExcavationTeamInfoParam param) {
        IPage<MkExcavationTeamInfo> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkExcavationTeamInfo> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.eq(StringUtils.isNotBlank(param.getName()), MkExcavationTeamInfo::getName, param.getName());
        wrapper.eq(StringUtils.isNotBlank(param.getTunnelName()), MkExcavationTeamInfo::getTunnelName, param.getTunnelName());
        return wrapper.page(page).convert(mkExcavationTeamInfoConvert::toDTO);
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkExcavationTeamInfoDTO> queryList(MkExcavationTeamInfoParam param) {
        LambdaQueryWrapper<MkExcavationTeamInfo> wrapper = new LambdaQueryWrapper<>();
        return mkExcavationTeamInfoConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param  数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkExcavationTeamInfoParam param) {
        MkExcavationTeamInfo entity = mkExcavationTeamInfoConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return MkCoalInfoDTO
     */
    @Override
    public MkExcavationTeamInfoDTO getCurrentDetails(MkExcavationTeamInfoParam param) {
        return mkExcavationTeamInfoConvert.toDTO(super.getById(param.getId()));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkExcavationTeamInfoParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }
}
