package com.yhd.admin.api.service.monitor.digging.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.digging.TbMkDiggingEnvironmentDao;
import com.yhd.admin.api.domain.monitor.digging.convert.TbMkDiggingEnvironmentConvert;
import com.yhd.admin.api.domain.monitor.digging.dto.TbMkDiggingEnvironmentDTO;
import com.yhd.admin.api.domain.monitor.digging.entity.TbMkDiggingEnvironment;
import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingEnvironmentCreateVO;
import com.yhd.admin.api.domain.monitor.digging.vo.page.TbMkDiggingEnvironmentPageVO;
import com.yhd.admin.api.domain.monitor.digging.vo.update.TbMkDiggingEnvironmentUpdateVO;
import com.yhd.admin.api.service.monitor.digging.TbMkDiggingEnvironmentService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 掘进机环境参数 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkDiggingEnvironmentServiceImpl extends ServiceImpl<TbMkDiggingEnvironmentDao, TbMkDiggingEnvironment> implements TbMkDiggingEnvironmentService {

    @Resource
    private TbMkDiggingEnvironmentConvert tbMkDiggingEnvironmentConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkDiggingEnvironmentCreateVO createVO) {
        TbMkDiggingEnvironment tbMkDiggingEnvironment = tbMkDiggingEnvironmentConvert.convert(createVO);
        baseMapper.insert(tbMkDiggingEnvironment);
        return tbMkDiggingEnvironment.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkDiggingEnvironmentUpdateVO updateVO) {
        TbMkDiggingEnvironment tbMkDiggingEnvironment = tbMkDiggingEnvironmentConvert.convert(updateVO);
        return baseMapper.updateById(tbMkDiggingEnvironment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkDiggingEnvironment get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkDiggingEnvironmentDTO> page(TbMkDiggingEnvironmentPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkDiggingEnvironment> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkDiggingEnvironment> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.eq(param.getDiggingId() != null, TbMkDiggingEnvironment::getDiggingId, param.getDiggingId());
        queryChainWrapper.eq(param.getPersonnelCount() != null, TbMkDiggingEnvironment::getPersonnelCount, param.getPersonnelCount());
        queryChainWrapper.orderByDesc(TbMkDiggingEnvironment::getId);
        return queryChainWrapper.page(iPage).convert(tbMkDiggingEnvironmentConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkDiggingEnvironmentCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 掘进机环境参数：传入的列表为空");
            return 0;
        }
        List<TbMkDiggingEnvironment> entities = createVOs.stream()
                .map(tbMkDiggingEnvironmentConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 掘进机环境参数 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkDiggingEnvironmentUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 掘进机环境参数：传入的列表为空");
            return 0;
        }
        List<TbMkDiggingEnvironment> entities = updateVOs.stream()
                .map(tbMkDiggingEnvironmentConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 掘进机环境参数：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 掘进机环境参数 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
