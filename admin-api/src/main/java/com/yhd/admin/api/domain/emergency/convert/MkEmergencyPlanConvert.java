package com.yhd.admin.api.domain.emergency.convert;

import com.yhd.admin.api.domain.emergency.dto.MkEmergencyPlanDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyPlan;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyPlanParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyPlanVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 应急救援数字预案表转换类
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface MkEmergencyPlanConvert {

    /**
     * Entity转DTO
     */
    MkEmergencyPlanDTO toDTO(MkEmergencyPlan entity);

    /**
     * Entity列表转DTO列表
     */
    List<MkEmergencyPlanDTO> toDTO(List<MkEmergencyPlan> entityList);

    /**
     * DTO转VO
     */
    @Mapping(target = "planStatusName", expression = "java(getPlanStatusName(dto.getPlanStatus()))")
    @Mapping(target = "isExpiringSoon", expression = "java(isExpiringSoon(dto.getExpiryDate()))")
    @Mapping(target = "needsDrill", expression = "java(needsDrill(dto.getLastDrillDate(), dto.getDrillFrequency()))")
    @Mapping(target = "needsReview", expression = "java(needsReview(dto.getLastReviewDate(), dto.getReviewCycle()))")
    MkEmergencyPlanVO toVO(MkEmergencyPlanDTO dto);

    /**
     * DTO列表转VO列表
     */
    List<MkEmergencyPlanVO> toVO(List<MkEmergencyPlanDTO> dtoList);

    /**
     * Param转Entity
     */
    MkEmergencyPlan toEntity(MkEmergencyPlanParam param);

    /**
     * Param列表转Entity列表
     */
    List<MkEmergencyPlan> toEntity(List<MkEmergencyPlanParam> paramList);

    /**
     * 获取预案状态名称
     */
    default String getPlanStatusName(Integer planStatus) {
        if (planStatus == null) {
            return null;
        }
        switch (planStatus) {
            case 1:
                return "草稿";
            case 2:
                return "审核中";
            case 3:
                return "已发布";
            case 4:
                return "已废止";
            default:
                return "未知";
        }
    }

    /**
     * 判断是否即将到期（30天内）
     */
    default Boolean isExpiringSoon(LocalDateTime expiryDate) {
        if (expiryDate == null) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime thirtyDaysLater = now.plusDays(30);
        return expiryDate.isBefore(thirtyDaysLater) && expiryDate.isAfter(now);
    }

    /**
     * 判断是否需要演练
     */
    default Boolean needsDrill(LocalDateTime lastDrillDate, String drillFrequency) {
        if (lastDrillDate == null || drillFrequency == null) {
            return true;
        }
        
        LocalDateTime now = LocalDateTime.now();
        int months = getMonthsFromFrequency(drillFrequency);
        if (months <= 0) {
            return false;
        }
        
        LocalDateTime nextDrillDate = lastDrillDate.plusMonths(months);
        return now.isAfter(nextDrillDate);
    }

    /**
     * 判断是否需要评审
     */
    default Boolean needsReview(LocalDateTime lastReviewDate, Integer reviewCycle) {
        if (lastReviewDate == null) {
            return true;
        }
        if (reviewCycle == null || reviewCycle <= 0) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextReviewDate = lastReviewDate.plusMonths(reviewCycle);
        return now.isAfter(nextReviewDate);
    }

    /**
     * 从演练频次字符串中提取月数
     */
    default int getMonthsFromFrequency(String frequency) {
        if (frequency == null) {
            return 0;
        }
        
        frequency = frequency.toLowerCase();
        if (frequency.contains("月")) {
            try {
                String number = frequency.replaceAll("[^0-9]", "");
                return Integer.parseInt(number);
            } catch (NumberFormatException e) {
                return 0;
            }
        } else if (frequency.contains("季")) {
            return 3;
        } else if (frequency.contains("半年")) {
            return 6;
        } else if (frequency.contains("年")) {
            return 12;
        }
        
        return 0;
    }
}
