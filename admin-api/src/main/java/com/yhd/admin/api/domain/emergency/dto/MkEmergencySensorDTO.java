package com.yhd.admin.api.domain.emergency.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 应急救援监测信息表(传感器表) DTO
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkEmergencySensorDTO extends BaseDTO implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 传感器编码
     */
    private String sensorCode;

    /**
     * 传感器名称
     */
    private String sensorName;

    /**
     * 传感器类型：氧气传感器、二氧化碳传感器、气体传感器、温度传感器等
     */
    private String sensorType;

    /**
     * 安装位置
     */
    private String location;

    /**
     * 关联工作面
     */
    private String workFace;

    /**
     * 关联巷道
     */
    private String tunnel;

    /**
     * 父级传感器ID，用于构建层级关系
     */
    private Integer parentId;

    /**
     * 层级类型：mine-矿井，area-采区，workface-工作面，tunnel-巷道，point-监测点
     */
    private String levelType;

    /**
     * 层级编码，如：1302、1308等工作面编码
     */
    private String levelCode;

    /**
     * 层级名称，如：1302工作面采区
     */
    private String levelName;

    /**
     * 层级路径，如：/mine/area_1302/workface_1302
     */
    private String levelPath;

    /**
     * 同级排序号
     */
    private Integer sortOrder;

    /**
     * 是否叶子节点：0-否，1-是
     */
    private Integer isLeaf;

    /**
     * 传感器状态：1-正常，2-异常，3-离线
     */
    private Integer status;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 当前值
     */
    private BigDecimal currentValue;

    /**
     * 最小阈值
     */
    private BigDecimal thresholdMin;

    /**
     * 最大阈值
     */
    private BigDecimal thresholdMax;

    /**
     * 测量单位
     */
    private String unit;

    /**
     * 传感器描述
     */
    private String description;

    /**
     * 备注
     */
    private String remark;

    /**
     * 子节点列表（用于树形结构）
     */
    private List<MkEmergencySensorDTO> children;

    /**
     * 父节点名称（用于显示）
     */
    private String parentName;

    /**
     * 状态文本（用于显示）
     */
    private String statusText;

    /**
     * 传感器类型文本（用于显示）
     */
    private String sensorTypeText;

    /**
     * 层级类型文本（用于显示）
     */
    private String levelTypeText;
}
