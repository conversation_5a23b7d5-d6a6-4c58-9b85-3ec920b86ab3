package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkStorageDetailConvert;
import com.yhd.admin.api.domain.emergency.dto.MkStorageDetailDTO;
import com.yhd.admin.api.domain.emergency.query.MkStorageDetailParam;
import com.yhd.admin.api.domain.emergency.vo.MkStorageDetailVO;
import com.yhd.admin.api.service.emergency.MkStorageDetailService;
import com.yhd.admin.common.domain.PageRespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 应急物资记录表 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/open/emergency/storage")
public class MkStorageDetailController {

    @Resource
    private MkStorageDetailService storageDetailService;

    @Resource
    private MkStorageDetailConvert convert;

    /**
     * 分页查询应急物资信息
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkStorageDetailVO> pagingQuery(@RequestBody MkStorageDetailParam queryParam) {
        log.info("分页查询应急物资信息，参数：{}", queryParam);
        IPage<MkStorageDetailDTO> iPage = storageDetailService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }


}
