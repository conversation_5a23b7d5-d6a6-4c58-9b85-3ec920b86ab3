package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkStorageDetailConvert;
import com.yhd.admin.api.domain.emergency.dto.MkStorageDetailDTO;
import com.yhd.admin.api.domain.emergency.query.MkStorageDetailParam;
import com.yhd.admin.api.domain.emergency.vo.MkStorageDetailVO;
import com.yhd.admin.api.service.emergency.MkStorageDetailService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 应急物资记录表 Controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/emergency/storage")
public class MkStorageDetailController {

    @Resource
    private MkStorageDetailService storageDetailService;

    @Resource
    private MkStorageDetailConvert convert;

    /**
     * 分页查询应急物资信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkStorageDetailVO> pagingQuery(@RequestBody MkStorageDetailParam queryParam) {
        log.info("分页查询应急物资信息，参数：{}", queryParam);
        IPage<MkStorageDetailDTO> iPage = storageDetailService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    /**
     * 新增应急物资信息
     * @param param 应急物资信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkStorageDetailParam param) {
        log.info("新增应急物资信息，参数：{}", param);
        Boolean result = storageDetailService.add(param);
        return RespJson.success(result);
    }

    /**
     * 修改应急物资信息
     * @param param 应急物资信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> modify(@RequestBody MkStorageDetailParam param) {
        log.info("修改应急物资信息，参数：{}", param);
        Boolean result = storageDetailService.modify(param);
        return RespJson.success(result);
    }

    /**
     * 获取应急物资详情
     * @param param 查询参数
     * @return 应急物资详情
     */
    @PostMapping(value = "/getCurrentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkStorageDetailVO> getCurrentDetail(@RequestBody MkStorageDetailParam param) {
        log.info("获取应急物资详情，参数：{}", param);
        MkStorageDetailDTO dto = storageDetailService.getCurrentDetail(param);
        return RespJson.success(convert.toVO(dto));
    }

    /**
     * 批量删除应急物资信息
     * @param param 批量删除参数
     * @return 操作结果
     */
    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody BatchParam param) {
        log.info("批量删除应急物资信息，参数：{}", param);
        Boolean result = storageDetailService.removeBatch(param);
        return RespJson.success(result);
    }
}
