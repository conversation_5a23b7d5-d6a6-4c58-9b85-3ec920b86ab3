package com.yhd.admin.api.domain.comm.convert;

import com.yhd.admin.api.domain.comm.dto.MkCommContactDTO;
import com.yhd.admin.api.domain.comm.entity.MkCommContact;
import com.yhd.admin.api.domain.comm.query.MkCommContactParam;
import com.yhd.admin.api.domain.comm.vo.MkCommContactVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MkCommContactConvert {
  MkCommContactDTO toDTO(MkCommContact entity);

  MkCommContactVO toVO(MkCommContactDTO dto);

  MkCommContact toEntity(MkCommContactParam param);
}
