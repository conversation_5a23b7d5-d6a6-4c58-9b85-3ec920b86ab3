package com.yhd.admin.api.domain.monitor.nitrogen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.*;

/**
 * 制氮系统监控数据-主表
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_mk_nitrogen_system_main")
public class TbMkNitrogenSystemMain extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 系统名称
     */
    @TableField("system_name")
    private String systemName;

    /**
     * 制氮控制箱状态: 0-停止, 1-运行, 2-故障, 3-待机
     */
    @TableField("control_box_status")
    private Byte controlBoxStatus;
}
