package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yhd.admin.api.dao.operation.MkNoticeStrategyDao;
import com.yhd.admin.api.domain.operation.convert.MkNoticeStrategyConvert;
import com.yhd.admin.api.domain.operation.dto.MkNoticeStrategyDTO;
import com.yhd.admin.api.domain.operation.entity.MkNoticeStrategy;
import com.yhd.admin.api.domain.operation.query.MkNoticeStrategyParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.common.utils.JacksonUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 消息策略表服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-08 09:12:47
 */
@Service
public class MkNoticeStrategyServiceImpl extends ServiceImpl<MkNoticeStrategyDao, MkNoticeStrategy> implements MkNoticeStrategyService {

    private final MkNoticeStrategyConvert mkNoticeStrategyConvert;

    public MkNoticeStrategyServiceImpl(MkNoticeStrategyConvert mkNoticeStrategyConvert) {
        this.mkNoticeStrategyConvert = mkNoticeStrategyConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public IPage<MkNoticeStrategyDTO> pagingQuery(MkNoticeStrategyParam param) {
        IPage<MkNoticeStrategy> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkNoticeStrategy> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.eq(StringUtils.isNotBlank(param.getOrgCode()), MkNoticeStrategy::getOrgCode, param.getOrgCode());
        wrapper.like(StringUtils.isNotBlank(param.getName()), MkNoticeStrategy::getName, param.getName())
            .or().like(StringUtils.isNotBlank(param.getName()), MkNoticeStrategy::getCode, param.getName());
        IPage<MkNoticeStrategyDTO> result = wrapper.page(page).convert(mkNoticeStrategyConvert::toDTO);
        List<MkNoticeStrategyDTO> list = result.getRecords();
        for (MkNoticeStrategyDTO dto : list) {
            String channel = dto.getChannel();
            // 消息通道
            dto.setChannels(JacksonUtils.jsonStr2Obj(channel, new TypeReference<>() {}));
        }
        result.setRecords(list);
        return result;
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkNoticeStrategyDTO> queryList(MkNoticeStrategyParam param) {
        LambdaQueryWrapper<MkNoticeStrategy> wrapper = new LambdaQueryWrapper<>();
        return mkNoticeStrategyConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkNoticeStrategyParam param) {
        MkNoticeStrategy entity = mkNoticeStrategyConvert.toEntity(param);
        entity.setChannel(JacksonUtils.obj2JsonStr(param.getChannels()));
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 主键
     * @return 详细信息
     */
    @Override
    public MkNoticeStrategyDTO getCurrentDetails(MkNoticeStrategyParam param) {
        MkNoticeStrategyDTO dto = mkNoticeStrategyConvert.toDTO(super.getById(param.getId()));
        String channel = dto.getChannel();
        dto.setChannels(JacksonUtils.jsonStr2Obj(channel,
            new TypeReference<Map<String, List<Map<String, Object>>>>() {}));

        return dto;
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkNoticeStrategyParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }
}
