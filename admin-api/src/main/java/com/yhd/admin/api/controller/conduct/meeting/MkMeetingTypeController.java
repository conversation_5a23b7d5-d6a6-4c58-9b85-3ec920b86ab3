package com.yhd.admin.api.controller.conduct.meeting;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.meeting.MkMeetingTypeConvert;
import com.yhd.admin.api.domain.conduct.dto.meeting.MkMeetingTypeDTO;
import com.yhd.admin.api.domain.conduct.query.meeting.MkMeetingTypeParam;
import com.yhd.admin.api.domain.conduct.vo.meeting.MkMeetingTypeVO;
import com.yhd.admin.api.service.conduct.meeting.MkMeetingTypeService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 会议类型表控制层
 *
 * <AUTHOR>
 * @since 2025-07-28 10:36:44
 */
@RestController
@RequestMapping("/conduct/meeting/type")
public class MkMeetingTypeController {

    private final MkMeetingTypeService mkMeetingTypeService;
    private final MkMeetingTypeConvert mkMeetingTypeConvert;

    public MkMeetingTypeController(
        MkMeetingTypeService mkMeetingTypeService,
        MkMeetingTypeConvert mkMeetingTypeConvert
    ) {
        this.mkMeetingTypeService = mkMeetingTypeService;
        this.mkMeetingTypeConvert = mkMeetingTypeConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkMeetingTypeParam param) {
        IPage<MkMeetingTypeDTO> page = mkMeetingTypeService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkMeetingTypeConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkMeetingTypeParam param) {
        return RespJson.success(mkMeetingTypeConvert.toVOList(mkMeetingTypeService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkMeetingTypeVO> getCurrentDetails(@RequestBody MkMeetingTypeParam param) {
        return RespJson.success(mkMeetingTypeConvert.toVO(mkMeetingTypeService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkMeetingTypeParam param) {
        Boolean retVal = mkMeetingTypeService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkMeetingTypeParam param) {
        Boolean retVal = mkMeetingTypeService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

