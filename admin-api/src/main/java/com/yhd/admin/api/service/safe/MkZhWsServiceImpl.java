package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.safe.MkZhWsDao;
import com.yhd.admin.api.domain.safe.convert.MkZhWsConvert;
import com.yhd.admin.api.domain.safe.dto.MkZhWsDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhWs;
import com.yhd.admin.api.domain.safe.query.MkZhWsParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;


@Service
public class MkZhWsServiceImpl extends ServiceImpl<MkZhWsDao, MkZhWs> implements MkZhWsService {

    private final MkZhWsConvert convert;

    public MkZhWsServiceImpl(MkZhWsConvert convert) {
        this.convert = convert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addOrModify(MkZhWsParam param) {
        MkZhWs entity = convert.toEntity(param);
        return super.saveOrUpdate(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(MkZhWsParam param) {
        return super.removeById(param.getId());
    }

    @Override
    public MkZhWsDTO getCurrentDetail(MkZhWsParam param) {
        MkZhWsDTO result;
        if (Objects.isNull(param.getId())) {
            throw new BMSException(ExceptionEnum.MISS_PARAM_EXCEPTION);
        }
        MkZhWs entity = super.getById(param);
        result = convert.toDTO(entity);
        return result;
    }

    @Override
    public IPage<MkZhWsDTO> pagingQuery(MkZhWsParam param) {
        IPage<MkZhWs> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkZhWs> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        // 排序
        wrapper.orderByAsc(MkZhWs::getUpdatedTime);
        IPage<MkZhWs> iPage = wrapper.page(page);
        return iPage.convert(convert::toDTO);
    }

    @Override
    public MkZhWsDTO getData() {
        LambdaQueryChainWrapper<MkZhWs> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return convert.toDTO(wrapper.list().get(0));
    }
}
