package com.yhd.admin.api.controller.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.operation.convert.MkNoticeRuleConvert;
import com.yhd.admin.api.domain.operation.dto.MkNoticeRuleDTO;
import com.yhd.admin.api.domain.operation.query.MkNoticeRuleParam;
import com.yhd.admin.api.domain.operation.vo.MkNoticeRuleVO;
import com.yhd.admin.api.service.operation.MkNoticeRuleService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 消息下发规则表控制层
 *
 * <AUTHOR>
 * @since 2025-08-08 09:12:47
 */
@RestController
@RequestMapping("/operation/notice/rule")
public class MkNoticeRuleController {

    private final MkNoticeRuleService mkNoticeRuleService;
    private final MkNoticeRuleConvert mkNoticeRuleConvert;

    public MkNoticeRuleController(
        MkNoticeRuleService mkNoticeRuleService,
        MkNoticeRuleConvert mkNoticeRuleConvert
    ) {
        this.mkNoticeRuleService = mkNoticeRuleService;
        this.mkNoticeRuleConvert = mkNoticeRuleConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkNoticeRuleParam param) {
        IPage<MkNoticeRuleDTO> page = mkNoticeRuleService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkNoticeRuleConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkNoticeRuleParam param) {
        return RespJson.success(mkNoticeRuleConvert.toVOList(mkNoticeRuleService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkNoticeRuleVO> getCurrentDetails(@RequestBody MkNoticeRuleParam param) {
        return RespJson.success(mkNoticeRuleConvert.toVO(mkNoticeRuleService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> addOrModify(@RequestBody MkNoticeRuleParam param) {
        Boolean retVal = mkNoticeRuleService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkNoticeRuleParam param) {
        Boolean retVal = mkNoticeRuleService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

