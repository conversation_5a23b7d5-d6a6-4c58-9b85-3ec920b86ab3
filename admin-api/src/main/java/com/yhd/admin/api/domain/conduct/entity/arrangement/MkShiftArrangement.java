package com.yhd.admin.api.domain.conduct.entity.arrangement;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 值班带班安排表(MkShiftArrangement)实体类
 *
 * <AUTHOR>
 * @since 2025-07-29 14:11:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkShiftArrangement extends BaseEntity implements Serializable {

    /** id */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 班次日期 */
    private LocalDate shiftDate;
    /** 班次星期 */
    private String shiftWeek;
    /** 班次类型名称 1：值班 2：夜班 3：白班 4：中班 */
    private String shiftTypeName;
    /** 班次类型代码 1：值班 2：夜班 3：白班 4：中班 */
    private String shiftTypeCode;
    /** 值班人员账号 */
    private String account;
    /** 值班人员姓名 */
    private String name;
    /** 计划值班人员账号 */
    private String planAccount;
    /** 计划值班人员姓名 */
    private String planName;
    /** 是否调班（0：否 1：是） */
    private Integer isPlanFlag;
}

