package com.yhd.admin.api.domain.sys.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/3 10:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ClientDTO extends BaseDTO {
    private String id;
    private String clientName;
    /** 模块名称 */
    private String clientId;
    /** 资源名称 */
    private String resourceId;
}
