package com.yhd.admin.api.domain.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 图文类别表
 *
 * <AUTHOR>
 * @date 2025/7/29 9:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysFileType extends BaseEntity implements Serializable {

  @TableId(type = IdType.AUTO)
  private Long id;
  /** 类型名称 */
  private String typeName;
  /** 父级ID */
  private Long parentId;
  /** 父级名称 */
  private String parentName;
  /** 状态 */
  private Boolean status;
  /** 排序，越小越靠前 */
  private Long sortNum;
}
