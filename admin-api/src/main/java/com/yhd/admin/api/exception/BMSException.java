package com.yhd.admin.api.exception;

import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BMSException.java
 * @Description TODO
 * @createTime 2020年04月01日 15:02:00
 */
@Getter
public class BMSException extends RuntimeException {

    private final String code;

    private final String desc;

    public BMSException(String code, String desc) {
        super(desc);
        this.code = code;
        this.desc = desc;
    }

    public BMSException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum.getDesc());
        this.code = exceptionEnum.getCode();
        this.desc = exceptionEnum.getDesc();
    }
}
