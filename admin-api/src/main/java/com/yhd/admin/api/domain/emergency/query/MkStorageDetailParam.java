package com.yhd.admin.api.domain.emergency.query;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.yhd.admin.common.domain.query.QueryParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 应急物资记录表查询参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
public class MkStorageDetailParam extends QueryParam implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 物资系统编码
     */
    private String materialSystemCode;

    /**
     * 物资编码
     */
    private String materialCode;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 仓库标识
     */
    private String storageId;

    /**
     * 物资名称（模糊查询）
     */
    private String materialName;

    /**
     * 物资大类（模糊查询）
     */
    private String catalogType;

    /**
     * 物资分类（模糊查询）
     */
    private String materialType;

    /**
     * 规格型号（模糊查询）
     */
    private String materialModel;

    /**
     * 主要技术参数
     */
    private String materialSpecification;

    /**
     * 供货厂家（模糊查询）
     */
    private String materialManufacture;

    /**
     * 计量单位
     */
    private String unitOfMeasure;

    /**
     * 库存数量
     */
    private Double qtyInStock;

    /**
     * 在修数量
     */
    private Double qtyInRepair;

    /**
     * 使用数量
     */
    private Double qtyInUse;

    /**
     * 报废数量
     */
    private Double qtyInScrap;

    /**
     * 总数量
     */
    private Double totalQty;

    /**
     * 库存数量-最小值
     */
    private Double qtyInStockMin;

    /**
     * 库存数量-最大值
     */
    private Double qtyInStockMax;

    /**
     * 状态
     */
    private String materialStatus;

    /**
     * 使用情况
     */
    private String usageSituation;

    /**
     * 采购到货情况
     */
    private String purchaseSituation;

    /**
     * 最后检测时间
     */
    private LocalDate checkDate;

    /**
     * 到期时间
     */
    private LocalDate dqsjDate;

    /**
     * 最后检测时间-开始
     */
    private LocalDate checkDateStart;

    /**
     * 最后检测时间-结束
     */
    private LocalDate checkDateEnd;

    /**
     * 检测情况
     */
    private String checkSituation;

    /**
     * 到期时间-开始
     */
    private LocalDate dqsjDateStart;

    /**
     * 到期时间-结束
     */
    private LocalDate dqsjDateEnd;
}
