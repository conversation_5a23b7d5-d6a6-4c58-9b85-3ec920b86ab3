package com.yhd.admin.api.domain.alarm.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 测点数据表
 *
 * <AUTHOR>
 * @date 2025/8/7 16:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkMonitorPointDataDTO extends BaseDTO implements Serializable {

  private Long id;

  /** 单位编码 */
  private String orgCode;
  /** 单位名称 */
  private String orgName;
  /** 子系统code */
  private String systemCode;
  /** 子系统名称 */
  private String systemName;
  /** 测点名称 */
  private String pointName;
  /** 测点编号 */
  private String pointCode;
  /** 监测类型code */
  private String monitorTypeCode;
  /** 监测类型名称 */
  private String monitorTypeName;
  /** 测点数据 */
  private Double dataValue;
  /** 数据单位 */
  private String dataUnit;
  /** 报警类型 */
  private String alarmType;
  /** 连接状态 */
  private String connectStatus;
}
