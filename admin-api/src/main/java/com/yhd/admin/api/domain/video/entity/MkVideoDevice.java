package com.yhd.admin.api.domain.video.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 视频设备表
 *
 * <AUTHOR>
 * @date 2025/8/7 14:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkVideoDevice extends BaseEntity implements Serializable {
  @TableId(type = IdType.AUTO)
  private Long id;

  /** 设备名称 */
  private String name;
  /** 设备ip地址 */
  private String ipAddr;
  /** 设备端口 */
  private String port;
  /** 设备类型code */
  private String devTypeCode;
  /** 设备类型名称 */
  private String devTypeName;
  /** 设备登录账号 */
  private String userName;
  /** 设备登录密码 */
  private String userPwd;
  /** 设备状态 */
  private String deviceStatus;
}
