package com.yhd.admin.api.domain.emergency.vo;

import lombok.Data;

/**
 * 安全隐患清单VO
 *
 * <AUTHOR>
 */
@Data
public class MkSafetyHazardListVO {

    /**
     * 隐患ID
     */
    private Long hazardId;

    /**
     * 隐患名称
     */
    private String hazardName;

    /**
     * 隐患描述
     */
    private String hazardDescription;

    /**
     * 隐患等级：一般、重大
     */
    private String hazardLevel;

    /**
     * 隐患等级颜色
     */
    private String hazardLevelColor;

    /**
     * 专业类型
     */
    private String professionalType;

    /**
     * 发现时间
     */
    private String discoveryTime;

    /**
     * 责任人
     */
    private String responsiblePerson;

    /**
     * 整改状态
     */
    private String rectificationStatus;

    /**
     * 整改期限
     */
    private String rectificationDeadline;
}