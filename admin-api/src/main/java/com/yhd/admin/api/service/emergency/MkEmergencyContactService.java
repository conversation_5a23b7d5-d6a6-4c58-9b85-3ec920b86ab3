package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyContactDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyContact;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyContactParam;
import com.yhd.admin.common.domain.query.BatchParam;

import java.util.List;

/**
 * 应急救援通讯录表 Service
 * <AUTHOR>
 */
public interface MkEmergencyContactService extends IService<MkEmergencyContact> {

    /**
     * 分页查询应急救援通讯录信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    IPage<MkEmergencyContactDTO> pagingQuery(MkEmergencyContactParam queryParam);

    /**
     * 新增应急救援通讯录信息
     * @param param 应急救援通讯录信息参数
     * @return 是否成功
     */
    Boolean add(MkEmergencyContactParam param);

    /**
     * 修改应急救援通讯录信息
     * @param param 应急救援通讯录信息参数
     * @return 是否成功
     */
    Boolean modify(MkEmergencyContactParam param);

    /**
     * 获取应急救援通讯录详情
     * @param param 查询参数
     * @return 应急救援通讯录详情
     */
    MkEmergencyContactDTO getCurrentDetail(MkEmergencyContactParam param);

    /**
     * 批量删除应急救援通讯录信息
     * @param param 批量删除参数
     * @return 是否成功
     */
    Boolean removeBatch(BatchParam param);

    /**
     * 根据部门查询联系人列表
     * @param department 部门名称
     * @return 联系人列表
     */
    List<MkEmergencyContactDTO> getContactsByDepartment(String department);

    /**
     * 获取应急负责人列表
     * @param orgCode 组织编码
     * @return 应急负责人列表
     */
    List<MkEmergencyContactDTO> getEmergencyLeaders(String orgCode);
}
