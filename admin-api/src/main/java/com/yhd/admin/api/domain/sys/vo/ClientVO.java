package com.yhd.admin.api.domain.sys.vo;

import java.io.Serializable;

import com.yhd.admin.common.domain.vo.BaseVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/3 10:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ClientVO extends BaseVO implements Serializable, Cloneable {

    private String id;

    private String clientName;
    /** 模块名称 */
    private String clientId;
    /** 资源名称 */
    private String resourceId;
}
