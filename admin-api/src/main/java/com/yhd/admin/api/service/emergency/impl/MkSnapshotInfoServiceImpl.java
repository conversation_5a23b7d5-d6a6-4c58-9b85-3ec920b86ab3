package com.yhd.admin.api.service.emergency.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.emergency.MkSnapshotInfoDao;
import com.yhd.admin.api.domain.emergency.convert.MkSnapshotInfoConvert;
import com.yhd.admin.api.domain.emergency.dto.MkSnapshotInfoDTO;
import com.yhd.admin.api.domain.emergency.entity.MkSnapshotInfo;
import com.yhd.admin.api.domain.emergency.query.MkSnapshotInfoParam;
import com.yhd.admin.api.service.emergency.MkSnapshotInfoService;
import com.yhd.admin.common.domain.query.BatchParam;

import lombok.RequiredArgsConstructor;

/**
 * 快照信息服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Service
@RequiredArgsConstructor
public class MkSnapshotInfoServiceImpl extends ServiceImpl<MkSnapshotInfoDao, MkSnapshotInfo> implements MkSnapshotInfoService {

    private final MkSnapshotInfoConvert convert;

    @Override
    public IPage<MkSnapshotInfoDTO> pagingQuery(MkSnapshotInfoParam param) {
        IPage<MkSnapshotInfo> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryWrapper<MkSnapshotInfo> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(param.getSnapshotId() != null, MkSnapshotInfo::getSnapshotId, param.getSnapshotId())
               .eq(StringUtils.isNotBlank(param.getListType()), MkSnapshotInfo::getListType, param.getListType())
               .like(StringUtils.isNotBlank(param.getEventId()), MkSnapshotInfo::getEventId, param.getEventId())
               .like(StringUtils.isNotBlank(param.getEventName()), MkSnapshotInfo::getEventName, param.getEventName())
               .eq(StringUtils.isNotBlank(param.getInfoType()), MkSnapshotInfo::getInfoType, param.getInfoType())
               .like(StringUtils.isNotBlank(param.getInfoCategory()), MkSnapshotInfo::getInfoCategory, param.getInfoCategory())
               .like(StringUtils.isNotBlank(param.getInfoName()), MkSnapshotInfo::getInfoName, param.getInfoName())
               .like(StringUtils.isNotBlank(param.getDataSource()), MkSnapshotInfo::getDataSource, param.getDataSource())
               .like(StringUtils.isNotBlank(param.getLocationInfo()), MkSnapshotInfo::getLocationInfo, param.getLocationInfo())
               .eq(param.getPriorityLevel() != null, MkSnapshotInfo::getPriorityLevel, param.getPriorityLevel())
               .eq(StringUtils.isNotBlank(param.getStatus()), MkSnapshotInfo::getStatus, param.getStatus())
               .ge(param.getCollectTimeStart() != null, MkSnapshotInfo::getCollectTime, param.getCollectTimeStart())
               .le(param.getCollectTimeEnd() != null, MkSnapshotInfo::getCollectTime, param.getCollectTimeEnd())
               .orderByDesc(MkSnapshotInfo::getCollectTime);

        return page(page, wrapper).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkSnapshotInfoParam param) {
        MkSnapshotInfo entity = convert.toEntity(param);
        return save(entity);
    }

    @Override
    public Boolean modify(MkSnapshotInfoParam param) {
        MkSnapshotInfo entity = convert.toEntity(param);
        return updateById(entity);
    }

    @Override
    public MkSnapshotInfoDTO getCurrentDetail(MkSnapshotInfoParam param) {
        if (param.getId() == null) {
            return null;
        }
        MkSnapshotInfo entity = getById(param.getId());
        return entity != null ? convert.toDTO(entity) : null;
    }

    @Override
    public Boolean removeBatch(BatchParam param) {
        if (param.getId() == null || param.getId().isEmpty()) {
            return false;
        }
        return removeByIds(param.getId());
    }

    @Override
    public List<MkSnapshotInfoDTO> getInfosBySnapshotId(Long snapshotId) {
        LambdaQueryWrapper<MkSnapshotInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkSnapshotInfo::getSnapshotId, snapshotId)
               .orderByDesc(MkSnapshotInfo::getCollectTime);
        List<MkSnapshotInfo> list = list(wrapper);
        return convert.toDTO(list);
    }

    @Override
    public List<MkSnapshotInfoDTO> getInfosBySnapshotIdAndType(Long snapshotId, String infoType) {
        LambdaQueryWrapper<MkSnapshotInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkSnapshotInfo::getSnapshotId, snapshotId)
               .eq(MkSnapshotInfo::getInfoType, infoType)
               .orderByDesc(MkSnapshotInfo::getCollectTime);
        List<MkSnapshotInfo> list = list(wrapper);
        return convert.toDTO(list);
    }

    @Override
    public List<MkSnapshotInfoDTO> getInfosBySnapshotIdAndStatus(Long snapshotId, String status) {
        LambdaQueryWrapper<MkSnapshotInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkSnapshotInfo::getSnapshotId, snapshotId)
               .eq(MkSnapshotInfo::getStatus, status)
               .orderByDesc(MkSnapshotInfo::getCollectTime);
        List<MkSnapshotInfo> list = list(wrapper);
        return convert.toDTO(list);
    }

    @Override
    public List<MkSnapshotInfoDTO> getInfosBySnapshotIdAndPriority(Long snapshotId, Integer priorityLevel) {
        LambdaQueryWrapper<MkSnapshotInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkSnapshotInfo::getSnapshotId, snapshotId)
               .eq(MkSnapshotInfo::getPriorityLevel, priorityLevel)
               .orderByDesc(MkSnapshotInfo::getCollectTime);
        List<MkSnapshotInfo> list = list(wrapper);
        return convert.toDTO(list);
    }

    @Override
    public List<MkSnapshotInfoDTO> getInfosByListType(MkSnapshotInfoParam param) {
        LambdaQueryWrapper<MkSnapshotInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(param.getListType()), MkSnapshotInfo::getListType, param.getListType())
               .like(StringUtils.isNotBlank(param.getEventId()), MkSnapshotInfo::getEventId, param.getEventId())
               .like(StringUtils.isNotBlank(param.getEventName()), MkSnapshotInfo::getEventName, param.getEventName())
               .eq(StringUtils.isNotBlank(param.getInfoType()), MkSnapshotInfo::getInfoType, param.getInfoType())
               .like(StringUtils.isNotBlank(param.getInfoCategory()), MkSnapshotInfo::getInfoCategory, param.getInfoCategory())
               .like(StringUtils.isNotBlank(param.getInfoName()), MkSnapshotInfo::getInfoName, param.getInfoName())
               .like(StringUtils.isNotBlank(param.getDataSource()), MkSnapshotInfo::getDataSource, param.getDataSource())
               .like(StringUtils.isNotBlank(param.getLocationInfo()), MkSnapshotInfo::getLocationInfo, param.getLocationInfo())
               .eq(param.getPriorityLevel() != null, MkSnapshotInfo::getPriorityLevel, param.getPriorityLevel())
               .eq(StringUtils.isNotBlank(param.getStatus()), MkSnapshotInfo::getStatus, param.getStatus())
               .ge(param.getCollectTimeStart() != null, MkSnapshotInfo::getCollectTime, param.getCollectTimeStart())
               .le(param.getCollectTimeEnd() != null, MkSnapshotInfo::getCollectTime, param.getCollectTimeEnd())
               .orderByDesc(MkSnapshotInfo::getCollectTime);
        List<MkSnapshotInfo> list = list(wrapper);
        return convert.toDTO(list);
    }

    @Override
    public List<MkSnapshotInfoDTO> getTimeList(Long snapshotId) {
        LambdaQueryWrapper<MkSnapshotInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkSnapshotInfo::getSnapshotId, snapshotId)
               .eq(MkSnapshotInfo::getListType, "TIME")
               .orderByDesc(MkSnapshotInfo::getCollectTime);
        List<MkSnapshotInfo> list = list(wrapper);
        return convert.toDTO(list);
    }

    @Override
    public List<MkSnapshotInfoDTO> getEventList(Long snapshotId) {
        LambdaQueryWrapper<MkSnapshotInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkSnapshotInfo::getSnapshotId, snapshotId)
               .eq(MkSnapshotInfo::getListType, "EVENT")
               .orderByDesc(MkSnapshotInfo::getCollectTime);
        List<MkSnapshotInfo> list = list(wrapper);
        return convert.toDTO(list);
    }

    @Override
    public List<MkSnapshotInfoDTO> getInfosByListType(Long snapshotId, String listType) {
        LambdaQueryWrapper<MkSnapshotInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkSnapshotInfo::getSnapshotId, snapshotId)
               .eq(MkSnapshotInfo::getListType, listType)
               .orderByDesc(MkSnapshotInfo::getCollectTime);
        List<MkSnapshotInfo> list = list(wrapper);
        return convert.toDTO(list);
    }
}
