package com.yhd.admin.api.domain.monitor.nitrogen.vo;

import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenCompressorDataCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 制氮空压机详情表
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkNitrogenCompressorDataVO extends TbMkNitrogenCompressorDataCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
