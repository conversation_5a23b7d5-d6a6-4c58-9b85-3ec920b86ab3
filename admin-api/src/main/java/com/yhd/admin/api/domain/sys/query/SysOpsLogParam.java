package com.yhd.admin.api.domain.sys.query;

import com.yhd.admin.common.domain.query.QueryParam;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/4 18:35
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class SysOpsLogParam extends QueryParam {

    private Long id;

    /** URL */
    private String url;
    /** 方法名称 */
    private String method;
    /** 参数 */
    private String params;
    /** 客户端IP */
    private String clientIp;
    /** 来源模块 */
    private String clientModule;
    /** 操作人 */
    private String opsName;

    private String startDate;
    private String endDate;
}
