package com.yhd.admin.api.controller.monitor.fan;

import com.yhd.admin.api.domain.monitor.fan.convert.TbMkLocalFanSystemConvert;
import com.yhd.admin.api.domain.monitor.fan.dto.TbMkLocalFanSystemDTO;
import com.yhd.admin.api.domain.monitor.fan.entity.TbMkLocalFanSystem;
import com.yhd.admin.api.domain.monitor.fan.vo.create.TbMkLocalFanSystemCreateVO;
import com.yhd.admin.api.domain.monitor.fan.vo.page.TbMkLocalFanSystemPageVO;
import com.yhd.admin.api.domain.monitor.fan.vo.update.TbMkLocalFanSystemUpdateVO;
import com.yhd.admin.api.service.monitor.fan.TbMkLocalFanSystemService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 局扇控制系统 Controller
 * <AUTHOR>
 * @since
 */
@Slf4j
@RestController
@RequestMapping("/monitor/fan/tbMkLocalFanSystem")
public class TbMkLocalFanSystemController {

    @Resource
    private TbMkLocalFanSystemService tbMkLocalFanSystemService;

    @Resource
    private TbMkLocalFanSystemConvert tbMkLocalFanSystemConvert;

    /**
     * 分页查询
     *
     * @param pageVO 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<TbMkLocalFanSystemDTO> page(@RequestBody @Valid TbMkLocalFanSystemPageVO pageVO) {
        log.debug("分页查询 局扇控制系统，参数：{}", pageVO);
        return new PageRespJson<>(tbMkLocalFanSystemService.page(pageVO));
    }

    /**
     * 新增
     *
     * @param createVO 创建参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> create(@RequestBody @Valid TbMkLocalFanSystemCreateVO createVO) {
        log.debug("新增 局扇控制系统，参数：{}", createVO);
        return RespJson.success(tbMkLocalFanSystemService.create(createVO));
    }

    /**
     * 更新
     *
     * @param updateVO 更新参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> update(@RequestBody @Valid TbMkLocalFanSystemUpdateVO updateVO) {
        log.debug("更新 局扇控制系统，参数：{}", updateVO);
        return RespJson.success(tbMkLocalFanSystemService.update(updateVO));
    }


    /**
      * 删除
      *
      * @param id 要删除的记录ID
      * @return 操作结果
      */
    @DeleteMapping("/{id}")
    public RespJson<?> delete(@PathVariable Long id) {
        log.debug("删除 局扇控制系统，ID：{}", id);
        return RespJson.success(tbMkLocalFanSystemService.delete(id));
    }

    /**
     * 查看详情
     *
     * @param id 要查看的记录ID
     * @return 详情信息
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<TbMkLocalFanSystemDTO> getDetail(@PathVariable Long id) {
        log.debug("查看详情 局扇控制系统，ID：{}", id);
        TbMkLocalFanSystem entity = tbMkLocalFanSystemService.get(id);
        if (entity == null) {
            return RespJson.failure("未找到对应记录");
        }
        return RespJson.success(tbMkLocalFanSystemConvert.toDTO(entity));
    }
    /**
     * 批量删除
     *
     * @param ids 要删除的记录ID列表
     * @return 操作结果
     */
    @PostMapping(value = "/batchDelete", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchDelete(@RequestBody List<Long> ids) {
        log.debug("批量删除 局扇控制系统，IDs：{}", ids);
        return RespJson.success(tbMkLocalFanSystemService.batchDelete(ids));
    }

    /**
     * 批量新增
     *
     * @param createVOs 新增参数列表
     * @return 新增的记录ID列表
     */
    @PostMapping(value = "/batchAdd", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchCreate(@RequestBody @Valid List<TbMkLocalFanSystemCreateVO> createVOs) {
        log.debug("批量新增 局扇控制系统，数量：{}", createVOs.size());
        return RespJson.success(tbMkLocalFanSystemService.batchCreate(createVOs));
    }

    /**
     * 批量更新
     *
     * @param updateVOs 更新参数列表
     * @return 更新的记录数量
     */
    @PostMapping(value = "/batchModify", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> batchUpdate(@RequestBody @Valid List<TbMkLocalFanSystemUpdateVO> updateVOs) {
        log.debug("批量更新 局扇控制系统，数量：{}", updateVOs.size());
        return RespJson.success(tbMkLocalFanSystemService.batchUpdate(updateVOs));
    }
}
