package com.yhd.admin.api.domain.sys.query;

import java.io.Serializable;

import com.yhd.admin.common.domain.query.QueryParam;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/3 10:12
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ClientQueryParam extends QueryParam implements Cloneable, Serializable {
    private String id;

    private String clientName;
    /** 模块名称 */
    private String clientId;
    /** 资源名称 */
    private String resourceId;
}
