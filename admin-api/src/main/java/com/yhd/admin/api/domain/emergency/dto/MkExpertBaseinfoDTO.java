package com.yhd.admin.api.domain.emergency.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 应急救援中心-专家库 DTO
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkExpertBaseinfoDTO extends BaseDTO implements Serializable {

    /**
     * 专家id
     */
    private Integer id;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 专家姓名
     */
    private String name;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 电话号码1
     */
    private String phone1;

    /**
     * 电话号码2
     */
    private String phone2;

    /**
     * 照片路径
     */
    private String picSrc;

    /**
     * 任职单位
     */
    private String employer;

    /**
     * 职务
     */
    private String duty;

    /**
     * 职称
     */
    private String ranks;

    /**
     * 学历
     */
    private String education;

    /**
     * 简历
     */
    private String resume;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态，1：启用，2：停用
     */
    private Integer status;
}
