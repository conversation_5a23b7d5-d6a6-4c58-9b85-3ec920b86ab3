package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkOutputInfoDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputInfo;
import com.yhd.admin.api.domain.operation.query.MkOutputInfoParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 生产录入-产量信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
public interface MkOutputInfoService extends IService<MkOutputInfo> {

    IPage<MkOutputInfoDTO> pagingQuery(MkOutputInfoParam queryParam);

    Boolean add(MkOutputInfoParam param);

    Boolean modify(MkOutputInfoParam param);

    Boolean removeBatch(BatchParam param);

    MkOutputInfoDTO getCurrentDetail(MkOutputInfoParam param);
}
