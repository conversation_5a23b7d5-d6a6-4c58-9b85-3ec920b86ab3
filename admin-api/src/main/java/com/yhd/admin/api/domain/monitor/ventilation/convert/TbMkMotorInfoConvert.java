package com.yhd.admin.api.domain.monitor.ventilation.convert;

import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkMotorInfoDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkMotorInfo;
import com.yhd.admin.api.domain.monitor.ventilation.vo.TbMkMotorInfoVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkMotorInfoCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkMotorInfoUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 电机基本信息
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkMotorInfoConvert {

    @Mapping(target = "id", ignore = true)
    TbMkMotorInfo convert(TbMkMotorInfoCreateVO createVO);

    TbMkMotorInfo convert(TbMkMotorInfoUpdateVO updateVO);

    TbMkMotorInfoVO convert(TbMkMotorInfo po);

    TbMkMotorInfoDTO toDTO(TbMkMotorInfo po);



}
