package com.yhd.admin.api.domain.conduct.dto.arrangement;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 调班记录表(MkShiftArrangementRecord)DTO
 *
 * <AUTHOR>
 * @since 2025-07-29 16:34:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkShiftArrangementRecordDTO extends BaseDTO implements Serializable {
    /** id */
    private Long id;
    /** 排班id */
    private Long arrangementId;
    /** 班次日期 */
    private LocalDate shiftDate;
    /** 班次类型名称 1：值班 2：夜班 3：白班 4：中班 */
    private String shiftTypeName;
    /** 班次类型代码 1：值班 2：夜班 3：白班 4：中班 */
    private String shiftTypeCode;
    /** 计划值班人员姓名 */
    private String planName;
    /** 计划值班人员账号 */
    private String planAccount;
    /** 值班人员姓名 */
    private String name;
    /** 值班人员账号 */
    private String account;
    /** 备注 */
    private String remark;
    /** 记录审批状态名称 1：待审批 2：通过 3：驳回 4：取消 */
    private String statusName;
    /** 记录审批状态代码 1：待审批 2：通过 3：驳回 4：取消 */
    private String statusCode;
    /** 操作人姓名 */
    private String creator;
    /** 调整日期 */
    private LocalDate createdDate;
}

