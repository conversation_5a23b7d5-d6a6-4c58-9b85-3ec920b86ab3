package com.yhd.admin.api.domain.home.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import java.io.Serializable;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 综合管控大屏
 *
 * <AUTHOR>
 * @date 2025/7/29 09:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkHome extends BaseEntity implements Serializable {

  @TableId(type = IdType.AUTO)
  private Long id;

  /** 安全生产日期 */
  private LocalDate date;

  /** 主采煤层 */
  private String mainCoalMining;

  /** 煤仓情况 */
  private String coalBunker;

  /** 设备状态 */
  private String equipmentStatus;

  /** 安全状态 */
  private String safeStatus;

  /** 监控画面 */
  private String monitorUrl;
}
