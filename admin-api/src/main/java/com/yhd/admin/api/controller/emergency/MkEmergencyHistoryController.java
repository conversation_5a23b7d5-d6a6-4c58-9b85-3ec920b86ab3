package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyHistoryConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyHistoryDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyHistoryParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyHistoryVO;
import com.yhd.admin.api.service.emergency.MkEmergencyHistoryService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 应急历史事件记录表 Controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/emergency/history")
public class MkEmergencyHistoryController {

    @Resource
    private MkEmergencyHistoryService emergencyHistoryService;

    @Resource
    private MkEmergencyHistoryConvert convert;

    /**
     * 分页查询应急历史事件信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkEmergencyHistoryVO> pagingQuery(@RequestBody MkEmergencyHistoryParam queryParam) {
        log.info("分页查询应急历史事件信息，参数：{}", queryParam);
        IPage<MkEmergencyHistoryDTO> iPage = emergencyHistoryService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    /**
     * 新增应急历史事件信息
     * @param param 应急历史事件信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkEmergencyHistoryParam param) {
        log.info("新增应急历史事件信息，参数：{}", param);
        Boolean result = emergencyHistoryService.add(param);
        return RespJson.success(result);
    }

    /**
     * 修改应急历史事件信息
     * @param param 应急历史事件信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> modify(@RequestBody MkEmergencyHistoryParam param) {
        log.info("修改应急历史事件信息，参数：{}", param);
        Boolean result = emergencyHistoryService.modify(param);
        return RespJson.success(result);
    }

    /**
     * 获取应急历史事件详情
     * @param param 查询参数
     * @return 应急历史事件详情
     */
    @PostMapping(value = "/getCurrentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkEmergencyHistoryVO> getCurrentDetail(@RequestBody MkEmergencyHistoryParam param) {
        log.info("获取应急历史事件详情，参数：{}", param);
        MkEmergencyHistoryDTO dto = emergencyHistoryService.getCurrentDetail(param);
        return RespJson.success(convert.toVO(dto));
    }

    /**
     * 批量删除应急历史事件信息
     * @param param 批量删除参数
     * @return 操作结果
     */
    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody BatchParam param) {
        log.info("批量删除应急历史事件信息，参数：{}", param);
        Boolean result = emergencyHistoryService.removeBatch(param);
        return RespJson.success(result);
    }
}
