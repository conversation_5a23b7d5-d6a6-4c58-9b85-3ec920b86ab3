package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyHistoryConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyHistoryDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyHistoryParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyHistoryVO;
import com.yhd.admin.api.service.emergency.MkEmergencyHistoryService;
import com.yhd.admin.common.domain.PageRespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 应急历史事件记录表 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/open/emergency/history")
public class MkEmergencyHistoryController {

    @Resource
    private MkEmergencyHistoryService emergencyHistoryService;

    @Resource
    private MkEmergencyHistoryConvert convert;

    /**
     * 分页查询应急历史事件信息
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkEmergencyHistoryVO> pagingQuery(@RequestBody MkEmergencyHistoryParam queryParam) {
        log.info("分页查询应急历史事件信息，参数：{}", queryParam);
        IPage<MkEmergencyHistoryDTO> iPage = emergencyHistoryService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }


}
