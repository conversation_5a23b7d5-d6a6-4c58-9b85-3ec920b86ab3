package com.yhd.admin.api.domain.conduct.dto.base;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 掘进队信息(MkExcavationTeamInfo)DTO
 *
 * <AUTHOR>
 * @since 2025-07-28 09:25:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkExcavationTeamInfoDTO extends BaseDTO implements Serializable {
    /** Id */
    private Long id;
    /** 所属煤矿 */
    private String mineName;
    /** 所属煤矿id */
    private Long mineId;
    /** 名称 */
    private String name;
    /** 所属单位 */
    private String orgName;
    /** 所属单位id */
    private String orgId;
    /** 队组类型名称 */
    private String typeName;
    /** 队组类型代码 */
    private String typeCode;
    /** 在册人数 */
    private Integer memberNumber;
    /** 管理人数 */
    private Integer managementNumber;
    /** 队长姓名 */
    private String leaderName;
    /** 队长账号 */
    private String leaderAccount;
    /** 技术员姓名 */
    private String technicianName;
    /** 技术员账号 */
    private String technicianAccount;
    /** 巷道名称 */
    private String tunnelName;
    /** 巷道 Id */
    private Long tunnelId;
}

