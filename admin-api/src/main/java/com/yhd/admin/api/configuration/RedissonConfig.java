package com.yhd.admin.api.configuration;

import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

@Data
@Configuration
@ConfigurationProperties(value = "spring.data.redis")
public class RedissonConfig {

    private String host;
    private String port;
    private String password;
    private int dateBase;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        SingleServerConfig singleServerConfig = config.useSingleServer();
        singleServerConfig.setAddress("redis://" + host + ":" + port);
        singleServerConfig.setDatabase(dateBase);
        if (StringUtils.isNotBlank(password)) {
            singleServerConfig.setPassword(password);
        }
        singleServerConfig.setTimeout(60000);
        singleServerConfig.setConnectionPoolSize(50);
        singleServerConfig.setConnectTimeout(30000);
        singleServerConfig.setIdleConnectionTimeout(60000);
        return Redisson.create(config);
    }
}
