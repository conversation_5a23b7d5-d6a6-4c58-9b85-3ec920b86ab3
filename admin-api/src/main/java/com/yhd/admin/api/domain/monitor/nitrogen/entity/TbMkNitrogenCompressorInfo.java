package com.yhd.admin.api.domain.monitor.nitrogen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.*;

/**
 * 制氮系统空压机基本信息表
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_mk_nitrogen_compressor_info")
public class TbMkNitrogenCompressorInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属制氮系统ID
     */
    @TableField("nitrogen_system_id")
    private Long nitrogenSystemId;

    /**
     * 空压机名称
     */
    @TableField("compressor_name")
    private String compressorName;

    /**
     * 空压机编号
     */
    @TableField("compressor_number")
    private String compressorNumber;

    /**
     * 备注信息
     */
    @TableField("remarks")
    private String remarks;
}
