package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.sys.dto.TemplateTableDTO;
import com.yhd.admin.api.domain.sys.entity.TemplateTable;
import com.yhd.admin.api.domain.sys.query.TemplateTableParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 表格模版业务类
 *
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2021/3/17 15:22
 */

public interface TemplateTableService extends IService<TemplateTable> {

    Boolean addOrModify(TemplateTableParam addParam);

    Boolean removeBatch(BatchParam param);

    IPage<TemplateTableDTO> pagingQuery(TemplateTableParam queryParam);

    TemplateTableDTO getDetail(Long id);

}
