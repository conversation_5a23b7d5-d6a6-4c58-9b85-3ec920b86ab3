package com.yhd.admin.api.domain.monitor.ventilation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 通风机整体运行参数
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_mk_ventilation_operation")
public class TbMkVentilationOperation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联通风机基本信息表ID
     */
    @TableField("ventilation_id")
    private Long ventilationId;

    /**
     * 蝶阀状态
     */
    @TableField("valve_status")
    private String valveStatus;

    /**
     * 风速(m/s)
     */
    @TableField("wind_speed")
    private BigDecimal windSpeed;
}
