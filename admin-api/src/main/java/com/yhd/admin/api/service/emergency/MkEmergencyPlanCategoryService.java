package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyPlanCategoryDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyPlanCategory;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyPlanCategoryParam;
import com.yhd.admin.common.domain.query.BatchParam;

import java.util.List;

/**
 * 应急预案分类表 Service
 * <AUTHOR>
 */
public interface MkEmergencyPlanCategoryService extends IService<MkEmergencyPlanCategory> {

    /**
     * 分页查询应急预案分类信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    IPage<MkEmergencyPlanCategoryDTO> pagingQuery(MkEmergencyPlanCategoryParam queryParam);

    /**
     * 新增应急预案分类信息
     * @param param 应急预案分类信息参数
     * @return 是否成功
     */
    Boolean add(MkEmergencyPlanCategoryParam param);

    /**
     * 修改应急预案分类信息
     * @param param 应急预案分类信息参数
     * @return 是否成功
     */
    Boolean modify(MkEmergencyPlanCategoryParam param);

    /**
     * 获取应急预案分类详情
     * @param param 查询参数
     * @return 应急预案分类详情
     */
    MkEmergencyPlanCategoryDTO getCurrentDetail(MkEmergencyPlanCategoryParam param);

    /**
     * 批量删除应急预案分类信息
     * @param param 批量删除参数
     * @return 是否成功
     */
    Boolean removeBatch(BatchParam param);

    /**
     * 获取树形分类列表
     * @return 树形分类列表
     */
    List<MkEmergencyPlanCategoryDTO> getCategoryTree();

    /**
     * 根据父级ID获取子分类列表
     * @param parentId 父级ID
     * @return 子分类列表
     */
    List<MkEmergencyPlanCategoryDTO> getCategoriesByParentId(Integer parentId);

    /**
     * 获取所有启用的分类列表
     * @return 分类列表
     */
    List<MkEmergencyPlanCategoryDTO> getEnabledCategories();
}
