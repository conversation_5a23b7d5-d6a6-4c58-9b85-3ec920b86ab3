package com.yhd.admin.api.domain.emergency.query;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.yhd.admin.common.domain.query.QueryParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 应急救援通讯录表查询参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
public class MkEmergencyContactParam extends QueryParam implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 联系人编码
     */
    private String contactCode;

    /**
     * 联系人姓名（模糊查询）
     */
    private String contactName;

    /**
     * 所属部门（模糊查询）
     */
    private String department;

    /**
     * 职位（模糊查询）
     */
    private String position;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 联系人类型：1-内部人员，2-外部联系人，3-应急专家
     */
    private Integer contactType;

    /**
     * 应急角色（模糊查询）
     */
    private String emergencyRole;

    /**
     * 专业特长（模糊查询）
     */
    private String speciality;

    /**
     * 在职状态：1-在职，2-离职，3-调岗
     */
    private Integer dutyStatus;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 是否应急负责人：0-否，1-是
     */
    private Boolean isEmergencyLeader;

    /**
     * 备用联系方式
     */
    private String backupContact;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 备注
     */
    private String remark;
}
