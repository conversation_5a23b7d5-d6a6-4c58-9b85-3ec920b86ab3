package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.emergency.dto.MkExpertBaseinfoDTO;
import com.yhd.admin.api.domain.emergency.entity.MkExpertBaseinfo;
import com.yhd.admin.api.domain.emergency.query.MkExpertBaseinfoParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 应急救援中心-专家库 Service
 * <AUTHOR>
 */
public interface MkExpertBaseinfoService extends IService<MkExpertBaseinfo> {

    /**
     * 分页查询专家信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    IPage<MkExpertBaseinfoDTO> pagingQuery(MkExpertBaseinfoParam queryParam);

    /**
     * 新增专家信息
     * @param param 专家信息参数
     * @return 是否成功
     */
    Boolean add(MkExpertBaseinfoParam param);

    /**
     * 修改专家信息
     * @param param 专家信息参数
     * @return 是否成功
     */
    Boolean modify(MkExpertBaseinfoParam param);

    /**
     * 获取专家详情
     * @param param 查询参数
     * @return 专家详情
     */
    MkExpertBaseinfoDTO getCurrentDetail(MkExpertBaseinfoParam param);

    /**
     * 批量删除专家信息
     * @param param 批量删除参数
     * @return 是否成功
     */
    Boolean removeBatch(BatchParam param);
}
