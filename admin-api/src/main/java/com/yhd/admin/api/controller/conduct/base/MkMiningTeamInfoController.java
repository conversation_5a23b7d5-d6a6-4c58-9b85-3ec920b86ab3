package com.yhd.admin.api.controller.conduct.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.query.base.MkMiningTeamInfoParam;
import com.yhd.admin.api.service.conduct.base.MkMiningTeamInfoService;
import com.yhd.admin.api.domain.conduct.convert.base.MkMiningTeamInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.base.MkMiningTeamInfoDTO;
import com.yhd.admin.api.domain.conduct.vo.base.MkMiningTeamInfoVO;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 采煤队控制层
 *
 * <AUTHOR>
 * @since 2025-07-25 10:34:53
 * @module 采煤队信息
 */
@RestController
@RequestMapping("/conduct/miningTeam")
public class MkMiningTeamInfoController {

    public final MkMiningTeamInfoService mkMiningTeamInfoService;
    public final MkMiningTeamInfoConvert mkMiningTeamInfoConvert;

    public MkMiningTeamInfoController(
        MkMiningTeamInfoService mkMiningTeamInfoService,
        MkMiningTeamInfoConvert mkMiningTeamInfoConvert
    ) {
        this.mkMiningTeamInfoService = mkMiningTeamInfoService;
        this.mkMiningTeamInfoConvert = mkMiningTeamInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkMiningTeamInfoParam param) {
        IPage<MkMiningTeamInfoDTO> page = mkMiningTeamInfoService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkMiningTeamInfoConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkMiningTeamInfoParam param) {
        return RespJson.success(mkMiningTeamInfoConvert.toVOList(mkMiningTeamInfoService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkMiningTeamInfoVO> getCurrentDetails(@RequestBody MkMiningTeamInfoParam param) {
        return RespJson.success(mkMiningTeamInfoConvert.toVO(mkMiningTeamInfoService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkMiningTeamInfoParam param) {
        Boolean retVal = mkMiningTeamInfoService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */

    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkMiningTeamInfoParam param) {
        Boolean retVal = mkMiningTeamInfoService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

