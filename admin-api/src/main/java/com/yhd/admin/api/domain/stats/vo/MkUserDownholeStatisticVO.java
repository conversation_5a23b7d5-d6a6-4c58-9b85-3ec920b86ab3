package com.yhd.admin.api.domain.stats.vo;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 人员下井统计表
 *
 * <AUTHOR>
 * @date 2025/7/31 15:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkUserDownholeStatisticVO extends BaseVO implements Serializable {

  private Long id;

  /** 监视tag */
  private String monitorTag;
  /** 组织机构编码 */
  private String orgCode;
  /** 组织机构名称 */
  private String orgName;
  /** 部门编码 */
  private String departmentCode;
  /** 部门名称 */
  private String departmentName;
  /** 职务编码 */
  private String postCode;
  /** 职务名称 */
  private String postName;
  /** 工种编码 */
  private String jobCode;
  /** 工种名称 */
  private String jobName;
  /** 姓名 */
  private String realName;
  /** 卡号 */
  private String cardId;
  /** 工号 */
  private String jobId;
  /** 下井时间(yyyy-mm-dd hh:mm:ss) */
  private LocalDateTime downTime;
  /** 出井时间(yyyy-mm-dd hh:mm:ss) */
  private LocalDateTime upTime;
  /** 下井时长 */
  private String duration;

  /** 下井次数 */
  private int downCount;
}
