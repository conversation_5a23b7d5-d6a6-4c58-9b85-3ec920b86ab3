package com.yhd.admin.api.controller.operation;

import com.yhd.admin.api.domain.operation.dto.MkDiskSpeedDTO;
import com.yhd.admin.api.domain.operation.dto.MkNetworkSpeedDTO;
import com.yhd.admin.api.domain.operation.dto.MkServerDTO;
import com.yhd.admin.api.domain.operation.dto.MkTimeValuePairDTO;
import com.yhd.admin.api.service.operation.MkServerService;
import com.yhd.admin.common.domain.RespJson;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 服务器信息
 * <AUTHOR>
 */
@RestController
@RequestMapping("/operation/server")
@RequiredArgsConstructor
public class MkServerController {

    private final MkServerService mkServerService;

    /**
     * 获取实时服务器监控信息
     */
    @PostMapping("/getCurrentDetails")
    public RespJson<MkServerDTO> getRealTimeMonitorInfo() {
        return RespJson.success(mkServerService.getRealTimeMonitorInfo());
    }

    /**
     * 获取过去60秒的CPU使用率
     */
    @GetMapping("/cpu-usage")
    public RespJson<List<MkTimeValuePairDTO>> getCpuUsageInLastMinute() {
        return RespJson.success(mkServerService.getCpuUsageInLastMinute());
    }

    /**
     * 获取过去60秒的内存使用率
     */
    @GetMapping("/memory-usage")
    public RespJson<List<MkTimeValuePairDTO>> getMemoryUsageInLastMinute() {
        return RespJson.success(mkServerService.getMemoryUsageInLastMinute());
    }

    /**
     * 获取过去60秒的网络速度
     */
    @GetMapping("/network-speed")
    public RespJson<List<MkNetworkSpeedDTO>> getNetworkSpeedInLastMinute() {
        return RespJson.success(mkServerService.getNetworkSpeedInLastMinute());
    }

    /**
     * 获取实时硬盘读写速度
     */
    @GetMapping("/disk-speed")
    public RespJson<List<MkDiskSpeedDTO>> getRealTimeDiskSpeed() {
        return RespJson.success(mkServerService.getRealTimeDiskSpeed());
    }
}
