package com.yhd.admin.api.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.admin.api.dao.emergency.*;
import com.yhd.admin.api.domain.emergency.entity.*;
import com.yhd.admin.api.domain.emergency.query.MkSafetyDecisionParam;
import com.yhd.admin.api.domain.emergency.vo.*;
import com.yhd.admin.api.service.emergency.MkSafetyDecisionService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 安全决策大屏 Service 实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MkSafetyDecisionServiceImpl implements MkSafetyDecisionService {

    @Resource
    private MkHazardStatsDao hazardStatsDao;

    @Resource
    private MkViolationStatsDao violationStatsDao;

    @Resource
    private MkAlarmStatsDao alarmStatsDao;

    @Resource
    private MkRiskDetailedStatsDao riskDetailedStatsDao;

    @Resource
    private MkRiskManagementListDao riskManagementListDao;

    @Resource
    private MkProfessionalAnalysisStatsDao professionalAnalysisStatsDao;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public MkSafetyDecisionVO getSafetyDecisionData(MkSafetyDecisionParam param) {
        log.info("获取安全决策大屏数据，参数：{}", param);

        MkSafetyDecisionVO result = new MkSafetyDecisionVO();

        LocalDate statsDate = param.getStatsDate() != null ? param.getStatsDate() : LocalDate.now();
        String orgCode = param.getOrgCode();

        // 获取隐患统计数据
        result.setHazardStats(getHazardStats(statsDate, orgCode));

        // 获取三违统计数据
        result.setViolationStats(getViolationStats(statsDate, orgCode));

        // 获取报警统计数据
        result.setAlarmStats(getAlarmStats(statsDate, orgCode));

        // 获取风险统计数据
        result.setRiskStats(getRiskStats(statsDate, orgCode));

        // 获取风险管控清单
        result.setRiskManagementList(getRiskManagementList(orgCode));

        // 获取专业分析统计数据
        result.setProfessionalAnalysisList(getProfessionalAnalysisList(statsDate, orgCode));

        // 设置中央环形数据
        result.setCentralRingData(getCentralRingData(result));

        return result;
    }

    /**
     * 获取隐患统计数据
     */
    private MkSafetyHazardStatsVO getHazardStats(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkHazardStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkHazardStats::getStatsDate, statsDate)
                .eq(MkHazardStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkHazardStats::getOrgCode, orgCode)
                .orderByDesc(MkHazardStats::getCreateTime)
                .last("LIMIT 1");

        MkHazardStats stats = hazardStatsDao.selectOne(queryWrapper);

        MkSafetyHazardStatsVO vo = new MkSafetyHazardStatsVO();
        if (stats != null) {
            vo.setUnrectifiedCount(stats.getUnrectifiedCount());
            vo.setRectifiedUnverifiedCount(stats.getRectifiedUnverifiedCount());
            vo.setVerifiedUnacceptedCount(stats.getVerifiedUnacceptedCount());
            vo.setVerifiedPassedCount(stats.getVerifiedPassedCount());
            vo.setTotalCount(stats.getTotalCount());
        }
        return vo;
    }

    /**
     * 获取三违统计数据
     */
    private MkSafetyViolationStatsVO getViolationStats(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkViolationStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkViolationStats::getStatsDate, statsDate)
                .eq(MkViolationStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkViolationStats::getOrgCode, orgCode)
                .orderByDesc(MkViolationStats::getCreateTime)
                .last("LIMIT 1");

        MkViolationStats stats = violationStatsDao.selectOne(queryWrapper);

        MkSafetyViolationStatsVO vo = new MkSafetyViolationStatsVO();
        if (stats != null) {
            vo.setDailyViolationCount(stats.getDailyViolationCount());

            // 解析趋势数据
            try {
                if (StringUtils.hasText(stats.getTrendData())) {
                    List<Integer> trendData = objectMapper.readValue(stats.getTrendData(),
                            new TypeReference<List<Integer>>() {
                            });
                    vo.setTrendData(trendData);
                }
            } catch (Exception e) {
                log.warn("解析三违趋势数据失败", e);
                vo.setTrendData(new ArrayList<>());
            }

            // 解析类型分解数据
            try {
                if (StringUtils.hasText(stats.getViolationTypeBreakdown())) {
                    Object typeBreakdown = objectMapper.readValue(stats.getViolationTypeBreakdown(), Object.class);
                    vo.setViolationTypeBreakdown(typeBreakdown);
                }
            } catch (Exception e) {
                log.warn("解析三违类型分解数据失败", e);
            }
        }
        return vo;
    }

    /**
     * 获取报警统计数据
     */
    private MkSafetyAlarmStatsVO getAlarmStats(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkAlarmStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkAlarmStats::getStatsDate, statsDate)
                .eq(MkAlarmStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkAlarmStats::getOrgCode, orgCode)
                .orderByDesc(MkAlarmStats::getCreateTime)
                .last("LIMIT 1");

        MkAlarmStats stats = alarmStatsDao.selectOne(queryWrapper);

        MkSafetyAlarmStatsVO vo = new MkSafetyAlarmStatsVO();
        if (stats != null) {
            vo.setYesterdayCount(stats.getYesterdayCount());
            vo.setTodayCount(stats.getTodayCount());
            vo.setGrowthRate(stats.getGrowthRate());

            // 解析趋势数据
            try {
                if (StringUtils.hasText(stats.getTrendData())) {
                    List<java.math.BigDecimal> trendData = objectMapper.readValue(stats.getTrendData(),
                            new TypeReference<List<java.math.BigDecimal>>() {
                            });
                    vo.setTrendData(trendData);
                }
            } catch (Exception e) {
                log.warn("解析报警趋势数据失败", e);
                vo.setTrendData(new ArrayList<>());
            }

            // 解析类型分解数据
            try {
                if (StringUtils.hasText(stats.getAlarmTypeBreakdown())) {
                    Object typeBreakdown = objectMapper.readValue(stats.getAlarmTypeBreakdown(), Object.class);
                    vo.setAlarmTypeBreakdown(typeBreakdown);
                }
            } catch (Exception e) {
                log.warn("解析报警类型分解数据失败", e);
            }
        }
        return vo;
    }

    /**
     * 获取风险统计数据
     */
    private MkSafetyRiskStatsVO getRiskStats(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkRiskDetailedStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkRiskDetailedStats::getStatsDate, statsDate)
                .eq(MkRiskDetailedStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkRiskDetailedStats::getOrgCode, orgCode)
                .orderByDesc(MkRiskDetailedStats::getCreateTime)
                .last("LIMIT 1");

        MkRiskDetailedStats stats = riskDetailedStatsDao.selectOne(queryWrapper);

        MkSafetyRiskStatsVO vo = new MkSafetyRiskStatsVO();
        if (stats != null) {
            vo.setMajorRiskCount(stats.getMajorRiskCount());
            vo.setSignificantRiskCount(stats.getSignificantRiskCount());
            vo.setGeneralRiskCount(stats.getGeneralRiskCount());
            vo.setLowRiskCount(stats.getLowRiskCount());
            vo.setGeneralALevelCount(stats.getGeneralALevelCount());
            vo.setGeneralBLevelCount(stats.getGeneralBLevelCount());
            vo.setGeneralCLevelCount(stats.getGeneralCLevelCount());
            vo.setTotalRiskCount(stats.getTotalRiskCount());
        }
        return vo;
    }

    /**
     * 获取风险管控清单
     */
    private List<MkSafetyRiskManagementVO> getRiskManagementList(String orgCode) {
        LambdaQueryWrapper<MkRiskManagementList> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkRiskManagementList::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkRiskManagementList::getOrgCode, orgCode)
                .orderByDesc(MkRiskManagementList::getEntryTime)
                .last("LIMIT 10"); // 限制显示最近10条

        List<MkRiskManagementList> list = riskManagementListDao.selectList(queryWrapper);

        List<MkSafetyRiskManagementVO> voList = new ArrayList<>();
        for (MkRiskManagementList item : list) {
            MkSafetyRiskManagementVO vo = new MkSafetyRiskManagementVO();
            vo.setItemCode(item.getItemCode());
            vo.setRiskDescription(item.getRiskDescription());
            vo.setRiskLevel(item.getRiskLevel());
            vo.setRiskLevelColor(item.getRiskLevelColor());
            vo.setResponsiblePerson(item.getResponsiblePerson());
            vo.setDepartment(item.getDepartment());
            vo.setEntryTime(item.getEntryTime());
            vo.setCompletionStatus(item.getCompletionStatus());
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取专业分析统计数据
     */
    private List<MkSafetyProfessionalAnalysisVO> getProfessionalAnalysisList(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkProfessionalAnalysisStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkProfessionalAnalysisStats::getStatsDate, statsDate)
                .eq(MkProfessionalAnalysisStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkProfessionalAnalysisStats::getOrgCode, orgCode)
                .orderByDesc(MkProfessionalAnalysisStats::getHazardCount);

        List<MkProfessionalAnalysisStats> list = professionalAnalysisStatsDao.selectList(queryWrapper);

        List<MkSafetyProfessionalAnalysisVO> voList = new ArrayList<>();
        for (MkProfessionalAnalysisStats item : list) {
            MkSafetyProfessionalAnalysisVO vo = new MkSafetyProfessionalAnalysisVO();
            vo.setProfessionalType(item.getProfessionalType());
            vo.setHazardCount(item.getHazardCount());

            // 解析隐患清单数据
            try {
                if (StringUtils.hasText(item.getHazardListData())) {
                    Object hazardListData = objectMapper.readValue(item.getHazardListData(), Object.class);
                    vo.setHazardListData(hazardListData);
                }
            } catch (Exception e) {
                log.warn("解析隐患清单数据失败", e);
            }

            // 解析趋势数据
            try {
                if (StringUtils.hasText(item.getTrendData())) {
                    Object trendData = objectMapper.readValue(item.getTrendData(), Object.class);
                    vo.setTrendData(trendData);
                }
            } catch (Exception e) {
                log.warn("解析专业分析趋势数据失败", e);
            }

            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取中央环形数据
     */
    private MkSafetyDecisionVO.CentralRingData getCentralRingData(MkSafetyDecisionVO result) {
        MkSafetyDecisionVO.CentralRingData centralData = new MkSafetyDecisionVO.CentralRingData();

        // 接警总数 = 今日报警数量
        Integer alarmTotal = result.getAlarmStats() != null ? result.getAlarmStats().getTodayCount() : 0;
        centralData.setAlarmTotal(alarmTotal != null ? alarmTotal : 0);

        // 三违总数 = 当日三违数量
        Integer violationTotal = result.getViolationStats() != null
                ? result.getViolationStats().getDailyViolationCount()
                : 0;
        centralData.setViolationTotal(violationTotal != null ? violationTotal : 0);

        // 风险总数
        Integer riskTotal = result.getRiskStats() != null ? result.getRiskStats().getTotalRiskCount() : 0;
        centralData.setRiskTotal(riskTotal != null ? riskTotal : 0);

        // 隐患总数
        Integer hazardTotal = result.getHazardStats() != null ? result.getHazardStats().getTotalCount() : 0;
        centralData.setHazardTotal(hazardTotal != null ? hazardTotal : 0);

        return centralData;
    }
}