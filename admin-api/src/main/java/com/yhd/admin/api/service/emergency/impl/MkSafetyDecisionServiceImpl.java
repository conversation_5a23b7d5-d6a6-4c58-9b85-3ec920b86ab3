package com.yhd.admin.api.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yhd.admin.api.dao.emergency.*;
import com.yhd.admin.api.domain.emergency.entity.*;
import com.yhd.admin.api.domain.emergency.query.MkSafetyDecisionParam;
import com.yhd.admin.api.domain.emergency.vo.*;
import com.yhd.admin.api.service.emergency.MkSafetyDecisionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 安全决策大屏 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MkSafetyDecisionServiceImpl implements MkSafetyDecisionService {

    @Resource
    private MkHazardStatsDao hazardStatsDao;

    @Resource
    private MkViolationStatsDao violationStatsDao;

    @Resource
    private MkAlarmStatsDao alarmStatsDao;

    @Resource
    private MkRiskDetailedStatsDao riskDetailedStatsDao;

    @Resource
    private MkRiskManagementListDao riskManagementListDao;

    @Resource
    private MkProfessionalAnalysisStatsDao professionalAnalysisStatsDao;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public MkSafetyDecisionVO getSafetyDecisionData(MkSafetyDecisionParam param) {
        log.info("获取安全决策大屏数据，参数：{}", param);

        MkSafetyDecisionVO result = new MkSafetyDecisionVO();

        String orgCode = param.getOrgCode();

        // 获取隐患统计数据
        result.setHazardStats(getHazardStats(orgCode));

        // 获取三违统计数据
        result.setViolationStats(getViolationStats(orgCode));

        // 获取报警统计数据
        result.setAlarmStats(getAlarmStats(orgCode));

        // 获取风险统计数据
        result.setRiskStats(getRiskStats(orgCode));

        // 获取风险管控清单
        result.setRiskManagementList(getRiskManagementList(orgCode));

        // 获取隐患专业分析数据
        result.setHazardProfessionalAnalysisList(getHazardProfessionalAnalysisList(orgCode));

        // 获取隐患清单数据
        result.setHazardList(getHazardList(orgCode));

        // 设置中央环形数据
        result.setCentralRingData(getCentralRingData(result));

        return result;
    }

    /**
     * 获取隐患统计数据
     */
    private MkSafetyHazardStatsVO getHazardStats(String orgCode) {
        LambdaQueryWrapper<MkHazardStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkHazardStats::getStatus, 1)
            .eq(StringUtils.hasText(orgCode), MkHazardStats::getOrgCode, orgCode)
            .orderByDesc(MkHazardStats::getCreateTime)
            .last("LIMIT 1");

        MkHazardStats stats = hazardStatsDao.selectOne(queryWrapper);

        MkSafetyHazardStatsVO vo = new MkSafetyHazardStatsVO();
        if (stats != null) {
            vo.setUnrectifiedCount(stats.getUnrectifiedCount() != null ? stats.getUnrectifiedCount() : 0);
            vo.setRectifiedUnverifiedCount(
                stats.getRectifiedUnverifiedCount() != null ? stats.getRectifiedUnverifiedCount() : 0);
            vo.setVerifiedUnacceptedCount(
                stats.getVerifiedUnacceptedCount() != null ? stats.getVerifiedUnacceptedCount() : 0);
            vo.setVerifiedPassedCount(stats.getVerifiedPassedCount() != null ? stats.getVerifiedPassedCount() : 0);
            vo.setTotalCount(stats.getTotalCount() != null ? stats.getTotalCount() : 0);
        } else {
            // 如果没有数据，设置默认值
            vo.setUnrectifiedCount(0);
            vo.setRectifiedUnverifiedCount(0);
            vo.setVerifiedUnacceptedCount(0);
            vo.setVerifiedPassedCount(0);
            vo.setTotalCount(0);
        }
        return vo;
    }

    /**
     * 获取三违统计数据
     */
    private MkSafetyViolationStatsVO getViolationStats(String orgCode) {
        LambdaQueryWrapper<MkViolationStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkViolationStats::getStatus, 1)
            .eq(StringUtils.hasText(orgCode), MkViolationStats::getOrgCode, orgCode)
            .orderByDesc(MkViolationStats::getCreateTime)
            .last("LIMIT 1");

        MkViolationStats stats = violationStatsDao.selectOne(queryWrapper);

        MkSafetyViolationStatsVO vo = new MkSafetyViolationStatsVO();
        if (stats != null) {
            vo.setDailyViolationCount(stats.getDailyViolationCount() != null ? stats.getDailyViolationCount() : 0);

            // 解析趋势数据
            try {
                if (StringUtils.hasText(stats.getTrendData())) {
                    String trendDataStr = stats.getTrendData().trim();
                    log.debug("解析三违趋势数据: {}", trendDataStr);

                    // 尝试多种数据类型解析
                    Object parsedData = objectMapper.readValue(trendDataStr, Object.class);

                    if (parsedData instanceof List<?> dataList) {
                        // 直接数组格式：[1, 2, 3, 4, 5]
                        List<Integer> trendData = new ArrayList<>();
                        for (Object item : dataList) {
                            if (item instanceof Number) {
                                trendData.add(((Number) item).intValue());
                            } else if (item instanceof String) {
                                try {
                                    trendData.add(Integer.parseInt((String) item));
                                } catch (NumberFormatException e) {
                                    log.warn("无法将字符串转换为整数: {}", item);
                                    trendData.add(0);
                                }
                            } else {
                                log.warn("未知的趋势数据类型: {}", item != null ? item.getClass() : "null");
                                trendData.add(0);
                            }
                        }
                        vo.setTrendData(trendData);
                    } else if (parsedData instanceof Map) {
                        // Map格式处理
                        Map<String, Object> dataMap = (Map<String, Object>) parsedData;
                        log.debug("Map格式的趋势数据，包含的keys: {}", dataMap.keySet());

                        // 检查是否是日期-数值格式的Map（如: {"05-15": 0, "05-17": 0}）
                        boolean isDateValueMap = true;
                        for (String key : dataMap.keySet()) {
                            // 检查key是否像日期格式（MM-dd 或 yyyy-MM-dd）
                            if (!key.matches("\\d{2}-\\d{2}") && !key.matches("\\d{4}-\\d{2}-\\d{2}")) {
                                isDateValueMap = false;
                                break;
                            }
                        }

                        if (isDateValueMap && !dataMap.isEmpty()) {
                            log.debug("检测到日期-数值格式的Map数据，保持键值对形式");
                            // 保持键值对格式，转换数值为Integer类型
                            Map<String, Integer> trendDataMap = new java.util.LinkedHashMap<>();
                            dataMap.entrySet().stream()
                                .sorted(Map.Entry.comparingByKey()) // 按日期排序
                                .forEach(entry -> {
                                    Object value = entry.getValue();
                                    Integer intValue = 0;
                                    if (value instanceof Number) {
                                        intValue = ((Number) value).intValue();
                                        log.debug("保存日期数值 {}: {}", entry.getKey(), intValue);
                                    } else if (value instanceof String) {
                                        try {
                                            intValue = Integer.parseInt((String) value);
                                            log.debug("保存日期字符串数值 {}: {}", entry.getKey(), intValue);
                                        } catch (NumberFormatException e) {
                                            log.warn("无法将日期对应的字符串转换为整数: {} = {}", entry.getKey(), value);
                                            intValue = 0;
                                        }
                                    } else {
                                        log.warn("日期对应的值类型不支持: {} = {} ({})",
                                            entry.getKey(), value, value != null ? value.getClass() : "null");
                                        intValue = 0;
                                    }
                                    trendDataMap.put(entry.getKey(), intValue);
                                });
                            vo.setTrendData(trendDataMap);
                        } else {
                            // 尝试标准key格式 {"trend": [1,2,3], "data": [1,2,3]}
                            Object trendObj = null;
                            String[] possibleKeys = {"trend", "trends", "data", "list", "values", "trendData"};

                            for (String key : possibleKeys) {
                                if (dataMap.containsKey(key)) {
                                    trendObj = dataMap.get(key);
                                    log.debug("找到趋势数据字段: {} = {}", key, trendObj);
                                    break;
                                }
                            }

                            if (trendObj instanceof List<?> dataList) {
                                List<Integer> trendData = new ArrayList<>();
                                for (Object item : dataList) {
                                    if (item instanceof Number) {
                                        trendData.add(((Number) item).intValue());
                                    } else if (item instanceof String) {
                                        try {
                                            trendData.add(Integer.parseInt((String) item));
                                        } catch (NumberFormatException e) {
                                            log.warn("Map中无法将字符串转换为整数: {}", item);
                                            trendData.add(0);
                                        }
                                    } else {
                                        trendData.add(0);
                                    }
                                }
                                vo.setTrendData(trendData);
                            } else {
                                // 原有逻辑：按顺序处理所有数值
                                List<Integer> trendData = new ArrayList<>();
                                for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                                    Object value = entry.getValue();
                                    if (value instanceof Number) {
                                        trendData.add(((Number) value).intValue());
                                        log.debug("添加数值字段 {}: {}", entry.getKey(), value);
                                    }
                                }
                                vo.setTrendData(trendData);
                            }
                        }
                    } else {
                        log.warn("趋势数据格式不支持，类型: {}, 内容: {}",
                            parsedData != null ? parsedData.getClass() : "null", parsedData);
                        vo.setTrendData(new ArrayList<>());
                    }
                } else {
                    vo.setTrendData(new ArrayList<>());
                }
            } catch (Exception e) {
                log.warn("解析三违趋势数据失败，原始数据: [{}], 错误信息: {}",
                    stats.getTrendData(), e.getMessage(), e);
                vo.setTrendData(new ArrayList<>());
            }

            // 解析类型分解数据
            try {
                if (StringUtils.hasText(stats.getViolationTypeBreakdown())) {
                    String typeBreakdownStr = stats.getViolationTypeBreakdown().trim();
                    log.debug("解析三违类型分解数据: {}", typeBreakdownStr);

                    Object typeBreakdown = objectMapper.readValue(typeBreakdownStr, Object.class);
                    vo.setViolationTypeBreakdown(typeBreakdown);
                }
            } catch (Exception e) {
                log.warn("解析三违类型分解数据失败，原始数据: [{}], 错误信息: {}",
                    stats.getViolationTypeBreakdown(), e.getMessage(), e);
            }
        } else {
            // 如果没有数据，设置默认值
            vo.setDailyViolationCount(0);
            vo.setTrendData(new ArrayList<>());
        }
        return vo;
    }

    /**
     * 获取报警统计数据
     */
    private MkSafetyAlarmStatsVO getAlarmStats(String orgCode) {
        LambdaQueryWrapper<MkAlarmStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkAlarmStats::getStatus, 1)
            .eq(StringUtils.hasText(orgCode), MkAlarmStats::getOrgCode, orgCode)
            .orderByDesc(MkAlarmStats::getCreateTime)
            .last("LIMIT 1");

        MkAlarmStats stats = alarmStatsDao.selectOne(queryWrapper);
        log.debug("查询报警统计数据，orgCode: {}, 查询结果: {}", orgCode, stats != null ? "有数据" : "无数据");

        MkSafetyAlarmStatsVO vo = new MkSafetyAlarmStatsVO();
        if (stats != null) {
            log.debug("报警统计原始数据 - ID: {}, trendData: [{}], alarmTypeBreakdown: [{}]",
                stats.getId(), stats.getTrendData(), stats.getAlarmTypeBreakdown());

            vo.setYesterdayCount(stats.getYesterdayCount() != null ? stats.getYesterdayCount() : 0);
            vo.setTodayCount(stats.getTodayCount() != null ? stats.getTodayCount() : 0);
            vo.setGrowthRate(stats.getGrowthRate() != null ? stats.getGrowthRate() : java.math.BigDecimal.ZERO);

            // 解析趋势数据
            try {
                if (StringUtils.hasText(stats.getTrendData())) {
                    String trendDataStr = stats.getTrendData().trim();
                    log.debug("开始解析报警趋势数据，长度: {}, 内容: [{}]", trendDataStr.length(), trendDataStr);

                    // 尝试多种数据类型解析
                    Object parsedData = objectMapper.readValue(trendDataStr, Object.class);
                    log.debug("JSON解析成功，解析结果类型: {}, 内容: {}",
                        parsedData != null ? parsedData.getClass().getSimpleName() : "null", parsedData);

                    if (parsedData instanceof List<?> dataList) {
                        // 直接数组格式：[1, 2, 3, 4, 5] - 转换为BigDecimal数组
                        List<BigDecimal> trendData = new ArrayList<>();
                        for (Object item : dataList) {
                            if (item instanceof Number) {
                                trendData.add(java.math.BigDecimal.valueOf(((Number) item).doubleValue()));
                            } else if (item instanceof String) {
                                BigDecimal parsedValue = parseStringToBigDecimal((String) item, "报警趋势数据");
                                trendData.add(parsedValue);
                            } else {
                                log.warn("未知的报警趋势数据类型: {}", item != null ? item.getClass() : "null");
                                trendData.add(java.math.BigDecimal.ZERO);
                            }
                        }
                        vo.setTrendData(trendData);
                        log.debug("设置直接数组格式的趋势数据，数据点数量: {}", trendData.size());
                    } else if (parsedData instanceof Map) {
                        // Map格式处理
                        Map<String, Object> dataMap = (Map<String, Object>) parsedData;
                        log.debug("Map格式的报警趋势数据，包含的keys: {}", dataMap.keySet());

                        // 检查是否是多条曲线格式（包含yesterday, today, growthRate等字段）
                        String[] curveKeys = {"yesterday", "today", "growthRate", "昨日", "今日", "环比增长率"};
                        boolean isMultiCurveFormat = false;
                        for (String curveKey : curveKeys) {
                            if (dataMap.containsKey(curveKey)) {
                                isMultiCurveFormat = true;
                                break;
                            }
                        }

                        if (isMultiCurveFormat) {
                            log.debug("检测到多条曲线格式的报警数据");
                            // 保持多条曲线格式，确保数值类型为BigDecimal
                            Map<String, Object> processedData = new java.util.LinkedHashMap<>();

                            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                                String key = entry.getKey();
                                Object value = entry.getValue();

                                if (value instanceof List<?> valueList) {
                                    // 处理数组格式的曲线数据
                                    List<BigDecimal> processedList = new ArrayList<>();

                                    for (Object item : valueList) {
                                        if (item instanceof Number) {
                                            processedList.add(BigDecimal.valueOf(((Number) item).doubleValue()));
                                        } else if (item instanceof String) {
                                            try {
                                                processedList.add(new BigDecimal((String) item));
                                            } catch (NumberFormatException e) {
                                                log.warn("无法将曲线数据字符串转换为BigDecimal: {} = {}", key, item);
                                                processedList.add(BigDecimal.ZERO);
                                            }
                                        } else {
                                            log.warn("曲线数据类型不支持: {} = {} ({})",
                                                key, item, item != null ? item.getClass() : "null");
                                            processedList.add(BigDecimal.ZERO);
                                        }
                                    }
                                    processedData.put(key, processedList);
                                    log.debug("处理曲线数据 {}: {} 个数据点", key, processedList.size());
                                } else {
                                    // 保持非数组数据原样
                                    processedData.put(key, value);
                                    log.debug("保持原数据 {}: {}", key, value);
                                }
                            }
                            vo.setTrendData(processedData);
                            log.debug("设置多条曲线格式的趋势数据，包含字段: {}", processedData.keySet());
                        } else {
                            // 检查是否是横轴类别格式（包含categories和对应的数据）
                            boolean isCategoryFormat = dataMap.containsKey("categories") || dataMap.containsKey("类别");

                            if (isCategoryFormat) {
                                log.debug("检测到横轴类别格式的报警数据");
                                // 处理包含横轴类别的数据格式
                                Map<String, Object> processedData = new java.util.LinkedHashMap<>();

                                // 处理类别数据
                                Object categoriesObj = dataMap.get("categories");
                                if (categoriesObj == null) {
                                    categoriesObj = dataMap.get("类别");
                                }

                                if (categoriesObj instanceof List) {
                                    processedData.put("categories", categoriesObj);
                                    log.debug("处理横轴类别: {}", categoriesObj);
                                }

                                // 处理各条曲线数据，确保与类别数量对应
                                String[] seriesKeys = {"yesterday", "today", "growthRate", "昨日", "今日", "环比增长率",
                                    "series"};
                                for (String seriesKey : seriesKeys) {
                                    if (dataMap.containsKey(seriesKey)) {
                                        Object seriesData = dataMap.get(seriesKey);
                                        if (seriesData instanceof List<?> valueList) {
                                            List<BigDecimal> processedList = new ArrayList<>();

                                            for (Object item : valueList) {
                                                if (item instanceof Number) {
                                                    processedList
                                                        .add(BigDecimal.valueOf(((Number) item).doubleValue()));
                                                } else if (item instanceof String) {
                                                    BigDecimal parsedValue = parseStringToBigDecimal((String) item, "类别数据-" + seriesKey);
                                                    processedList.add(parsedValue);
                                                } else {
                                                    log.warn("类别数据类型不支持: {} = {} ({})",
                                                        seriesKey, item, item != null ? item.getClass() : "null");
                                                    processedList.add(BigDecimal.ZERO);
                                                }
                                            }
                                            processedData.put(seriesKey, processedList);
                                            log.debug("处理类别曲线数据 {}: {} 个数据点", seriesKey, processedList.size());
                                        } else if (seriesData instanceof Map) {
                                            // 如果是Map格式的系列数据，递归处理
                                            Map<String, Object> seriesMap = (Map<String, Object>) seriesData;
                                            Map<String, Object> processedSeriesMap = new java.util.LinkedHashMap<>();

                                            for (Map.Entry<String, Object> seriesEntry : seriesMap.entrySet()) {
                                                String subKey = seriesEntry.getKey();
                                                Object subValue = seriesEntry.getValue();

                                                if (subValue instanceof List<?> subValueList) {
                                                    List<BigDecimal> processedSubList = new ArrayList<>();

                                                    for (Object subItem : subValueList) {
                                                        if (subItem instanceof Number) {
                                                            processedSubList.add(BigDecimal
                                                                .valueOf(((Number) subItem).doubleValue()));
                                                        } else if (subItem instanceof String) {
                                                            try {
                                                                processedSubList.add(new BigDecimal((String) subItem));
                                                            } catch (NumberFormatException e) {
                                                                log.warn("无法将子类别数据字符串转换为BigDecimal: {}.{} = {}",
                                                                    seriesKey, subKey, subItem);
                                                                processedSubList.add(BigDecimal.ZERO);
                                                            }
                                                        } else {
                                                            processedSubList.add(BigDecimal.ZERO);
                                                        }
                                                    }
                                                    processedSeriesMap.put(subKey, processedSubList);
                                                } else {
                                                    processedSeriesMap.put(subKey, subValue);
                                                }
                                            }
                                            processedData.put(seriesKey, processedSeriesMap);
                                            log.debug("处理嵌套类别数据 {}: {} 个子项", seriesKey, processedSeriesMap.size());
                                        }
                                    }
                                }

                                // 如果没有找到标准的系列数据，尝试查找其他可能的数据字段
                                if (processedData.size() <= 1) { // 只有categories
                                    for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                                        String key = entry.getKey();
                                        if (!key.equals("categories") && !key.equals("类别")) {
                                            Object value = entry.getValue();
                                            if (value instanceof List<?> valueList) {
                                                List<BigDecimal> processedList = new ArrayList<>();

                                                for (Object item : valueList) {
                                                    if (item instanceof Number) {
                                                        processedList
                                                            .add(BigDecimal.valueOf(((Number) item).doubleValue()));
                                                    } else if (item instanceof String) {
                                                        BigDecimal parsedValue = parseStringToBigDecimal((String) item, "其他类别数据-" + key);
                                                        processedList.add(parsedValue);
                                                    } else {
                                                        processedList.add(BigDecimal.ZERO);
                                                    }
                                                }
                                                processedData.put(key, processedList);
                                                log.debug("处理其他类别数据 {}: {} 个数据点", key, processedList.size());
                                            } else {
                                                processedData.put(key, value);
                                            }
                                        }
                                    }
                                }

                                vo.setTrendData(processedData);
                                log.debug("设置横轴类别格式的趋势数据，包含字段: {}", processedData.keySet());
                            } else {
                                // 检查是否是日期-数值格式的Map（如: {"05-15": 0, "05-17": 0}）
                                boolean isDateValueMap = true;
                                for (String key : dataMap.keySet()) {
                                    // 检查key是否像日期格式（MM-dd 或 yyyy-MM-dd）
                                    if (!key.matches("\\d{2}-\\d{2}") && !key.matches("\\d{4}-\\d{2}-\\d{2}")) {
                                        isDateValueMap = false;
                                        break;
                                    }
                                }

                                if (isDateValueMap && !dataMap.isEmpty()) {
                                    log.debug("检测到日期-数值格式的报警Map数据，保持键值对形式");
                                    // 保持键值对格式，转换数值为BigDecimal类型
                                    Map<String, BigDecimal> trendDataMap = new java.util.LinkedHashMap<>();
                                    dataMap.entrySet().stream()
                                        .sorted(Map.Entry.comparingByKey()) // 按日期排序
                                        .forEach(entry -> {
                                            Object value = entry.getValue();
                                            BigDecimal decimalValue = BigDecimal.ZERO;
                                            if (value instanceof Number) {
                                                decimalValue = BigDecimal.valueOf(((Number) value).doubleValue());
                                                log.debug("保存报警日期数值 {}: {}", entry.getKey(), decimalValue);
                                            } else if (value instanceof String) {
                                                try {
                                                    decimalValue = new BigDecimal((String) value);
                                                    log.debug("保存报警日期字符串数值 {}: {}", entry.getKey(), decimalValue);
                                                } catch (NumberFormatException e) {
                                                    log.warn("无法将报警日期对应的字符串转换为BigDecimal: {} = {}", entry.getKey(),
                                                        value);
                                                    decimalValue = BigDecimal.ZERO;
                                                }
                                            } else {
                                                log.warn("报警日期对应的值类型不支持: {} = {} ({})",
                                                    entry.getKey(), value,
                                                    value != null ? value.getClass() : "null");
                                                decimalValue = BigDecimal.ZERO;
                                            }
                                            trendDataMap.put(entry.getKey(), decimalValue);
                                        });
                                    vo.setTrendData(trendDataMap);
                                } else {
                                    // 尝试标准key格式 {"trend": [1,2,3], "data": [1,2,3]}
                                    Object trendObj = null;
                                    String[] possibleKeys = {"trend", "trends", "data", "list", "values",
                                        "trendData"};

                                    for (String key : possibleKeys) {
                                        if (dataMap.containsKey(key)) {
                                            trendObj = dataMap.get(key);
                                            log.debug("找到报警趋势数据字段: {} = {}", key, trendObj);
                                            break;
                                        }
                                    }

                                    if (trendObj instanceof List<?> dataList) {
                                        List<BigDecimal> trendData = new ArrayList<>();
                                        for (Object item : dataList) {
                                            if (item instanceof Number) {
                                                trendData.add(
                                                    java.math.BigDecimal.valueOf(((Number) item).doubleValue()));
                                            } else if (item instanceof String) {
                                                BigDecimal parsedValue = parseStringToBigDecimal((String) item, "Map趋势数据");
                                                trendData.add(parsedValue);
                                            } else {
                                                trendData.add(java.math.BigDecimal.ZERO);
                                            }
                                        }
                                        vo.setTrendData(trendData);
                                    } else {
                                        log.debug("报警统计trendData字段为空或null，设置默认空数组");
                                        vo.setTrendData(new ArrayList<>());
                                    }
                                }
                            }
                        }
                    } else {
                        log.warn("报警趋势数据格式不支持，类型: {}, 内容: {}",
                            parsedData != null ? parsedData.getClass() : "null", parsedData);
                        vo.setTrendData(new ArrayList<>());
                    }
                } else {
                    log.debug("报警统计trendData字段为空或null，设置默认空数组");
                    vo.setTrendData(new ArrayList<>());
                }
            } catch (Exception e) {
                log.warn("解析报警趋势数据失败，原始数据: [{}], 错误信息: {}, 异常类型: {}",
                    stats.getTrendData(), e.getMessage(), e.getClass().getSimpleName(), e);
                vo.setTrendData(new ArrayList<>());
            }

            // 解析类型分解数据
            try {
                if (StringUtils.hasText(stats.getAlarmTypeBreakdown())) {
                    String typeBreakdownStr = stats.getAlarmTypeBreakdown().trim();
                    log.debug("解析报警类型分解数据: {}", typeBreakdownStr);

                    Object typeBreakdown = objectMapper.readValue(typeBreakdownStr, Object.class);
                    vo.setAlarmTypeBreakdown(typeBreakdown);
                }
            } catch (Exception e) {
                log.warn("解析报警类型分解数据失败，原始数据: [{}], 错误信息: {}",
                    stats.getAlarmTypeBreakdown(), e.getMessage(), e);
            }
        } else {
            // 如果没有数据，设置默认值
            log.debug("报警统计数据为空，设置默认值，orgCode: {}", orgCode);
            vo.setYesterdayCount(0);
            vo.setTodayCount(0);
            vo.setGrowthRate(java.math.BigDecimal.ZERO);
            vo.setTrendData(new ArrayList<>());
        }
        return vo;
    }

    /**
     * 获取风险统计数据
     */
    private MkSafetyRiskStatsVO getRiskStats(String orgCode) {
        LambdaQueryWrapper<MkRiskDetailedStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkRiskDetailedStats::getStatus, 1)
            .eq(StringUtils.hasText(orgCode), MkRiskDetailedStats::getOrgCode, orgCode)
            .orderByDesc(MkRiskDetailedStats::getCreateTime)
            .last("LIMIT 1");

        MkRiskDetailedStats stats = riskDetailedStatsDao.selectOne(queryWrapper);

        MkSafetyRiskStatsVO vo = new MkSafetyRiskStatsVO();
        if (stats != null) {
            vo.setMajorRiskCount(stats.getMajorRiskCount() != null ? stats.getMajorRiskCount() : 0);
            vo.setSignificantRiskCount(stats.getSignificantRiskCount() != null ? stats.getSignificantRiskCount() : 0);
            vo.setGeneralRiskCount(stats.getGeneralRiskCount() != null ? stats.getGeneralRiskCount() : 0);
            vo.setLowRiskCount(stats.getLowRiskCount() != null ? stats.getLowRiskCount() : 0);
            vo.setGeneralALevelCount(stats.getGeneralALevelCount() != null ? stats.getGeneralALevelCount() : 0);
            vo.setGeneralBLevelCount(stats.getGeneralBLevelCount() != null ? stats.getGeneralBLevelCount() : 0);
            vo.setGeneralCLevelCount(stats.getGeneralCLevelCount() != null ? stats.getGeneralCLevelCount() : 0);
            vo.setTotalRiskCount(stats.getTotalRiskCount() != null ? stats.getTotalRiskCount() : 0);
        } else {
            // 如果没有数据，设置默认值
            vo.setMajorRiskCount(0);
            vo.setSignificantRiskCount(0);
            vo.setGeneralRiskCount(0);
            vo.setLowRiskCount(0);
            vo.setGeneralALevelCount(0);
            vo.setGeneralBLevelCount(0);
            vo.setGeneralCLevelCount(0);
            vo.setTotalRiskCount(0);
        }
        return vo;
    }

    /**
     * 获取风险管控清单
     */
    private List<MkSafetyRiskManagementVO> getRiskManagementList(String orgCode) {
        LambdaQueryWrapper<MkRiskManagementList> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkRiskManagementList::getStatus, 1)
            .eq(StringUtils.hasText(orgCode), MkRiskManagementList::getOrgCode, orgCode)
            .orderByDesc(MkRiskManagementList::getEntryTime)
            .last("LIMIT 10"); // 限制显示最近10条

        List<MkRiskManagementList> list = riskManagementListDao.selectList(queryWrapper);

        List<MkSafetyRiskManagementVO> voList = new ArrayList<>();
        for (MkRiskManagementList item : list) {
            MkSafetyRiskManagementVO vo = new MkSafetyRiskManagementVO();
            vo.setItemCode(item.getItemCode());
            vo.setRiskDescription(item.getRiskDescription());
            vo.setRiskLevel(item.getRiskLevel());
            vo.setRiskLevelColor(item.getRiskLevelColor());
            vo.setResponsiblePerson(item.getResponsiblePerson());
            vo.setDepartment(item.getDepartment());
            vo.setEntryTime(item.getEntryTime());
            vo.setCompletionStatus(item.getCompletionStatus());
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取隐患专业分析数据
     */
    private List<MkSafetyProfessionalAnalysisVO> getHazardProfessionalAnalysisList(String orgCode) {
        LambdaQueryWrapper<MkProfessionalAnalysisStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkProfessionalAnalysisStats::getStatus, 1)
            .eq(StringUtils.hasText(orgCode), MkProfessionalAnalysisStats::getOrgCode, orgCode)
            .orderByDesc(MkProfessionalAnalysisStats::getCreateTime)
            .orderByDesc(MkProfessionalAnalysisStats::getHazardCount);

        List<MkProfessionalAnalysisStats> list = professionalAnalysisStatsDao.selectList(queryWrapper);

        List<MkSafetyProfessionalAnalysisVO> voList = new ArrayList<>();
        for (MkProfessionalAnalysisStats item : list) {
            MkSafetyProfessionalAnalysisVO vo = new MkSafetyProfessionalAnalysisVO();
            vo.setProfessionalType(item.getProfessionalType());
            vo.setHazardCount(item.getHazardCount() != null ? item.getHazardCount() : 0);

            // 解析趋势数据（专业分析只需要趋势数据）
            try {
                if (StringUtils.hasText(item.getTrendData())) {
                    String trendDataStr = item.getTrendData().trim();
                    log.debug("解析专业分析趋势数据: {}", trendDataStr);

                    Object trendData = objectMapper.readValue(trendDataStr, Object.class);
                    vo.setTrendData(trendData);
                }
            } catch (Exception e) {
                log.warn("解析专业分析趋势数据失败，专业类型: [{}], 原始数据: [{}], 错误信息: {}",
                    item.getProfessionalType(), item.getTrendData(), e.getMessage(), e);
            }

            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取隐患清单数据
     */
    private List<MkSafetyHazardListVO> getHazardList(String orgCode) {
        LambdaQueryWrapper<MkProfessionalAnalysisStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkProfessionalAnalysisStats::getStatus, 1)
            .eq(StringUtils.hasText(orgCode), MkProfessionalAnalysisStats::getOrgCode, orgCode)
            .orderByDesc(MkProfessionalAnalysisStats::getCreateTime)
            .last("LIMIT 20"); // 限制显示最近20条隐患清单

        List<MkProfessionalAnalysisStats> list = professionalAnalysisStatsDao.selectList(queryWrapper);

        List<MkSafetyHazardListVO> voList = new ArrayList<>();
        for (MkProfessionalAnalysisStats item : list) {
            // 解析隐患清单数据
            try {
                if (StringUtils.hasText(item.getHazardListData())) {
                    String hazardListDataStr = item.getHazardListData().trim();
                    log.debug("解析隐患清单数据: {}", hazardListDataStr);

                    Object hazardListData = objectMapper.readValue(hazardListDataStr, Object.class);

                    // 如果hazardListData是一个包含hazards数组的对象
                    if (hazardListData instanceof java.util.Map) {
                        java.util.Map<String, Object> dataMap = (java.util.Map<String, Object>) hazardListData;
                        Object hazardsObj = dataMap.get("hazards");

                        if (hazardsObj instanceof java.util.List) {
                            java.util.List<Object> hazardsList = (java.util.List<Object>) hazardsObj;

                            for (Object hazardObj : hazardsList) {
                                if (hazardObj instanceof java.util.Map) {
                                    java.util.Map<String, Object> hazardMap = (java.util.Map<String, Object>) hazardObj;

                                    MkSafetyHazardListVO vo = new MkSafetyHazardListVO();
                                    vo.setHazardId(getLongValue(hazardMap.get("id")));
                                    vo.setHazardName(getStringValue(hazardMap.get("name")));
                                    vo.setHazardDescription(getStringValue(hazardMap.get("description")));
                                    vo.setHazardLevel(getStringValue(hazardMap.get("level")));
                                    vo.setHazardLevelColor(getHazardLevelColor(getStringValue(hazardMap.get("level"))));
                                    vo.setProfessionalType(item.getProfessionalType());
                                    vo.setDiscoveryTime(getStringValue(hazardMap.get("discoveryTime")));
                                    vo.setResponsiblePerson(getStringValue(hazardMap.get("responsiblePerson")));
                                    vo.setRectificationStatus(getStringValue(hazardMap.get("rectificationStatus")));
                                    vo.setRectificationDeadline(getStringValue(hazardMap.get("rectificationDeadline")));

                                    voList.add(vo);
                                }
                            }
                        } else {
                            log.warn("隐患清单数据格式错误，hazards字段不是数组类型: {}",
                                hazardsObj != null ? hazardsObj.getClass() : "null");
                        }
                    } else {
                        log.warn("隐患清单数据格式错误，根节点不是Map类型: {}",
                            hazardListData != null ? hazardListData.getClass() : "null");
                    }
                }
            } catch (Exception e) {
                log.warn("解析隐患清单数据失败，专业类型: [{}], 原始数据: [{}], 错误信息: {}",
                    item.getProfessionalType(), item.getHazardListData(), e.getMessage(), e);
            }
        }
        return voList;
    }

    /**
     * 获取字符串值
     */
    private String getStringValue(Object value) {
        return value != null ? value.toString() : "";
    }

    /**
     * 获取Long值
     */
    private Long getLongValue(Object value) {
        if (value == null)
            return null;
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 根据隐患等级获取颜色
     */
    private String getHazardLevelColor(String level) {
        if ("重大".equals(level)) {
            return "#FF4D4F"; // 红色
        } else if ("一般".equals(level)) {
            return "#FAAD14"; // 橙色
        }
        return "#52C41A"; // 绿色（默认）
    }

    /**
     * 获取中央环形数据
     */
    private MkSafetyDecisionVO.CentralRingData getCentralRingData(MkSafetyDecisionVO result) {
        MkSafetyDecisionVO.CentralRingData centralData = new MkSafetyDecisionVO.CentralRingData();

        // 接警总数 = 今日报警数量，添加空值检查
        Integer alarmTotal = 0;
        if (result.getAlarmStats() != null && result.getAlarmStats().getTodayCount() != null) {
            alarmTotal = result.getAlarmStats().getTodayCount();
        }
        centralData.setAlarmTotal(alarmTotal);

        // 三违总数 = 当日三违数量，添加空值检查
        Integer violationTotal = 0;
        if (result.getViolationStats() != null && result.getViolationStats().getDailyViolationCount() != null) {
            violationTotal = result.getViolationStats().getDailyViolationCount();
        }
        centralData.setViolationTotal(violationTotal);

        // 风险总数，添加空值检查
        Integer riskTotal = 0;
        if (result.getRiskStats() != null && result.getRiskStats().getTotalRiskCount() != null) {
            riskTotal = result.getRiskStats().getTotalRiskCount();
        }
        centralData.setRiskTotal(riskTotal);

        // 隐患总数，添加空值检查
        Integer hazardTotal = 0;
        if (result.getHazardStats() != null && result.getHazardStats().getTotalCount() != null) {
            hazardTotal = result.getHazardStats().getTotalCount();
        }
        centralData.setHazardTotal(hazardTotal);

        return centralData;
    }

    /**
     * 将字符串安全地转换为BigDecimal
     * 支持多种格式的字符串解析，提供更强的容错性
     *
     * @param str 要转换的字符串
     * @param context 上下文信息（用于日志）
     * @return 转换后的BigDecimal，失败时返回BigDecimal.ZERO
     */
    private BigDecimal parseStringToBigDecimal(String str, String context) {
        if (str == null || str.trim().isEmpty()) {
            log.debug("字符串为空或null，返回0: context={}", context);
            return BigDecimal.ZERO;
        }

        String cleanStr = str.trim();

        try {
            // 直接尝试解析
            return new BigDecimal(cleanStr);
        } catch (NumberFormatException e1) {
            try {
                // 尝试去除可能的百分号
                if (cleanStr.endsWith("%")) {
                    String numStr = cleanStr.substring(0, cleanStr.length() - 1).trim();
                    return new BigDecimal(numStr).divide(BigDecimal.valueOf(100), 4, BigDecimal.ROUND_HALF_UP);
                }

                // 尝试去除可能的逗号分隔符
                if (cleanStr.contains(",")) {
                    String numStr = cleanStr.replace(",", "");
                    return new BigDecimal(numStr);
                }

                // 尝试解析科学计数法
                if (cleanStr.toLowerCase().contains("e")) {
                    return new BigDecimal(cleanStr);
                }

                // 尝试解析浮点数（去除多余的空格和特殊字符）
                String numStr = cleanStr.replaceAll("[^\\d.-]", "");
                if (!numStr.isEmpty()) {
                    return new BigDecimal(numStr);
                }

            } catch (NumberFormatException e2) {
                log.warn("多种方式尝试解析字符串失败: {} = '{}', context={}", context, str, e2.getMessage());
            }
        }

        // 所有解析方式都失败，返回0
        log.warn("无法将字符串转换为BigDecimal，返回0: {} = '{}'", context, str);
        return BigDecimal.ZERO;
    }
}
