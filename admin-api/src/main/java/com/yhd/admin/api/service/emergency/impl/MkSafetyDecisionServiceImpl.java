package com.yhd.admin.api.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.admin.api.dao.emergency.*;
import com.yhd.admin.api.domain.emergency.entity.*;
import com.yhd.admin.api.domain.emergency.query.MkSafetyDecisionParam;
import com.yhd.admin.api.domain.emergency.vo.*;
import com.yhd.admin.api.service.emergency.MkSafetyDecisionService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 安全决策大屏 Service 实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MkSafetyDecisionServiceImpl implements MkSafetyDecisionService {

    @Resource
    private MkHazardStatsDao hazardStatsDao;

    @Resource
    private MkViolationStatsDao violationStatsDao;

    @Resource
    private MkAlarmStatsDao alarmStatsDao;

    @Resource
    private MkRiskDetailedStatsDao riskDetailedStatsDao;

    @Resource
    private MkRiskManagementListDao riskManagementListDao;

    @Resource
    private MkProfessionalAnalysisStatsDao professionalAnalysisStatsDao;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public MkSafetyDecisionVO getSafetyDecisionData(MkSafetyDecisionParam param) {
        log.info("获取安全决策大屏数据，参数：{}", param);

        MkSafetyDecisionVO result = new MkSafetyDecisionVO();

        String orgCode = param.getOrgCode();

        // 获取隐患统计数据
        result.setHazardStats(getHazardStats(orgCode));

        // 获取三违统计数据
        result.setViolationStats(getViolationStats(orgCode));

        // 获取报警统计数据
        result.setAlarmStats(getAlarmStats(orgCode));

        // 获取风险统计数据
        result.setRiskStats(getRiskStats(orgCode));

        // 获取风险管控清单
        result.setRiskManagementList(getRiskManagementList(orgCode));

        // 获取隐患专业分析数据
        result.setHazardProfessionalAnalysisList(getHazardProfessionalAnalysisList(orgCode));

        // 获取隐患清单数据
        result.setHazardList(getHazardList(orgCode));

        // 设置中央环形数据
        result.setCentralRingData(getCentralRingData(result));

        return result;
    }

    /**
     * 获取隐患统计数据
     */
    private MkSafetyHazardStatsVO getHazardStats(String orgCode) {
        LambdaQueryWrapper<MkHazardStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkHazardStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkHazardStats::getOrgCode, orgCode)
                .orderByDesc(MkHazardStats::getCreateTime)
                .last("LIMIT 1");

        MkHazardStats stats = hazardStatsDao.selectOne(queryWrapper);

        MkSafetyHazardStatsVO vo = new MkSafetyHazardStatsVO();
        if (stats != null) {
            vo.setUnrectifiedCount(stats.getUnrectifiedCount() != null ? stats.getUnrectifiedCount() : 0);
            vo.setRectifiedUnverifiedCount(
                    stats.getRectifiedUnverifiedCount() != null ? stats.getRectifiedUnverifiedCount() : 0);
            vo.setVerifiedUnacceptedCount(
                    stats.getVerifiedUnacceptedCount() != null ? stats.getVerifiedUnacceptedCount() : 0);
            vo.setVerifiedPassedCount(stats.getVerifiedPassedCount() != null ? stats.getVerifiedPassedCount() : 0);
            vo.setTotalCount(stats.getTotalCount() != null ? stats.getTotalCount() : 0);
        } else {
            // 如果没有数据，设置默认值
            vo.setUnrectifiedCount(0);
            vo.setRectifiedUnverifiedCount(0);
            vo.setVerifiedUnacceptedCount(0);
            vo.setVerifiedPassedCount(0);
            vo.setTotalCount(0);
        }
        return vo;
    }

    /**
     * 获取三违统计数据
     */
    private MkSafetyViolationStatsVO getViolationStats(String orgCode) {
        LambdaQueryWrapper<MkViolationStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkViolationStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkViolationStats::getOrgCode, orgCode)
                .orderByDesc(MkViolationStats::getCreateTime)
                .last("LIMIT 1");

        MkViolationStats stats = violationStatsDao.selectOne(queryWrapper);

        MkSafetyViolationStatsVO vo = new MkSafetyViolationStatsVO();
        if (stats != null) {
            vo.setDailyViolationCount(stats.getDailyViolationCount() != null ? stats.getDailyViolationCount() : 0);

            // 解析趋势数据
            try {
                if (StringUtils.hasText(stats.getTrendData())) {
                    String trendDataStr = stats.getTrendData().trim();
                    log.debug("解析三违趋势数据: {}", trendDataStr);

                    // 尝试多种数据类型解析
                    Object parsedData = objectMapper.readValue(trendDataStr, Object.class);
                    List<Integer> trendData = new ArrayList<>();

                    if (parsedData instanceof List) {
                        List<?> dataList = (List<?>) parsedData;
                        for (Object item : dataList) {
                            if (item instanceof Number) {
                                trendData.add(((Number) item).intValue());
                            } else if (item instanceof String) {
                                try {
                                    trendData.add(Integer.parseInt((String) item));
                                } catch (NumberFormatException e) {
                                    log.warn("无法将字符串转换为整数: {}", item);
                                    trendData.add(0);
                                }
                            } else {
                                log.warn("未知的趋势数据类型: {}", item != null ? item.getClass() : "null");
                                trendData.add(0);
                            }
                        }
                    } else if (parsedData instanceof Map) {
                        // 如果是Map格式，查看所有key并尝试找到合适的字段
                        Map<String, Object> dataMap = (Map<String, Object>) parsedData;
                        log.debug("Map格式的趋势数据，包含的keys: {}", dataMap.keySet());

                        // 尝试多个可能的key名称
                        Object trendObj = null;
                        String[] possibleKeys = { "trend", "trends", "data", "list", "values", "trendData" };

                        for (String key : possibleKeys) {
                            if (dataMap.containsKey(key)) {
                                trendObj = dataMap.get(key);
                                log.debug("找到趋势数据字段: {} = {}", key, trendObj);
                                break;
                            }
                        }

                        // 如果没找到预期的key，使用所有数值类型的value
                        if (trendObj == null) {
                            log.debug("未找到预期的趋势数据字段，尝试解析所有数值");
                            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                                Object value = entry.getValue();
                                if (value instanceof Number) {
                                    trendData.add(((Number) value).intValue());
                                    log.debug("添加数值字段 {}: {}", entry.getKey(), value);
                                } else if (value instanceof List) {
                                    // 如果值是List，使用它作为趋势数据
                                    trendObj = value;
                                    log.debug("找到List类型字段 {}: {}", entry.getKey(), value);
                                    break;
                                }
                            }
                        }

                        // 处理找到的趋势数据
                        if (trendObj instanceof List) {
                            List<?> dataList = (List<?>) trendObj;
                            for (Object item : dataList) {
                                if (item instanceof Number) {
                                    trendData.add(((Number) item).intValue());
                                } else if (item instanceof String) {
                                    try {
                                        trendData.add(Integer.parseInt((String) item));
                                    } catch (NumberFormatException e) {
                                        log.warn("Map中无法将字符串转换为整数: {}", item);
                                        trendData.add(0);
                                    }
                                } else {
                                    trendData.add(0);
                                }
                            }
                        }
                    } else {
                        log.warn("趋势数据格式不支持，类型: {}, 内容: {}",
                                parsedData != null ? parsedData.getClass() : "null", parsedData);
                    }

                    vo.setTrendData(trendData);
                } else {
                    vo.setTrendData(new ArrayList<>());
                }
            } catch (Exception e) {
                log.warn("解析三违趋势数据失败，原始数据: [{}], 错误信息: {}",
                        stats.getTrendData(), e.getMessage(), e);
                vo.setTrendData(new ArrayList<>());
            }

            // 解析类型分解数据
            try {
                if (StringUtils.hasText(stats.getViolationTypeBreakdown())) {
                    String typeBreakdownStr = stats.getViolationTypeBreakdown().trim();
                    log.debug("解析三违类型分解数据: {}", typeBreakdownStr);

                    Object typeBreakdown = objectMapper.readValue(typeBreakdownStr, Object.class);
                    vo.setViolationTypeBreakdown(typeBreakdown);
                }
            } catch (Exception e) {
                log.warn("解析三违类型分解数据失败，原始数据: [{}], 错误信息: {}",
                        stats.getViolationTypeBreakdown(), e.getMessage(), e);
            }
        } else {
            // 如果没有数据，设置默认值
            vo.setDailyViolationCount(0);
            vo.setTrendData(new ArrayList<>());
        }
        return vo;
    }

    /**
     * 获取报警统计数据
     */
    private MkSafetyAlarmStatsVO getAlarmStats(String orgCode) {
        LambdaQueryWrapper<MkAlarmStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkAlarmStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkAlarmStats::getOrgCode, orgCode)
                .orderByDesc(MkAlarmStats::getCreateTime)
                .last("LIMIT 1");

        MkAlarmStats stats = alarmStatsDao.selectOne(queryWrapper);

        MkSafetyAlarmStatsVO vo = new MkSafetyAlarmStatsVO();
        if (stats != null) {
            vo.setYesterdayCount(stats.getYesterdayCount() != null ? stats.getYesterdayCount() : 0);
            vo.setTodayCount(stats.getTodayCount() != null ? stats.getTodayCount() : 0);
            vo.setGrowthRate(stats.getGrowthRate() != null ? stats.getGrowthRate() : java.math.BigDecimal.ZERO);

            // 解析趋势数据
            try {
                if (StringUtils.hasText(stats.getTrendData())) {
                    String trendDataStr = stats.getTrendData().trim();
                    log.debug("解析报警趋势数据: {}", trendDataStr);

                    // 尝试多种数据类型解析
                    Object parsedData = objectMapper.readValue(trendDataStr, Object.class);
                    List<java.math.BigDecimal> trendData = new ArrayList<>();

                    if (parsedData instanceof List) {
                        List<?> dataList = (List<?>) parsedData;
                        for (Object item : dataList) {
                            if (item instanceof Number) {
                                trendData.add(java.math.BigDecimal.valueOf(((Number) item).doubleValue()));
                            } else if (item instanceof String) {
                                try {
                                    trendData.add(new java.math.BigDecimal((String) item));
                                } catch (NumberFormatException e) {
                                    log.warn("无法将字符串转换为BigDecimal: {}", item);
                                    trendData.add(java.math.BigDecimal.ZERO);
                                }
                            } else {
                                log.warn("未知的报警趋势数据类型: {}", item != null ? item.getClass() : "null");
                                trendData.add(java.math.BigDecimal.ZERO);
                            }
                        }
                    } else if (parsedData instanceof Map) {
                        // 如果是Map格式，尝试获取trend字段
                        Map<String, Object> dataMap = (Map<String, Object>) parsedData;
                        Object trendObj = dataMap.get("trend");
                        if (trendObj instanceof List) {
                            List<?> dataList = (List<?>) trendObj;
                            for (Object item : dataList) {
                                if (item instanceof Number) {
                                    trendData.add(java.math.BigDecimal.valueOf(((Number) item).doubleValue()));
                                } else {
                                    trendData.add(java.math.BigDecimal.ZERO);
                                }
                            }
                        }
                    }

                    vo.setTrendData(trendData);
                } else {
                    vo.setTrendData(new ArrayList<>());
                }
            } catch (Exception e) {
                log.warn("解析报警趋势数据失败，原始数据: [{}], 错误信息: {}",
                        stats.getTrendData(), e.getMessage(), e);
                vo.setTrendData(new ArrayList<>());
            }

            // 解析类型分解数据
            try {
                if (StringUtils.hasText(stats.getAlarmTypeBreakdown())) {
                    String typeBreakdownStr = stats.getAlarmTypeBreakdown().trim();
                    log.debug("解析报警类型分解数据: {}", typeBreakdownStr);

                    Object typeBreakdown = objectMapper.readValue(typeBreakdownStr, Object.class);
                    vo.setAlarmTypeBreakdown(typeBreakdown);
                }
            } catch (Exception e) {
                log.warn("解析报警类型分解数据失败，原始数据: [{}], 错误信息: {}",
                        stats.getAlarmTypeBreakdown(), e.getMessage(), e);
            }
        } else {
            // 如果没有数据，设置默认值
            vo.setYesterdayCount(0);
            vo.setTodayCount(0);
            vo.setGrowthRate(java.math.BigDecimal.ZERO);
            vo.setTrendData(new ArrayList<>());
        }
        return vo;
    }

    /**
     * 获取风险统计数据
     */
    private MkSafetyRiskStatsVO getRiskStats(String orgCode) {
        LambdaQueryWrapper<MkRiskDetailedStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkRiskDetailedStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkRiskDetailedStats::getOrgCode, orgCode)
                .orderByDesc(MkRiskDetailedStats::getCreateTime)
                .last("LIMIT 1");

        MkRiskDetailedStats stats = riskDetailedStatsDao.selectOne(queryWrapper);

        MkSafetyRiskStatsVO vo = new MkSafetyRiskStatsVO();
        if (stats != null) {
            vo.setMajorRiskCount(stats.getMajorRiskCount() != null ? stats.getMajorRiskCount() : 0);
            vo.setSignificantRiskCount(stats.getSignificantRiskCount() != null ? stats.getSignificantRiskCount() : 0);
            vo.setGeneralRiskCount(stats.getGeneralRiskCount() != null ? stats.getGeneralRiskCount() : 0);
            vo.setLowRiskCount(stats.getLowRiskCount() != null ? stats.getLowRiskCount() : 0);
            vo.setGeneralALevelCount(stats.getGeneralALevelCount() != null ? stats.getGeneralALevelCount() : 0);
            vo.setGeneralBLevelCount(stats.getGeneralBLevelCount() != null ? stats.getGeneralBLevelCount() : 0);
            vo.setGeneralCLevelCount(stats.getGeneralCLevelCount() != null ? stats.getGeneralCLevelCount() : 0);
            vo.setTotalRiskCount(stats.getTotalRiskCount() != null ? stats.getTotalRiskCount() : 0);
        } else {
            // 如果没有数据，设置默认值
            vo.setMajorRiskCount(0);
            vo.setSignificantRiskCount(0);
            vo.setGeneralRiskCount(0);
            vo.setLowRiskCount(0);
            vo.setGeneralALevelCount(0);
            vo.setGeneralBLevelCount(0);
            vo.setGeneralCLevelCount(0);
            vo.setTotalRiskCount(0);
        }
        return vo;
    }

    /**
     * 获取风险管控清单
     */
    private List<MkSafetyRiskManagementVO> getRiskManagementList(String orgCode) {
        LambdaQueryWrapper<MkRiskManagementList> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkRiskManagementList::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkRiskManagementList::getOrgCode, orgCode)
                .orderByDesc(MkRiskManagementList::getEntryTime)
                .last("LIMIT 10"); // 限制显示最近10条

        List<MkRiskManagementList> list = riskManagementListDao.selectList(queryWrapper);

        List<MkSafetyRiskManagementVO> voList = new ArrayList<>();
        for (MkRiskManagementList item : list) {
            MkSafetyRiskManagementVO vo = new MkSafetyRiskManagementVO();
            vo.setItemCode(item.getItemCode());
            vo.setRiskDescription(item.getRiskDescription());
            vo.setRiskLevel(item.getRiskLevel());
            vo.setRiskLevelColor(item.getRiskLevelColor());
            vo.setResponsiblePerson(item.getResponsiblePerson());
            vo.setDepartment(item.getDepartment());
            vo.setEntryTime(item.getEntryTime());
            vo.setCompletionStatus(item.getCompletionStatus());
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取隐患专业分析数据
     */
    private List<MkSafetyProfessionalAnalysisVO> getHazardProfessionalAnalysisList(String orgCode) {
        LambdaQueryWrapper<MkProfessionalAnalysisStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkProfessionalAnalysisStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkProfessionalAnalysisStats::getOrgCode, orgCode)
                .orderByDesc(MkProfessionalAnalysisStats::getCreateTime)
                .orderByDesc(MkProfessionalAnalysisStats::getHazardCount);

        List<MkProfessionalAnalysisStats> list = professionalAnalysisStatsDao.selectList(queryWrapper);

        List<MkSafetyProfessionalAnalysisVO> voList = new ArrayList<>();
        for (MkProfessionalAnalysisStats item : list) {
            MkSafetyProfessionalAnalysisVO vo = new MkSafetyProfessionalAnalysisVO();
            vo.setProfessionalType(item.getProfessionalType());
            vo.setHazardCount(item.getHazardCount() != null ? item.getHazardCount() : 0);

            // 解析趋势数据（专业分析只需要趋势数据）
            try {
                if (StringUtils.hasText(item.getTrendData())) {
                    String trendDataStr = item.getTrendData().trim();
                    log.debug("解析专业分析趋势数据: {}", trendDataStr);

                    Object trendData = objectMapper.readValue(trendDataStr, Object.class);
                    vo.setTrendData(trendData);
                }
            } catch (Exception e) {
                log.warn("解析专业分析趋势数据失败，专业类型: [{}], 原始数据: [{}], 错误信息: {}",
                        item.getProfessionalType(), item.getTrendData(), e.getMessage(), e);
            }

            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取隐患清单数据
     */
    private List<MkSafetyHazardListVO> getHazardList(String orgCode) {
        LambdaQueryWrapper<MkProfessionalAnalysisStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkProfessionalAnalysisStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkProfessionalAnalysisStats::getOrgCode, orgCode)
                .orderByDesc(MkProfessionalAnalysisStats::getCreateTime)
                .last("LIMIT 20"); // 限制显示最近20条隐患清单

        List<MkProfessionalAnalysisStats> list = professionalAnalysisStatsDao.selectList(queryWrapper);

        List<MkSafetyHazardListVO> voList = new ArrayList<>();
        for (MkProfessionalAnalysisStats item : list) {
            // 解析隐患清单数据
            try {
                if (StringUtils.hasText(item.getHazardListData())) {
                    String hazardListDataStr = item.getHazardListData().trim();
                    log.debug("解析隐患清单数据: {}", hazardListDataStr);

                    Object hazardListData = objectMapper.readValue(hazardListDataStr, Object.class);

                    // 如果hazardListData是一个包含hazards数组的对象
                    if (hazardListData instanceof java.util.Map) {
                        java.util.Map<String, Object> dataMap = (java.util.Map<String, Object>) hazardListData;
                        Object hazardsObj = dataMap.get("hazards");

                        if (hazardsObj instanceof java.util.List) {
                            java.util.List<Object> hazardsList = (java.util.List<Object>) hazardsObj;

                            for (Object hazardObj : hazardsList) {
                                if (hazardObj instanceof java.util.Map) {
                                    java.util.Map<String, Object> hazardMap = (java.util.Map<String, Object>) hazardObj;

                                    MkSafetyHazardListVO vo = new MkSafetyHazardListVO();
                                    vo.setHazardId(getLongValue(hazardMap.get("id")));
                                    vo.setHazardName(getStringValue(hazardMap.get("name")));
                                    vo.setHazardDescription(getStringValue(hazardMap.get("description")));
                                    vo.setHazardLevel(getStringValue(hazardMap.get("level")));
                                    vo.setHazardLevelColor(getHazardLevelColor(getStringValue(hazardMap.get("level"))));
                                    vo.setProfessionalType(item.getProfessionalType());
                                    vo.setDiscoveryTime(getStringValue(hazardMap.get("discoveryTime")));
                                    vo.setResponsiblePerson(getStringValue(hazardMap.get("responsiblePerson")));
                                    vo.setRectificationStatus(getStringValue(hazardMap.get("rectificationStatus")));
                                    vo.setRectificationDeadline(getStringValue(hazardMap.get("rectificationDeadline")));

                                    voList.add(vo);
                                }
                            }
                        } else {
                            log.warn("隐患清单数据格式错误，hazards字段不是数组类型: {}",
                                    hazardsObj != null ? hazardsObj.getClass() : "null");
                        }
                    } else {
                        log.warn("隐患清单数据格式错误，根节点不是Map类型: {}",
                                hazardListData != null ? hazardListData.getClass() : "null");
                    }
                }
            } catch (Exception e) {
                log.warn("解析隐患清单数据失败，专业类型: [{}], 原始数据: [{}], 错误信息: {}",
                        item.getProfessionalType(), item.getHazardListData(), e.getMessage(), e);
            }
        }
        return voList;
    }

    /**
     * 获取字符串值
     */
    private String getStringValue(Object value) {
        return value != null ? value.toString() : "";
    }

    /**
     * 获取Long值
     */
    private Long getLongValue(Object value) {
        if (value == null)
            return null;
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 根据隐患等级获取颜色
     */
    private String getHazardLevelColor(String level) {
        if ("重大".equals(level)) {
            return "#FF4D4F"; // 红色
        } else if ("一般".equals(level)) {
            return "#FAAD14"; // 橙色
        }
        return "#52C41A"; // 绿色（默认）
    }

    /**
     * 获取中央环形数据
     */
    private MkSafetyDecisionVO.CentralRingData getCentralRingData(MkSafetyDecisionVO result) {
        MkSafetyDecisionVO.CentralRingData centralData = new MkSafetyDecisionVO.CentralRingData();

        // 接警总数 = 今日报警数量，添加空值检查
        Integer alarmTotal = 0;
        if (result.getAlarmStats() != null && result.getAlarmStats().getTodayCount() != null) {
            alarmTotal = result.getAlarmStats().getTodayCount();
        }
        centralData.setAlarmTotal(alarmTotal);

        // 三违总数 = 当日三违数量，添加空值检查
        Integer violationTotal = 0;
        if (result.getViolationStats() != null && result.getViolationStats().getDailyViolationCount() != null) {
            violationTotal = result.getViolationStats().getDailyViolationCount();
        }
        centralData.setViolationTotal(violationTotal);

        // 风险总数，添加空值检查
        Integer riskTotal = 0;
        if (result.getRiskStats() != null && result.getRiskStats().getTotalRiskCount() != null) {
            riskTotal = result.getRiskStats().getTotalRiskCount();
        }
        centralData.setRiskTotal(riskTotal);

        // 隐患总数，添加空值检查
        Integer hazardTotal = 0;
        if (result.getHazardStats() != null && result.getHazardStats().getTotalCount() != null) {
            hazardTotal = result.getHazardStats().getTotalCount();
        }
        centralData.setHazardTotal(hazardTotal);

        return centralData;
    }
}