package com.yhd.admin.api.domain.monitor.nitrogen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 制氮空压机详情表
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_mk_nitrogen_compressor_data")
public class TbMkNitrogenCompressorData extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联制氮空压机信息表ID
     */
    @TableField("nitrogen_compressor_info_id")
    private Long nitrogenCompressorInfoId;

    /**
     * 空压机状态: 0-停止, 1-运行, 2-故障, 3-待机
     */
    @TableField("status")
    private Byte status;

    /**
     * 排气压力(MPa)
     */
    @TableField("discharge_pressure")
    private BigDecimal dischargePressure;

    /**
     * 螺杆温度(℃)
     */
    @TableField("screw_temperature")
    private BigDecimal screwTemperature;

    /**
     * 运行时间(h)
     */
    @TableField("runtime_hours")
    private Integer runtimeHours;

    /**
     * 电机电流(A)
     */
    @TableField("motor_current")
    private BigDecimal motorCurrent;

    /**
     * 电源电压(V)
     */
    @TableField("power_voltage")
    private BigDecimal powerVoltage;

    /**
     * 风扇电流(A)
     */
    @TableField("fan_current")
    private BigDecimal fanCurrent;
}
