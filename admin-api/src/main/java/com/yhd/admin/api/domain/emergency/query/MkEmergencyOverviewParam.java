package com.yhd.admin.api.domain.emergency.query;

import lombok.Data;

import java.time.LocalDate;

/**
 * 应急救援中心总览查询参数
 * 
 * <AUTHOR>
 */
@Data
public class MkEmergencyOverviewParam {

    /**
     * 组织机构编码
     */
    private String orgCode;

    /**
     * 统计日期（用于人员统计查询）
     */
    private LocalDate statsDate;

    /**
     * 分类类型：TEAM-区队类型，AREA-区域类型
     */
    private String categoryType;

    /**
     * 导航类型（用于查询特定导航下的列表项目）
     */
    private String navType;

    /**
     * 是否只查询下井领导
     */
    private Boolean onlyUnderground;

    /**
     * 是否只查询活跃工作面
     */
    private Boolean onlyActive;

    /**
     * 总结日期（用于安全总结查询）
     */
    private LocalDate summaryDate;
}