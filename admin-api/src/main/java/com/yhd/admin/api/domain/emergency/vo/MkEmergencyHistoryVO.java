package com.yhd.admin.api.domain.emergency.vo;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 应急历史事件记录表 VO
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
public class MkEmergencyHistoryVO extends BaseVO implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 事件编码
     */
    private String eventCode;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件位置
     */
    private String eventLocation;

    /**
     * 事件类型：1-应急事件，2-演练事件
     */
    private Integer eventType;

    /**
     * 事件类型名称
     */
    private String eventTypeName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 事件等级
     */
    private String eventLevel;

    /**
     * 事件状态：1-进行中，2-已完成，3-已关闭
     */
    private Integer eventStatus;

    /**
     * 事件状态名称
     */
    private String eventStatusName;

    /**
     * 事件时间
     */
    private LocalDateTime eventTime;

    /**
     * 处理人
     */
    private String handler;

    /**
     * 事件描述
     */
    private String description;

    /**
     * 处理方案
     */
    private String solution;

    /**
     * 处理结果
     */
    private String result;

    /**
     * 备注
     */
    private String remark;
}
