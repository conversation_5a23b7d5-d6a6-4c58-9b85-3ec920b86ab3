package com.yhd.admin.api.domain.sys.convert;

import java.util.List;

import org.mapstruct.Mapper;

import com.yhd.admin.api.domain.sys.dto.DicItemDTO;
import com.yhd.admin.api.domain.sys.entity.SysDicItem;
import com.yhd.admin.api.domain.sys.query.DicItemParam;
import com.yhd.admin.api.domain.sys.vo.DicItemVO;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName DicItemConvert.java @Description TODO
 * @createTime 2020年05月20日 17:00:00
 */
@Mapper(componentModel = "spring")
public interface DicItemConvert {

    SysDicItem toEntity(DicItemParam param);

    List<SysDicItem> toEntity(List<DicItemParam> params);

    DicItemDTO toDTO(SysDicItem dicItem);

    List<DicItemDTO> toDTO(List<SysDicItem> params);

    DicItemVO toVO(DicItemDTO dicItemDTO);

    List<DicItemVO> toVO(List<DicItemDTO> dicItemDTOS);
}
