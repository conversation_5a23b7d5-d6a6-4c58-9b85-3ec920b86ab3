package com.yhd.admin.api.domain.monitor.ventilation.vo.page;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 通风机整体运行参数
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkVentilationOperationPageVO extends QueryParam {

    private static final long serialVersionUID = 1L;



        /**
         * 关联通风机基本信息表ID
         */
        private Long ventilationId;

        /**
         * 蝶阀状态
         */
        private String valveStatus;

        /**
         * 风速(m/s)
         */
        private BigDecimal windSpeed;
}
