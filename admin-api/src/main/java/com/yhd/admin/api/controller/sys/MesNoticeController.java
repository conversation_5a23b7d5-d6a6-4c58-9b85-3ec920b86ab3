package com.yhd.admin.api.controller.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.controller.BaseController;
import com.yhd.admin.api.domain.sys.convert.MesNoticeConvert;
import com.yhd.admin.api.domain.sys.dto.MesNoticeDTO;
import com.yhd.admin.api.domain.sys.entity.MesNotice;
import com.yhd.admin.api.domain.sys.query.DicItemParam;
import com.yhd.admin.api.domain.sys.query.MesNoticeParam;
import com.yhd.admin.api.domain.sys.vo.MesNoticeVO;
import com.yhd.admin.api.domain.sys.vo.TreeNode;
import com.yhd.admin.api.service.sys.MesNoticeService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息通知表
 */
@RestController
@RequestMapping("/sys/notice")
@Slf4j
public class MesNoticeController extends BaseController<MesNoticeConvert, MesNoticeService> {

    public MesNoticeController(MesNoticeConvert convert, MesNoticeService service) {
        super(convert, service);
    }

    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MesNoticeVO> pagingQuery(@RequestBody MesNoticeParam queryParam) {
        log.debug("{}", queryParam);
        IPage<MesNoticeDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(value = "/getCurrentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson getCurrentDetail(@RequestBody MesNoticeParam param) {
        return RespJson.success(convert.toVO(service.getCurrentDetail(param)));
    }

    @PostMapping(value = "/addOrModify", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson addOrModify(@RequestBody MesNoticeParam param) {
        Boolean retVal = service.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam param) {
        Boolean retVal = service.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(value = "/getNoticeDict", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<TreeNode>> getNoticeDict(@RequestBody DicItemParam queryParam) {
        return RespJson.success(service.getNoticeDictList(queryParam));
    }

    @PostMapping(value = "/getCurrentNoticeCount", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson getCurrentNoticeCount(@RequestBody MesNoticeParam param) {
        Integer count = service.getCurrentNoticeCount(param);
        return RespJson.success(count);
    }

    @PostMapping(value = "/readBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson readBatch(@RequestBody BatchParam param) {
        Boolean retVal = service.readBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(value = {"/readtype/{type}"})
    public RespJson readtype(@PathVariable("type") String type) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Boolean readtype = service.readtype(type, authentication.getName());
        return readtype ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(value = {"/read/{id}"})
    public RespJson read(@PathVariable("id") Long id) {
        MesNotice notify = new MesNotice();
        notify.setId(id);
        notify.setReadState(true);
        Boolean read = service.read(notify);
        return read ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(value = "/message", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MesNoticeVO>> message() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return RespJson.success(service.queryList(authentication.getName()));
    }

}
