package com.yhd.admin.api.service.monitor.nitrogen;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.monitor.nitrogen.dto.TbMkNitrogenParametersDTO;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenParameters;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenParametersCreateVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.page.TbMkNitrogenParametersPageVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.update.TbMkNitrogenParametersUpdateVO;

import java.util.List;

/**
 * 制氮系统参数表 服务类
 *
 * <AUTHOR>
 * @since
 */
public interface TbMkNitrogenParametersService  extends IService<TbMkNitrogenParameters> {

    /**
     * 新建
     *
     * @param createVO 新建VO
     * @return id
     */
    Long create(TbMkNitrogenParametersCreateVO createVO);

    /**
     * 更新
     *
     * @param updateVO 更新VO
     */
    int update(TbMkNitrogenParametersUpdateVO updateVO);

    /**
     * 删除
     *
     * @param id id
     */
    int delete(Long id);

    /**
     * 查询单个
     *
     * @param id id
     * @return po
     */
    TbMkNitrogenParameters get(Long id);

    /**
     * 分页查询
     *
     * @param pageVO 分页VO
     * @return pageResult
     */
    IPage<TbMkNitrogenParametersDTO> page(TbMkNitrogenParametersPageVO pageVO);

    /**
      * 批量删除
      *
      * @param ids 要删除的记录ID列表
      * @return 受影响的行数
      */
    int batchDelete(List<Long> ids);

    /**
     * 批量新增
     *
     * @param createVOs 新增VO列表
     * @return 新增的记录ID列表
     */
    int batchCreate(List<TbMkNitrogenParametersCreateVO> createVOs);

    /**
     * 批量更新
     *
     * @param updateVOs 更新VO列表
     * @return 受影响的行数
     */
    int batchUpdate(List<TbMkNitrogenParametersUpdateVO> updateVOs);
}
