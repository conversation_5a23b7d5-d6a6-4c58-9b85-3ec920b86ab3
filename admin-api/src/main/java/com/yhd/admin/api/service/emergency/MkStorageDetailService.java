package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.emergency.dto.MkStorageDetailDTO;
import com.yhd.admin.api.domain.emergency.entity.MkStorageDetail;
import com.yhd.admin.api.domain.emergency.query.MkStorageDetailParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 应急物资记录表 Service
 * <AUTHOR>
 */
public interface MkStorageDetailService extends IService<MkStorageDetail> {

    /**
     * 分页查询应急物资信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    IPage<MkStorageDetailDTO> pagingQuery(MkStorageDetailParam queryParam);

    /**
     * 新增应急物资信息
     * @param param 应急物资信息参数
     * @return 是否成功
     */
    Boolean add(MkStorageDetailParam param);

    /**
     * 修改应急物资信息
     * @param param 应急物资信息参数
     * @return 是否成功
     */
    Boolean modify(MkStorageDetailParam param);

    /**
     * 获取应急物资详情
     * @param param 查询参数
     * @return 应急物资详情
     */
    MkStorageDetailDTO getCurrentDetail(MkStorageDetailParam param);

    /**
     * 批量删除应急物资信息
     * @param param 批量删除参数
     * @return 是否成功
     */
    Boolean removeBatch(BatchParam param);
}
