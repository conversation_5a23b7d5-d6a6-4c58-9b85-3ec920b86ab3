package com.yhd.admin.api.domain.conduct.query.transfer;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 收发文登记表(MkTransferDispatch)Param
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkTransferDispatchParam extends QueryParam implements Serializable {

    /** 主键列表ids */
    private List<Long> ids;
    /** 收发文id */
    private Long id;
    /** 收发文名称 */
    private String dispatchName;
    /** 类型名称 1：收文 2：发文 */
    private String dispatchType;
    /** 部门名称 */
    private String transferOrgName;
    /** 部门id */
    private Long transferOrgId;
    /** 保存期限名称 1：一年 2：两年 3：5年 */
    private String dispatchDeadlineName;
    /** 保存期限代码 1：一年 2：两年 3：5年 */
    private String dispatchDeadlineCode;
    /** 收发文日期 */
    private LocalDate dispatchDate;
    /** 备注 */
    private String remark;
    /** 正文文件 */
    private List<String> textFile;
    /** 附件文件 */
    private List<String> annexFile;
    /** 审批附件文件 */
    private List<String> approveFile;
    /** 创建人姓名 */
    private String creator;
    /** 开始日期 */
    private LocalDate startDate;
    /** 结束日期 */
    private LocalDate endDate;

}

