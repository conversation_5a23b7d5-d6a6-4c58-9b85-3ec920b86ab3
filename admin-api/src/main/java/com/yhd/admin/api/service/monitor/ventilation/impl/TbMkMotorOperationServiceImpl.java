package com.yhd.admin.api.service.monitor.ventilation.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.ventilation.TbMkMotorOperationDao;
import com.yhd.admin.api.domain.monitor.ventilation.convert.TbMkMotorOperationConvert;
import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkMotorOperationDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkMotorOperation;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkMotorOperationCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.page.TbMkMotorOperationPageVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkMotorOperationUpdateVO;
import com.yhd.admin.api.service.monitor.ventilation.TbMkMotorOperationService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 电机运行参数 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkMotorOperationServiceImpl extends ServiceImpl<TbMkMotorOperationDao, TbMkMotorOperation> implements TbMkMotorOperationService {

    @Resource
    private TbMkMotorOperationConvert tbMkMotorOperationConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkMotorOperationCreateVO createVO) {
        TbMkMotorOperation tbMkMotorOperation = tbMkMotorOperationConvert.convert(createVO);
        baseMapper.insert(tbMkMotorOperation);
        return tbMkMotorOperation.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkMotorOperationUpdateVO updateVO) {
        TbMkMotorOperation tbMkMotorOperation = tbMkMotorOperationConvert.convert(updateVO);
        return baseMapper.updateById(tbMkMotorOperation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkMotorOperation get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkMotorOperationDTO> page(TbMkMotorOperationPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkMotorOperation> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkMotorOperation> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.eq(param.getMotorId() != null, TbMkMotorOperation::getMotorId, param.getMotorId());
        queryChainWrapper.orderByDesc(TbMkMotorOperation::getId);
        return queryChainWrapper.page(iPage).convert(tbMkMotorOperationConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkMotorOperationCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 电机运行参数：传入的列表为空");
            return 0;
        }
        List<TbMkMotorOperation> entities = createVOs.stream()
                .map(tbMkMotorOperationConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 电机运行参数 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkMotorOperationUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 电机运行参数：传入的列表为空");
            return 0;
        }
        List<TbMkMotorOperation> entities = updateVOs.stream()
                .map(tbMkMotorOperationConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 电机运行参数：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 电机运行参数 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
