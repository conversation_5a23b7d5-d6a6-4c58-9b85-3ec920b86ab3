package com.yhd.admin.api.domain.stats.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 人员下井统计表
 *
 * <AUTHOR>
 * @date 2025/7/31 15:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkUserDownholeStatistic extends BaseEntity implements Serializable {

  @TableId(type = IdType.AUTO)
  private Long id;

  /** 监视tag */
  private String monitorTag;
  /** 组织机构编码 */
  private String orgCode;
  /** 组织机构名称 */
  private String orgName;
  /** 部门编码 */
  private String departmentCode;
  /** 部门名称 */
  private String departmentName;
  /** 职务编码 */
  private String postCode;
  /** 职务名称 */
  private String postName;
  /** 工种编码 */
  private String jobCode;
  /** 工种名称 */
  private String jobName;
  /** 姓名 */
  private String realName;
  /** 卡号 */
  private String cardId;
  /** 工号 */
  private String jobId;
  /** 下井时间(yyyy-mm-dd hh:mm:ss) */
  private LocalDateTime downTime;
  /** 出井时间(yyyy-mm-dd hh:mm:ss) */
  private LocalDateTime upTime;
  /** 下井时长 */
  private Long duration;
}
