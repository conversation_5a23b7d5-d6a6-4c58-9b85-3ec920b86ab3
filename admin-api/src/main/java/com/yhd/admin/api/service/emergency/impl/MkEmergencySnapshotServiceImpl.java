package com.yhd.admin.api.service.emergency.impl;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.emergency.MkEmergencySnapshotDao;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencySnapshotConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencySnapshotDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencySnapshot;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySnapshotParam;
import com.yhd.admin.api.service.emergency.MkEmergencySnapshotService;
import com.yhd.admin.common.domain.query.BatchParam;

import lombok.RequiredArgsConstructor;

/**
 * 应急快照服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Service
@RequiredArgsConstructor
public class MkEmergencySnapshotServiceImpl extends ServiceImpl<MkEmergencySnapshotDao, MkEmergencySnapshot> implements MkEmergencySnapshotService {

    private final MkEmergencySnapshotConvert convert;

    @Override
    public IPage<MkEmergencySnapshotDTO> pagingQuery(MkEmergencySnapshotParam param) {
        IPage<MkEmergencySnapshot> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryWrapper<MkEmergencySnapshot> wrapper = new LambdaQueryWrapper<>();

        wrapper.like(StringUtils.isNotBlank(param.getSnapshotName()), MkEmergencySnapshot::getSnapshotName, param.getSnapshotName())
               .eq(StringUtils.isNotBlank(param.getSnapshotType()), MkEmergencySnapshot::getSnapshotType, param.getSnapshotType())
               .eq(param.getMineId() != null, MkEmergencySnapshot::getMineId, param.getMineId())
               .like(StringUtils.isNotBlank(param.getMineName()), MkEmergencySnapshot::getMineName, param.getMineName())
               .eq(StringUtils.isNotBlank(param.getSnapshotStatus()), MkEmergencySnapshot::getSnapshotStatus, param.getSnapshotStatus())
               .ge(param.getSnapshotTimeStart() != null, MkEmergencySnapshot::getSnapshotTime, param.getSnapshotTimeStart())
               .le(param.getSnapshotTimeEnd() != null, MkEmergencySnapshot::getSnapshotTime, param.getSnapshotTimeEnd())
               .ge(param.getSetTimeStart() != null, MkEmergencySnapshot::getSetTime, param.getSetTimeStart())
               .le(param.getSetTimeEnd() != null, MkEmergencySnapshot::getSetTime, param.getSetTimeEnd())
               .ge(param.getBacktrackTimeStart() != null, MkEmergencySnapshot::getBacktrackTime, param.getBacktrackTimeStart())
               .le(param.getBacktrackTimeEnd() != null, MkEmergencySnapshot::getBacktrackTime, param.getBacktrackTimeEnd())
               .orderByDesc(MkEmergencySnapshot::getSnapshotTime);

        return page(page, wrapper).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkEmergencySnapshotParam param) {
        MkEmergencySnapshot entity = convert.toEntity(param);
        entity.setSnapshotTime(LocalDateTime.now());
        entity.setSnapshotStatus("ACTIVE");
        return save(entity);
    }

    @Override
    public Boolean modify(MkEmergencySnapshotParam param) {
        MkEmergencySnapshot entity = convert.toEntity(param);
        return updateById(entity);
    }

    @Override
    public MkEmergencySnapshotDTO getCurrentDetail(MkEmergencySnapshotParam param) {
        if (param.getId() == null) {
            return null;
        }
        MkEmergencySnapshot entity = getById(param.getId());
        return entity != null ? convert.toDTO(entity) : null;
    }

    @Override
    public Boolean removeBatch(BatchParam param) {
        if (param.getId() == null || param.getId().isEmpty()) {
            return false;
        }
        return removeByIds(param.getId());
    }

    @Override
    public List<MkEmergencySnapshotDTO> getSnapshotsByMineId(Long mineId) {
        LambdaQueryWrapper<MkEmergencySnapshot> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkEmergencySnapshot::getMineId, mineId)
               .orderByDesc(MkEmergencySnapshot::getSnapshotTime);
        List<MkEmergencySnapshot> list = list(wrapper);
        return convert.toDTO(list);
    }

    @Override
    public List<MkEmergencySnapshotDTO> getSnapshotsByType(String snapshotType) {
        LambdaQueryWrapper<MkEmergencySnapshot> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkEmergencySnapshot::getSnapshotType, snapshotType)
               .orderByDesc(MkEmergencySnapshot::getSnapshotTime);
        List<MkEmergencySnapshot> list = list(wrapper);
        return convert.toDTO(list);
    }

    @Override
    public MkEmergencySnapshotDTO getLatestSnapshotByMineIdAndType(Long mineId, String snapshotType) {
        LambdaQueryWrapper<MkEmergencySnapshot> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkEmergencySnapshot::getMineId, mineId)
               .eq(MkEmergencySnapshot::getSnapshotType, snapshotType)
               .orderByDesc(MkEmergencySnapshot::getSnapshotTime)
               .last("LIMIT 1");
        MkEmergencySnapshot entity = getOne(wrapper);
        return entity != null ? convert.toDTO(entity) : null;
    }

    @Override
    public MkEmergencySnapshotDTO getLatestSnapshot() {
        LambdaQueryWrapper<MkEmergencySnapshot> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(MkEmergencySnapshot::getSnapshotTime)
               .last("LIMIT 1");
        MkEmergencySnapshot entity = getOne(wrapper);
        return entity != null ? convert.toDTO(entity) : null;
    }

    @Override
    public MkEmergencySnapshotDTO createEmergencySnapshot(Long mineId, String mineName, String description) {
        MkEmergencySnapshot entity = new MkEmergencySnapshot();
        entity.setSnapshotName("应急快照_" + mineName + "_" + LocalDateTime.now().toString().replace(":", "").replace("-", "").substring(0, 15));
        entity.setSnapshotType("EMERGENCY");
        entity.setMineId(mineId);
        entity.setMineName(mineName);
        entity.setSnapshotTime(LocalDateTime.now());
        entity.setSnapshotStatus("ACTIVE");
        entity.setDescription(description);

        save(entity);
        return convert.toDTO(entity);
    }

    @Override
    public List<MkEmergencySnapshotDTO> getTimeList(Long mineId) {
        LambdaQueryWrapper<MkEmergencySnapshot> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(mineId != null, MkEmergencySnapshot::getMineId, mineId)
               .orderByDesc(MkEmergencySnapshot::getSnapshotTime)
               .groupBy(MkEmergencySnapshot::getSnapshotTime);
        List<MkEmergencySnapshot> list = list(wrapper);
        return convert.toDTO(list);
    }
}
