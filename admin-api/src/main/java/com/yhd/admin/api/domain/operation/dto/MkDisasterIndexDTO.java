package com.yhd.admin.api.domain.operation.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.EqualsAndHashCode;

/**
 * 算法模型-自然灾害评价指标表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MkDisasterIndexDTO extends BaseDTO implements Cloneable, Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    private Long id;

    /**
     * 评价级别code
     */
    private String indexCode;

    /**
     * 评价级别name
     */
    private String indexName;

    /**
     * 初始值
     */
    private Integer initialValue;

    /**
     * 最大值
     */
    private Integer maxValue;

    /**
     * 起始分数
     */
    private Integer startNum;

    /**
     * 终止分数
     */
    private Integer endNum;

    /**
     * 权重
     */
    private BigDecimal weight;

    /**
     * 风险等级
     */
    private String level;

}
