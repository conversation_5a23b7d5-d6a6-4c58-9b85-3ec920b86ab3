package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyDrillDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyDrill;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyDrillParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 应急演练记录表 Service
 * <AUTHOR>
 */
public interface MkEmergencyDrillService extends IService<MkEmergencyDrill> {

    /**
     * 分页查询应急演练信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    IPage<MkEmergencyDrillDTO> pagingQuery(MkEmergencyDrillParam queryParam);

    /**
     * 新增应急演练信息
     * @param param 应急演练信息参数
     * @return 是否成功
     */
    Boolean add(MkEmergencyDrillParam param);

    /**
     * 修改应急演练信息
     * @param param 应急演练信息参数
     * @return 是否成功
     */
    Boolean modify(MkEmergencyDrillParam param);

    /**
     * 获取应急演练详情
     * @param param 查询参数
     * @return 应急演练详情
     */
    MkEmergencyDrillDTO getCurrentDetail(MkEmergencyDrillParam param);

    /**
     * 批量删除应急演练信息
     * @param param 批量删除参数
     * @return 是否成功
     */
    Boolean removeBatch(BatchParam param);
}
