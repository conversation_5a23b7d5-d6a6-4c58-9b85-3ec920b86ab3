package com.yhd.admin.api.controller.sys;

import java.security.Principal;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.sys.convert.DicConvert;
import com.yhd.admin.api.domain.sys.convert.DicItemConvert;
import com.yhd.admin.api.domain.sys.dto.DicDTO;
import com.yhd.admin.api.domain.sys.query.DicItemParam;
import com.yhd.admin.api.domain.sys.query.DicParam;
import com.yhd.admin.api.domain.sys.vo.DicVO;
import com.yhd.admin.api.service.sys.DicItemService;
import com.yhd.admin.api.service.sys.DicService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName DicContorller.java @Description TODO 字典项管理
 * @createTime 2020年05月22日 09:49:00
 */
@RestController
@RequestMapping("/dic")
public class DicController {

    private final DicConvert convert;

    private final DicService dicService;

    private final DicItemService dicItemService;
    private final DicItemConvert dicItemConvert;

    public DicController(DicConvert convert, DicService dicService, DicItemService dicItemService,
        DicItemConvert dicItemConvert) {
        this.convert = convert;
        this.dicService = dicService;
        this.dicItemConvert = dicItemConvert;
        this.dicItemService = dicItemService;
    }

    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<DicVO> pagingQuery(@RequestBody DicParam queryParam) {
        IPage<DicDTO> iPage = dicService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(value = "/saveOrUpdate", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson saveOrUpdate(@RequestBody DicParam addParam) {
        dicService.saveOrUpdate(addParam);
        return RespJson.success();
    }

    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody DicParam modifyParam, Principal principal) {
        modifyParam.setUpdatedBy(principal.getName());
        dicService.modifyDic(modifyParam);
        return RespJson.success();
    }

    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam batchParam) {
        dicService.removeBatch(batchParam);
        return RespJson.success();
    }

    @PostMapping(value = "/currentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<DicVO> currentDetail(@RequestBody DicParam queryParam) {
        return RespJson.success(convert.toVO(dicService.currentDetail(queryParam.getId())));
    }

    @PostMapping(value = "/itemSaveOrUpdateBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson itemSaveOrUpdateBatch(@RequestBody DicParam batchParam) {
        dicItemService.saveOrUpdateBatch(batchParam.getItems());
        return RespJson.success();
    }

    @PostMapping(value = "/queryDicItemByDicId", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson queryDicItemByDicId(@RequestBody DicItemParam queryParam) {
        return RespJson.success(dicItemConvert.toVO(dicItemService.queryDicItemByDicId(queryParam)));
    }

    @PostMapping(value = "/removeItemById", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeItemById(@RequestBody DicItemParam removeParam) {
        dicItemService.removeById(removeParam.getId());
        return RespJson.success();
    }

    @PostMapping(value = "/validateIfNotExist", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> validateDicIfNotExist(@RequestBody DicParam validateParam) {
        Boolean ifNotExist = dicService.validateDicIfNotExist(validateParam);
        return RespJson.success(ifNotExist);
    }

    @PostMapping(value = "/toggle", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> toggle(@RequestBody DicParam param) {

        return RespJson.success(dicService.toggle(param.getParams()));
    }
}
