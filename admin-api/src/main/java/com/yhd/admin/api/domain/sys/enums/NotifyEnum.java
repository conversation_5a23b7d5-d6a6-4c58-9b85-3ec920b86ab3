package com.yhd.admin.api.domain.sys.enums;

import lombok.Getter;

@Getter
public enum NotifyEnum {
  NOTIFICATION("notification", "notification"),
  MESSAGE("message", "message"),

  /** 检修提醒 */
  OVERHAUL_REMIND("检修提醒", "检修提醒"),


  /** 三违登记 */
  WT_THREE("三违登记", "三违登记"),
  WT_THREE_REVEIVE("三违登记", "您收到一条三违登记通知，请前往处理！"),
  WT_THREE_REVEIVE_YES("三违登记", "已经接收到三违登记通知！"),
  WT_THREE_REVEIVE_NO("三违登记", "不接收到三违登记通知，请前往处理！"),

  /** 罚款通知 */
  WT_FNM_PUB("罚款通知", "罚款通知---您有待处理的罚款！"),
  WT_FNM_MAKE("罚款通知", "---罚款已接收，请您确认处理情况！"),

  /** 隐患管理 */
  IS_CANCEL("已销号", "未销号"),
  WT_HTM("隐患管理", "隐患管理"),
  WT_HTM_REVEIVE("隐患管理", "您收到一条隐患整改通知，请前往处理！"),
  WT_HTM_DUTY_DONE("隐患管理", "隐患整改已经完成，请您复查整改情况！"),
  WT_HTM_AGAIN_DONE("隐患管理", "隐患整改复查通过，请您销号确认！"),
  WT_HTM_AGAIN_PASS("隐患管理", "您的隐患整改复查通过！"),
  WT_HTM_CANCEL_PASS("隐患管理", "您的隐患整改已销号！"),
  WT_HTM_AGAIN_NO("隐患管理", "您的隐患整改复查节点驳回！"),
  WT_HTM_CANCEL_NO("隐患管理", "您的隐患整改销号节点驳回！"),
  /** 安全培训签到 */
  SAFE_TRAIN_SIGN("安全培训签到通知", "请签到本节培训课程 培训时间：date 培训内容：content"),
  /** 安全考试通知 */
  USER_SAFE_EXAM("安全考试通知", "您有一场安全考试消息，请查收"),
  TRAIN_EVALUATE_EFFECT("培训效果及培训教师打分通知", "请对本次content培训效果及培训教师打分"),
  ;
  private final String key;
  private final String desc;

  NotifyEnum(String key, String desc) {
    this.key = key;
    this.desc = desc;
  }
}
