package com.yhd.admin.api.domain.conduct.dto.meeting;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 会议类型表(MkMeetingType)DTO
 *
 * <AUTHOR>
 * @since 2025-07-28 10:40:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkMeetingTypeDTO extends BaseDTO implements Serializable {
    /** 会议类型id */
    private Long id;
    /** 会议类型名称 */
    private String name;
    /** 会议例会周期名称 0：不定期 1：1天 2：1周 3：1旬 4：1月 */
    private String cycleName;
    /** 会议例会周期代码 0：不定期 1：1天 2：1周 3：1旬 4：1月 */
    private String cycleCode;
    /** 会议类型说明 */
    private String description;
}

