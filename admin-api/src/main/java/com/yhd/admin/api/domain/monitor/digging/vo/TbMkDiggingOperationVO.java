package com.yhd.admin.api.domain.monitor.digging.vo;

import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingOperationCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 掘进机运行参数
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkDiggingOperationVO extends TbMkDiggingOperationCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
