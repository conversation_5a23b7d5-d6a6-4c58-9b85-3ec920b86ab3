package com.yhd.admin.api.domain.monitor.fan.vo.page;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 局扇控制系统
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkLocalFanSystemPageVO extends QueryParam {

    private static final long serialVersionUID = 1L;



        /**
         * 局扇系统名称
         */
        private String systemName;

        /**
         * 安装位置
         */
        private String location;

        /**
         * 备注
         */
        private String remarks;
}
