package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyPlanCategoryConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyPlanCategoryDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyPlanCategoryParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyPlanCategoryVO;
import com.yhd.admin.api.service.emergency.MkEmergencyPlanCategoryService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应急预案分类表 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/emergency/planCategory")
public class MkEmergencyPlanCategoryController {

    @Resource
    private MkEmergencyPlanCategoryService emergencyPlanCategoryService;

    @Resource
    private MkEmergencyPlanCategoryConvert convert;

    /**
     * 分页查询应急预案分类信息
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkEmergencyPlanCategoryVO> pagingQuery(@RequestBody MkEmergencyPlanCategoryParam queryParam) {
        log.info("分页查询应急预案分类信息，参数：{}", queryParam);
        IPage<MkEmergencyPlanCategoryDTO> iPage = emergencyPlanCategoryService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    /**
     * 新增应急预案分类信息
     *
     * @param param 应急预案分类信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkEmergencyPlanCategoryParam param) {
        log.info("新增应急预案分类信息，参数：{}", param);
        Boolean result = emergencyPlanCategoryService.add(param);
        return RespJson.success(result);
    }

    /**
     * 修改应急预案分类信息
     *
     * @param param 应急预案分类信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> modify(@RequestBody MkEmergencyPlanCategoryParam param) {
        log.info("修改应急预案分类信息，参数：{}", param);
        Boolean result = emergencyPlanCategoryService.modify(param);
        return RespJson.success(result);
    }

    /**
     * 获取应急预案分类详情
     *
     * @param param 查询参数
     * @return 应急预案分类详情
     */
    @PostMapping(value = "/getCurrentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkEmergencyPlanCategoryVO> getCurrentDetail(@RequestBody MkEmergencyPlanCategoryParam param) {
        log.info("获取应急预案分类详情，参数：{}", param);
        MkEmergencyPlanCategoryDTO dto = emergencyPlanCategoryService.getCurrentDetail(param);
        return RespJson.success(convert.toVO(dto));
    }

    /**
     * 批量删除应急预案分类信息
     *
     * @param param 批量删除参数
     * @return 操作结果
     */
    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody BatchParam param) {
        log.info("批量删除应急预案分类信息，参数：{}", param);
        Boolean result = emergencyPlanCategoryService.removeBatch(param);
        return RespJson.success(result);
    }

    /**
     * 获取树形分类列表
     *
     * @return 树形分类列表
     */
    @GetMapping(value = "/getCategoryTree", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanCategoryVO>> getCategoryTree() {
        log.info("获取树形分类列表");
        List<MkEmergencyPlanCategoryDTO> dtoList = emergencyPlanCategoryService.getCategoryTree();
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据父级ID获取子分类列表
     *
     * @param parentId 父级ID
     * @return 子分类列表
     */
    @GetMapping(value = "/getCategoriesByParentId", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanCategoryVO>> getCategoriesByParentId(
        @RequestParam(required = false, defaultValue = "0") Integer parentId) {
        log.info("根据父级ID获取子分类列表，父级ID：{}", parentId);
        List<MkEmergencyPlanCategoryDTO> dtoList = emergencyPlanCategoryService.getCategoriesByParentId(parentId);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 获取所有启用的分类列表
     *
     * @return 分类列表
     */
    @GetMapping(value = "/getEnabledCategories", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanCategoryVO>> getEnabledCategories() {
        log.info("获取所有启用的分类列表");
        List<MkEmergencyPlanCategoryDTO> dtoList = emergencyPlanCategoryService.getEnabledCategories();
        return RespJson.success(convert.toVO(dtoList));
    }
}
