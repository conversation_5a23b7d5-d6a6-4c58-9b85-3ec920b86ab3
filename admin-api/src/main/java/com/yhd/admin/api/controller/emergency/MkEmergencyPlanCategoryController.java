package com.yhd.admin.api.controller.emergency;

import com.yhd.admin.api.domain.emergency.convert.MkEmergencyPlanCategoryConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyPlanCategoryDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyPlanCategoryParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyPlanCategoryVO;
import com.yhd.admin.api.service.emergency.MkEmergencyPlanCategoryService;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 应急预案分类表 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/emergency/planCategory")
public class MkEmergencyPlanCategoryController {

    @Resource
    private MkEmergencyPlanCategoryService emergencyPlanCategoryService;

    @Resource
    private MkEmergencyPlanCategoryConvert convert;

    /**
     * 获取树形分类列表
     *
     * @param param 查询参数
     * @return 树形分类列表
     */
    @PostMapping(value = "/getCategoryTree", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanCategoryVO>> getCategoryTree(
            @RequestBody(required = false) MkEmergencyPlanCategoryParam param) {
        log.info("获取树形分类列表，参数：{}", param);
        List<MkEmergencyPlanCategoryDTO> dtoList = emergencyPlanCategoryService.getCategoryTree();
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据父级ID获取子分类列表
     *
     * @param param 查询参数
     * @return 子分类列表
     */
    @PostMapping(value = "/getCategoriesByParentId", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanCategoryVO>> getCategoriesByParentId(
            @RequestBody MkEmergencyPlanCategoryParam param) {
        log.info("根据父级ID获取子分类列表，参数：{}", param);
        // 如果没有传parentId，默认为0（根节点）
        Integer parentId = param.getParentId() != null ? param.getParentId() : 0;
        List<MkEmergencyPlanCategoryDTO> dtoList = emergencyPlanCategoryService.getCategoriesByParentId(parentId);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 获取所有启用的分类列表
     *
     * @param param 查询参数
     * @return 分类列表
     */
    @PostMapping(value = "/getEnabledCategories", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanCategoryVO>> getEnabledCategories(
            @RequestBody(required = false) MkEmergencyPlanCategoryParam param) {
        log.info("获取所有启用的分类列表，参数：{}", param);
        List<MkEmergencyPlanCategoryDTO> dtoList = emergencyPlanCategoryService.getEnabledCategories();
        return RespJson.success(convert.toVO(dtoList));
    }
}
