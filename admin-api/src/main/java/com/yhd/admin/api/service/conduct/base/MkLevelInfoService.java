package com.yhd.admin.api.service.conduct.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.base.MkLevelInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkLevelInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkLevelInfoParam;

import java.util.List;

/**
 * 水平信息表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-25 10:32:52
 */
public interface MkLevelInfoService extends IService<MkLevelInfo> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkLevelInfoDTO> pagingQuery(MkLevelInfoParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkLevelInfoDTO> queryList(MkLevelInfoParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkLevelInfoParam param);

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    MkLevelInfoDTO getCurrentDetails(MkLevelInfoParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkLevelInfoParam param);

}
