package com.yhd.admin.api.domain.sys.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName SysDic.java @Description 字典表
 * @createTime 2020年05月20日 14:11:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysDic extends BaseEntity implements Serializable, Cloneable {
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 字典名称 */
    private String name;
    /** 字典类别 */
    private String category;
    /** 状态 */
    private Boolean status;
    /** 排序 */
    private Long orderNum;
}
