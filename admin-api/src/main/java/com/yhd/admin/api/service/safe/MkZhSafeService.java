package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.safe.dto.MkZhSafeDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhSafe;
import com.yhd.admin.api.domain.safe.query.MkZhSafeParam;


public interface MkZhSafeService extends IService<MkZhSafe> {

    Boolean addOrModify(MkZhSafeParam param);

    Boolean remove(MkZhSafeParam param);

    MkZhSafeDTO getCurrentDetail(MkZhSafeParam param);

    IPage<MkZhSafeDTO> pagingQuery(MkZhSafeParam param);

    MkZhSafeDTO getData();
}
