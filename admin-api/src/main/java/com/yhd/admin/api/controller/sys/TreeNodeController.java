package com.yhd.admin.api.controller.sys;

import com.yhd.admin.api.domain.sys.convert.TreeNodeConvert;
import com.yhd.admin.api.domain.sys.dto.ClientDTO;
import com.yhd.admin.api.domain.sys.dto.MenuDTO;
import com.yhd.admin.api.domain.sys.query.ClientQueryParam;
import com.yhd.admin.api.domain.sys.query.OrgParam;
import com.yhd.admin.api.domain.sys.vo.TreeNode;
import com.yhd.admin.api.service.sys.ClientService;
import com.yhd.admin.api.service.sys.MenuService;
import com.yhd.admin.api.service.sys.OrgService;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName TreeNodeController.java @Description TODO
 * @createTime 2020年05月11日 14:44:00
 */
@RestController
@RequestMapping("/tree")
public class TreeNodeController {

    private final MenuService menuService;

    private final TreeNodeConvert convert;

    private final OrgService orgService;

    private final ClientService clientService;

    public TreeNodeController(MenuService menuService, TreeNodeConvert convert, OrgService orgService,
                              ClientService clientService) {
        this.menuService = menuService;
        this.convert = convert;
        this.orgService = orgService;
        this.clientService = clientService;
    }

    @PostMapping(value = "/menuTree", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson querySubMenuIncludeSelf() {
        // 先获取多少个模块
        List<ClientDTO> clients = clientService.queryList(new ClientQueryParam(), false);
        Map<String, List<MenuDTO>> allMenus = menuService.querySubMenuById(0L).stream()
            .sorted(Comparator.comparing(MenuDTO::getOrderNum)).collect(Collectors.groupingBy(MenuDTO::getClientId));
        List<TreeNode> treeNodes = new ArrayList<>();
        clients.forEach(o -> {
            TreeNode treeNode = new TreeNode();
            treeNode.setTitle(o.getClientName());
            treeNode.setKey(o.getClientId());
            treeNode.setCheckable(Boolean.FALSE);
            treeNode.setSelectable(Boolean.FALSE);
            if (allMenus.containsKey(o.getClientId())) {
                treeNode.setChildren(convert.toTreeNode(allMenus.get(o.getClientId())));
                treeNode.setIsLeaf(Boolean.FALSE);
            } else {
                treeNode.setIsLeaf(Boolean.TRUE);
            }

            treeNodes.add(treeNode);
        });

        return RespJson.success(treeNodes);
    }

    @PostMapping(value = "/orgTree", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson queryOrgTree(@RequestBody OrgParam orgParam) {
        if (orgParam.getId() == null) {
            orgParam.setId(1L);
        }
        return RespJson.success(convert.orgToTreeNode(orgService.queryList(orgParam.getId())));
    }
}
