package com.yhd.admin.api.domain.conduct.convert.base;

import com.yhd.admin.api.domain.conduct.dto.base.MkLevelInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkLevelInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkLevelInfoParam;
import com.yhd.admin.api.domain.conduct.vo.base.MkLevelInfoVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 水平信息表(MkLevelInfo)Convert
 *
 * <AUTHOR>
 * @since 2025-07-25 10:32:52
 */
@Mapper(componentModel = "spring")
public interface MkLevelInfoConvert {
    MkLevelInfo toEntity(MkLevelInfoParam param);

    MkLevelInfoDTO toDTO(MkLevelInfo entity);

    MkLevelInfoVO toVO(MkLevelInfoDTO dto);

    List<MkLevelInfoDTO> toDTOList(List<MkLevelInfo> entityList);

    List<MkLevelInfoVO> toVOList(List<MkLevelInfoDTO> dtoList);
}

