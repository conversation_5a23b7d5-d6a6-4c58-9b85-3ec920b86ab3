package com.yhd.admin.api.domain.monitor.digging.vo.create;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 掘进机运行参数
 *
 * <AUTHOR>
 * @since
 */
@Data
public class TbMkDiggingOperationCreateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联掘进机基本信息表ID
     */
    private Long diggingId;

    /**
     * 运行开始时间
     */
    private LocalDateTime runningStartTime;

    /**
     * 油泵电流(A)
     */
    private BigDecimal oilPumpCurrent;

    /**
     * 二运电流(A)
     */
    private BigDecimal secondaryTransportCurrent;

    /**
     * 高速电流(A)
     */
    private BigDecimal highSpeedCurrent;

    /**
     * 运行速度(m/s)
     */
    private BigDecimal operationSpeed;

    /**
     * 低速电机转速(rpm)
     */
    private BigDecimal lowSpeedMotorSpeed;

    /**
     * 高速电机转速(rpm)
     */
    private BigDecimal highSpeedMotorSpeed;

    /**
     * 二运电机转速(rpm)
     */
    private BigDecimal secondaryTransportMotorSpeed;

    /**
     * 自动截割深度(m)
     */
    private BigDecimal automaticCuttingDepth;

    /**
     * 自动截割速度(m/min)
     */
    private BigDecimal automaticCuttingSpeed;

    /**
     * 油泵流量(L/min)
     */
    private BigDecimal oilPumpFlowRate;

    /**
     * 油泵压力(bar)
     */
    private BigDecimal oilPumpPressure;

    /**
     * 油泵转速(RPM)
     */
    private Integer oilPumpRpm;

}
