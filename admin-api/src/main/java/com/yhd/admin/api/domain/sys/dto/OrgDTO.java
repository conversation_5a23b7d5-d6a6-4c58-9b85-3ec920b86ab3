package com.yhd.admin.api.domain.sys.dto;

import java.util.List;

import com.yhd.admin.common.domain.dto.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = false)
@Data
public class OrgDTO extends BaseDTO {
    private Long id;

    /** 组织名称 */
    private String orgName;
    /** 父级ID */
    private Long parentId;
    /** 父级名称 */
    private String parentName;
    /** 状态 */
    private Boolean status;
    /** 排序 */
    private Long sortNum;

    /*
     * 子节点
     */
    private List<OrgDTO> children;
}
