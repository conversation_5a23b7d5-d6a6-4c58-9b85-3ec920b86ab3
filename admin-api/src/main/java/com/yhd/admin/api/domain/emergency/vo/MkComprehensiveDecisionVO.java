package com.yhd.admin.api.domain.emergency.vo;

import lombok.Data;
import java.util.List;

/**
 * 综合决策大屏数据VO
 *
 * <AUTHOR>
 */
@Data
public class MkComprehensiveDecisionVO {

    /**
     * 生产统计数据
     */
    private MkProductionStatsVO productionStats;

    /**
     * 传感器统计数据
     */
    private MkSensorStatsVO sensorStats;

    /**
     * 风险管控统计数据
     */
    private MkRiskControlStatsVO riskControlStats;

    /**
     * 隐患管控统计数据
     */
    private MkHazardControlStatsVO hazardControlStats;

    /**
     * 人员统计数据
     */
    private MkPersonnelStatsVO personnelStats;

    /**
     * 智能工作面统计数据
     */
    private MkIntelligentWorkfaceStatsVO intelligentWorkfaceStats;

    /**
     * 水文监测统计数据
     */
    private MkHydrologyMonitorStatsVO hydrologyMonitorStats;

    /**
     * 设备统计数据
     */
    private MkEquipmentStatsVO equipmentStats;

    /**
     * 能耗统计数据列表
     */
    private List<MkEnergyConsumptionStatsVO> energyConsumptionStats;
}