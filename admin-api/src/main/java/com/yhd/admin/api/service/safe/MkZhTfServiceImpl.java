package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.safe.MkZhTfDao;
import com.yhd.admin.api.domain.safe.convert.MkZhTfConvert;
import com.yhd.admin.api.domain.safe.dto.MkZhTfDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhTf;
import com.yhd.admin.api.domain.safe.query.MkZhTfParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;


@Service
public class MkZhTfServiceImpl extends ServiceImpl<MkZhTfDao, MkZhTf> implements MkZhTfService {

    private final MkZhTfConvert convert;

    public MkZhTfServiceImpl(MkZhTfConvert convert) {
        this.convert = convert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addOrModify(MkZhTfParam param) {
        MkZhTf entity = convert.toEntity(param);
        return super.saveOrUpdate(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(MkZhTfParam param) {
        return super.removeById(param.getId());
    }

    @Override
    public MkZhTfDTO getCurrentDetail(MkZhTfParam param) {
        MkZhTfDTO result;
        if (Objects.isNull(param.getId())) {
            throw new BMSException(ExceptionEnum.MISS_PARAM_EXCEPTION);
        }
        MkZhTf entity = super.getById(param);
        result = convert.toDTO(entity);
        return result;
    }

    @Override
    public IPage<MkZhTfDTO> pagingQuery(MkZhTfParam param) {
        IPage<MkZhTf> page = new Page<>(param.getCurrent(), param.getPageSize());
        LambdaQueryChainWrapper<MkZhTf> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        // 排序
        wrapper.orderByAsc(MkZhTf::getUpdatedTime);
        IPage<MkZhTf> iPage = wrapper.page(page);
        return iPage.convert(convert::toDTO);
    }

    @Override
    public MkZhTfDTO getData() {
        LambdaQueryChainWrapper<MkZhTf> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        return convert.toDTO(wrapper.list().get(0));
    }
}
