package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.emergency.MkEmergencyPlanDao;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyPlanConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyPlanDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyPlan;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyPlanParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 应急救援数字预案表 Service实现类
 * <AUTHOR>
 */
@Service
public class MkEmergencyPlanServiceImpl extends ServiceImpl<MkEmergencyPlanDao, MkEmergencyPlan>
        implements MkEmergencyPlanService {

    @Resource
    private MkEmergencyPlanConvert convert;

    @Override
    public IPage<MkEmergencyPlanDTO> pagingQuery(MkEmergencyPlanParam param) {
        IPage<MkEmergencyPlan> iPage = new Page<>(param.getCurrent(), param.getPageSize());

        LambdaQueryChainWrapper<MkEmergencyPlan> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);

        // 组织编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getOrgCode()),
                MkEmergencyPlan::getOrgCode, param.getOrgCode());

        // 预案编码精确查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getPlanCode()),
                MkEmergencyPlan::getPlanCode, param.getPlanCode());

        // 预案名称模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getPlanName()),
                MkEmergencyPlan::getPlanName, param.getPlanName());

        // 预案类型查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getPlanType()),
                MkEmergencyPlan::getPlanType, param.getPlanType());

        // 分类ID精确查询
        queryChainWrapper.eq(param.getCategoryId() != null,
                MkEmergencyPlan::getCategoryId, param.getCategoryId());

        // 预案级别查询
        queryChainWrapper.eq(StringUtils.isNotBlank(param.getPlanLevel()),
                MkEmergencyPlan::getPlanLevel, param.getPlanLevel());

        // 预案状态查询
        queryChainWrapper.eq(param.getPlanStatus() != null,
                MkEmergencyPlan::getPlanStatus, param.getPlanStatus());

        // 责任人模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getResponsiblePerson()),
                MkEmergencyPlan::getResponsiblePerson, param.getResponsiblePerson());

        // 责任部门模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getResponsibleDepartment()),
                MkEmergencyPlan::getResponsibleDepartment, param.getResponsibleDepartment());

        // 关键词模糊查询
        queryChainWrapper.like(StringUtils.isNotBlank(param.getKeywords()),
                MkEmergencyPlan::getKeywords, param.getKeywords());

        // 生效日期范围查询
        if (param.getEffectiveDateBegin() != null) {
            queryChainWrapper.ge(MkEmergencyPlan::getEffectiveDate,
                    param.getEffectiveDateBegin().atStartOfDay());
        }
        if (param.getEffectiveDateEnd() != null) {
            queryChainWrapper.le(MkEmergencyPlan::getEffectiveDate,
                    param.getEffectiveDateEnd().atTime(23, 59, 59));
        }

        // 失效日期范围查询
        if (param.getExpiryDateBegin() != null) {
            queryChainWrapper.ge(MkEmergencyPlan::getExpiryDate,
                    param.getExpiryDateBegin().atStartOfDay());
        }
        if (param.getExpiryDateEnd() != null) {
            queryChainWrapper.le(MkEmergencyPlan::getExpiryDate,
                    param.getExpiryDateEnd().atTime(23, 59, 59));
        }

        // 排序：按排序号升序，创建时间倒序
        queryChainWrapper.orderByAsc(MkEmergencyPlan::getSortOrder)
                         .orderByDesc(MkEmergencyPlan::getCreatedTime);

        return queryChainWrapper.page(iPage).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkEmergencyPlanParam param) {
        // 必填字段校验
        if (StringUtils.isBlank(param.getPlanCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getPlanName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getPlanType())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        // 设置默认值
        if (param.getPlanStatus() == null) {
            param.setPlanStatus(1); // 默认为草稿状态
        }
        if (StringUtils.isBlank(param.getVersion())) {
            param.setVersion("1.0");
        }
        if (param.getReviewCycle() == null) {
            param.setReviewCycle(12); // 默认12个月评审周期
        }

        return super.save(convert.toEntity(param));
    }

    @Override
    public Boolean modify(MkEmergencyPlanParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        // 必填字段校验
        if (StringUtils.isBlank(param.getPlanCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getPlanName())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getPlanType())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        if (StringUtils.isBlank(param.getOrgCode())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        return super.updateById(convert.toEntity(param));
    }

    @Override
    public MkEmergencyPlanDTO getCurrentDetail(MkEmergencyPlanParam param) {
        if (param.getId() == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        MkEmergencyPlan entity = super.getById(param.getId());
        return convert.toDTO(entity);
    }

    @Override
    public Boolean removeBatch(BatchParam param) {
        if (param.getId() == null || param.getId().isEmpty()) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        return super.removeByIds(param.getId());
    }

    @Override
    public List<MkEmergencyPlanDTO> getPlansByType(String planType) {
        if (StringUtils.isBlank(planType)) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        List<MkEmergencyPlan> entityList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(MkEmergencyPlan::getPlanType, planType)
                .eq(MkEmergencyPlan::getPlanStatus, 3) // 已发布状态
                .orderByAsc(MkEmergencyPlan::getSortOrder)
                .list();

        return convert.toDTO(entityList);
    }

    @Override
    public Boolean publishPlan(Integer planId) {
        if (planId == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        MkEmergencyPlan plan = super.getById(planId);
        if (plan == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        plan.setPlanStatus(3); // 设置为已发布状态
        plan.setEffectiveDate(LocalDateTime.now());

        return super.updateById(plan);
    }

    @Override
    public Boolean abolishPlan(Integer planId) {
        if (planId == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        MkEmergencyPlan plan = super.getById(planId);
        if (plan == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        plan.setPlanStatus(4); // 设置为已废止状态
        plan.setExpiryDate(LocalDateTime.now());

        return super.updateById(plan);
    }

    @Override
    public List<MkEmergencyPlanDTO> getExpiringSoonPlans(String orgCode) {
        if (StringUtils.isBlank(orgCode)) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime thirtyDaysLater = now.plusDays(30);

        List<MkEmergencyPlan> entityList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(MkEmergencyPlan::getOrgCode, orgCode)
                .eq(MkEmergencyPlan::getPlanStatus, 3) // 已发布状态
                .isNotNull(MkEmergencyPlan::getExpiryDate)
                .between(MkEmergencyPlan::getExpiryDate, now, thirtyDaysLater)
                .orderByAsc(MkEmergencyPlan::getExpiryDate)
                .list();

        return convert.toDTO(entityList);
    }

    @Override
    public List<MkEmergencyPlanDTO> getNeedsDrillPlans(String orgCode) {
        if (StringUtils.isBlank(orgCode)) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        // 这里简化处理，实际应该根据演练频次计算
        List<MkEmergencyPlan> entityList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(MkEmergencyPlan::getOrgCode, orgCode)
                .eq(MkEmergencyPlan::getPlanStatus, 3) // 已发布状态
                .orderByAsc(MkEmergencyPlan::getLastDrillDate)
                .list();

        return convert.toDTO(entityList);
    }

    @Override
    public List<MkEmergencyPlanDTO> getNeedsReviewPlans(String orgCode) {
        if (StringUtils.isBlank(orgCode)) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        // 这里简化处理，实际应该根据评审周期计算
        List<MkEmergencyPlan> entityList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(MkEmergencyPlan::getOrgCode, orgCode)
                .eq(MkEmergencyPlan::getPlanStatus, 3) // 已发布状态
                .orderByAsc(MkEmergencyPlan::getLastReviewDate)
                .list();

        return convert.toDTO(entityList);
    }

    @Override
    public List<MkEmergencyPlanDTO> getPlansByCategoryId(Integer categoryId) {
        if (categoryId == null) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }

        List<MkEmergencyPlan> entityList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(MkEmergencyPlan::getCategoryId, categoryId)
                .orderByAsc(MkEmergencyPlan::getSortOrder)
                .orderByAsc(MkEmergencyPlan::getId)
                .list();

        return convert.toDTO(entityList);
    }
}
