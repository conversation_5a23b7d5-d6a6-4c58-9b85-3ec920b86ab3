package com.yhd.admin.api.domain.sys.enums;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName UserAccountEnum.java @Description TODO
 * @createTime 2020年04月19日 10:08:00
 */
public enum APIRedisKeyEnum {
    /** KEY为用户名 */
    USER_MENU("API:USER:MENU:%1$s:%2$s", "用户菜单缓存KEY"),
    /** KEY为登录账号名称 */
    USER("API:USER:%1$s", "用户REDIS 缓存 KEY，"),

    /** KEY为角色ID */
    ROLE("API:ROLE:%1$s", "角色缓存信息"),

    /** 字典项 */
    DIC("API:DIC:ALL", "字典项全部缓存"),

    INSPECTION_PLAN("API:INSPECTION:PLAN:%1$d", "巡检计划KEY"),

    INSPECTION_PLAN_LOCK_KEY("API:INSPECTION:PLAN:LOCK:%1$s", "巡检计划加锁"),

    /**
     * 检修管理
     */
    OVERHAUL_ORDER("API:OVERHAUL:ORDER:%1$s", "检修单轮询key");

    private final String key;
    private final String desc;

    APIRedisKeyEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
}
