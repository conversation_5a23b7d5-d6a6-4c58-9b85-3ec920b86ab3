package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkOutputDutyDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputDuty;
import com.yhd.admin.api.domain.operation.query.MkOutputDutyParam;
import com.yhd.admin.api.domain.operation.vo.MkOutputDutyVO;
import org.mapstruct.Mapper;


/**
* 生产录入-值班人员表
*
* <AUTHOR>
* @since 1.0.0 2025-07-30
*/
@Mapper(componentModel = "spring")
public interface MkOutputDutyConvert {

    MkOutputDuty toEntity(MkOutputDutyParam param);

    MkOutputDutyVO toVO(MkOutputDutyDTO dto);

    MkOutputDutyDTO toDTO(MkOutputDuty entity);

}
