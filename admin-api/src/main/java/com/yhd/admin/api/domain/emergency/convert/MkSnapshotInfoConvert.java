package com.yhd.admin.api.domain.emergency.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.yhd.admin.api.domain.emergency.dto.MkSnapshotInfoDTO;
import com.yhd.admin.api.domain.emergency.entity.MkSnapshotInfo;
import com.yhd.admin.api.domain.emergency.query.MkSnapshotInfoParam;
import com.yhd.admin.api.domain.emergency.vo.MkSnapshotInfoVO;

/**
 * 快照信息转换器
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Mapper(componentModel = "spring")
public interface MkSnapshotInfoConvert {

    /**
     * 实体转DTO
     *
     * @param entity 实体
     * @return DTO
     */
    MkSnapshotInfoDTO toDTO(MkSnapshotInfo entity);

    /**
     * 实体列表转DTO列表
     *
     * @param entityList 实体列表
     * @return DTO列表
     */
    List<MkSnapshotInfoDTO> toDTO(List<MkSnapshotInfo> entityList);

    /**
     * DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    MkSnapshotInfo toEntity(MkSnapshotInfoDTO dto);

    /**
     * 参数转实体
     *
     * @param param 参数
     * @return 实体
     */
    MkSnapshotInfo toEntity(MkSnapshotInfoParam param);

    /**
     * DTO转VO
     *
     * @param dto DTO
     * @return VO
     */
    MkSnapshotInfoVO toVO(MkSnapshotInfoDTO dto);

    /**
     * DTO列表转VO列表
     *
     * @param dtoList DTO列表
     * @return VO列表
     */
    List<MkSnapshotInfoVO> toVO(List<MkSnapshotInfoDTO> dtoList);
}
