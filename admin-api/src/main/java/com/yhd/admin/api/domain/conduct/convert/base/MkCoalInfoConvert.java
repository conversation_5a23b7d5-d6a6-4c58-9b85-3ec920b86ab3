package com.yhd.admin.api.domain.conduct.convert.base;

import com.yhd.admin.api.domain.conduct.dto.base.MkCoalInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkCoalInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkCoalInfoParam;
import com.yhd.admin.api.domain.conduct.vo.base.MkCoalInfoVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 煤层信息(MkCoalInfo)Convert
 *
 * <AUTHOR>
 * @since 2025-07-25 10:32:52
 */
@Mapper(componentModel = "spring")
public interface MkCoalInfoConvert {
    MkCoalInfo toEntity(MkCoalInfoParam param);

    MkCoalInfoDTO toDTO(MkCoalInfo entity);

    MkCoalInfoVO toVO(MkCoalInfoDTO dto);

    List<MkCoalInfoDTO> toDTOList(List<MkCoalInfo> entityList);

    List<MkCoalInfoVO> toVOList(List<MkCoalInfoDTO> dtoList);
}

