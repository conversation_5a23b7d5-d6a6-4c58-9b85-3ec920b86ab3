package com.yhd.admin.api.service.da;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.da.MkReportDayElectricDao;
import com.yhd.admin.api.domain.da.convert.MkReportDayElectricConvert;
import com.yhd.admin.api.domain.da.dto.MkReportDayElectricDTO;
import com.yhd.admin.api.domain.da.entity.MkReportDayElectric;
import com.yhd.admin.api.domain.da.query.MkReportDayElectricParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.common.utils.DateUtil;
import com.yhd.admin.common.utils.NumberUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 电度能耗分析-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2025/8/1 14:53
 */
@Service
public class MkReportDayElectricServiceImpl
    extends ServiceImpl<MkReportDayElectricDao, MkReportDayElectric>
    implements MkReportDayElectricService {

  private final MkReportDayElectricConvert convert;

  public MkReportDayElectricServiceImpl(MkReportDayElectricConvert convert) {
    this.convert = convert;
  }

  /**
   * 根据条件查询列表
   *
   * @param param 参数
   * @return 电度能耗列表
   */
  @Override
  public List<MkReportDayElectricDTO> queryList(MkReportDayElectricParam param) {
    LambdaQueryWrapper<MkReportDayElectric> wrapper = new LambdaQueryWrapper<>();
    // 日期范围(start <= xx <= end)
    if (Objects.nonNull(param.getStartDate())) {
      wrapper.ge(MkReportDayElectric::getStatsDate, param.getStartDate());
    }
    if (Objects.nonNull(param.getEndDate())) {
      wrapper.le(MkReportDayElectric::getStatsDate, param.getEndDate());
    }
    wrapper.orderByAsc(MkReportDayElectric::getStatsDate);

    List<MkReportDayElectric> selectList = baseMapper.selectList(wrapper);

    return selectList.stream().map(convert::toDTO).collect(Collectors.toList());
  }

  /**
   * 峰平谷累计用量占比(日)统计
   *
   * @param param 参数
   * @return 占比(日)统计数据
   */
  @Override
  public MkReportDayElectricDTO dayElectricRatioCount(MkReportDayElectricParam param) {
    // 查询日期范围日电度能耗列表
    List<MkReportDayElectricDTO> queryList = this.queryList(param);

    return electricRatioCount(queryList);
  }

  /**
   * 峰平谷累计用量占比(月)统计
   *
   * @param param 参数
   * @return 占比(月)统计数据
   */
  @Override
  public MkReportDayElectricDTO monthElectricRatioCount(MkReportDayElectricParam param) {
    if (StringUtils.isBlank(param.getMonth())) {
      throw new BMSException("error", "请传入正确参数");
    }
    // 获取月开始时间，结束时间
    LocalDate startDate = LocalDate.parse(DateUtil.getMonthBeginDate(param.getMonth()));
    param.setStartDate(startDate);
    LocalDate endDate = LocalDate.parse(DateUtil.getMonthEndDate(param.getMonth()));
    param.setEndDate(endDate);
    List<MkReportDayElectricDTO> queryList = this.queryList(param);

    return electricRatioCount(queryList);
  }

  /**
   * 峰平谷累计用量占比(年)统计
   *
   * @param param 参数
   * @return 占比(年)统计数据
   */
  @Override
  public MkReportDayElectricDTO yearElectricRatioCount(MkReportDayElectricParam param) {
    if (StringUtils.isBlank(param.getYear())) {
      throw new BMSException("error", "请传入正确参数");
    }
    // 获取年开始时间，结束时间
    LocalDate startDate = LocalDate.parse(DateUtil.getYearFirst(Integer.parseInt(param.getYear())));
    param.setStartDate(startDate);
    LocalDate endDate = LocalDate.parse(DateUtil.getYearLast(Integer.parseInt(param.getYear())));
    param.setEndDate(endDate);
    List<MkReportDayElectricDTO> queryList = this.queryList(param);

    return electricRatioCount(queryList);
  }

  /**
   * 日期范围电度能耗统计
   *
   * @param param 参数
   * @return 日电度能耗列表
   */
  @Override
  public List<MkReportDayElectricDTO> dayElectricCountList(MkReportDayElectricParam param) {
    List<MkReportDayElectricDTO> result = Lists.newArrayList();
    // 获取日期范围列表
    List<LocalDate> dateList =
        DateUtil.dateBetweenToLocalDate(param.getStartDate(), param.getEndDate());
    dateList.forEach(
        date -> {
          MkReportDayElectricDTO dto = new MkReportDayElectricDTO();
          dto.setStatsDate(date);
          dto.setTotalValue(0d);
        });

    // 查询日期范围日电度能耗列表
    List<MkReportDayElectricDTO> queryList = this.queryList(param);
    if (CollectionUtils.isNotEmpty(queryList)) {
      // 每天峰平谷累计
      queryList.forEach(
          data ->
              data.setTotalValue(
                  NumberUtil.add(
                      data.getValleyValue() == null ? 0d : data.getValleyValue(),
                      data.getNormalValue() == null ? 0d : data.getNormalValue(),
                      data.getPeakValue() == null ? 0d : data.getPeakValue())));
      // 根据统计日期进行分组
      Map<LocalDate, List<MkReportDayElectricDTO>> listMap =
          queryList.stream().collect(Collectors.groupingBy(MkReportDayElectricDTO::getStatsDate));

      result.forEach(
          v -> {
            List<MkReportDayElectricDTO> electricList = listMap.get(v.getStatsDate());
            if (CollectionUtils.isNotEmpty(electricList)) {
              double sumTotalValue =
                  electricList.stream().mapToDouble(MkReportDayElectricDTO::getTotalValue).sum();
              v.setTotalValue(sumTotalValue);
            }
          });
    }

    return result;
  }

  /**
   * 月范围电度能耗统计
   *
   * @param param 参数
   * @return 月电度能耗列表
   */
  @Override
  public List<MkReportDayElectricDTO> monthElectricCountList(MkReportDayElectricParam param) {
    List<MkReportDayElectricDTO> result = Lists.newArrayList();
    // 获取月范围列表
    List<String> monthList = DateUtil.dateMonthBetween(param.getStartMonth(), param.getEndMonth());
    monthList.forEach(
        month -> {
          MkReportDayElectricDTO dto = new MkReportDayElectricDTO();
          dto.setMonth(month);
          double totalValue = 0d;
          MkReportDayElectricParam queryParam = new MkReportDayElectricParam();
          // 获取月开始结束时间
          LocalDate startDate = LocalDate.parse(DateUtil.getMonthBeginDate(month));
          LocalDate endDate = LocalDate.parse(DateUtil.getMonthEndDate(month));
          queryParam.setStartDate(startDate);
          queryParam.setEndDate(endDate);
          List<MkReportDayElectricDTO> queryList = this.queryList(queryParam);
          if (CollectionUtils.isNotEmpty(queryList)) {
            double sumNormalValue =
                queryList.stream()
                    .mapToDouble(v -> v.getNormalValue() == null ? 0d : v.getNormalValue())
                    .sum();
            double sumValleyValue =
                queryList.stream()
                    .mapToDouble(v -> v.getValleyValue() == null ? 0d : v.getValleyValue())
                    .sum();
            double sumPeakValue =
                queryList.stream()
                    .mapToDouble(v -> v.getPeakValue() == null ? 0d : v.getPeakValue())
                    .sum();

            totalValue = NumberUtil.add(sumNormalValue, sumValleyValue, sumPeakValue);
          }
          dto.setTotalValue(totalValue);

          result.add(dto);
        });

    return result;
  }

  /**
   * 年电度能耗统计
   *
   * @param param 参数
   * @return 月电度能耗列表
   */
  @Override
  public List<MkReportDayElectricDTO> yearElectricCountList(MkReportDayElectricParam param) {
    List<MkReportDayElectricDTO> result = Lists.newArrayList();
    // 获取年份范围列表
    List<String> yearList = Lists.newArrayList();
    for (int i = Integer.parseInt(param.getStartYear());
        i <= Integer.parseInt(param.getEndYear());
        i++) {
      yearList.add(i + "");
    }
    yearList.forEach(
        year -> {
          MkReportDayElectricDTO dto = new MkReportDayElectricDTO();
          dto.setYear(year);
          double totalValue = 0d;
          MkReportDayElectricParam queryParam = new MkReportDayElectricParam();
          // 获取年开始结束时间
          LocalDate startDate = LocalDate.parse(DateUtil.getYearFirst(Integer.parseInt(year)));
          LocalDate endDate = LocalDate.parse(DateUtil.getYearLast(Integer.parseInt(year)));
          queryParam.setStartDate(startDate);
          queryParam.setEndDate(endDate);
          List<MkReportDayElectricDTO> queryList = this.queryList(queryParam);
          if (CollectionUtils.isNotEmpty(queryList)) {
            double sumNormalValue =
                queryList.stream()
                    .mapToDouble(v -> v.getNormalValue() == null ? 0d : v.getNormalValue())
                    .sum();
            double sumValleyValue =
                queryList.stream()
                    .mapToDouble(v -> v.getValleyValue() == null ? 0d : v.getValleyValue())
                    .sum();
            double sumPeakValue =
                queryList.stream()
                    .mapToDouble(v -> v.getPeakValue() == null ? 0d : v.getPeakValue())
                    .sum();

            totalValue = NumberUtil.add(sumNormalValue, sumValleyValue, sumPeakValue);
          }
          dto.setTotalValue(totalValue);

          result.add(dto);
        });

    return result;
  }

  /**
   * 峰平谷累计用量占比统计
   *
   * @param dataList 数据列表
   * @return 峰平谷累计用量占比统计数据
   */
  private MkReportDayElectricDTO electricRatioCount(List<MkReportDayElectricDTO> dataList) {
    MkReportDayElectricDTO result = new MkReportDayElectricDTO();
    double sumPeakValue = 0d;
    double sumValleyValue = 0d;
    double sumNormalValue = 0d;
    if (CollectionUtils.isNotEmpty(dataList)) {
      // 峰用电统计
      sumPeakValue =
          dataList.stream()
              .mapToDouble(v -> null == v.getPeakValue() ? 0d : v.getPeakValue())
              .sum();
      // 谷用电统计
      sumValleyValue =
          dataList.stream()
              .mapToDouble(v -> null == v.getValleyValue() ? 0d : v.getValleyValue())
              .sum();
      // 平用电统计
      sumNormalValue =
          dataList.stream()
              .mapToDouble(v -> null == v.getNormalValue() ? 0d : v.getNormalValue())
              .sum();
    }
    result.setPeakValue(sumPeakValue);
    result.setValleyValue(sumValleyValue);
    result.setNormalValue(sumNormalValue);

    return result;
  }
}
