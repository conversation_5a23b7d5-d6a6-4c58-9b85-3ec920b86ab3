package com.yhd.admin.api.domain.monitor.digging.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 掘进机综合信息数据传输对象
 * 用于封装掘进机基本信息及其最新的运行、导航、环境参数，供前端展示。
 */
@Data
public class TbMkDiggingComprehensiveDTO {

    /**
     * 掘进机ID
     */
    private Long diggingId;

    /**
     * 设备编号
     */
    private String deviceNumber;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 运行开始时间
     */
    private LocalDateTime runningStartTime;

    /**
     * 油泵电流(A)
     */
    private BigDecimal oilPumpCurrent;

    /**
     * 二运电流(A)
     */
    private BigDecimal secondaryTransportCurrent;

    /**
     * 高速电流(A)
     */
    private BigDecimal highSpeedCurrent;

    /**
     * 运行速度(m/s)
     */
    private BigDecimal operationSpeed;

    /**
     * 低速电机转速(rpm)
     */
    private BigDecimal lowSpeedMotorSpeed;

    /**
     * 高速电机转速(rpm)
     */
    private BigDecimal highSpeedMotorSpeed;

    /**
     * 二运电机转速(rpm)
     */
    private BigDecimal secondaryTransportMotorSpeed;

    /**
     * 自动截割深度(m)
     */
    private BigDecimal automaticCuttingDepth;

    /**
     * 自动截割速度(m/min)
     */
    private BigDecimal automaticCuttingSpeed;

    /**
     * 油泵流量(L/min)
     */
    private BigDecimal oilPumpFlowRate;

    /**
     * 油泵压力(bar)
     */
    private BigDecimal oilPumpPressure;

    /**
     * 油泵转速(RPM)
     */
    private Integer oilPumpRpm;

    /**
     * 俯仰角(°)
     */
    private BigDecimal pitchAngle;

    /**
     * 翻滚角(°)
     */
    private BigDecimal rollAngle;

    /**
     * 航向角(°)
     */
    private BigDecimal yawAngle;

    /**
     * 人员数量
     */
    private Integer personnelCount;

    /**
     * 粉尘浓度(mg/m³)
     */
    private BigDecimal dustConcentration;

    /**
     * 瓦斯浓度(ppm)
     */
    private BigDecimal methaneConcentration;

    /**
     * 温度(℃)
     */
    private BigDecimal temperature;

    /**
     * 风筒风速(m/s)
     */
    private BigDecimal windSpeed;

}
