package com.yhd.admin.api.controller.conduct.transfer;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.transfer.MkTransferDispatchConvert;
import com.yhd.admin.api.domain.conduct.dto.transfer.MkTransferDispatchDTO;
import com.yhd.admin.api.domain.conduct.query.transfer.MkTransferDispatchParam;
import com.yhd.admin.api.domain.conduct.vo.transfer.MkTransferDispatchVO;
import com.yhd.admin.api.service.conduct.transfer.MkTransferDispatchService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 收发文登记表控制层
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:02
 */
@RestController
@RequestMapping("/conduct/transfer/dispatch")
public class MkTransferDispatchController {

    private final MkTransferDispatchService mkTransferDispatchService;
    private final MkTransferDispatchConvert mkTransferDispatchConvert;

    public MkTransferDispatchController(
        MkTransferDispatchService mkTransferDispatchService,
        MkTransferDispatchConvert mkTransferDispatchConvert
    ) {
        this.mkTransferDispatchService = mkTransferDispatchService;
        this.mkTransferDispatchConvert = mkTransferDispatchConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkTransferDispatchParam param) {
        IPage<MkTransferDispatchDTO> page = mkTransferDispatchService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkTransferDispatchConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkTransferDispatchParam param) {
        return RespJson.success(mkTransferDispatchConvert.toVOList(mkTransferDispatchService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkTransferDispatchVO> getCurrentDetails(@RequestBody MkTransferDispatchParam param) {
        return RespJson.success(mkTransferDispatchConvert.toVO(mkTransferDispatchService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkTransferDispatchParam param) {
        Boolean retVal = mkTransferDispatchService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkTransferDispatchParam param) {
        Boolean retVal = mkTransferDispatchService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 导出文件
     *
     * @return 文件地址
     */
    @PostMapping(
        value = "/export",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<String> export(@RequestBody MkTransferDispatchParam param) {
        String url = mkTransferDispatchService.export(param);
        return RespJson.success(url);
    }

}

