package com.yhd.admin.api.domain.operation.query;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 运维配置中心-部门管理-部门管理表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class MkUserDeptParam extends QueryParam implements Serializable {
	/**
	* Id
	*/
	private Long id;

	/**
	* 部门编码
	*/
	private String deptCode;

	/**
	* 部门名称
	*/
	private String deptName;

	/**
	* 部门全称
	*/
	private String deptFullName;

	/**
	* 上级部门id
	*/
	private Long upId;

	/**
	* 上级部门名称
	*/
	private String upName;

	/**
	* 部门类型id
	*/
	private Long deptTypeId;

	/**
	* 部门类型名称
	*/
	private String deptTypeName;

	/**
	* 子类型id
	*/
	private Long downId;

	/**
	* 子类型名称
	*/
	private String downName;

	/**
	* 部门电话
	*/
	private String phone;

    /**
     * 单位id
     */
    private Long orgId;

}
