package com.yhd.admin.api.service.conduct.transfer;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.transfer.MkTransferOrgDTO;
import com.yhd.admin.api.domain.conduct.entity.transfer.MkTransferOrg;
import com.yhd.admin.api.domain.conduct.query.transfer.MkTransferOrgParam;

import java.util.List;

/**
 * 收发文部门表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
public interface MkTransferOrgService extends IService<MkTransferOrg> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkTransferOrgDTO> pagingQuery(MkTransferOrgParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkTransferOrgDTO> queryList(MkTransferOrgParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkTransferOrgParam param);

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    MkTransferOrgDTO getCurrentDetails(MkTransferOrgParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkTransferOrgParam param);

    /**
     * 导出文件
     *
     * @return 文件地址
     */
    String export(MkTransferOrgParam param);
}
