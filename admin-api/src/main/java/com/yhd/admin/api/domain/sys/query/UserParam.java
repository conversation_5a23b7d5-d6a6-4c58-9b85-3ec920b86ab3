package com.yhd.admin.api.domain.sys.query;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.*;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName UserParam.java @Description TODO
 * @createTime 2020年04月22日 14:32:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserParam extends QueryParam implements Serializable {

    private String uid;
    /** 姓名 */
    private String name;
    /** 所属部门 */
    private Long orgId;

    /** 部门名称 */
    private String orgTxt;
    /** 头像 */
    private String avatar;
    /** 邮件 */
    private String email;
    /** 签名 */
    private String signature;
    /** 头衔 */
    private String title;
    /** 地址 */
    private String address;
    /** 手机电话 */
    private String phone;
    /** 标签 */
    private String tags;
    /** 档案号 */
    private String fileNo;
    /** 职务/工种 */
    private String job;
    /** 职务/工种 */
    private String jobTxt;
    /** 级别 */
    private String level;
    /** 性别 */
    private String gender;
    /** 民族 */
    private String nation;
    /** 身份证号码 */
    private String sfz;
    /** 出生日期 */
    private LocalDate birthday;
    /** 年龄 */
    private Integer age;
    /** 文化程度 */
    private String education;
    /** 文化程度 文本 */
    private String educationTxt;
    /** 毕业院校 */
    private String graduatedSchool;
    /** 参加工作时间 */
    private LocalDate dateOfRecruitment;
    /** 工龄 */
    private Integer workingAge;
    /** 调入时间 */
    private LocalDate transferInTime;
    /** 工作状态 */
    private String workStatus;
    /** 工作状态 文本 */
    private String workStatusTxt;
    /** 调出时间 */
    private LocalDate callOutTime;
    /** 用工形式 */
    private String employmentForm;
    /** 用工形式 文本 */
    private String employmentFormTxt;
    /** 备注 */
    private String remark;
    /** 状态;0禁用，1启用 */
    private Boolean isEnable;

    /** 创建人 */
    private String createdBy;
    /** 创建时间 */
    private LocalDateTime createdTime;
    /** 更新人 */
    private String updatedBy;
    /** 更新时间 */
    private LocalDateTime updatedTime;

    private String oldAccountName;

    // 账号部分信息
    /** 登录账号 */
    private String username;
    /** 密码 */
    private String password;
    /** 模块ID */
    private String clientId;
    /** 账户未过期 */
    private Boolean isAccountNonExpired;
    /** 账户未锁定 */
    private Boolean isAccountNonLocked;
    /** 密码未过期 */
    private Boolean isCredentialsNonExpired;

    // 角色部分
    private List<Long> roles;

    /** 是否创建登录账号 */
    private Boolean isCreated;

    private String originalPassword;

    private List<String> selectedRowKeys;
}
