package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencySnapshotConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencySnapshotDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySnapshotParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencySnapshotVO;
import com.yhd.admin.api.service.emergency.MkEmergencySnapshotService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应急救援中心-矿井快照 Controller
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/emergency/snapshot")
public class MkEmergencySnapshotController {

    @Resource
    private MkEmergencySnapshotService snapshotService;

    @Resource
    private MkEmergencySnapshotConvert convert;

    /**
     * 分页查询应急快照信息
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkEmergencySnapshotVO> pagingQuery(@RequestBody MkEmergencySnapshotParam queryParam) {
        log.info("分页查询应急快照信息，参数：{}", queryParam);
        IPage<MkEmergencySnapshotDTO> iPage = snapshotService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    /**
     * 新增应急快照信息
     *
     * @param param 应急快照信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkEmergencySnapshotParam param) {
        log.info("新增应急快照信息，参数：{}", param);
        Boolean result = snapshotService.add(param);
        return RespJson.success(result);
    }

    /**
     * 修改应急快照信息
     *
     * @param param 应急快照信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> modify(@RequestBody MkEmergencySnapshotParam param) {
        log.info("修改应急快照信息，参数：{}", param);
        Boolean result = snapshotService.modify(param);
        return RespJson.success(result);
    }

    /**
     * 获取应急快照详情
     *
     * @param param 查询参数
     * @return 应急快照详情
     */
    @PostMapping(value = "/getCurrentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkEmergencySnapshotVO> getCurrentDetail(@RequestBody MkEmergencySnapshotParam param) {
        log.info("获取应急快照详情，参数：{}", param);
        MkEmergencySnapshotDTO dto = snapshotService.getCurrentDetail(param);
        return RespJson.success(convert.toVO(dto));
    }

    /**
     * 批量删除应急快照信息
     *
     * @param param 批量删除参数
     * @return 操作结果
     */
    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody BatchParam param) {
        log.info("批量删除应急快照信息，参数：{}", param);
        Boolean result = snapshotService.removeBatch(param);
        return RespJson.success(result);
    }

    /**
     * 根据矿井ID查询快照列表
     *
     * @param mineId 矿井ID
     * @return 快照列表
     */
    @GetMapping(value = "/getSnapshotsByMineId", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencySnapshotVO>> getSnapshotsByMineId(@RequestParam Long mineId) {
        log.info("根据矿井ID查询快照列表，矿井ID：{}", mineId);
        List<MkEmergencySnapshotDTO> dtoList = snapshotService.getSnapshotsByMineId(mineId);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据快照类型查询快照列表
     *
     * @param snapshotType 快照类型
     * @return 快照列表
     */
    @GetMapping(value = "/getSnapshotsByType", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencySnapshotVO>> getSnapshotsByType(@RequestParam String snapshotType) {
        log.info("根据快照类型查询快照列表，快照类型：{}", snapshotType);
        List<MkEmergencySnapshotDTO> dtoList = snapshotService.getSnapshotsByType(snapshotType);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 获取最新快照
     * @return 最新快照
     */
    @GetMapping(value = "/getLatestSnapshot", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkEmergencySnapshotVO> getLatestSnapshot() {
        log.info("获取最新快照");
        MkEmergencySnapshotDTO dto = snapshotService.getLatestSnapshot();
        return RespJson.success(convert.toVO(dto));
    }

    /**
     * 创建应急快照
     *
     * @param mineId      矿井ID
     * @param mineName    矿井名称
     * @param description 描述
     * @return 创建的快照信息
     */
    @PostMapping(value = "/createEmergencySnapshot", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkEmergencySnapshotVO> createEmergencySnapshot(@RequestParam Long mineId,
                                                                   @RequestParam String mineName,
                                                                   @RequestParam(required = false) String description) {
        log.info("创建应急快照，矿井ID：{}，矿井名称：{}，描述：{}", mineId, mineName, description);
        MkEmergencySnapshotDTO dto = snapshotService.createEmergencySnapshot(mineId, mineName, description);
        return RespJson.success(convert.toVO(dto));
    }

    /**
     * 获取应急快照时间列表
     *
     * @param mineId 矿井ID
     * @return 时间列表
     */
    @GetMapping(value = "/getTimeList", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencySnapshotVO>> getTimeList(@RequestParam(required = false) Long mineId) {
        log.info("获取应急快照时间列表，矿井ID：{}", mineId);
        List<MkEmergencySnapshotDTO> dtoList = snapshotService.getTimeList(mineId);
        return RespJson.success(convert.toVO(dtoList));
    }
}
