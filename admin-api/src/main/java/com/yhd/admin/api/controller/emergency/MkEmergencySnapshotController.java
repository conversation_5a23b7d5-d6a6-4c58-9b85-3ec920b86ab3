package com.yhd.admin.api.controller.emergency;

import com.yhd.admin.api.domain.emergency.convert.MkEmergencySnapshotConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencySnapshotDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySnapshotParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencySnapshotVO;
import com.yhd.admin.api.service.emergency.MkEmergencySnapshotService;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 应急救援中心-矿井快照 Controller
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/open/emergency/snapshot")
public class MkEmergencySnapshotController {

    @Resource
    private MkEmergencySnapshotService snapshotService;

    @Resource
    private MkEmergencySnapshotConvert convert;

    /**
     * 根据快照类型查询快照列表
     *
     * @param param 查询参数
     * @return 快照列表
     */
    @PostMapping(value = "/getSnapshotsByType", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencySnapshotVO>> getSnapshotsByType(@RequestBody MkEmergencySnapshotParam param) {
        log.info("根据快照类型查询快照列表，参数：{}", param);
        List<MkEmergencySnapshotDTO> dtoList = snapshotService.getSnapshotsByType(param.getSnapshotType());
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 获取最新快照
     *
     * @param param 查询参数
     * @return 最新快照
     */
    @PostMapping(value = "/getLatestSnapshot", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkEmergencySnapshotVO> getLatestSnapshot(
        @RequestBody(required = false) MkEmergencySnapshotParam param) {
        log.info("获取最新快照，参数：{}", param);
        MkEmergencySnapshotDTO dto = snapshotService.getLatestSnapshot();
        return RespJson.success(convert.toVO(dto));
    }

}
