package com.yhd.admin.api.domain.home.convert;

import com.yhd.admin.api.domain.home.dto.MkHomeDTO;
import com.yhd.admin.api.domain.home.entity.MkHome;
import com.yhd.admin.api.domain.home.query.MkHomeParam;
import org.mapstruct.Mapper;

/**
 * 综合管控大屏
 *
 * <AUTHOR>
 * @date 2025/7/29 09:34
 */
@Mapper(componentModel = "spring")
public interface MkHomeConvert {
  MkHomeDTO toDTO(MkHome entity);

  MkHome toEntity(MkHomeParam param);
}
