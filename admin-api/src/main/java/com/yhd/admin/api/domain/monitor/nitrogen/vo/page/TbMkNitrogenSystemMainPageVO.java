package com.yhd.admin.api.domain.monitor.nitrogen.vo.page;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 制氮系统监控数据-主表
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkNitrogenSystemMainPageVO extends QueryParam {

    private static final long serialVersionUID = 1L;



        /**
         * 系统名称
         */
        private String systemName;

        /**
         * 制氮控制箱状态: 0-停止, 1-运行, 2-故障, 3-待机
         */
        private Byte controlBoxStatus;
}
