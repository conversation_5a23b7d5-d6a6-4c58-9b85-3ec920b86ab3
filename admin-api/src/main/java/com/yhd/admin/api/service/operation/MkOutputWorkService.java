package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkOutputWorkDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputWork;
import com.yhd.admin.api.domain.operation.query.MkOutputBatchParam;
import com.yhd.admin.api.domain.operation.query.MkOutputWorkParam;
import com.yhd.admin.common.domain.query.BatchParam;


/**
 * 生产录入-主要工作录入表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
public interface MkOutputWorkService extends IService<MkOutputWork> {

    IPage<MkOutputWorkDTO> pagingQuery(MkOutputWorkParam queryParam);

    Boolean removeBatch(BatchParam param);

    MkOutputWorkDTO getCurrentDetail(MkOutputWorkParam param);

    Boolean batchAddAndModify(MkOutputBatchParam batchParam);
}
