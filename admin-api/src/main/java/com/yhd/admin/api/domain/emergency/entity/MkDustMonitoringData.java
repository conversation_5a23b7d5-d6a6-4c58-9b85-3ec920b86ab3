package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkDustMonitoringData.java
 * @Description 粉尘监测数据表
 * @createTime 2025年08月02日 10:00:00
 */
@Data
@TableName("tb_mk_dust_monitoring_data")
public class MkDustMonitoringData {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据编码
     */
    private String dataCode;

    /**
     * 传感器编码
     */
    private String sensorCode;

    /**
     * 传感器名称
     */
    private String sensorName;

    /**
     * 监测类型：粉尘监测、顶板检测
     */
    private String monitoringType;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 测量值（粉尘浓度mg/m³或顶板位移mm）
     */
    private BigDecimal measurementValue;

    /**
     * 测量单位：mg/m³、mm等
     */
    private String unit;

    /**
     * 测量时间
     */
    private LocalDateTime measurementTime;

    /**
     * 数据状态：正常、异常、超标
     */
    private String dataStatus;

    /**
     * 告警等级：正常、低风险、中风险、高风险
     */
    private String alertLevel;

    /**
     * 最小阈值
     */
    private BigDecimal thresholdMin;

    /**
     * 最大阈值
     */
    private BigDecimal thresholdMax;

    /**
     * 是否报警：0-正常，1-报警
     */
    private Boolean isAlarm;

    /**
     * 报警类型
     */
    private String alarmType;

    /**
     * 报警信息
     */
    private String alarmMessage;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 采集方式：auto-自动，manual-手动
     */
    private String collectionMethod;

    /**
     * 数据来源：sensor-传感器，manual-人工
     */
    private String dataSource;

    /**
     * 数据质量标识：good-良好，suspect-可疑，bad-错误
     */
    private String qualityFlag;

    /**
     * 是否已处理：0-未处理，1-已处理
     */
    private Boolean processed;

    /**
     * 处理人员
     */
    private String processor;

    /**
     * 处理时间
     */
    private LocalDateTime processTime;

    /**
     * 处理备注
     */
    private String processRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}