package com.yhd.admin.api.controller.sys;

import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/28
 */
@RestController
@RequestMapping("/common")
@Slf4j
public class CommonController {
    @Resource
    WebApplicationContext applicationContext;

    @RequestMapping(path = {"/getUrls"}, method = {RequestMethod.POST, RequestMethod.GET})
    public RespJson<List<String>> getAllUrl() {
        RequestMappingHandlerMapping mapping =
            applicationContext.getBean("requestMappingHandlerMapping", RequestMappingHandlerMapping.class);
        List<String> urls = new ArrayList<>();
        // 获取url与类和方法的对应信息
        Map<RequestMappingInfo, HandlerMethod> map = mapping.getHandlerMethods();
        for (Map.Entry<RequestMappingInfo, HandlerMethod> m : map.entrySet()) {
            RequestMappingInfo info = m.getKey();
            HandlerMethod method = m.getValue();
            // 获取当前请求的url
            urls.addAll(info.getPatternValues());
        }

        return RespJson.success(urls.stream().distinct().sorted().collect(Collectors.toList()));
    }
}
