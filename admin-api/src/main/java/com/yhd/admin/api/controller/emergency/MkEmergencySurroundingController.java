package com.yhd.admin.api.controller.emergency;

import com.yhd.admin.api.domain.emergency.convert.MkEmergencySurroundingConvert;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencySurrounding;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySurroundingParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencySurroundingVO;
import com.yhd.admin.api.service.emergency.MkEmergencySurroundingService;
import com.yhd.admin.common.domain.RespJson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencySurroundingController.java
 * @Description 应急救援周边信息-灾害防真Controller
 * @createTime 2025年01月20日 14:00:00
 */
@RestController
@RequestMapping("/emergency/surrounding")
@Slf4j
public class MkEmergencySurroundingController {

    private final MkEmergencySurroundingService surroundingService;
    private final MkEmergencySurroundingConvert convert;

    public MkEmergencySurroundingController(MkEmergencySurroundingService surroundingService,
            MkEmergencySurroundingConvert convert) {
        this.surroundingService = surroundingService;
        this.convert = convert;
    }

    /**
     * 查询周边信息列表
     *
     * @param param 查询参数（可选，如果不传或参数为空则查询全部）
     * @return 周边信息列表
     */
    @PostMapping(value = "/queryList", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencySurroundingVO>> queryList(
            @RequestBody(required = false) MkEmergencySurroundingParam param) {
        log.info("查询周边信息列表，参数：{}", param);
        List<MkEmergencySurrounding> list = surroundingService.queryList(param);
        List<MkEmergencySurroundingVO> voList = list.stream().map(convert::toVO).collect(Collectors.toList());
        return RespJson.success(voList);
    }

}
