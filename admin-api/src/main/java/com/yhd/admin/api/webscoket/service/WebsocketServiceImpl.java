package com.yhd.admin.api.webscoket.service;

import com.yhd.admin.api.domain.sys.entity.MesNotice;
import com.yhd.admin.api.service.sys.MesNoticeService;
import jakarta.annotation.Resource;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * @<PERSON> wangshengman
 * @Date 2024/1/2 15:06
 * @Description: 发送消息
 * @Version 1.0
 */

@Service
public class WebsocketServiceImpl implements WebsocketService {
    @Resource
    private SimpMessagingTemplate template;

    @Resource
    private MesNoticeService mesNoticeService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(MesNotice mesNotice) {
        Assert.hasText(mesNotice.getReceiverAccount(), "账号参数为空");
        Assert.hasText(mesNotice.getType(), "消息类别参数为空");
        Assert.hasText(mesNotice.getContent(), "消息内容参数为空");
        //未读状态
        mesNotice.setReadState(false);
        mesNotice.setIsDelete(false);
        mesNotice.setPushState("1");
        mesNotice.setPriority("2");
        //存储消息
        mesNoticeService.save(mesNotice);
        //发送消息
        template.convertAndSendToUser(mesNotice.getReceiverAccount(), "/queue/sendUser", mesNotice);
    }

    /**
     * @param mesNotice
     * @Description: 广播/群发
     */
    @Override
    public void sendMassMessage(MesNotice mesNotice) {
        Assert.hasText(mesNotice.getReceiverAccount(), "账号参数为空");
        Assert.hasText(mesNotice.getType(), "消息类别参数为空");
        Assert.hasText(mesNotice.getContent(), "消息内容参数为空");
        //未读状态
        mesNotice.setReadState(false);
        mesNotice.setIsDelete(false);
        mesNotice.setPushState("1");
        mesNotice.setPriority("2");
        //存储消息
        mesNoticeService.save(mesNotice);
        //发送消息
        template.convertAndSend("/topic/sendTopic", mesNotice);
    }
}
