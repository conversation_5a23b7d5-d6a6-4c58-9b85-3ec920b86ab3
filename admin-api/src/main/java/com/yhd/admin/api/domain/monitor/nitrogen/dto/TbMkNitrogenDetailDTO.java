package com.yhd.admin.api.domain.monitor.nitrogen.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 制氮系统综合信息数据传输对象
 */
@Data
public class TbMkNitrogenDetailDTO {

    /**
     * 空压机ID
     */
    private Long compressorId;

    /**
     * 空压机名称
     */
    private String compressorName;

    /**
     * 空压机编号
     */
    private String compressorNumber;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 空压机状态: 0-停止, 1-运行, 2-故障, 3-待机
     */
    private Byte status;

    /**
     * 排气压力(MPa)
     */
    private BigDecimal dischargePressure;

    /**
     * 螺杆温度(℃)
     */
    private BigDecimal screwTemperature;

    /**
     * 运行时间(h)
     */
    private Integer runtimeHours;

    /**
     * 电机电流(A)
     */
    private BigDecimal motorCurrent;

    /**
     * 电源电压(V)
     */
    private BigDecimal powerVoltage;

    /**
     * 风扇电流(A)
     */
    private BigDecimal fanCurrent;
}
