package com.yhd.admin.api.domain.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class FileManager extends BaseEntity implements Serializable, Cloneable {
  @TableId(type = IdType.AUTO)
  private Long id;
  /** 关联Id */
  private Long relationId;
  /** 业务类型 */
  private String businessType;
  /** 附件地址 */
  private String fileUrl;
  /** 附件名称 */
  private String fileName;
  /** 附件类型 */
  private String fileType;
}
