package com.yhd.admin.api.domain.emergency.vo;

import lombok.Data;

/**
 * 风险统计VO
 *
 * <AUTHOR>
 */
@Data
public class MkSafetyRiskStatsVO {

    /**
     * 重大风险数量
     */
    private Integer majorRiskCount;

    /**
     * 较大风险数量
     */
    private Integer significantRiskCount;

    /**
     * 一般风险数量
     */
    private Integer generalRiskCount;

    /**
     * 低风险数量
     */
    private Integer lowRiskCount;

    /**
     * 一般A级数量
     */
    private Integer generalALevelCount;

    /**
     * 一般B级数量
     */
    private Integer generalBLevelCount;

    /**
     * 一般C级数量
     */
    private Integer generalCLevelCount;

    /**
     * 风险总数
     */
    private Integer totalRiskCount;
}