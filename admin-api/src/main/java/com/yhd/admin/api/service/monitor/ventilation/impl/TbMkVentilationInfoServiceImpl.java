package com.yhd.admin.api.service.monitor.ventilation.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.ventilation.TbMkVentilationInfoDao;
import com.yhd.admin.api.domain.monitor.ventilation.convert.TbMkVentilationInfoConvert;
import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkVentilationInfoDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkVentilationInfo;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkVentilationInfoCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.page.TbMkVentilationInfoPageVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkVentilationInfoUpdateVO;
import com.yhd.admin.api.service.monitor.ventilation.TbMkVentilationInfoService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 通风机基本信息 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkVentilationInfoServiceImpl extends ServiceImpl<TbMkVentilationInfoDao, TbMkVentilationInfo> implements TbMkVentilationInfoService {

    @Resource
    private TbMkVentilationInfoConvert tbMkVentilationInfoConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkVentilationInfoCreateVO createVO) {
        TbMkVentilationInfo tbMkVentilationInfo = tbMkVentilationInfoConvert.convert(createVO);
        baseMapper.insert(tbMkVentilationInfo);
        return tbMkVentilationInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkVentilationInfoUpdateVO updateVO) {
        TbMkVentilationInfo tbMkVentilationInfo = tbMkVentilationInfoConvert.convert(updateVO);
        return baseMapper.updateById(tbMkVentilationInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkVentilationInfo get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkVentilationInfoDTO> page(TbMkVentilationInfoPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkVentilationInfo> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkVentilationInfo> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.like(StringUtils.isNotBlank(param.getVentilationName()), TbMkVentilationInfo::getVentilationName, param.getVentilationName());
        queryChainWrapper.like(StringUtils.isNotBlank(param.getRemarks()), TbMkVentilationInfo::getRemarks, param.getRemarks());
        queryChainWrapper.orderByDesc(TbMkVentilationInfo::getId);
        return queryChainWrapper.page(iPage).convert(tbMkVentilationInfoConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkVentilationInfoCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 通风机基本信息：传入的列表为空");
            return 0;
        }
        List<TbMkVentilationInfo> entities = createVOs.stream()
                .map(tbMkVentilationInfoConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 通风机基本信息 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkVentilationInfoUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 通风机基本信息：传入的列表为空");
            return 0;
        }
        List<TbMkVentilationInfo> entities = updateVOs.stream()
                .map(tbMkVentilationInfoConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 通风机基本信息：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 通风机基本信息 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
