package com.yhd.admin.api.domain.monitor.digging.vo.update;

import com.yhd.admin.api.domain.monitor.digging.vo.create.TbMkDiggingInfoCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 掘进机基本信息
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkDiggingInfoUpdateVO extends TbMkDiggingInfoCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
