package com.yhd.admin.api.domain.sys.vo;

import java.io.Serializable;

import com.yhd.admin.common.domain.vo.BaseVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName ParameterVO.java @Description TODO
 * @createTime 2020年05月12日 14:52:00
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class ParameterVO extends BaseVO implements Cloneable, Serializable {
    private String id;
    /** 参数KEY */
    private String paramKey;
    /** 参数值 */
    private String paramVal;
    /** 状态;0否1是 */
    private Boolean isEnable;

    /** 备注 */
    private String remark;

    @Override
    public String toString() {
        return "ParameterVO{" + "paramKey='" + paramKey + '\'' + ", paramVal='" + paramVal + '\'' + ", isEnable="
            + isEnable + '}';
    }

    @Override
    public ParameterVO clone() {
        try {
            // TODO: copy mutable state here, so the clone can't change the internals of the original
            return (ParameterVO)super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
