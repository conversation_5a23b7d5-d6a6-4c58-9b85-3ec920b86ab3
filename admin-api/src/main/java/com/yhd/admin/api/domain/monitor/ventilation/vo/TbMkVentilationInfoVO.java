package com.yhd.admin.api.domain.monitor.ventilation.vo;

import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkVentilationInfoCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通风机基本信息
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkVentilationInfoVO extends TbMkVentilationInfoCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
