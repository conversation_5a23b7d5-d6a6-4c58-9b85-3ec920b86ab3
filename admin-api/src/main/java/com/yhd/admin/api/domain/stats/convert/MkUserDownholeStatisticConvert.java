package com.yhd.admin.api.domain.stats.convert;

import com.yhd.admin.api.domain.stats.dto.MkUserDownholeStatisticDTO;
import com.yhd.admin.api.domain.stats.entity.MkUserDownholeStatistic;
import com.yhd.admin.api.domain.stats.query.MkUserDownholeStatisticParam;
import com.yhd.admin.api.domain.stats.vo.MkUserDownholeStatisticVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MkUserDownholeStatisticConvert {
  MkUserDownholeStatisticDTO toDTO(MkUserDownholeStatistic entity);

  MkUserDownholeStatisticVO toVO(MkUserDownholeStatisticDTO dto);

  MkUserDownholeStatistic toEntity(MkUserDownholeStatisticParam param);
}
