package com.yhd.admin.api.domain.sys.query;

import java.io.Serializable;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = false)
@Data
public class OrgParam extends QueryParam implements Serializable, Cloneable {

    private Long id;
    /** 组织名称 */
    private String orgName;
    /** 父级ID */
    private Long parentId;
    /** 父级名称 */
    private String parentName;
    /** 状态 */
    private Boolean status;
    /** 排序 */
    private Long sortNum;
}
