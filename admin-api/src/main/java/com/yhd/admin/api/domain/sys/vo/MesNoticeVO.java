package com.yhd.admin.api.domain.sys.vo;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 消息通知表
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class MesNoticeVO extends BaseVO implements Cloneable, Serializable {
    private Long id;
    /**
     * 接收消息的用户账号
     */
    private String receiverAccount;
    /**
     * 接收消息的用户账号名称
     */
    private String receiverAccountName;
    /**
     * 消息类型;0-系统消息；1-用户消息
     */
    private String type;
    /**
     * 操作类型;通知、提醒、点赞、@、评论等行为
     */
    private String action;
    /**
     * 被操作的对象;业务类型
     */
    private String targetType;
    /**
     * 被操作的对象名称;业务类型名称
     */
    private String targetTypeName;
    /**
     * 被操作的对象id;关联业务id
     */
    private String targetId;
    /**
     * 读取状态;0-已读，1-未读
     */
    private Boolean readState;
    /**
     * 推送状态;0-已推送，1-未推送
     */
    private String pushState;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;
    /**
     * 跳转路由
     */
    private String jumpRoute;
    /**
     * 跳转参数
     */
    private String jumpParam;
    /**
     * 优先级
     */
    private String priority;

    /**
     * 逻辑删除
     */
    private Boolean isDelete;
}

