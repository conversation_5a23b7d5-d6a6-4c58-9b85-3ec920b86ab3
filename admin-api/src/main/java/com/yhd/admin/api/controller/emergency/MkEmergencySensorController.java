package com.yhd.admin.api.controller.emergency;

import com.yhd.admin.api.domain.emergency.convert.MkEmergencySensorConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencySensorDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySensorTreeParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencySensorVO;
import com.yhd.admin.api.service.emergency.MkEmergencySensorService;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 应急救援监测信息表(传感器表) Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/emergency/sensor")
public class MkEmergencySensorController {

    @Resource
    private MkEmergencySensorService emergencySensorService;

    @Resource
    private MkEmergencySensorConvert convert;


    /**
     * 获取传感器树形结构
     *
     * @param param 查询参数（不传参查询全部数据，可根据sensorName名称过滤）
     * @return 树形结构数据
     */
    @PostMapping(value = "/getSensorTree", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencySensorVO>> getSensorTree(@RequestBody(required = false) MkEmergencySensorTreeParam param) {
        log.info("获取传感器树形结构，参数：{}", param);
        List<MkEmergencySensorDTO> dtoList = emergencySensorService.getSensorTreeByParam(param);
        return RespJson.success(convert.toVO(dtoList));
    }


}
