package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencySensorConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencySensorDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySensorParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencySensorVO;
import com.yhd.admin.api.service.emergency.MkEmergencySensorService;
import com.yhd.admin.common.domain.query.BatchParam;
import com.yhd.admin.common.domain.vo.PageRespJson;
import com.yhd.admin.common.domain.vo.RespJson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 应急救援监测信息表(传感器表) Controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/emergency/sensor")
public class MkEmergencySensorController {

    @Resource
    private MkEmergencySensorService emergencySensorService;

    @Resource
    private MkEmergencySensorConvert convert;

    /**
     * 分页查询应急救援监测信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkEmergencySensorVO> pagingQuery(@RequestBody MkEmergencySensorParam queryParam) {
        log.info("分页查询应急救援监测信息，参数：{}", queryParam);
        IPage<MkEmergencySensorDTO> iPage = emergencySensorService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    /**
     * 新增应急救援监测信息
     * @param param 应急救援监测信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkEmergencySensorParam param) {
        log.info("新增应急救援监测信息，参数：{}", param);
        Boolean result = emergencySensorService.add(param);
        return RespJson.success(result);
    }

    /**
     * 修改应急救援监测信息
     * @param param 应急救援监测信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> modify(@RequestBody MkEmergencySensorParam param) {
        log.info("修改应急救援监测信息，参数：{}", param);
        Boolean result = emergencySensorService.modify(param);
        return RespJson.success(result);
    }

    /**
     * 获取应急救援监测信息详情
     * @param id 传感器ID
     * @return 详情信息
     */
    @GetMapping(value = "/getCurrentDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkEmergencySensorVO> getCurrentDetail(@RequestParam Integer id) {
        log.info("获取应急救援监测信息详情，ID：{}", id);
        MkEmergencySensorParam param = new MkEmergencySensorParam();
        param.setId(id);
        MkEmergencySensorDTO dto = emergencySensorService.getCurrentDetail(param);
        return RespJson.success(convert.toVO(dto));
    }

    /**
     * 批量删除应急救援监测信息
     * @param param 批量删除参数
     * @return 操作结果
     */
    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody BatchParam param) {
        log.info("批量删除应急救援监测信息，参数：{}", param);
        Boolean result = emergencySensorService.removeBatch(param);
        return RespJson.success(result);
    }

    /**
     * 获取传感器树形结构
     * @param orgCode 组织编码
     * @param parentId 父级ID（可选）
     * @return 树形结构数据
     */
    @GetMapping(value = "/getSensorTree", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencySensorVO>> getSensorTree(@RequestParam String orgCode,
                                                             @RequestParam(required = false) Integer parentId) {
        log.info("获取传感器树形结构，组织编码：{}，父级ID：{}", orgCode, parentId);
        List<MkEmergencySensorDTO> dtoList = emergencySensorService.getSensorTree(orgCode, parentId);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据传感器编码查询
     * @param sensorCode 传感器编码
     * @return 传感器信息
     */
    @GetMapping(value = "/getBySensorCode", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkEmergencySensorVO> getBySensorCode(@RequestParam String sensorCode) {
        log.info("根据传感器编码查询，传感器编码：{}", sensorCode);
        MkEmergencySensorDTO dto = emergencySensorService.getBySensorCode(sensorCode);
        return RespJson.success(convert.toVO(dto));
    }

    /**
     * 查询异常传感器
     * @param orgCode 组织编码
     * @return 异常传感器列表
     */
    @GetMapping(value = "/getAbnormalSensors", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencySensorVO>> getAbnormalSensors(@RequestParam String orgCode) {
        log.info("查询异常传感器，组织编码：{}", orgCode);
        List<MkEmergencySensorDTO> dtoList = emergencySensorService.getAbnormalSensors(orgCode);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 查询超出阈值的传感器
     * @param orgCode 组织编码
     * @return 超出阈值的传感器列表
     */
    @GetMapping(value = "/getOutOfRangeSensors", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencySensorVO>> getOutOfRangeSensors(@RequestParam String orgCode) {
        log.info("查询超出阈值的传感器，组织编码：{}", orgCode);
        List<MkEmergencySensorDTO> dtoList = emergencySensorService.getOutOfRangeSensors(orgCode);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据层级类型查询传感器
     * @param levelType 层级类型
     * @param orgCode 组织编码
     * @return 传感器列表
     */
    @GetMapping(value = "/getByLevelType", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencySensorVO>> getByLevelType(@RequestParam String levelType,
                                                              @RequestParam String orgCode) {
        log.info("根据层级类型查询传感器，层级类型：{}，组织编码：{}", levelType, orgCode);
        List<MkEmergencySensorDTO> dtoList = emergencySensorService.getByLevelType(levelType, orgCode);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据工作面查询传感器
     * @param workFace 工作面
     * @param orgCode 组织编码
     * @return 传感器列表
     */
    @GetMapping(value = "/getByWorkFace", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencySensorVO>> getByWorkFace(@RequestParam String workFace,
                                                             @RequestParam String orgCode) {
        log.info("根据工作面查询传感器，工作面：{}，组织编码：{}", workFace, orgCode);
        List<MkEmergencySensorDTO> dtoList = emergencySensorService.getByWorkFace(workFace, orgCode);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据巷道查询传感器
     * @param tunnel 巷道
     * @param orgCode 组织编码
     * @return 传感器列表
     */
    @GetMapping(value = "/getByTunnel", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencySensorVO>> getByTunnel(@RequestParam String tunnel,
                                                           @RequestParam String orgCode) {
        log.info("根据巷道查询传感器，巷道：{}，组织编码：{}", tunnel, orgCode);
        List<MkEmergencySensorDTO> dtoList = emergencySensorService.getByTunnel(tunnel, orgCode);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据传感器类型查询传感器
     * @param sensorType 传感器类型
     * @param orgCode 组织编码
     * @return 传感器列表
     */
    @GetMapping(value = "/getBySensorType", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencySensorVO>> getBySensorType(@RequestParam String sensorType,
                                                               @RequestParam String orgCode) {
        log.info("根据传感器类型查询传感器，传感器类型：{}，组织编码：{}", sensorType, orgCode);
        List<MkEmergencySensorDTO> dtoList = emergencySensorService.getBySensorType(sensorType, orgCode);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 更新传感器状态
     * @param id 传感器ID
     * @param status 状态
     * @return 操作结果
     */
    @PostMapping(value = "/updateStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> updateStatus(@RequestParam Integer id, @RequestParam Integer status) {
        log.info("更新传感器状态，ID：{}，状态：{}", id, status);
        Boolean result = emergencySensorService.updateStatus(id, status);
        return RespJson.success(result);
    }

    /**
     * 批量更新传感器状态
     * @param ids 传感器ID列表
     * @param status 状态
     * @return 操作结果
     */
    @PostMapping(value = "/batchUpdateStatus", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchUpdateStatus(@RequestBody List<Integer> ids, @RequestParam Integer status) {
        log.info("批量更新传感器状态，IDs：{}，状态：{}", ids, status);
        Boolean result = emergencySensorService.batchUpdateStatus(ids, status);
        return RespJson.success(result);
    }

    /**
     * 更新传感器当前值
     * @param sensorCode 传感器编码
     * @param currentValue 当前值
     * @return 操作结果
     */
    @PostMapping(value = "/updateCurrentValue", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> updateCurrentValue(@RequestParam String sensorCode, @RequestParam BigDecimal currentValue) {
        log.info("更新传感器当前值，传感器编码：{}，当前值：{}", sensorCode, currentValue);
        Boolean result = emergencySensorService.updateCurrentValue(sensorCode, currentValue);
        return RespJson.success(result);
    }

    /**
     * 批量更新传感器当前值
     * @param sensorDataMap 传感器数据映射
     * @return 操作结果
     */
    @PostMapping(value = "/batchUpdateCurrentValue", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchUpdateCurrentValue(@RequestBody Map<String, BigDecimal> sensorDataMap) {
        log.info("批量更新传感器当前值，数据量：{}", sensorDataMap.size());
        Boolean result = emergencySensorService.batchUpdateCurrentValue(sensorDataMap);
        return RespJson.success(result);
    }

    /**
     * 统计各状态传感器数量
     * @param orgCode 组织编码
     * @return 统计结果
     */
    @GetMapping(value = "/countByStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Map<String, Long>> countByStatus(@RequestParam String orgCode) {
        log.info("统计各状态传感器数量，组织编码：{}", orgCode);
        Map<String, Long> result = emergencySensorService.countByStatus(orgCode);
        return RespJson.success(result);
    }

    /**
     * 统计各类型传感器数量
     * @param orgCode 组织编码
     * @return 统计结果
     */
    @GetMapping(value = "/countBySensorType", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Map<String, Long>> countBySensorType(@RequestParam String orgCode) {
        log.info("统计各类型传感器数量，组织编码：{}", orgCode);
        Map<String, Long> result = emergencySensorService.countBySensorType(orgCode);
        return RespJson.success(result);
    }

    /**
     * 获取传感器监控概览
     * @param orgCode 组织编码
     * @return 监控概览数据
     */
    @GetMapping(value = "/getMonitorOverview", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Map<String, Object>> getMonitorOverview(@RequestParam String orgCode) {
        log.info("获取传感器监控概览，组织编码：{}", orgCode);
        Map<String, Object> result = emergencySensorService.getMonitorOverview(orgCode);
        return RespJson.success(result);
    }

    /**
     * 校验传感器编码是否唯一
     * @param sensorCode 传感器编码
     * @param excludeId 排除的ID（可选）
     * @return 是否唯一
     */
    @GetMapping(value = "/checkSensorCodeUnique", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> checkSensorCodeUnique(@RequestParam String sensorCode,
                                                   @RequestParam(required = false) Integer excludeId) {
        log.info("校验传感器编码是否唯一，传感器编码：{}，排除ID：{}", sensorCode, excludeId);
        Boolean result = emergencySensorService.checkSensorCodeUnique(sensorCode, excludeId);
        return RespJson.success(result);
    }
}
