package com.yhd.admin.api.domain.emergency.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DeviceRealtimeChartVO.java
 * @Description 设备实时值曲线数据VO
 * @createTime 2025年01月20日 12:00:00
 */
@Data
public class DeviceRealtimeChartVO {

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 测量单位
     */
    private String unit;

    /**
     * 当前值
     */
    private BigDecimal currentValue;

    /**
     * 最小阈值
     */
    private BigDecimal thresholdMin;

    /**
     * 最大阈值
     */
    private BigDecimal thresholdMax;

    /**
     * 历史数据点列表
     */
    private List<ChartDataPoint> dataPoints;

    /**
     * 图表数据点
     */
    @Data
    public static class ChartDataPoint {
        /**
         * 时间
         */
        private LocalDateTime time;

        /**
         * 数值
         */
        private BigDecimal value;

        /**
         * 状态：1-正常，2-异常，3-离线
         */
        private Integer status;

        /**
         * 状态名称
         */
        private String statusName;
    }
}