package com.yhd.admin.api.domain.monitor.digging.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 掘进机导航参数
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_mk_digging_navigation")
public class TbMkDiggingNavigation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联掘进机基本信息表ID
     */
    @TableField("digging_id")
    private Long diggingId;

    /**
     * 俯仰角(°)
     */
    @TableField("pitch_angle")
    private BigDecimal pitchAngle;

    /**
     * 翻滚角(°)
     */
    @TableField("roll_angle")
    private BigDecimal rollAngle;

    /**
     * 航向角(°)
     */
    @TableField("yaw_angle")
    private BigDecimal yawAngle;
}
