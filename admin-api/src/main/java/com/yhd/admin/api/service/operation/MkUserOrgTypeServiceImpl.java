package com.yhd.admin.api.service.operation;

import com.yhd.admin.api.dao.operation.MkUserOrgTypeDao;
import com.yhd.admin.api.domain.operation.convert.MkUserOrgTypeConvert;
import com.yhd.admin.api.domain.operation.dto.MkUserOrgTypeDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserOrgType;
import com.yhd.admin.api.domain.operation.query.MkUserOrgTypeParam;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 运维配置中心-单位类型-Service
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */
@Service
public class MkUserOrgTypeServiceImpl extends ServiceImpl<MkUserOrgTypeDao, MkUserOrgType> implements MkUserOrgTypeService {

    @Resource
    private MkUserOrgTypeConvert convert;

    @Override
    public IPage<MkUserOrgTypeDTO> pagingQuery(MkUserOrgTypeParam queryParam) {
        Page<MkUserOrgType> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkUserOrgType> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        queryChain.orderByAsc(MkUserOrgType::getId);
        return queryChain.page(page).convert(convert::toDTO);
    }


    @Override
    public Boolean add(MkUserOrgTypeParam param) {
        MkUserOrgType entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MkUserOrgTypeParam param) {
        MkUserOrgType entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MkUserOrgTypeDTO getCurrentDetail(MkUserOrgTypeParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    public List<MkUserOrgTypeDTO> getList(MkUserOrgTypeParam param) {
        return convert.toDTOList(super.list());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

}
