package com.yhd.admin.api.controller.monitor.ventilation;

import com.yhd.admin.api.domain.monitor.ventilation.convert.TbMkMotorOperationConvert;
import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkMotorOperationDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkMotorOperation;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkMotorOperationCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.page.TbMkMotorOperationPageVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkMotorOperationUpdateVO;
import com.yhd.admin.api.service.monitor.ventilation.TbMkMotorOperationService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 电机运行参数 Controller
 * <AUTHOR>
 * @since
 */
@Slf4j
@RestController
@RequestMapping("/monitor/ventilation/tbMkMotorOperation")
public class TbMkMotorOperationController {

    @Resource
    private TbMkMotorOperationService tbMkMotorOperationService;

    @Resource
    private TbMkMotorOperationConvert tbMkMotorOperationConvert;

    /**
     * 分页查询
     *
     * @param pageVO 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<TbMkMotorOperationDTO> page(@RequestBody @Valid TbMkMotorOperationPageVO pageVO) {
        log.debug("分页查询 电机运行参数，参数：{}", pageVO);
        return new PageRespJson<>(tbMkMotorOperationService.page(pageVO));
    }

    /**
     * 新增
     *
     * @param createVO 创建参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> create(@RequestBody @Valid TbMkMotorOperationCreateVO createVO) {
        log.debug("新增 电机运行参数，参数：{}", createVO);
        return RespJson.success(tbMkMotorOperationService.create(createVO));
    }

    /**
     * 更新
     *
     * @param updateVO 更新参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> update(@RequestBody @Valid TbMkMotorOperationUpdateVO updateVO) {
        log.debug("更新 电机运行参数，参数：{}", updateVO);
        return RespJson.success(tbMkMotorOperationService.update(updateVO));
    }


    /**
      * 删除
      *
      * @param id 要删除的记录ID
      * @return 操作结果
      */
    @DeleteMapping("/{id}")
    public RespJson<?> delete(@PathVariable Long id) {
        log.debug("删除 电机运行参数，ID：{}", id);
        return RespJson.success(tbMkMotorOperationService.delete(id));
    }

    /**
     * 查看详情
     *
     * @param id 要查看的记录ID
     * @return 详情信息
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<TbMkMotorOperationDTO> getDetail(@PathVariable Long id) {
        log.debug("查看详情 电机运行参数，ID：{}", id);
        TbMkMotorOperation entity = tbMkMotorOperationService.get(id);
        if (entity == null) {
            return RespJson.failure("未找到对应记录");
        }
        return RespJson.success(tbMkMotorOperationConvert.toDTO(entity));
    }
    /**
     * 批量删除
     *
     * @param ids 要删除的记录ID列表
     * @return 操作结果
     */
    @PostMapping(value = "/batchDelete", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchDelete(@RequestBody List<Long> ids) {
        log.debug("批量删除 电机运行参数，IDs：{}", ids);
        return RespJson.success(tbMkMotorOperationService.batchDelete(ids));
    }

    /**
     * 批量新增
     *
     * @param createVOs 新增参数列表
     * @return 新增的记录ID列表
     */
    @PostMapping(value = "/batchAdd", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchCreate(@RequestBody @Valid List<TbMkMotorOperationCreateVO> createVOs) {
        log.debug("批量新增 电机运行参数，数量：{}", createVOs.size());
        return RespJson.success(tbMkMotorOperationService.batchCreate(createVOs));
    }

    /**
     * 批量更新
     *
     * @param updateVOs 更新参数列表
     * @return 更新的记录数量
     */
    @PostMapping(value = "/batchModify", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> batchUpdate(@RequestBody @Valid List<TbMkMotorOperationUpdateVO> updateVOs) {
        log.debug("批量更新 电机运行参数，数量：{}", updateVOs.size());
        return RespJson.success(tbMkMotorOperationService.batchUpdate(updateVOs));
    }
}
