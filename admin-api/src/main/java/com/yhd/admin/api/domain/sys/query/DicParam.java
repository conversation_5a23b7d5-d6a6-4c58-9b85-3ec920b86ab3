package com.yhd.admin.api.domain.sys.query;

import java.io.Serializable;
import java.util.List;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName DicParam.java @Description TODO 字典项
 * @createTime 2020年05月20日 14:33:00
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class DicParam extends QueryParam implements Serializable, Cloneable {

    private Long id;

    /** 字典名称 */
    private String name;
    /** 字典类别 */
    private String category;
    /** 状态 */
    private Boolean status;
    /** 排序 */
    private String orderNum;

    private List<DicItemParam> items;

    private List<DicParam> params;
}
