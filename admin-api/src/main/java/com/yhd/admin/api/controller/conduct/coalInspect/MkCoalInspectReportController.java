package com.yhd.admin.api.controller.conduct.coalInspect;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.coalInspect.MkCoalInspectReportConvert;
import com.yhd.admin.api.domain.conduct.dto.coalInspect.MkCoalInspectReportDTO;
import com.yhd.admin.api.domain.conduct.entity.coalInspect.MkCoalBlock;
import com.yhd.admin.api.domain.conduct.entity.coalInspect.MkCoalData;
import com.yhd.admin.api.domain.conduct.query.coalInspect.MkCoalInspectReportParam;
import com.yhd.admin.api.domain.conduct.vo.coalInspect.MkCoalInspectReportVO;
import com.yhd.admin.api.service.conduct.coalInspect.MkCoalInspectReportService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 煤质检验报告控制层
 *
 * <AUTHOR>
 * @since 2025-08-05 09:12:06
 */
@RestController
@RequestMapping("/conduct/coal/inspect")
public class MkCoalInspectReportController {

    private final MkCoalInspectReportService mkCoalInspectReportService;
    private final MkCoalInspectReportConvert mkCoalInspectReportConvert;

    public MkCoalInspectReportController(
        MkCoalInspectReportService mkCoalInspectReportService,
        MkCoalInspectReportConvert mkCoalInspectReportConvert
    ) {
        this.mkCoalInspectReportService = mkCoalInspectReportService;
        this.mkCoalInspectReportConvert = mkCoalInspectReportConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkCoalInspectReportParam param) {
        IPage<MkCoalInspectReportDTO> page = mkCoalInspectReportService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkCoalInspectReportConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkCoalInspectReportParam param) {
        return RespJson.success(mkCoalInspectReportConvert.toVOList(mkCoalInspectReportService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkCoalInspectReportVO> getCurrentDetails(@RequestBody MkCoalInspectReportParam param) {
        return RespJson.success(mkCoalInspectReportConvert.toVO(mkCoalInspectReportService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> addOrModify(@RequestBody MkCoalInspectReportParam param) {
        Boolean retVal = mkCoalInspectReportService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkCoalInspectReportParam param) {
        Boolean retVal = mkCoalInspectReportService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 导出文件
     *
     * @return 文件地址
     */
    @PostMapping(
        value = "/export",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<String> export(@RequestBody MkCoalInspectReportParam param) {
        String url = mkCoalInspectReportService.export(param);
        return RespJson.success(url);
    }

    /**
     * 获取煤质数据
     * @param param 查询参数（包含煤种名称）
     * @return 煤质数据（包含近10天、近30天和近12个月数据曲线）
     */
    @PostMapping(
        value = "/getData",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkCoalData> getData(@RequestBody MkCoalInspectReportParam param) {
        return RespJson.success(mkCoalInspectReportService.getData(param));
    }

    /**
     * 获取出块率曲线数据
     * 生成包含最近10天、30天和12个月的出块率数据曲线，并计算各种平均值
     *
     * @return MkCoalBlock 包含三类出块率曲线数据及各种平均值的对象
     */
    @PostMapping(value = "/getDataOfBlock")
    public RespJson<MkCoalBlock> getDataOfBlock() {
        return RespJson.success(mkCoalInspectReportService.getDataOfBlock());
    }

}

