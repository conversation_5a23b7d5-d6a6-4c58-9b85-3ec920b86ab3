package com.yhd.admin.api.dao.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yhd.admin.api.domain.sys.entity.SysRole;
import com.yhd.admin.api.domain.sys.query.UserParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName RoleDao.java @Description TODO
 * @createTime 2020年03月30日 11:06:00
 */
public interface RoleDao extends BaseMapper<SysRole> {

    /**
     * 根据用户账户查询对应的角色
     *
     * @param param
     * @return
     */
    List<SysRole> selectRoleListByUser(UserParam param);
}
