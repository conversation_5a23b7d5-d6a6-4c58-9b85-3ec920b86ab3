package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyContactConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyContactDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyContactParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyContactVO;
import com.yhd.admin.api.service.emergency.MkEmergencyContactService;
import com.yhd.admin.common.domain.PageRespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 应急救援通讯录表 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/open/emergency/contact")
public class MkEmergencyContactController {

    @Resource
    private MkEmergencyContactService emergencyContactService;

    @Resource
    private MkEmergencyContactConvert convert;

    /**
     * 分页查询应急救援通讯录信息
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkEmergencyContactVO> pagingQuery(@RequestBody MkEmergencyContactParam queryParam) {
        log.info("分页查询应急救援通讯录信息，参数：{}", queryParam);
        IPage<MkEmergencyContactDTO> iPage = emergencyContactService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }


}
