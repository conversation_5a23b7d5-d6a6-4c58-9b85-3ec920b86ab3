package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyContactConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyContactDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyContactParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyContactVO;
import com.yhd.admin.api.service.emergency.MkEmergencyContactService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应急救援通讯录表 Controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/emergency/contact")
public class MkEmergencyContactController {

    @Resource
    private MkEmergencyContactService emergencyContactService;

    @Resource
    private MkEmergencyContactConvert convert;

    /**
     * 分页查询应急救援通讯录信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkEmergencyContactVO> pagingQuery(@RequestBody MkEmergencyContactParam queryParam) {
        log.info("分页查询应急救援通讯录信息，参数：{}", queryParam);
        IPage<MkEmergencyContactDTO> iPage = emergencyContactService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    /**
     * 新增应急救援通讯录信息
     * @param param 应急救援通讯录信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkEmergencyContactParam param) {
        log.info("新增应急救援通讯录信息，参数：{}", param);
        Boolean result = emergencyContactService.add(param);
        return RespJson.success(result);
    }

    /**
     * 修改应急救援通讯录信息
     * @param param 应急救援通讯录信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> modify(@RequestBody MkEmergencyContactParam param) {
        log.info("修改应急救援通讯录信息，参数：{}", param);
        Boolean result = emergencyContactService.modify(param);
        return RespJson.success(result);
    }

    /**
     * 获取应急救援通讯录详情
     * @param param 查询参数
     * @return 应急救援通讯录详情
     */
    @PostMapping(value = "/getCurrentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkEmergencyContactVO> getCurrentDetail(@RequestBody MkEmergencyContactParam param) {
        log.info("获取应急救援通讯录详情，参数：{}", param);
        MkEmergencyContactDTO dto = emergencyContactService.getCurrentDetail(param);
        return RespJson.success(convert.toVO(dto));
    }

    /**
     * 批量删除应急救援通讯录信息
     * @param param 批量删除参数
     * @return 操作结果
     */
    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody BatchParam param) {
        log.info("批量删除应急救援通讯录信息，参数：{}", param);
        Boolean result = emergencyContactService.removeBatch(param);
        return RespJson.success(result);
    }

    /**
     * 根据部门查询联系人列表
     * @param department 部门名称
     * @return 联系人列表
     */
    @GetMapping(value = "/getContactsByDepartment", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyContactVO>> getContactsByDepartment(@RequestParam String department) {
        log.info("根据部门查询联系人列表，部门：{}", department);
        List<MkEmergencyContactDTO> dtoList = emergencyContactService.getContactsByDepartment(department);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 获取应急负责人列表
     * @param orgCode 组织编码
     * @return 应急负责人列表
     */
    @GetMapping(value = "/getEmergencyLeaders", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyContactVO>> getEmergencyLeaders(@RequestParam String orgCode) {
        log.info("获取应急负责人列表，组织编码：{}", orgCode);
        List<MkEmergencyContactDTO> dtoList = emergencyContactService.getEmergencyLeaders(orgCode);
        return RespJson.success(convert.toVO(dtoList));
    }
}
