package com.yhd.admin.api.controller.conduct.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.project.MkMiningProjectInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.project.MkMiningProjectInfoDTO;
import com.yhd.admin.api.domain.conduct.query.project.MkMiningProjectInfoParam;
import com.yhd.admin.api.domain.conduct.vo.project.MkMiningProjectInfoVO;
import com.yhd.admin.api.service.conduct.project.MkMiningProjectInfoService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 回采项目信息控制层
 *
 * <AUTHOR>
 * @since 2025-07-30 14:05:52
 */
@RestController
@RequestMapping("/conduct/mining/project")
public class MkMiningProjectInfoController {

    private final MkMiningProjectInfoService mkMiningProjectInfoService;
    private final MkMiningProjectInfoConvert mkMiningProjectInfoConvert;

    public MkMiningProjectInfoController(
        MkMiningProjectInfoService mkMiningProjectInfoService,
        MkMiningProjectInfoConvert mkMiningProjectInfoConvert
    ) {
        this.mkMiningProjectInfoService = mkMiningProjectInfoService;
        this.mkMiningProjectInfoConvert = mkMiningProjectInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkMiningProjectInfoParam param) {
        IPage<MkMiningProjectInfoDTO> page = mkMiningProjectInfoService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkMiningProjectInfoConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkMiningProjectInfoParam param) {
        return RespJson.success(mkMiningProjectInfoConvert.toVOList(mkMiningProjectInfoService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkMiningProjectInfoVO> getCurrentDetails(@RequestBody MkMiningProjectInfoParam param) {
        return RespJson.success(mkMiningProjectInfoConvert.toVO(mkMiningProjectInfoService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkMiningProjectInfoParam param) {
        Boolean retVal = mkMiningProjectInfoService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkMiningProjectInfoParam param) {
        Boolean retVal = mkMiningProjectInfoService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 提交审批
     *
     * @param param 数据
     * @return 是否成功
     */
    @PostMapping(
        value = "/submit",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> submit(@RequestBody MkMiningProjectInfoParam param) {
        Boolean retVal = mkMiningProjectInfoService.submit(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 通过审批、驳回
     *
     * @param param 数据
     * @return 是否成功
     */
    @PostMapping(
        value = "/handle",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> handle(@RequestBody MkMiningProjectInfoParam param) {
        Boolean retVal = mkMiningProjectInfoService.handle(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

