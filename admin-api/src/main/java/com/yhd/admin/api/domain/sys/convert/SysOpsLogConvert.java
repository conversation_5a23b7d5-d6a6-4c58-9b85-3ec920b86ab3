package com.yhd.admin.api.domain.sys.convert;

import org.mapstruct.Mapper;

import com.yhd.admin.api.domain.sys.dto.SysOpsLogDTO;
import com.yhd.admin.api.domain.sys.entity.SysOpsLog;
import com.yhd.admin.api.domain.sys.query.SysOpsLogParam;
import com.yhd.admin.api.domain.sys.vo.SysOpsLogVO;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName SysOpsLogConvert.java @Description TODO
 * @createTime 2020年05月12日 15:05:00
 */
@Mapper(componentModel = "spring")
public interface SysOpsLogConvert {

    SysOpsLogDTO toDTO(SysOpsLog SysOpsLog);

    SysOpsLogDTO toDTO(SysOpsLogParam SysOpsLog);

    SysOpsLog toEntity(SysOpsLogDTO SysOpsLogDTO);

    SysOpsLog toEntity(SysOpsLogParam SysOpsLogDTO);

    SysOpsLogVO toVO(SysOpsLogDTO SysOpsLogDTO);
}
