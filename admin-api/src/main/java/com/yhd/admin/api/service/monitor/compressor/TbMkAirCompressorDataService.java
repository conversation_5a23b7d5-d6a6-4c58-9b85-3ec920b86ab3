package com.yhd.admin.api.service.monitor.compressor;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.monitor.compressor.dto.TbMkAirCompressorDataDTO;
import com.yhd.admin.api.domain.monitor.compressor.entity.TbMkAirCompressorData;
import com.yhd.admin.api.domain.monitor.compressor.vo.create.TbMkAirCompressorDataCreateVO;
import com.yhd.admin.api.domain.monitor.compressor.vo.page.TbMkAirCompressorDataPageVO;
import com.yhd.admin.api.domain.monitor.compressor.vo.update.TbMkAirCompressorDataUpdateVO;

import java.util.List;

/**
 * 空压机运行数据 服务类
 *
 * <AUTHOR>
 * @since
 */
public interface TbMkAirCompressorDataService  extends IService<TbMkAirCompressorData> {

    /**
     * 新建
     *
     * @param createVO 新建VO
     * @return id
     */
    Long create(TbMkAirCompressorDataCreateVO createVO);

    /**
     * 更新
     *
     * @param updateVO 更新VO
     */
    int update(TbMkAirCompressorDataUpdateVO updateVO);

    /**
     * 删除
     *
     * @param id id
     */
    int delete(Long id);

    /**
     * 查询单个
     *
     * @param id id
     * @return po
     */
    TbMkAirCompressorData get(Long id);

    /**
     * 分页查询
     *
     * @param pageVO 分页VO
     * @return pageResult
     */
    IPage<TbMkAirCompressorDataDTO> page(TbMkAirCompressorDataPageVO pageVO);

    /**
      * 批量删除
      *
      * @param ids 要删除的记录ID列表
      * @return 受影响的行数
      */
    int batchDelete(List<Long> ids);

    /**
     * 批量新增
     *
     * @param createVOs 新增VO列表
     * @return 新增的记录ID列表
     */
    int batchCreate(List<TbMkAirCompressorDataCreateVO> createVOs);

    /**
     * 批量更新
     *
     * @param updateVOs 更新VO列表
     * @return 受影响的行数
     */
    int batchUpdate(List<TbMkAirCompressorDataUpdateVO> updateVOs);
}
