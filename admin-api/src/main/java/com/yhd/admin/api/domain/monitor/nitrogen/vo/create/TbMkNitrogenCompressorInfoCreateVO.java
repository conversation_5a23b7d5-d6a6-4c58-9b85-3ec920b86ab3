package com.yhd.admin.api.domain.monitor.nitrogen.vo.create;

import lombok.Data;

import java.io.Serializable;

/**
 * 制氮系统空压机基本信息表
 *
 * <AUTHOR>
 * @since
 */
@Data
public class TbMkNitrogenCompressorInfoCreateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 所属制氮系统ID
     */
    private Long nitrogenSystemId;

    /**
     * 空压机名称
     */
    private String compressorName;

    /**
     * 空压机编号
     */
    private String compressorNumber;

    /**
     * 备注信息
     */
    private String remarks;

}
