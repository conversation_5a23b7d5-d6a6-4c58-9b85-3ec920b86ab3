package com.yhd.admin.api.domain.dispatch.query;

import com.yhd.admin.common.domain.query.QueryParam;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 智能调度中心-视频监控
 *
 * <AUTHOR>
 * @date 2025/7/31 09:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkDispatchMonitorParam extends QueryParam implements Serializable {
  /** 主键id列表 */
  private List<Long> ids;

  private Long id;
}
