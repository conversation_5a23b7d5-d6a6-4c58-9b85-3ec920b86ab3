package com.yhd.admin.api.domain.emergency.convert;

import com.yhd.admin.api.domain.emergency.dto.MkExpertBaseinfoDTO;
import com.yhd.admin.api.domain.emergency.entity.MkExpertBaseinfo;
import com.yhd.admin.api.domain.emergency.query.MkExpertBaseinfoParam;
import com.yhd.admin.api.domain.emergency.vo.MkExpertBaseinfoVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 应急救援中心-专家库转换类
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface MkExpertBaseinfoConvert {

    /**
     * Entity转DTO
     */
    MkExpertBaseinfoDTO toDTO(MkExpertBaseinfo entity);

    /**
     * Entity列表转DTO列表
     */
    List<MkExpertBaseinfoDTO> toDTO(List<MkExpertBaseinfo> entityList);

    /**
     * DTO转VO
     */
    MkExpertBaseinfoVO toVO(MkExpertBaseinfoDTO dto);

    /**
     * DTO列表转VO列表
     */
    List<MkExpertBaseinfoVO> toVO(List<MkExpertBaseinfoDTO> dtoList);

    /**
     * Param转Entity
     */
    MkExpertBaseinfo toEntity(MkExpertBaseinfoParam param);

    /**
     * Param列表转Entity列表
     */
    List<MkExpertBaseinfo> toEntity(List<MkExpertBaseinfoParam> paramList);
}
