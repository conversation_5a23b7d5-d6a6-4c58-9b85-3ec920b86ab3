package com.yhd.admin.api.service.emergency;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.emergency.dto.MkSnapshotInfoDTO;
import com.yhd.admin.api.domain.emergency.entity.MkSnapshotInfo;
import com.yhd.admin.api.domain.emergency.query.MkSnapshotInfoParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 快照信息服务接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface MkSnapshotInfoService extends IService<MkSnapshotInfo> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<MkSnapshotInfoDTO> pagingQuery(MkSnapshotInfoParam param);

    /**
     * 新增快照信息
     * @param param 快照信息参数
     * @return 是否成功
     */
    Boolean add(MkSnapshotInfoParam param);

    /**
     * 修改快照信息
     * @param param 快照信息参数
     * @return 是否成功
     */
    Boolean modify(MkSnapshotInfoParam param);

    /**
     * 获取快照信息详情
     * @param param 查询参数
     * @return 快照信息详情
     */
    MkSnapshotInfoDTO getCurrentDetail(MkSnapshotInfoParam param);

    /**
     * 批量删除快照信息
     * @param param 批量删除参数
     * @return 是否成功
     */
    Boolean removeBatch(BatchParam param);

    /**
     * 根据快照ID查询快照信息列表
     *
     * @param snapshotId 快照ID
     * @return 快照信息列表
     */
    List<MkSnapshotInfoDTO> getInfosBySnapshotId(Long snapshotId);

    /**
     * 根据快照ID和信息类型查询快照信息列表
     *
     * @param snapshotId 快照ID
     * @param infoType 信息类型
     * @return 快照信息列表
     */
    List<MkSnapshotInfoDTO> getInfosBySnapshotIdAndType(Long snapshotId, String infoType);

    /**
     * 根据快照ID和状态查询快照信息列表
     *
     * @param snapshotId 快照ID
     * @param status 状态
     * @return 快照信息列表
     */
    List<MkSnapshotInfoDTO> getInfosBySnapshotIdAndStatus(Long snapshotId, String status);

    /**
     * 根据快照ID和优先级查询快照信息列表
     *
     * @param snapshotId 快照ID
     * @param priorityLevel 优先级
     * @return 快照信息列表
     */
    List<MkSnapshotInfoDTO> getInfosBySnapshotIdAndPriority(Long snapshotId, Integer priorityLevel);

    /**
     * 根据列表类型查询快照信息列表
     *
     * @param param 查询参数
     * @return 快照信息列表
     */
    List<MkSnapshotInfoDTO> getInfosByListType(MkSnapshotInfoParam param);

    /**
     * 获取快照信息时间列表
     *
     * @param snapshotId 快照ID
     * @return 时间列表
     */
    List<MkSnapshotInfoDTO> getTimeList(Long snapshotId);

    /**
     * 获取快照信息事件列表
     *
     * @param snapshotId 快照ID
     * @return 事件列表
     */
    List<MkSnapshotInfoDTO> getEventList(Long snapshotId);

    /**
     * 根据列表类型查询快照信息列表
     *
     * @param snapshotId 快照ID
     * @param listType 列表类型(EVENT:事件列表,TIME:时间列表)
     * @return 快照信息列表
     */
    List<MkSnapshotInfoDTO> getInfosByListType(Long snapshotId, String listType);
}
