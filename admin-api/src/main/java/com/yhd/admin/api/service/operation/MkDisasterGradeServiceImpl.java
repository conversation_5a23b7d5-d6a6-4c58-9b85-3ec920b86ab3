package com.yhd.admin.api.service.operation;

import com.yhd.admin.api.dao.operation.MkDisasterGradeDao;
import com.yhd.admin.api.domain.operation.convert.MkDisasterGradeConvert;
import com.yhd.admin.api.domain.operation.dto.MkDisasterGradeDTO;
import com.yhd.admin.api.domain.operation.entity.MkDisasterGrade;
import com.yhd.admin.api.domain.operation.query.MkDisasterGradeParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.DicService;
import com.yhd.admin.common.constant.DicConstant;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;


/**
 * 算法模型-报警次级表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@Service
public class MkDisasterGradeServiceImpl extends ServiceImpl<MkDisasterGradeDao, MkDisasterGrade> implements MkDisasterGradeService {

    @Resource
    private MkDisasterGradeConvert convert;

    @Resource
    private MkDisasterGradeService service;

    @Resource
    private DicService dicService;

    @Override
    public IPage<MkDisasterGradeDTO> pagingQuery(MkDisasterGradeParam queryParam) {
        Page<MkDisasterGrade> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkDisasterGrade> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        //监测名称
        if (StringUtils.isBlank(queryParam.getMonitorName())) {
            throw new BMSException("error","监测名称不能为空");
        }
        queryChain.eq(MkDisasterGrade::getMonitorName, queryParam.getMonitorName());
        queryChain.orderByDesc(MkDisasterGrade::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkDisasterGradeParam param) {
        if (StringUtils.isNotBlank(param.getGradeCode())){
            param.setGradeName(dicService.transform(DicConstant.DISASTER_GRADE,param.getGradeCode()));
        }
        MkDisasterGrade entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MkDisasterGradeParam param) {
        if (StringUtils.isNotBlank(param.getGradeCode())){
            param.setGradeName(dicService.transform(DicConstant.DISASTER_GRADE,param.getGradeCode()));
        }
        MkDisasterGrade entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MkDisasterGradeDTO getCurrentDetail(MkDisasterGradeParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

}
