package com.yhd.admin.api.service.conduct.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.base.MkMiningAreaInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkMiningAreaInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkMiningAreaInfoParam;

import java.util.List;

/**
 * 采区信息服务接口
 *
 * <AUTHOR>
 * @since 2025-07-25 10:32:53
 */
public interface MkMiningAreaInfoService extends IService<MkMiningAreaInfo> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkMiningAreaInfoDTO> pagingQuery(MkMiningAreaInfoParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkMiningAreaInfoDTO> queryList(MkMiningAreaInfoParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkMiningAreaInfoParam param);

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    MkMiningAreaInfoDTO getCurrentDetails(MkMiningAreaInfoParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkMiningAreaInfoParam param);

}
