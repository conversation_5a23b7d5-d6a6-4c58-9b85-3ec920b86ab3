package com.yhd.admin.api.service.monitor.nitrogen.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.monitor.nitrogen.TbMkNitrogenParametersDao;
import com.yhd.admin.api.domain.monitor.nitrogen.convert.TbMkNitrogenParametersConvert;
import com.yhd.admin.api.domain.monitor.nitrogen.dto.TbMkNitrogenParametersDTO;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenParameters;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenParametersCreateVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.page.TbMkNitrogenParametersPageVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.update.TbMkNitrogenParametersUpdateVO;
import com.yhd.admin.api.service.monitor.nitrogen.TbMkNitrogenParametersService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 制氮系统参数表 服务实现类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbMkNitrogenParametersServiceImpl extends ServiceImpl<TbMkNitrogenParametersDao, TbMkNitrogenParameters> implements TbMkNitrogenParametersService {

    @Resource
    private TbMkNitrogenParametersConvert tbMkNitrogenParametersConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(TbMkNitrogenParametersCreateVO createVO) {
        TbMkNitrogenParameters tbMkNitrogenParameters = tbMkNitrogenParametersConvert.convert(createVO);
        baseMapper.insert(tbMkNitrogenParameters);
        return tbMkNitrogenParameters.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TbMkNitrogenParametersUpdateVO updateVO) {
        TbMkNitrogenParameters tbMkNitrogenParameters = tbMkNitrogenParametersConvert.convert(updateVO);
        return baseMapper.updateById(tbMkNitrogenParameters);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public TbMkNitrogenParameters get(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public IPage<TbMkNitrogenParametersDTO> page(TbMkNitrogenParametersPageVO param) {
        long currentPage = Optional.ofNullable(param.getCurrent()).orElse(1L);
        long pageSize = Optional.ofNullable(param.getPageSize()).orElse(10L);
        IPage<TbMkNitrogenParameters> iPage = new Page<>(currentPage, pageSize);
        LambdaQueryChainWrapper<TbMkNitrogenParameters> queryChainWrapper = new LambdaQueryChainWrapper<>(baseMapper);
        queryChainWrapper.eq(param.getNitrogenSystemId() != null, TbMkNitrogenParameters::getNitrogenSystemId, param.getNitrogenSystemId());
        queryChainWrapper.orderByDesc(TbMkNitrogenParameters::getId);
        return queryChainWrapper.page(iPage).convert(tbMkNitrogenParametersConvert::toDTO);
    }

    @Override
    public int batchDelete(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<TbMkNitrogenParametersCreateVO> createVOs) {
        if (createVOs == null || createVOs.isEmpty()) {
            log.warn("批量创建 制氮系统参数表：传入的列表为空");
            return 0;
        }
        List<TbMkNitrogenParameters> entities = createVOs.stream()
                .map(tbMkNitrogenParametersConvert::convert)
                .toList();
        boolean result = saveBatch(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量创建 制氮系统参数表 失败");
            throw new RuntimeException("批量创建失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdate(List<TbMkNitrogenParametersUpdateVO> updateVOs) {
        if (updateVOs == null || updateVOs.isEmpty()) {
            log.warn("批量更新 制氮系统参数表：传入的列表为空");
            return 0;
        }
        List<TbMkNitrogenParameters> entities = updateVOs.stream()
                .map(tbMkNitrogenParametersConvert::convert)
                .filter(entity -> entity.getId() != null)
                .toList();
        if (entities.isEmpty()) {
            log.warn("批量更新 制氮系统参数表：转换后无有效数据（缺少ID）");
            return 0;
        }
        boolean result = updateBatchById(entities);
        if (result) {
            return entities.size();
        } else {
            log.error("批量更新 制氮系统参数表 失败");
            throw new RuntimeException("批量更新失败");
        }
    }
}
