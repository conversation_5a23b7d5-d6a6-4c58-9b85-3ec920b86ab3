package com.yhd.admin.api.domain.emergency.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkAreaSafetyAssessmentVO.java
 * @Description 区域安全评估响应VO
 * @createTime 2025年08月02日 10:00:00
 */
@Data
public class MkAreaSafetyAssessmentVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 区域类型：工作面、机头、回风等
     */
    private String areaType;

    /**
     * 评估类型：粉尘监测、安全评估等
     */
    private String assessmentType;

    /**
     * 监测类型：粉尘监测、顶板检测
     */
    private String monitoringType;

    /**
     * 风险等级：低风险、中风险、高风险、极高风险
     */
    private String riskLevel;

    /**
     * 总评分
     */
    private Integer totalScore;

    /**
     * 最高分值
     */
    private Integer maxScore;

    /**
     * 评估结果：正常、异常、警告
     */
    private String assessmentResult;

    /**
     * 位置信息
     */
    private String location;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * X坐标
     */
    private BigDecimal coordinateX;

    /**
     * Y坐标
     */
    private BigDecimal coordinateY;

    /**
     * Z坐标
     */
    private BigDecimal coordinateZ;

    /**
     * 负责人
     */
    private String responsiblePerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 评估时间
     */
    private LocalDateTime assessmentDate;

    /**
     * 下次评估时间
     */
    private LocalDateTime nextAssessmentDate;

    /**
     * 状态：1-正常，2-异常，3-离线
     */
    private Integer status;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
