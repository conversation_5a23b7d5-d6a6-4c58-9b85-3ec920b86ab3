package com.yhd.admin.api.controller.monitor.nitrogen;

import com.yhd.admin.api.domain.monitor.nitrogen.convert.TbMkNitrogenCompressorInfoConvert;
import com.yhd.admin.api.domain.monitor.nitrogen.dto.TbMkNitrogenCompressorInfoDTO;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenCompressorInfo;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenCompressorInfoCreateVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.page.TbMkNitrogenCompressorInfoPageVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.update.TbMkNitrogenCompressorInfoUpdateVO;
import com.yhd.admin.api.service.monitor.nitrogen.TbMkNitrogenCompressorInfoService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 制氮系统空压机基本信息表 Controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/monitor/nitrogen/tbMkNitrogenCompressorInfo")
public class TbMkNitrogenCompressorInfoController {

    @Resource
    private TbMkNitrogenCompressorInfoService tbMkNitrogenCompressorInfoService;

    @Resource
    private TbMkNitrogenCompressorInfoConvert tbMkNitrogenCompressorInfoConvert;

    /**
     * 分页查询
     *
     * @param pageVO 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<TbMkNitrogenCompressorInfoDTO> page(@RequestBody @Valid TbMkNitrogenCompressorInfoPageVO pageVO) {
        log.debug("分页查询 制氮系统空压机基本信息表，参数：{}", pageVO);
        return new PageRespJson<>(tbMkNitrogenCompressorInfoService.page(pageVO));
    }

    /**
     * 新增
     *
     * @param createVO 创建参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> create(@RequestBody @Valid TbMkNitrogenCompressorInfoCreateVO createVO) {
        log.debug("新增 制氮系统空压机基本信息表，参数：{}", createVO);
        return RespJson.success(tbMkNitrogenCompressorInfoService.create(createVO));
    }

    /**
     * 更新
     *
     * @param updateVO 更新参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> update(@RequestBody @Valid TbMkNitrogenCompressorInfoUpdateVO updateVO) {
        log.debug("更新 制氮系统空压机基本信息表，参数：{}", updateVO);
        return RespJson.success(tbMkNitrogenCompressorInfoService.update(updateVO));
    }


    /**
      * 删除
      *
      * @param id 要删除的记录ID
      * @return 操作结果
      */
    @DeleteMapping("/{id}")
    public RespJson<?> delete(@PathVariable Long id) {
        log.debug("删除 制氮系统空压机基本信息表，ID：{}", id);
        return RespJson.success(tbMkNitrogenCompressorInfoService.delete(id));
    }

    /**
     * 查看详情
     *
     * @param id 要查看的记录ID
     * @return 详情信息
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<TbMkNitrogenCompressorInfoDTO> getDetail(@PathVariable Long id) {
        log.debug("查看详情 制氮系统空压机基本信息表，ID：{}", id);
        TbMkNitrogenCompressorInfo entity = tbMkNitrogenCompressorInfoService.get(id);
        if (entity == null) {
            return RespJson.failure("未找到对应记录");
        }
        return RespJson.success(tbMkNitrogenCompressorInfoConvert.toDTO(entity));
    }
    /**
     * 批量删除
     *
     * @param ids 要删除的记录ID列表
     * @return 操作结果
     */
    @PostMapping(value = "/batchDelete", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchDelete(@RequestBody List<Long> ids) {
        log.debug("批量删除 制氮系统空压机基本信息表，IDs：{}", ids);
        return RespJson.success(tbMkNitrogenCompressorInfoService.batchDelete(ids));
    }

    /**
     * 批量新增
     *
     * @param createVOs 新增参数列表
     * @return 新增的记录ID列表
     */
    @PostMapping(value = "/batchAdd", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchCreate(@RequestBody @Valid List<TbMkNitrogenCompressorInfoCreateVO> createVOs) {
        log.debug("批量新增 制氮系统空压机基本信息表，数量：{}", createVOs.size());
        return RespJson.success(tbMkNitrogenCompressorInfoService.batchCreate(createVOs));
    }

    /**
     * 批量更新
     *
     * @param updateVOs 更新参数列表
     * @return 更新的记录数量
     */
    @PostMapping(value = "/batchModify", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> batchUpdate(@RequestBody @Valid List<TbMkNitrogenCompressorInfoUpdateVO> updateVOs) {
        log.debug("批量更新 制氮系统空压机基本信息表，数量：{}", updateVOs.size());
        return RespJson.success(tbMkNitrogenCompressorInfoService.batchUpdate(updateVOs));
    }
}
