package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkOutputDutyDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputDuty;
import com.yhd.admin.api.domain.operation.query.MkOutputDutyParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 生产录入-值班人员表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
public interface MkOutputDutyService extends IService<MkOutputDuty> {

    IPage<MkOutputDutyDTO> pagingQuery(MkOutputDutyParam queryParam);

     Boolean add(MkOutputDutyParam param);

    Boolean modify(MkOutputDutyParam param);

    Boolean removeBatch(BatchParam param);

    MkOutputDutyDTO getCurrentDetail(MkOutputDutyParam param);
}
