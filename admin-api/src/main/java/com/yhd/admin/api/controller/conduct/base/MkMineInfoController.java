package com.yhd.admin.api.controller.conduct.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.query.base.MkMineInfoParam;
import com.yhd.admin.api.service.conduct.base.MkMineInfoService;
import com.yhd.admin.api.domain.conduct.convert.base.MkMineInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.base.MkMineInfoDTO;
import com.yhd.admin.api.domain.conduct.vo.base.MkMineInfoVO;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 矿井基础信息表控制层
 *
 * <AUTHOR>
 * @since 2025-07-25 10:34:53
 * @module 矿井基础信息
 */
@RestController
@RequestMapping("/conduct/mine")
public class MkMineInfoController {

    public final MkMineInfoService mkMineInfoService;
    public final MkMineInfoConvert mkMineInfoConvert;

    public MkMineInfoController(
        MkMineInfoService mkMineInfoService,
        MkMineInfoConvert mkMineInfoConvert
    ) {
        this.mkMineInfoService = mkMineInfoService;
        this.mkMineInfoConvert = mkMineInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkMineInfoParam param) {
        IPage<MkMineInfoDTO> page = mkMineInfoService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkMineInfoConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkMineInfoParam param) {
        return RespJson.success(mkMineInfoConvert.toVOList(mkMineInfoService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkMineInfoVO> getCurrentDetails(@RequestBody MkMineInfoParam param) {
        return RespJson.success(mkMineInfoConvert.toVO(mkMineInfoService.getCurrentDetails(param)));
    }


    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkMineInfoParam param) {
        Boolean retVal = mkMineInfoService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkMineInfoParam param) {
        Boolean retVal = mkMineInfoService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

