package com.yhd.admin.api.domain.emergency.query;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 应急救援监测信息表(传感器表)树形查询参数
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
public class MkEmergencySensorTreeParam implements Serializable {

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 父级传感器ID（可选，默认为0表示根目录）
     */
    private Integer parentId;

    /**
     * 传感器名称（模糊查询，可选）
     */
    private String sensorName;

    /**
     * 传感器类型（可选）
     */
    private String sensorType;

    /**
     * 层级类型（可选）
     */
    private String levelType;

    /**
     * 是否只查询叶子节点（可选）
     */
    private Boolean onlyLeaf;

    /**
     * 传感器状态（可选）
     */
    private Integer status;
}
