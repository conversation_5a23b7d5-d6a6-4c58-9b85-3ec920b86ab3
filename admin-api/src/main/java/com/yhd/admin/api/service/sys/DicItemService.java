package com.yhd.admin.api.service.sys;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.sys.dto.DicItemDTO;
import com.yhd.admin.api.domain.sys.entity.SysDicItem;
import com.yhd.admin.api.domain.sys.query.DicItemParam;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName DicItemService.java @Description TODO
 * @createTime 2020年05月20日 16:46:00
 */
public interface DicItemService extends IService<SysDicItem> {

    /**
     * 批量操作添加字典项
     *
     * @param params 集合
     * @return true 成功 false失败
     */
    Boolean saveOrUpdateBatch(List<DicItemParam> params);

    /**
     * 根据字典ID删除字典项
     *
     * @param id
     * @return true 成功 false失败
     */
    Boolean removeById(Long id);

    /**
     * 根据字典分类ID，查询该分类下所有的选项。
     *
     * @param param 查询参数
     * @return List<DicItemDTO>
     */
    List<DicItemDTO> queryDicItemByDicId(DicItemParam param);

    /**
     * 查询全部的数据字典。
     *
     * @return
     */
    List<DicItemDTO> queryAllDicItem();

    /**
     * 根据字典项ID更新状态
     *
     * @param id
     * @param status
     * @return
     */
    Boolean toggleItemStatus(Long id, Boolean status);
}
