package com.yhd.admin.api.domain.conduct.convert.base;

import com.yhd.admin.api.domain.conduct.dto.base.MkExcavationTeamInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.base.MkExcavationTeamInfo;
import com.yhd.admin.api.domain.conduct.query.base.MkExcavationTeamInfoParam;
import com.yhd.admin.api.domain.conduct.vo.base.MkExcavationTeamInfoVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 掘进队信息(MkExcavationTeamInfo)Convert
 *
 * <AUTHOR>
 * @since 2025-07-25 10:32:52
 */
@Mapper(componentModel = "spring")
public interface MkExcavationTeamInfoConvert {
    MkExcavationTeamInfo toEntity(MkExcavationTeamInfoParam param);

    MkExcavationTeamInfoDTO toDTO(MkExcavationTeamInfo entity);

    MkExcavationTeamInfoVO toVO(MkExcavationTeamInfoDTO dto);

    List<MkExcavationTeamInfoDTO> toDTOList(List<MkExcavationTeamInfo> entityList);

    List<MkExcavationTeamInfoVO> toVOList(List<MkExcavationTeamInfoDTO> dtoList);
}

