package com.yhd.admin.api.domain.operation.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产录入-生产系统内容表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MkOutputScxtDTO extends BaseDTO implements Cloneable, Serializable {
    /**
     * Id
     */
    private Long id;
	/**
	* 日期
	*/
	private Date outputDate;

	/**
	* 系统描述
	*/
	private String description;
}
