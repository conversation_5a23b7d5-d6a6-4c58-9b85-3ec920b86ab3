package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyHistoryDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyHistory;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyHistoryParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 应急历史事件记录表 Service
 * <AUTHOR>
 */
public interface MkEmergencyHistoryService extends IService<MkEmergencyHistory> {

    /**
     * 分页查询应急历史事件信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    IPage<MkEmergencyHistoryDTO> pagingQuery(MkEmergencyHistoryParam queryParam);

    /**
     * 新增应急历史事件信息
     * @param param 应急历史事件信息参数
     * @return 是否成功
     */
    Boolean add(MkEmergencyHistoryParam param);

    /**
     * 修改应急历史事件信息
     * @param param 应急历史事件信息参数
     * @return 是否成功
     */
    Boolean modify(MkEmergencyHistoryParam param);

    /**
     * 获取应急历史事件详情
     * @param param 查询参数
     * @return 应急历史事件详情
     */
    MkEmergencyHistoryDTO getCurrentDetail(MkEmergencyHistoryParam param);

    /**
     * 批量删除应急历史事件信息
     * @param param 批量删除参数
     * @return 是否成功
     */
    Boolean removeBatch(BatchParam param);
}
