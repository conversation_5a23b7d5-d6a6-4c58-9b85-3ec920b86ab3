package com.yhd.admin.api.domain.emergency.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 应急预案分类表 DTO
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkEmergencyPlanCategoryDTO extends BaseDTO implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 父级ID，0表示根节点
     */
    private Integer parentId;

    /**
     * 分类层级
     */
    private Integer categoryLevel;

    /**
     * 分类路径，如：1/2/3
     */
    private String categoryPath;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 分类图标
     */
    private String categoryIcon;

    /**
     * 是否叶子节点：0-否，1-是
     */
    private Boolean isLeaf;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 子分类列表
     */
    private List<MkEmergencyPlanCategoryDTO> children;
}
