package com.yhd.admin.api.controller.monitor.nitrogen;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yhd.admin.api.dao.monitor.nitrogen.TbMkNitrogenCompressorDataDao;
import com.yhd.admin.api.dao.monitor.nitrogen.TbMkNitrogenCompressorInfoDao;
import com.yhd.admin.api.dao.monitor.nitrogen.TbMkNitrogenParametersDao;
import com.yhd.admin.api.dao.monitor.nitrogen.TbMkNitrogenSystemMainDao;
import com.yhd.admin.api.domain.monitor.nitrogen.dto.TbMkNitrogenDTO;
import com.yhd.admin.api.domain.monitor.nitrogen.dto.TbMkNitrogenDetailDTO;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenCompressorData;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenCompressorInfo;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenParameters;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenSystemMain;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 制氮系统监控控制器
 */
@Slf4j
@RestController
@RequestMapping("/monitor/nitrogen/screen")
public class TbMkNitrogenController {

    @Resource
    private TbMkNitrogenSystemMainDao nitrogenSystemMainDao;

    @Resource
    private TbMkNitrogenCompressorInfoDao nitrogenCompressorInfoDao;

    @Resource
    private TbMkNitrogenCompressorDataDao nitrogenCompressorDataDao;

    @Resource
    private TbMkNitrogenParametersDao nitrogenParametersDao;

    /**
     * 获取所有制氮系统及其最新的空压机运行数据、制氮参数
     *
     * @return 包含制氮系统综合信息的列表
     */
    @GetMapping(value = "/info", produces = org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<TbMkNitrogenDTO>> getNitrogenInfo() {
        log.debug("获取制氮系统及其空压机、制氮参数");
        List<TbMkNitrogenDTO> result = new ArrayList<>();
        try {
            List<TbMkNitrogenSystemMain> systems = nitrogenSystemMainDao.selectList(null);
            for (TbMkNitrogenSystemMain system : systems) {
                TbMkNitrogenDTO dto = new TbMkNitrogenDTO();
                dto.setSystemId(system.getId());
                dto.setSystemName(system.getSystemName());
                dto.setControlBoxStatus(system.getControlBoxStatus());

                TbMkNitrogenParameters parameters = nitrogenParametersDao.selectOne(
                    new QueryWrapper<TbMkNitrogenParameters>()
                        .lambda()
                        .eq(TbMkNitrogenParameters::getNitrogenSystemId, system.getId())
                        .orderByDesc(TbMkNitrogenParameters::getUpdatedTime)
                        .last("limit 1")
                );
                if (parameters != null) {
                    BeanUtils.copyProperties(parameters, dto);
                }

                QueryWrapper<TbMkNitrogenCompressorInfo> infoQuery = new QueryWrapper<>();
                infoQuery.eq("nitrogen_system_id", system.getId());
                List<TbMkNitrogenCompressorInfo> compressorInfos = nitrogenCompressorInfoDao.selectList(infoQuery);

                List<TbMkNitrogenDetailDTO> compressorDTOs = new ArrayList<>();
                for (TbMkNitrogenCompressorInfo info : compressorInfos) {
                    TbMkNitrogenDetailDTO compDTO = new TbMkNitrogenDetailDTO();
                    BeanUtils.copyProperties(info, compDTO);
                    compDTO.setCompressorId(info.getId());

                    TbMkNitrogenCompressorData runtime = nitrogenCompressorDataDao.selectOne(
                        new QueryWrapper<TbMkNitrogenCompressorData>()
                            .lambda()
                            .eq(TbMkNitrogenCompressorData::getNitrogenCompressorInfoId, info.getId())
                            .orderByDesc(TbMkNitrogenCompressorData::getUpdatedTime)
                            .last("limit 1")
                    );
                    if (runtime != null) {
                        BeanUtils.copyProperties(runtime, compDTO);
                    }

                    compressorDTOs.add(compDTO);
                }
                dto.setCompressorList(compressorDTOs);
                result.add(dto);
            }

            return RespJson.success(result);

        } catch (Exception e) {
            log.error("查询制氮系统信息时发生异常", e);
            return RespJson.failure("查询失败");
        }
    }
}
