package com.yhd.admin.api.controller.operation;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.operation.convert.MkOutputSaleInfoConvert;
import com.yhd.admin.api.domain.operation.dto.MkOutputSaleInfoDTO;
import com.yhd.admin.api.domain.operation.query.MkOutputSaleInfoParam;
import com.yhd.admin.api.domain.operation.vo.MkOutputSaleInfoVO;
import com.yhd.admin.api.service.operation.MkOutputSaleInfoService;
import com.yhd.admin.common.domain.query.BatchParam;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 生产录入-产量销售库存表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@RestController
@RequestMapping("/output/saleInfo")
public class MkOutputSaleInfoController {

    @Resource
    private MkOutputSaleInfoConvert convert;

    @Resource
    private MkOutputSaleInfoService service;

    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkOutputSaleInfoVO> pagingQuery(@RequestBody MkOutputSaleInfoParam queryParam) {
        IPage<MkOutputSaleInfoDTO> iPage = service.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(
        value = "/getCurrentDetail",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> getCurrentDetail(@RequestBody MkOutputSaleInfoParam param) {
        return RespJson.success(convert.toVO(service.getCurrentDetail(param)));
    }

    @PostMapping(
        value = "/add",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson add(@RequestBody MkOutputSaleInfoParam param) {
        Boolean retVal = service.add(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(
        value = "/modify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody MkOutputSaleInfoParam param) {
        Boolean retVal = service.modify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    @PostMapping(
        value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam param) {
        Boolean retVal = service.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }
}
