package com.yhd.admin.api.domain.sys.convert;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Named;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName Transform.java @Description TODO
 * @createTime 2020年04月15日 16:46:00
 */
@Component
@Named("Transform")
public class Transform {

    @Named("checkIncludeLeafNode")
    public <T> Boolean checkIncludeLeafNode(List<T> nodeList) {
        return CollectionUtils.isEmpty(nodeList);
    }

    @Named("checkIsButton")
    public Boolean checkIsButton(String type) {
        return "2".equals(type);
    }

    @Named("negDisabled")
    public Boolean negDisabled(Boolean status) {
        return !status;
    }

    @Named("selectable")
    public <T> Boolean selectable(List<T> nodeList) {
        return CollectionUtils.isEmpty(nodeList);
    }

    @Named("checkable")
    public <T> Boolean checkable(List<T> nodeList) {
        return CollectionUtils.isEmpty(nodeList);
    }

    @Named("join")
    public String join(List<String> strList) {
        return StringUtils.join(strList, ",");
    }

    @Named("toList")
    public List<String> toList(String permission) {
        String[] arrays = StringUtils.split(permission, ",");
        if(arrays == null||arrays.length==0){
            return Collections.EMPTY_LIST;
        }
        return Arrays.stream(arrays).toList();
    }
}
