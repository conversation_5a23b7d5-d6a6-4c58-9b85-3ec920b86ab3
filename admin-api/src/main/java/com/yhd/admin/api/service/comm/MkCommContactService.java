package com.yhd.admin.api.service.comm;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.comm.dto.MkCommContactDTO;
import com.yhd.admin.api.domain.comm.entity.MkCommContact;
import com.yhd.admin.api.domain.comm.query.MkCommContactParam;

/**
 * 调度通讯-业务层接口
 *
 * <AUTHOR>
 * @date 2025/7/25 14:59
 */
public interface MkCommContactService extends IService<MkCommContact> {

  /**
   * 新增
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  Boolean add(MkCommContactParam param);

  /**
   * 编辑
   *
   * @param param 表单参数
   * @return true成功，false失败
   */
  Boolean modify(MkCommContactParam param);

  /**
   * 删除-根据主键
   *
   * @param param id
   * @return true成功，false失败
   */
  Boolean remove(MkCommContactParam param);

  /**
   * 查询详情信息
   *
   * @param param 主键id
   * @return 通讯详情信息
   */
  MkCommContactDTO getCurrentDetail(MkCommContactParam param);

  /**
   * 根据条件查询通讯信息分页列表
   *
   * @param param 查询条件
   * @return 通讯信息分页列表
   */
  IPage<MkCommContactDTO> pagingQuery(MkCommContactParam param);
}
