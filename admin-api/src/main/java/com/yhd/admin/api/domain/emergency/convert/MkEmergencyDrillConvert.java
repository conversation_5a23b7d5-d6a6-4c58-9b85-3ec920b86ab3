package com.yhd.admin.api.domain.emergency.convert;

import com.yhd.admin.api.domain.emergency.dto.MkEmergencyDrillDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyDrill;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyDrillParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyDrillVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 应急演练记录表转换类
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface MkEmergencyDrillConvert {

    /**
     * Entity转DTO
     */
    MkEmergencyDrillDTO toDTO(MkEmergencyDrill entity);

    /**
     * Entity列表转DTO列表
     */
    List<MkEmergencyDrillDTO> toDTO(List<MkEmergencyDrill> entityList);

    /**
     * DTO转VO
     */
    @Mapping(target = "drillStatusName", expression = "java(getDrillStatusName(dto.getDrillStatus()))")
    MkEmergencyDrillVO toVO(MkEmergencyDrillDTO dto);

    /**
     * DTO列表转VO列表
     */
    List<MkEmergencyDrillVO> toVO(List<MkEmergencyDrillDTO> dtoList);

    /**
     * Param转Entity
     */
    MkEmergencyDrill toEntity(MkEmergencyDrillParam param);

    /**
     * Param列表转Entity列表
     */
    List<MkEmergencyDrill> toEntity(List<MkEmergencyDrillParam> paramList);

    /**
     * 获取演练状态名称
     */
    default String getDrillStatusName(Integer drillStatus) {
        if (drillStatus == null) {
            return null;
        }
        switch (drillStatus) {
            case 1:
                return "计划中";
            case 2:
                return "进行中";
            case 3:
                return "已完成";
            case 4:
                return "已取消";
            default:
                return "未知";
        }
    }
}
