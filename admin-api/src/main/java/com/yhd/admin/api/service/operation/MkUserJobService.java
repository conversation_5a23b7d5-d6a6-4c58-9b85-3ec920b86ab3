package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkUserJobDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserJob;
import com.yhd.admin.api.domain.operation.query.MkUserJobParam;
import com.yhd.admin.common.domain.query.BatchParam;

import java.util.List;

/**
 * 运维配置中心-职务/工种管理-Service
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-27
 */
public interface MkUserJobService extends IService<MkUserJob> {

    IPage<MkUserJobDTO> pagingQuery(MkUserJobParam queryParam);

    Boolean addOrModify(MkUserJobParam param);

    Boolean removeBatch(BatchParam param);

    MkUserJobDTO getCurrentDetail(MkUserJobParam param);

    /**
     * 获取职务/工种集合
     *
     * @param param
     * @return
     */
    List<MkUserJobDTO> getJobList(MkUserJobParam param);
} 