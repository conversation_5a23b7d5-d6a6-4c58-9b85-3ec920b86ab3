package com.yhd.admin.api.domain.emergency.query;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yhd.admin.common.domain.query.QueryParam;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应急快照查询参数
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MkEmergencySnapshotParam extends QueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 快照名称
     */
    private String snapshotName;

    /**
     * 快照类型
     */
    private String snapshotType;

    /**
     * 矿井ID
     */
    private Long mineId;

    /**
     * 矿井名称
     */
    private String mineName;

    /**
     * 快照状态(ACTIVE:有效,INACTIVE:无效)
     */
    private String snapshotStatus;

    /**
     * 快照时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime snapshotTimeStart;

    /**
     * 快照时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime snapshotTimeEnd;

    /**
     * 设置时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime setTimeStart;

    /**
     * 设置时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime setTimeEnd;

    /**
     * 回溯时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime backtrackTimeStart;

    /**
     * 回溯时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime backtrackTimeEnd;
}
