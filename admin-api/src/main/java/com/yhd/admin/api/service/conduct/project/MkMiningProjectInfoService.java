package com.yhd.admin.api.service.conduct.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.project.MkMiningProjectInfoDTO;
import com.yhd.admin.api.domain.conduct.entity.project.MkMiningProjectInfo;
import com.yhd.admin.api.domain.conduct.query.project.MkMiningProjectInfoParam;

import java.util.List;

/**
 * 回采项目信息服务接口
 *
 * <AUTHOR>
 * @since 2025-07-30 14:05:52
 */
public interface MkMiningProjectInfoService extends IService<MkMiningProjectInfo> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkMiningProjectInfoDTO> pagingQuery(MkMiningProjectInfoParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkMiningProjectInfoDTO> queryList(MkMiningProjectInfoParam param);

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    Boolean addOrModify(MkMiningProjectInfoParam param);

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    MkMiningProjectInfoDTO getCurrentDetails(MkMiningProjectInfoParam param);

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    Boolean removeBatch(MkMiningProjectInfoParam param);

    /**
     * 提交审批
     *
     * @param param 数据
     * @return 是否成功
     */
    Boolean submit(MkMiningProjectInfoParam param);

    /**
     * 通过审批、驳回
     *
     * @param param 数据
     * @return 是否成功
     */
    Boolean handle(MkMiningProjectInfoParam param);
}
