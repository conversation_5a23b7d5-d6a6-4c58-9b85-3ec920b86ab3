package com.yhd.admin.api.domain.emergency.query;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.yhd.admin.common.domain.query.QueryParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 应急历史事件记录表查询参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
public class MkEmergencyHistoryParam extends QueryParam implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 事件编码
     */
    private String eventCode;

    /**
     * 事件名称（模糊查询）
     */
    private String eventName;

    /**
     * 事件位置（模糊查询）
     */
    private String eventLocation;

    /**
     * 事件类型：1-应急事件，2-演练事件
     */
    private Integer eventType;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 事件等级
     */
    private String eventLevel;

    /**
     * 事件状态：1-进行中，2-已完成，3-已关闭
     */
    private Integer eventStatus;

    /**
     * 事件时间
     */
    private LocalDateTime eventTime;

    /**
     * 处理人
     */
    private String handler;

    /**
     * 事件描述
     */
    private String description;

    /**
     * 处理方案
     */
    private String solution;

    /**
     * 处理结果
     */
    private String result;

    /**
     * 备注
     */
    private String remark;

    /**
     * 事件日期范围查询-开始日期
     */
    private LocalDate eventDateBegin;

    /**
     * 事件日期范围查询-结束日期
     */
    private LocalDate eventDateEnd;


}
