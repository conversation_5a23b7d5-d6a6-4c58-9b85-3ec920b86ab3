package com.yhd.admin.api.controller.monitor.ventilation;

import com.yhd.admin.api.domain.monitor.ventilation.convert.TbMkVentilationInfoConvert;
import com.yhd.admin.api.domain.monitor.ventilation.dto.TbMkVentilationInfoDTO;
import com.yhd.admin.api.domain.monitor.ventilation.entity.TbMkVentilationInfo;
import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkVentilationInfoCreateVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.page.TbMkVentilationInfoPageVO;
import com.yhd.admin.api.domain.monitor.ventilation.vo.update.TbMkVentilationInfoUpdateVO;
import com.yhd.admin.api.service.monitor.ventilation.TbMkVentilationInfoService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通风机基本信息 Controller
 * <AUTHOR>
 * @since
 */
@Slf4j
@RestController
@RequestMapping("/monitor/ventilation/tbMkVentilationInfo")
public class TbMkVentilationInfoController {

    @Resource
    private TbMkVentilationInfoService tbMkVentilationInfoService;

    @Resource
    private TbMkVentilationInfoConvert tbMkVentilationInfoConvert;

    /**
     * 分页查询
     *
     * @param pageVO 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<TbMkVentilationInfoDTO> page(@RequestBody @Valid TbMkVentilationInfoPageVO pageVO) {
        log.debug("分页查询 通风机基本信息，参数：{}", pageVO);
        return new PageRespJson<>(tbMkVentilationInfoService.page(pageVO));
    }

    /**
     * 新增
     *
     * @param createVO 创建参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> create(@RequestBody @Valid TbMkVentilationInfoCreateVO createVO) {
        log.debug("新增 通风机基本信息，参数：{}", createVO);
        return RespJson.success(tbMkVentilationInfoService.create(createVO));
    }

    /**
     * 更新
     *
     * @param updateVO 更新参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> update(@RequestBody @Valid TbMkVentilationInfoUpdateVO updateVO) {
        log.debug("更新 通风机基本信息，参数：{}", updateVO);
        return RespJson.success(tbMkVentilationInfoService.update(updateVO));
    }


    /**
      * 删除
      *
      * @param id 要删除的记录ID
      * @return 操作结果
      */
    @DeleteMapping("/{id}")
    public RespJson<?> delete(@PathVariable Long id) {
        log.debug("删除 通风机基本信息，ID：{}", id);
        return RespJson.success(tbMkVentilationInfoService.delete(id));
    }

    /**
     * 查看详情
     *
     * @param id 要查看的记录ID
     * @return 详情信息
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<TbMkVentilationInfoDTO> getDetail(@PathVariable Long id) {
        log.debug("查看详情 通风机基本信息，ID：{}", id);
        TbMkVentilationInfo entity = tbMkVentilationInfoService.get(id);
        if (entity == null) {
            return RespJson.failure("未找到对应记录");
        }
        return RespJson.success(tbMkVentilationInfoConvert.toDTO(entity));
    }
    /**
     * 批量删除
     *
     * @param ids 要删除的记录ID列表
     * @return 操作结果
     */
    @PostMapping(value = "/batchDelete", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchDelete(@RequestBody List<Long> ids) {
        log.debug("批量删除 通风机基本信息，IDs：{}", ids);
        return RespJson.success(tbMkVentilationInfoService.batchDelete(ids));
    }

    /**
     * 批量新增
     *
     * @param createVOs 新增参数列表
     * @return 新增的记录ID列表
     */
    @PostMapping(value = "/batchAdd", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> batchCreate(@RequestBody @Valid List<TbMkVentilationInfoCreateVO> createVOs) {
        log.debug("批量新增 通风机基本信息，数量：{}", createVOs.size());
        return RespJson.success(tbMkVentilationInfoService.batchCreate(createVOs));
    }

    /**
     * 批量更新
     *
     * @param updateVOs 更新参数列表
     * @return 更新的记录数量
     */
    @PostMapping(value = "/batchModify", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Integer> batchUpdate(@RequestBody @Valid List<TbMkVentilationInfoUpdateVO> updateVOs) {
        log.debug("批量更新 通风机基本信息，数量：{}", updateVOs.size());
        return RespJson.success(tbMkVentilationInfoService.batchUpdate(updateVOs));
    }
}
