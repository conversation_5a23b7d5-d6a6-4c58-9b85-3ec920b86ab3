package com.yhd.admin.api.domain.sys.vo;

import com.yhd.admin.common.domain.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SysFileTypeVO extends BaseVO {
  private Long id;
  /** 类型名称 */
  private String typeName;
  /** 父级ID */
  private Long parentId;
  /** 父级名称 */
  private String parentName;
  /** 状态 */
  private Boolean status;
  /** 排序，越小越靠前 */
  private Long sortNum;

  /** 子节点 */
  private List<SysFileTypeVO> children;
}
