package com.yhd.admin.api.service.sys;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.sys.dto.ClientDTO;
import com.yhd.admin.api.domain.sys.entity.SysClient;
import com.yhd.admin.api.domain.sys.query.ClientQueryParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/3 10:20
 */

public interface ClientService extends IService<SysClient> {

    /**
     * 新增客户端
     *
     * @param param
     * @return
     */
    Boolean saveOrUpdate(ClientQueryParam param);

    /**
     * 修改客户端
     *
     * @param param
     * @return
     */
    Boolean modifyClient(ClientQueryParam param);

    /**
     * 分页查询
     *
     * @param queryParam
     * @return
     */
    IPage<ClientDTO> pagingQuery(ClientQueryParam queryParam);

    /**
     * 根据ID查询详细信息
     *
     * @param queryParam
     * @return
     */
    ClientDTO currentDetail(ClientQueryParam queryParam);

    /**
     * 条件查询
     *
     * @param queryParam
     * @return List<ClientDTO>
     */
    List<ClientDTO> queryList(ClientQueryParam queryParam, Boolean check);

    Boolean removeBatch(BatchParam batchParam);

    Boolean checkIfNotExist(ClientQueryParam queryParam);
}
