package com.yhd.admin.api.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.emergency.MkEmergencySensorDao;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencySensorConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencySensorDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencySensor;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySensorParam;
import com.yhd.admin.api.service.emergency.MkEmergencySensorService;
import com.yhd.admin.common.domain.query.BatchParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应急救援监测信息表(传感器表) Service实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class MkEmergencySensorServiceImpl extends ServiceImpl<MkEmergencySensorDao, MkEmergencySensor>
        implements MkEmergencySensorService {

    @Resource
    private MkEmergencySensorConvert convert;

    @Override
    public IPage<MkEmergencySensorDTO> pagingQuery(MkEmergencySensorParam queryParam) {
        log.info("分页查询应急救援监测信息，参数：{}", queryParam);

        LambdaQueryWrapper<MkEmergencySensor> wrapper = buildQueryWrapper(queryParam);

        // 分页查询
        Page<MkEmergencySensor> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        IPage<MkEmergencySensor> entityPage = page(page, wrapper);

        // 转换为DTO
        IPage<MkEmergencySensorDTO> dtoPage = entityPage.convert(convert::toDTO);

        // 如果需要构建树形结构
        if (Boolean.TRUE.equals(queryParam.getBuildTree())) {
            List<MkEmergencySensorDTO> treeData = buildTree(dtoPage.getRecords());
            dtoPage.setRecords(treeData);
        }

        return dtoPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(MkEmergencySensorParam param) {
        log.info("新增应急救援监测信息，参数：{}", param);

        // 校验传感器编码唯一性
        if (!checkSensorCodeUnique(param.getSensorCode(), null)) {
            throw new RuntimeException("传感器编码已存在");
        }

        MkEmergencySensor entity = convert.toEntity(param);

        // 设置默认值
        if (entity.getSortOrder() == null) {
            entity.setSortOrder(0);
        }
        if (entity.getIsLeaf() == null) {
            entity.setIsLeaf(1);
        }
        if (entity.getStatus() == null) {
            entity.setStatus(1);
        }

        // 构建层级路径
        buildLevelPath(entity);

        return save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean modify(MkEmergencySensorParam param) {
        log.info("修改应急救援监测信息，参数：{}", param);

        if (param.getId() == null) {
            throw new RuntimeException("ID不能为空");
        }

        // 校验传感器编码唯一性
        if (StringUtils.hasText(param.getSensorCode()) &&
            !checkSensorCodeUnique(param.getSensorCode(), param.getId())) {
            throw new RuntimeException("传感器编码已存在");
        }

        MkEmergencySensor entity = getById(param.getId());
        if (entity == null) {
            throw new RuntimeException("记录不存在");
        }

        convert.updateEntity(param, entity);

        // 重新构建层级路径
        buildLevelPath(entity);

        return updateById(entity);
    }

    @Override
    public MkEmergencySensorDTO getCurrentDetail(MkEmergencySensorParam param) {
        log.info("获取应急救援监测信息详情，参数：{}", param);

        if (param.getId() == null) {
            throw new RuntimeException("ID不能为空");
        }

        MkEmergencySensor entity = getById(param.getId());
        if (entity == null) {
            return null;
        }

        MkEmergencySensorDTO dto = convert.toDTO(entity);

        // 设置父节点名称
        if (entity.getParentId() != null) {
            MkEmergencySensor parent = getById(entity.getParentId());
            if (parent != null) {
                dto.setParentName(parent.getSensorName());
            }
        }

        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        log.info("批量删除应急救援监测信息，参数：{}", param);

        if (CollectionUtils.isEmpty(param.getId())) {
            throw new RuntimeException("删除ID列表不能为空");
        }

        List<Integer> ids = param.getId().stream()
                .map(Long::intValue)
                .collect(Collectors.toList());

        // 检查是否有子节点
        for (Integer id : ids) {
            LambdaQueryWrapper<MkEmergencySensor> childWrapper = new LambdaQueryWrapper<>();
            childWrapper.eq(MkEmergencySensor::getParentId, id);

            long childCount = count(childWrapper);
            if (childCount > 0) {
                throw new RuntimeException("存在子节点，无法删除");
            }
        }

        return removeByIds(ids);
    }

    @Override
    public List<MkEmergencySensorDTO> getSensorTree(String orgCode, Integer parentId) {
        log.info("获取传感器树形结构，组织编码：{}，父级ID：{}", orgCode, parentId);

        LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.hasText(orgCode), MkEmergencySensor::getOrgCode, orgCode);
        wrapper.eq(MkEmergencySensor::getParentId, parentId);
        wrapper.orderByAsc(MkEmergencySensor::getSortOrder);

        List<MkEmergencySensor> entities = list(wrapper);
        List<MkEmergencySensorDTO> dtoList = convert.toDTO(entities);

        // 递归构建子节点
        for (MkEmergencySensorDTO dto : dtoList) {
            if (dto.getIsLeaf() != null && dto.getIsLeaf() == 0) {
                List<MkEmergencySensorDTO> children = getSensorTree(orgCode, dto.getId());
                dto.setChildren(children);
            }
        }

        return dtoList;
    }

    @Override
    public MkEmergencySensorDTO getBySensorCode(String sensorCode) {
        log.info("根据传感器编码查询，传感器编码：{}", sensorCode);

        LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkEmergencySensor::getSensorCode, sensorCode);
        wrapper.last("LIMIT 1");

        MkEmergencySensor entity = getOne(wrapper);
        return entity != null ? convert.toDTO(entity) : null;
    }

    @Override
    public List<MkEmergencySensorDTO> getAbnormalSensors(String orgCode) {
        log.info("查询异常传感器，组织编码：{}", orgCode);

        LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MkEmergencySensor::getStatus, Arrays.asList(2, 3)); // 异常或离线状态
        wrapper.eq(StringUtils.hasText(orgCode), MkEmergencySensor::getOrgCode, orgCode);
        wrapper.orderByDesc(MkEmergencySensor::getStatus);

        List<MkEmergencySensor> entities = list(wrapper);
        return convert.toDTO(entities);
    }

    @Override
    public List<MkEmergencySensorDTO> getOutOfRangeSensors(String orgCode) {
        log.info("查询超出阈值的传感器，组织编码：{}", orgCode);

        // 使用QueryWrapper实现复杂条件
        LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkEmergencySensor::getIsLeaf, 1);
        wrapper.isNotNull(MkEmergencySensor::getCurrentValue);
        wrapper.and(w ->
            w.apply("(threshold_min IS NOT NULL AND current_value < threshold_min) OR (threshold_max IS NOT NULL AND current_value > threshold_max)")
        );
        wrapper.eq(StringUtils.hasText(orgCode), MkEmergencySensor::getOrgCode, orgCode);

        List<MkEmergencySensor> entities = list(wrapper);
        return convert.toDTO(entities);
    }

    @Override
    public List<MkEmergencySensorDTO> getByLevelType(String levelType, String orgCode) {
        log.info("根据层级类型查询传感器，层级类型：{}，组织编码：{}", levelType, orgCode);

        LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkEmergencySensor::getLevelType, levelType);
        wrapper.eq(StringUtils.hasText(orgCode), MkEmergencySensor::getOrgCode, orgCode);
        wrapper.orderByAsc(MkEmergencySensor::getSortOrder);

        List<MkEmergencySensor> entities = list(wrapper);
        return convert.toDTO(entities);
    }

    @Override
    public List<MkEmergencySensorDTO> getByWorkFace(String workFace, String orgCode) {
        log.info("根据工作面查询传感器，工作面：{}，组织编码：{}", workFace, orgCode);

        LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkEmergencySensor::getWorkFace, workFace);
        wrapper.eq(StringUtils.hasText(orgCode), MkEmergencySensor::getOrgCode, orgCode);
        wrapper.orderByAsc(MkEmergencySensor::getSortOrder);

        List<MkEmergencySensor> entities = list(wrapper);
        return convert.toDTO(entities);
    }

    @Override
    public List<MkEmergencySensorDTO> getByTunnel(String tunnel, String orgCode) {
        log.info("根据巷道查询传感器，巷道：{}，组织编码：{}", tunnel, orgCode);

        LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkEmergencySensor::getTunnel, tunnel);
        wrapper.eq(StringUtils.hasText(orgCode), MkEmergencySensor::getOrgCode, orgCode);
        wrapper.orderByAsc(MkEmergencySensor::getSortOrder);

        List<MkEmergencySensor> entities = list(wrapper);
        return convert.toDTO(entities);
    }

    @Override
    public List<MkEmergencySensorDTO> getBySensorType(String sensorType, String orgCode) {
        log.info("根据传感器类型查询传感器，传感器类型：{}，组织编码：{}", sensorType, orgCode);

        LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkEmergencySensor::getSensorType, sensorType);
        wrapper.eq(StringUtils.hasText(orgCode), MkEmergencySensor::getOrgCode, orgCode);
        wrapper.orderByAsc(MkEmergencySensor::getSortOrder);

        List<MkEmergencySensor> entities = list(wrapper);
        return convert.toDTO(entities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(Integer id, Integer status) {
        log.info("更新传感器状态，ID：{}，状态：{}", id, status);

        MkEmergencySensor entity = new MkEmergencySensor();
        entity.setId(id);
        entity.setStatus(status);

        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateStatus(List<Integer> ids, Integer status) {
        log.info("批量更新传感器状态，IDs：{}，状态：{}", ids, status);

        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }

        // 使用MyBatis-Plus的批量更新
        List<MkEmergencySensor> entities = ids.stream().map(id -> {
            MkEmergencySensor entity = new MkEmergencySensor();
            entity.setId(id);
            entity.setStatus(status);
            return entity;
        }).collect(Collectors.toList());

        return updateBatchById(entities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCurrentValue(String sensorCode, BigDecimal currentValue) {
        log.info("更新传感器当前值，传感器编码：{}，当前值：{}", sensorCode, currentValue);

        // 使用MyBatis-Plus查询
        LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkEmergencySensor::getSensorCode, sensorCode);

        MkEmergencySensor entity = getOne(wrapper);
        if (entity == null) {
            log.warn("传感器不存在，传感器编码：{}", sensorCode);
            return false;
        }

        entity.setCurrentValue(currentValue);

        // 根据阈值判断状态
        if (currentValue != null) {
            if ((entity.getThresholdMin() != null && currentValue.compareTo(entity.getThresholdMin()) < 0) ||
                (entity.getThresholdMax() != null && currentValue.compareTo(entity.getThresholdMax()) > 0)) {
                entity.setStatus(2); // 异常
            } else {
                entity.setStatus(1); // 正常
            }
        }

        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateCurrentValue(Map<String, BigDecimal> sensorDataMap) {
        log.info("批量更新传感器当前值，数据量：{}", sensorDataMap.size());

        if (CollectionUtils.isEmpty(sensorDataMap)) {
            return false;
        }

        for (Map.Entry<String, BigDecimal> entry : sensorDataMap.entrySet()) {
            updateCurrentValue(entry.getKey(), entry.getValue());
        }

        return true;
    }

    @Override
    public Map<String, Long> countByStatus(String orgCode) {
        log.info("统计各状态传感器数量，组织编码：{}", orgCode);

        // 使用MyBatis-Plus的分组查询
        LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkEmergencySensor::getIsLeaf, 1); // 只统计叶子节点（实际传感器）
        wrapper.eq(StringUtils.hasText(orgCode), MkEmergencySensor::getOrgCode, orgCode);

        List<MkEmergencySensor> entities = list(wrapper);

        // 手动分组统计
        Map<String, Long> result = entities.stream()
                .collect(Collectors.groupingBy(
                        entity -> String.valueOf(entity.getStatus()),
                        Collectors.counting()
                ));

        return result;
    }

    @Override
    public Map<String, Long> countBySensorType(String orgCode) {
        log.info("统计各类型传感器数量，组织编码：{}", orgCode);

        // 使用MyBatis-Plus的分组查询
        LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkEmergencySensor::getIsLeaf, 1); // 只统计叶子节点（实际传感器）
        wrapper.eq(StringUtils.hasText(orgCode), MkEmergencySensor::getOrgCode, orgCode);

        List<MkEmergencySensor> entities = list(wrapper);

        // 手动分组统计
        Map<String, Long> result = entities.stream()
                .collect(Collectors.groupingBy(
                        MkEmergencySensor::getSensorType,
                        Collectors.counting()
                ));

        return result;
    }

    @Override
    public Map<String, Object> getMonitorOverview(String orgCode) {
        log.info("获取传感器监控概览，组织编码：{}", orgCode);

        Map<String, Object> overview = new HashMap<>();

        // 统计各状态数量
        Map<String, Long> statusCount = countByStatus(orgCode);
        overview.put("statusCount", statusCount);

        // 统计各类型数量
        Map<String, Long> typeCount = countBySensorType(orgCode);
        overview.put("typeCount", typeCount);

        // 总数量
        long totalCount = statusCount.values().stream().mapToLong(Long::longValue).sum();
        overview.put("totalCount", totalCount);

        // 异常数量
        long abnormalCount = statusCount.getOrDefault("2", 0L) + statusCount.getOrDefault("3", 0L);
        overview.put("abnormalCount", abnormalCount);

        // 正常率
        double normalRate = totalCount > 0 ? (double) statusCount.getOrDefault("1", 0L) / totalCount * 100 : 0;
        overview.put("normalRate", Math.round(normalRate * 100.0) / 100.0);

        return overview;
    }

    @Override
    public Boolean checkSensorCodeUnique(String sensorCode, Integer excludeId) {
        if (!StringUtils.hasText(sensorCode)) {
            return true;
        }

        LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MkEmergencySensor::getSensorCode, sensorCode);
        wrapper.ne(excludeId != null, MkEmergencySensor::getId, excludeId);

        return count(wrapper) == 0;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<MkEmergencySensor> buildQueryWrapper(MkEmergencySensorParam param) {
        LambdaQueryWrapper<MkEmergencySensor> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(param.getId() != null, MkEmergencySensor::getId, param.getId());
        wrapper.eq(StringUtils.hasText(param.getSensorCode()), MkEmergencySensor::getSensorCode, param.getSensorCode());
        wrapper.like(StringUtils.hasText(param.getSensorName()), MkEmergencySensor::getSensorName, param.getSensorName());
        wrapper.eq(StringUtils.hasText(param.getSensorType()), MkEmergencySensor::getSensorType, param.getSensorType());
        wrapper.like(StringUtils.hasText(param.getLocation()), MkEmergencySensor::getLocation, param.getLocation());
        wrapper.like(StringUtils.hasText(param.getWorkFace()), MkEmergencySensor::getWorkFace, param.getWorkFace());
        wrapper.like(StringUtils.hasText(param.getTunnel()), MkEmergencySensor::getTunnel, param.getTunnel());
        wrapper.eq(param.getParentId() != null, MkEmergencySensor::getParentId, param.getParentId());
        wrapper.eq(StringUtils.hasText(param.getLevelType()), MkEmergencySensor::getLevelType, param.getLevelType());
        wrapper.eq(StringUtils.hasText(param.getLevelCode()), MkEmergencySensor::getLevelCode, param.getLevelCode());
        wrapper.like(StringUtils.hasText(param.getLevelName()), MkEmergencySensor::getLevelName, param.getLevelName());
        wrapper.like(StringUtils.hasText(param.getLevelPath()), MkEmergencySensor::getLevelPath, param.getLevelPath());
        wrapper.eq(param.getIsLeaf() != null, MkEmergencySensor::getIsLeaf, param.getIsLeaf());
        wrapper.eq(param.getStatus() != null, MkEmergencySensor::getStatus, param.getStatus());
        wrapper.eq(StringUtils.hasText(param.getOrgCode()), MkEmergencySensor::getOrgCode, param.getOrgCode());
        wrapper.ge(param.getCurrentValueMin() != null, MkEmergencySensor::getCurrentValue, param.getCurrentValueMin());
        wrapper.le(param.getCurrentValueMax() != null, MkEmergencySensor::getCurrentValue, param.getCurrentValueMax());
        wrapper.eq(StringUtils.hasText(param.getUnit()), MkEmergencySensor::getUnit, param.getUnit());
        wrapper.like(StringUtils.hasText(param.getDescription()), MkEmergencySensor::getDescription, param.getDescription());
        wrapper.like(StringUtils.hasText(param.getRemark()), MkEmergencySensor::getRemark, param.getRemark());

        // 多状态查询
        wrapper.in(!CollectionUtils.isEmpty(param.getStatusList()), MkEmergencySensor::getStatus, param.getStatusList());

        // 多类型查询
        wrapper.in(!CollectionUtils.isEmpty(param.getSensorTypeList()), MkEmergencySensor::getSensorType, param.getSensorTypeList());

        // 多层级查询
        wrapper.in(!CollectionUtils.isEmpty(param.getLevelTypeList()), MkEmergencySensor::getLevelType, param.getLevelTypeList());

        // 只查询异常传感器
        if (Boolean.TRUE.equals(param.getOnlyAbnormal())) {
            wrapper.in(MkEmergencySensor::getStatus, Arrays.asList(2, 3));
        }

        // 排序
        if (StringUtils.hasText(param.getOrderBy())) {
            boolean isAsc = !"DESC".equalsIgnoreCase(param.getOrderDirection());
            switch (param.getOrderBy()) {
                case "sensorCode":
                    wrapper.orderBy(true, isAsc, MkEmergencySensor::getSensorCode);
                    break;
                case "sensorName":
                    wrapper.orderBy(true, isAsc, MkEmergencySensor::getSensorName);
                    break;
                case "status":
                    wrapper.orderBy(true, isAsc, MkEmergencySensor::getStatus);
                    break;
                case "currentValue":
                    wrapper.orderBy(true, isAsc, MkEmergencySensor::getCurrentValue);
                    break;
                default:
                    wrapper.orderByAsc(MkEmergencySensor::getSortOrder);
                    break;
            }
        } else {
            wrapper.orderByAsc(MkEmergencySensor::getSortOrder);
        }

        return wrapper;
    }

    /**
     * 构建层级路径
     */
    private void buildLevelPath(MkEmergencySensor entity) {
        if (entity.getParentId() == null) {
            // 根节点
            entity.setLevelPath("/" + entity.getLevelType());
        } else {
            // 子节点，获取父节点路径
            MkEmergencySensor parent = getById(entity.getParentId());
            if (parent != null && StringUtils.hasText(parent.getLevelPath())) {
                entity.setLevelPath(parent.getLevelPath() + "/" + entity.getLevelType() + "_" + entity.getLevelCode());
            }
        }
    }

    /**
     * 构建树形结构
     */
    private List<MkEmergencySensorDTO> buildTree(List<MkEmergencySensorDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        // 按父级ID分组
        Map<Integer, List<MkEmergencySensorDTO>> parentMap = list.stream()
                .collect(Collectors.groupingBy(dto -> dto.getParentId() == null ? -1 : dto.getParentId()));

        // 构建树形结构
        List<MkEmergencySensorDTO> roots = parentMap.getOrDefault(-1, new ArrayList<>());
        buildChildren(roots, parentMap);

        return roots;
    }

    /**
     * 递归构建子节点
     */
    private void buildChildren(List<MkEmergencySensorDTO> parents, Map<Integer, List<MkEmergencySensorDTO>> parentMap) {
        for (MkEmergencySensorDTO parent : parents) {
            List<MkEmergencySensorDTO> children = parentMap.get(parent.getId());
            if (!CollectionUtils.isEmpty(children)) {
                parent.setChildren(children);
                buildChildren(children, parentMap);
            }
        }
    }
}
