package com.yhd.admin.api.domain.emergency.convert;

import com.yhd.admin.api.domain.emergency.dto.MkEmergencyContactDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencyContact;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyContactParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyContactVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 应急救援通讯录表转换类
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface MkEmergencyContactConvert {

    /**
     * Entity转DTO
     */
    MkEmergencyContactDTO toDTO(MkEmergencyContact entity);

    /**
     * Entity列表转DTO列表
     */
    List<MkEmergencyContactDTO> toDTO(List<MkEmergencyContact> entityList);

    /**
     * DTO转VO
     */
    @Mapping(target = "contactTypeName", expression = "java(getContactTypeName(dto.getContactType()))")
    @Mapping(target = "dutyStatusName", expression = "java(getDutyStatusName(dto.getDutyStatus()))")
    @Mapping(target = "isEmergencyLeaderName", expression = "java(getIsEmergencyLeaderName(dto.getIsEmergencyLeader()))")
    MkEmergencyContactVO toVO(MkEmergencyContactDTO dto);

    /**
     * DTO列表转VO列表
     */
    List<MkEmergencyContactVO> toVO(List<MkEmergencyContactDTO> dtoList);

    /**
     * Param转Entity
     */
    MkEmergencyContact toEntity(MkEmergencyContactParam param);

    /**
     * Param列表转Entity列表
     */
    List<MkEmergencyContact> toEntity(List<MkEmergencyContactParam> paramList);

    /**
     * 获取联系人类型名称
     */
    default String getContactTypeName(Integer contactType) {
        if (contactType == null) {
            return null;
        }
        switch (contactType) {
            case 1:
                return "内部人员";
            case 2:
                return "外部联系人";
            case 3:
                return "应急专家";
            default:
                return "未知";
        }
    }

    /**
     * 获取在职状态名称
     */
    default String getDutyStatusName(Integer dutyStatus) {
        if (dutyStatus == null) {
            return null;
        }
        switch (dutyStatus) {
            case 1:
                return "在职";
            case 2:
                return "离职";
            case 3:
                return "调岗";
            default:
                return "未知";
        }
    }

    /**
     * 获取是否应急负责人名称
     */
    default String getIsEmergencyLeaderName(Boolean isEmergencyLeader) {
        if (isEmergencyLeader == null) {
            return "否";
        }
        return isEmergencyLeader ? "是" : "否";
    }
}
