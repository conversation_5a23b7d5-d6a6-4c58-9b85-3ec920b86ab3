package com.yhd.admin.api.domain.safe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


@EqualsAndHashCode(callSuper = true)
@Data
public class MkZhFc extends BaseEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String str;
}
