package com.yhd.admin.api.service.sys;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.sys.UserPinDao;
import com.yhd.admin.common.domain.dto.UserPinDTO;
import com.yhd.admin.common.domain.entity.SysUserPin;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName UserAccountServiceImpl.java @Description TODO 用户账户操作类
 * @createTime 2020年03月30日 11:15:00
 */
@Service
public class UserPinSrvImpl extends ServiceImpl<UserPinDao, SysUserPin> implements UserPinSrv {

    private final PasswordEncoder passwordEncoder;

    public UserPinSrvImpl(PasswordEncoder passwordEncoder) {
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    public UserPinDTO getUserPin(String uid, String username) {
        SysUserPin userPin = this.getById(DigestUtils.md5Hex(uid + username).toUpperCase());
        return UserPinDTO.builder().uid(userPin.getUid()).password(userPin.getPassword()).build();
    }

    @Override
    public Boolean saveOrUpdate(String uid, String username, String password) {
        String id = transformToUid(uid + username);
        SysUserPin entity = new SysUserPin();
        entity.setUid(id);
        entity.setPassword(passwordEncoder.encode(password));
        return super.saveOrUpdate(entity);
    }

    @Override
    public Boolean removeBatch(List<String> list) {
        return super.removeBatchByIds(list.stream().map(this::transformToUid).collect(Collectors.toList()));
    }

    @Override
    public Boolean resetPassWord(String uid, String username, String passWord) {
        return this.saveOrUpdate(uid, username, passWord);
    }

    protected String transformToUid(String uidAndUsername) {

        return DigestUtils.md5Hex(uidAndUsername).toUpperCase();
    }
}
