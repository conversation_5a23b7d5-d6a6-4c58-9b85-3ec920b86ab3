package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkEmergencySurrounding.java
 * @Description 应急救援周边信息表
 * @createTime 2025年01月20日 14:00:00
 */
@Data
@TableName("tb_mk_emergency_surrounding")
public class MkEmergencySurrounding {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 项目编码
     */
    private String itemCode;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 项目类型：人员、分站、视频广播、电话安监
     */
    private String itemType;

    /**
     * 位置信息
     */
    private String location;

    /**
     * 状态：1-正常，2-异常，3-离线
     */
    private Integer status;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * X坐标
     */
    private BigDecimal coordinateX;

    /**
     * Y坐标
     */
    private BigDecimal coordinateY;

    /**
     * Z坐标
     */
    private BigDecimal coordinateZ;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}