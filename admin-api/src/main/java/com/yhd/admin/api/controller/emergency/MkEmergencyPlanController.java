package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyPlanConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyPlanDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyPlanParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyPlanVO;
import com.yhd.admin.api.service.emergency.MkEmergencyPlanService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应急救援数字预案表 Controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/emergency/plan")
public class MkEmergencyPlanController {

    @Resource
    private MkEmergencyPlanService emergencyPlanService;

    @Resource
    private MkEmergencyPlanConvert convert;

    /**
     * 分页查询应急救援数字预案信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkEmergencyPlanVO> pagingQuery(@RequestBody MkEmergencyPlanParam queryParam) {
        log.info("分页查询应急救援数字预案信息，参数：{}", queryParam);
        IPage<MkEmergencyPlanDTO> iPage = emergencyPlanService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    /**
     * 新增应急救援数字预案信息
     * @param param 应急救援数字预案信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkEmergencyPlanParam param) {
        log.info("新增应急救援数字预案信息，参数：{}", param);
        Boolean result = emergencyPlanService.add(param);
        return RespJson.success(result);
    }

    /**
     * 修改应急救援数字预案信息
     * @param param 应急救援数字预案信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> modify(@RequestBody MkEmergencyPlanParam param) {
        log.info("修改应急救援数字预案信息，参数：{}", param);
        Boolean result = emergencyPlanService.modify(param);
        return RespJson.success(result);
    }

    /**
     * 获取应急救援数字预案详情
     * @param param 查询参数
     * @return 应急救援数字预案详情
     */
    @PostMapping(value = "/getCurrentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkEmergencyPlanVO> getCurrentDetail(@RequestBody MkEmergencyPlanParam param) {
        log.info("获取应急救援数字预案详情，参数：{}", param);
        MkEmergencyPlanDTO dto = emergencyPlanService.getCurrentDetail(param);
        return RespJson.success(convert.toVO(dto));
    }

    /**
     * 批量删除应急救援数字预案信息
     * @param param 批量删除参数
     * @return 操作结果
     */
    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody BatchParam param) {
        log.info("批量删除应急救援数字预案信息，参数：{}", param);
        Boolean result = emergencyPlanService.removeBatch(param);
        return RespJson.success(result);
    }

    /**
     * 根据预案类型查询预案列表
     * @param planType 预案类型
     * @return 预案列表
     */
    @GetMapping(value = "/getPlansByType", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanVO>> getPlansByType(@RequestParam String planType) {
        log.info("根据预案类型查询预案列表，预案类型：{}", planType);
        List<MkEmergencyPlanDTO> dtoList = emergencyPlanService.getPlansByType(planType);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 发布预案
     * @param planId 预案ID
     * @return 操作结果
     */
    @PostMapping(value = "/publishPlan", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> publishPlan(@RequestParam Integer planId) {
        log.info("发布预案，预案ID：{}", planId);
        Boolean result = emergencyPlanService.publishPlan(planId);
        return RespJson.success(result);
    }

    /**
     * 废止预案
     * @param planId 预案ID
     * @return 操作结果
     */
    @PostMapping(value = "/abolishPlan", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> abolishPlan(@RequestParam Integer planId) {
        log.info("废止预案，预案ID：{}", planId);
        Boolean result = emergencyPlanService.abolishPlan(planId);
        return RespJson.success(result);
    }

    /**
     * 获取即将到期的预案列表
     * @param orgCode 组织编码
     * @return 即将到期的预案列表
     */
    @GetMapping(value = "/getExpiringSoonPlans", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanVO>> getExpiringSoonPlans(@RequestParam String orgCode) {
        log.info("获取即将到期的预案列表，组织编码：{}", orgCode);
        List<MkEmergencyPlanDTO> dtoList = emergencyPlanService.getExpiringSoonPlans(orgCode);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 获取需要演练的预案列表
     * @param orgCode 组织编码
     * @return 需要演练的预案列表
     */
    @GetMapping(value = "/getNeedsDrillPlans", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanVO>> getNeedsDrillPlans(@RequestParam String orgCode) {
        log.info("获取需要演练的预案列表，组织编码：{}", orgCode);
        List<MkEmergencyPlanDTO> dtoList = emergencyPlanService.getNeedsDrillPlans(orgCode);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 获取需要评审的预案列表
     * @param orgCode 组织编码
     * @return 需要评审的预案列表
     */
    @GetMapping(value = "/getNeedsReviewPlans", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanVO>> getNeedsReviewPlans(@RequestParam String orgCode) {
        log.info("获取需要评审的预案列表，组织编码：{}", orgCode);
        List<MkEmergencyPlanDTO> dtoList = emergencyPlanService.getNeedsReviewPlans(orgCode);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据分类ID查询预案列表
     * @param categoryId 分类ID
     * @return 预案列表
     */
    @GetMapping(value = "/getPlansByCategoryId", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanVO>> getPlansByCategoryId(@RequestParam Integer categoryId) {
        log.info("根据分类ID查询预案列表，分类ID：{}", categoryId);
        List<MkEmergencyPlanDTO> dtoList = emergencyPlanService.getPlansByCategoryId(categoryId);
        return RespJson.success(convert.toVO(dtoList));
    }
}
