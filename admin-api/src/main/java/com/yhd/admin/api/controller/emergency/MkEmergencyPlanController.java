package com.yhd.admin.api.controller.emergency;

import com.yhd.admin.api.domain.emergency.convert.MkEmergencyPlanConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyPlanDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyPlanParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyPlanVO;
import com.yhd.admin.api.service.emergency.MkEmergencyPlanService;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 应急救援数字预案表 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/open/emergency/plan")
public class MkEmergencyPlanController {

    @Resource
    private MkEmergencyPlanService emergencyPlanService;

    @Resource
    private MkEmergencyPlanConvert convert;

    /**
     * 根据预案类型查询预案列表
     *
     * @param param 查询参数
     * @return 预案列表
     */
    @PostMapping(value = "/getPlansByType", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanVO>> getPlansByType(@RequestBody MkEmergencyPlanParam param) {
        log.info("根据预案类型查询预案列表，参数：{}", param);
        List<MkEmergencyPlanDTO> dtoList = emergencyPlanService.getPlansByType(param.getPlanType());
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 获取需要演练的预案列表
     *
     * @param param 查询参数
     * @return 需要演练的预案列表
     */
    @PostMapping(value = "/getNeedsDrillPlans", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanVO>> getNeedsDrillPlans(@RequestBody MkEmergencyPlanParam param) {
        log.info("获取需要演练的预案列表，参数：{}", param);
        List<MkEmergencyPlanDTO> dtoList = emergencyPlanService.getNeedsDrillPlans(param.getOrgCode());
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据分类ID查询预案列表
     *
     * @param param 查询参数
     * @return 预案列表
     */
    @PostMapping(value = "/getPlansByCategoryId", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkEmergencyPlanVO>> getPlansByCategoryId(@RequestBody MkEmergencyPlanParam param) {
        log.info("根据分类ID查询预案列表，参数：{}", param);
        List<MkEmergencyPlanDTO> dtoList = emergencyPlanService.getPlansByCategoryId(param.getCategoryId());
        return RespJson.success(convert.toVO(dtoList));
    }
}
