package com.yhd.admin.api.domain.dispatch.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 智能调度中心-应急广播
 *
 * <AUTHOR>
 * @date 2025/7/31 09:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkDispatchBroadcastDTO extends BaseDTO implements Serializable {

  private Long id;

  /** 应急广播数 */
  private Long urgencyNum;

  /** 在线数 */
  private Long onlineNum;

  /** 离线数 */
  private Long offlineNum;

  /** 广播状态 */
  private List<MkDispatchBroadcastStructureDTO> broadcastStatusList = new ArrayList<>();

  /** 文字广播 */
  private List<MkDispatchBroadcastStructureDTO> broadcastWritingList = new ArrayList<>();

  /** 广播列表 */
  private List<MkDispatchBroadcastStructureDTO> broadcastList = new ArrayList<>();
}
