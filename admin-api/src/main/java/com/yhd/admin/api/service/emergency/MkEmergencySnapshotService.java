package com.yhd.admin.api.service.emergency;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencySnapshotDTO;
import com.yhd.admin.api.domain.emergency.entity.MkEmergencySnapshot;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySnapshotParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 应急快照服务接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface MkEmergencySnapshotService extends IService<MkEmergencySnapshot> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<MkEmergencySnapshotDTO> pagingQuery(MkEmergencySnapshotParam param);

    /**
     * 新增应急快照信息
     * @param param 应急快照信息参数
     * @return 是否成功
     */
    Boolean add(MkEmergencySnapshotParam param);

    /**
     * 修改应急快照信息
     * @param param 应急快照信息参数
     * @return 是否成功
     */
    Boolean modify(MkEmergencySnapshotParam param);

    /**
     * 获取应急快照详情
     * @param param 查询参数
     * @return 应急快照详情
     */
    MkEmergencySnapshotDTO getCurrentDetail(MkEmergencySnapshotParam param);

    /**
     * 批量删除应急快照信息
     * @param param 批量删除参数
     * @return 是否成功
     */
    Boolean removeBatch(BatchParam param);

    /**
     * 根据矿井ID查询快照列表
     *
     * @param mineId 矿井ID
     * @return 快照列表
     */
    List<MkEmergencySnapshotDTO> getSnapshotsByMineId(Long mineId);

    /**
     * 根据快照类型查询快照列表
     *
     * @param snapshotType 快照类型
     * @return 快照列表
     */
    List<MkEmergencySnapshotDTO> getSnapshotsByType(String snapshotType);

    /**
     * 根据矿井ID和快照类型查询最新快照
     *
     * @param mineId 矿井ID
     * @param snapshotType 快照类型
     * @return 最新快照
     */
    MkEmergencySnapshotDTO getLatestSnapshotByMineIdAndType(Long mineId, String snapshotType);

    /**
     * 获取最新快照
     *
     * @return 最新快照
     */
    MkEmergencySnapshotDTO getLatestSnapshot();

    /**
     * 创建应急快照
     *
     * @param mineId 矿井ID
     * @param mineName 矿井名称
     * @param description 描述
     * @return 快照信息
     */
    MkEmergencySnapshotDTO createEmergencySnapshot(Long mineId, String mineName, String description);

    /**
     * 获取应急快照时间列表
     *
     * @param mineId 矿井ID（可选）
     * @return 时间列表
     */
    List<MkEmergencySnapshotDTO> getTimeList(Long mineId);
}
