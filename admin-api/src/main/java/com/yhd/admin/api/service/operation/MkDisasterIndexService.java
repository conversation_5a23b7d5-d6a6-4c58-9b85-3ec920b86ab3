package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkDisasterIndexDTO;
import com.yhd.admin.api.domain.operation.entity.MkDisasterIndex;
import com.yhd.admin.api.domain.operation.query.MkDisasterIndexParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * 算法模型-自然灾害评价指标表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
public interface MkDisasterIndexService extends IService<MkDisasterIndex> {

    IPage<MkDisasterIndexDTO> pagingQuery(MkDisasterIndexParam queryParam);

     Boolean add(MkDisasterIndexParam param);

    Boolean modify(MkDisasterIndexParam param);

    Boolean removeBatch(BatchParam param);

    MkDisasterIndexDTO getCurrentDetail(MkDisasterIndexParam param);
}
