package com.yhd.admin.api.service.conduct.arrangement;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yhd.admin.api.configuration.FastDFSClient;
import com.yhd.admin.api.dao.conduct.arrangement.MkShiftArrangementDao;
import com.yhd.admin.api.domain.conduct.convert.arrangement.MkShiftArrangementConvert;
import com.yhd.admin.api.domain.conduct.dto.arrangement.MkShiftArrangementDTO;
import com.yhd.admin.api.domain.conduct.dto.arrangement.MkShiftArrangementOfMonthDTO;
import com.yhd.admin.api.domain.conduct.entity.arrangement.MkShiftArrangement;
import com.yhd.admin.api.domain.conduct.query.arrangement.MkShiftArrangementParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.DicService;
import com.yhd.admin.api.service.sys.UserService;
import com.yhd.admin.common.constant.DicConstant;
import com.yhd.admin.common.domain.dto.UserDTO;
import com.yhd.admin.common.utils.excel.CellBuilder;
import com.yhd.admin.common.utils.excel.WorkbookBuilder;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 值班带班安排表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29 11:30:39
 */
@Service
public class MkShiftArrangementServiceImpl extends ServiceImpl<MkShiftArrangementDao, MkShiftArrangement> implements MkShiftArrangementService {

    private final MkShiftArrangementConvert mkShiftArrangementConvert;
    private final FastDFSClient fastDFSClient;
    private final DicService dicService;
    private final UserService userService;

    public MkShiftArrangementServiceImpl(MkShiftArrangementConvert mkShiftArrangementConvert, FastDFSClient fastDFSClient, DicService dicService, UserService userService) {
        this.mkShiftArrangementConvert = mkShiftArrangementConvert;
        this.fastDFSClient = fastDFSClient;
        this.dicService = dicService;
        this.userService = userService;
    }

    @Override
    public List<MkShiftArrangementOfMonthDTO> queryArrange(MkShiftArrangementParam param) {
        if (Objects.isNull(param.getPlanFlag())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        List<MkShiftArrangementOfMonthDTO> result = new ArrayList<>();
        LambdaQueryWrapper<MkShiftArrangement> wrapper = new LambdaQueryWrapper<>();
        YearMonth yearMonth = YearMonth.now();
        LocalDate today = LocalDate.now();
        // 查询条件 年、月 查询当月实际排班、计划排班
        if (StringUtils.isNotBlank(param.getYear()) && StringUtils.isNotBlank(param.getMonth())) {
            yearMonth = YearMonth.of(Integer.parseInt(param.getYear()), Integer.parseInt(param.getMonth()));
        }
        LocalDate startDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();
        wrapper.ge(MkShiftArrangement::getShiftDate, startDate);
        wrapper.le(MkShiftArrangement::getShiftDate, endDate);
        wrapper.orderByAsc(MkShiftArrangement::getShiftTypeCode);
        List<MkShiftArrangementDTO> dtoList = mkShiftArrangementConvert.toDTOList(baseMapper.selectList(wrapper));
        Map<LocalDate,List<MkShiftArrangementDTO>> map= dtoList.stream().filter(d -> d.getShiftDate() != null).collect
        (Collectors.groupingBy(MkShiftArrangementDTO::getShiftDate));
        for (Map.Entry<LocalDate, List<MkShiftArrangementDTO>> entry : map.entrySet()) {
            LocalDate date = entry.getKey();
            List<MkShiftArrangementDTO> list = entry.getValue();
            MkShiftArrangementOfMonthDTO dto = new MkShiftArrangementOfMonthDTO();
            dto.setShiftDate(date);
            dto.setShiftDay(String.valueOf(date.getDayOfMonth()));
            dto.setShiftWeek(list.get(0).getShiftWeek());
            if (today.isAfter(date)) {
                // 白色
                dto.setIsTodayFlag(0);
            } else if (today.equals(date)) {
                // 蓝色
                dto.setIsTodayFlag(1);
            } else {
                // 灰色
                dto.setIsTodayFlag(2);
            }
            for (MkShiftArrangementDTO item : list) {
                if (param.getPlanFlag() == 0) {
                    setShiftData(dto, item, item.getPlanName(), item.getPlanAccount(), item.getPlanFlag());
                } else if (param.getPlanFlag() == 1) {
                    setShiftData(dto, item, item.getName(), item.getAccount(), item.getPlanFlag());
                }
            }
            result.add(dto);
        }
        return result.stream().sorted(Comparator.comparing(MkShiftArrangementOfMonthDTO::getShiftDate)).toList();
    }

    private void setShiftData(MkShiftArrangementOfMonthDTO dto, MkShiftArrangementDTO item, String name, String account, Integer planFlag) {
        String shiftTypeCode = item.getShiftTypeCode();
        if (shiftTypeCode == null) {
            return;
        }
        switch (shiftTypeCode) {
            case DicConstant.SHIFT_TYPE_ONE:
                dto.setDutyShiftName(name);
                dto.setDutyShiftAccount(account);
                dto.setIsDutyPlanFlag(planFlag);
                break;
            case DicConstant.SHIFT_TYPE_TWO:
                dto.setNightShiftName(name);
                dto.setNightShiftAccount(account);
                dto.setIsNightPlanFlag(planFlag);
                break;
            case DicConstant.SHIFT_TYPE_THREE:
                dto.setDayShiftName(name);
                dto.setDayShiftAccount(account);
                dto.setIsDayPlanFlag(planFlag);
                break;
            case DicConstant.SHIFT_TYPE_FOUR:
                dto.setMiddleShiftName(name);
                dto.setMiddleShiftAccount(account);
                dto.setIsMiddlePlanFlag(planFlag);
                break;
            case DicConstant.SHIFT_TYPE_FIVE:
                dto.setLateShiftName(name);
                dto.setLateShiftAccount(account);
                dto.setIsLatePlanFlag(planFlag);
                break;
            default:
                break;
        }
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkShiftArrangementDTO> queryList(MkShiftArrangementParam param) {

        LambdaQueryWrapper<MkShiftArrangement> wrapper = new LambdaQueryWrapper<>();
        // 查询条件 日期 id 查询当日实际排班、计划排班
        wrapper.eq(Objects.nonNull(param.getShiftDate()), MkShiftArrangement::getShiftDate, param.getShiftDate());
        wrapper.eq(StringUtils.isNotBlank(param.getShiftTypeCode()), MkShiftArrangement::getShiftTypeCode, param.getShiftTypeCode());
        wrapper.eq(StringUtils.isNotBlank(param.getShiftTypeName()), MkShiftArrangement::getShiftTypeName,
            param.getShiftTypeName());
        wrapper.eq(Objects.nonNull(param.getId()), MkShiftArrangement::getId, param.getId());

        if (StringUtils.isNotBlank(param.getName())) {
            wrapper.and(w -> w.eq(MkShiftArrangement::getName, param.getName())
                .or().eq(MkShiftArrangement::getPlanName, param.getName()));
        }
        return mkShiftArrangementConvert.toDTOList(baseMapper.selectList(wrapper));
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkShiftArrangementParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }

    /**
     * 导入文件
     *
     * @return 文件地址
     */
    @Override
    public Boolean importExcel(MultipartFile file) {

        UserParam userParam = new UserParam();
        List<UserDTO> userList = userService.queryUsers(userParam);
        Map<String, UserDTO> userMap = userList.stream()
            .filter(u -> u.getName() != null)
            .collect(Collectors.toMap(UserDTO::getName, Function.identity()));

        // 获取EXCEL工作簿
        Workbook workbook = WorkbookBuilder.getWorkBook(file);
        // 校验EXCEL工作簿：sheet名称、标题名称、表头字段等
        YearMonth yearMonth = checkFile(workbook);

        int maxDayOfMonth = yearMonth.lengthOfMonth();
        // 循环读取sheet内容
        Sheet sheet = workbook.getSheetAt(0);
        List<MkShiftArrangement> arrangementList = Lists.newArrayList();
        String[] shiftType = {"值班","夜班","早班","中班","晚班"};
        for (int j = 3; j <= 7; j++) {
            for (int i = 1; i <= maxDayOfMonth; i++) {
                // 循环读取每列数据 从第4列开始
                String name = CellBuilder.getCellValue(sheet.getRow(j).getCell(i));
                MkShiftArrangement arrangement = new MkShiftArrangement();
                if (StringUtils.isNotBlank(name)) {
                    if (!userMap.containsKey(name)) {
                        throw new BMSException("error", "第"+ (j + 1) +"行第" + (i + 1) + "列账号不存在");
                    }
                    String account = userMap.get(name).getUsername();
                    LocalDate shiftDate = yearMonth.atDay(i);
                    DayOfWeek dayOfWeek = shiftDate.getDayOfWeek();
                    arrangement.setShiftDate(shiftDate);
                    arrangement.setShiftWeek(convertToChineseWeekday(dayOfWeek));
                    arrangement.setShiftTypeName(shiftType[j-3]);
                    arrangement.setShiftTypeCode(dicService.reversal("SHIFT_TYPE",shiftType[j-3]));
                    arrangement.setAccount(account);
                    arrangement.setName(name);
                    arrangement.setPlanAccount(account);
                    arrangement.setPlanName(name);
                    arrangement.setPlanFlag(0);
                    arrangementList.add(arrangement);

                }
            }
        }
        if (!CollectionUtils.isEmpty(arrangementList)) {
            return super.saveBatch(arrangementList);
        } else {
            throw new BMSException("error", "未导入数据");
        }
    }

    /**
     * 导出文件
     *
     * @param param 筛选条件
     * @return 文件地址
     */
    @Override
    public String export(MkShiftArrangementParam param) {
        LambdaQueryWrapper<MkShiftArrangement> wrapper = new LambdaQueryWrapper<>();
        YearMonth yearMonth = YearMonth.now();
        if (StringUtils.isNotBlank(param.getYear()) && StringUtils.isNotBlank(param.getMonth())) {
            yearMonth = YearMonth.of(Integer.parseInt(param.getYear()), Integer.parseInt(param.getMonth()));
        }
        LocalDate firstDay = yearMonth.atDay(1);
        LocalDate lastDay = yearMonth.atEndOfMonth();
        wrapper.ge(MkShiftArrangement::getShiftDate, firstDay);
        wrapper.le(MkShiftArrangement::getShiftDate, lastDay);
        wrapper.orderByAsc(MkShiftArrangement::getShiftTypeCode, MkShiftArrangement::getShiftDate);
        List<MkShiftArrangement> list = baseMapper.selectList(wrapper);

        if (CollectionUtils.isEmpty(list)) {
            throw new BMSException("error","没有排班数据");
        }

        // 先按shiftTypeCode分组并排序，然后转换为List<List>
        List<List<MkShiftArrangement>> result = list.stream()
            .collect(Collectors.groupingBy(
                MkShiftArrangement::getShiftTypeCode,
                LinkedHashMap::new,
                Collectors.toList()
            ))
            .values()
            .stream()
            .toList();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String fileName = "人员值班带班安排表-" + timestamp + ".xlsx";


        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            Workbook workbook = getWorkbook(param);
            Sheet sheet = workbook.getSheetAt(0);
            // 跳过标题行
            int rowNum = 3;
            CellStyle contentStyle = createContentStyle(workbook);

            if (param.getPlanFlag() == 1) {
                for (List<MkShiftArrangement> arrangementList : result) {
                    Row row = sheet.getRow(rowNum++);
                    for (MkShiftArrangement record : arrangementList) {
                        CellBuilder.build(row, record.getShiftDate().getDayOfMonth(), record.getName(), contentStyle);
                    }
                }
            } else {
                for (List<MkShiftArrangement> arrangementList : result) {
                    Row row = sheet.getRow(rowNum++);
                    for (MkShiftArrangement record : arrangementList) {
                        CellBuilder.build(row, record.getShiftDate().getDayOfMonth(), record.getPlanName(), contentStyle);
                    }
                }
            }
            workbook.write(outputStream);

            // 上传到文件服务器
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            // 执行上传
            return fastDFSClient.getFullPath(fastDFSClient.uploadFile(inputStream, fileName));

        } catch (Exception e) {
            throw new BMSException(ExceptionEnum.EXPORT_ERROR);
        }
    }

    private CellStyle createContentStyle(Workbook workbook) {
        CellStyle contentStyle = workbook.createCellStyle();
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setAlignment(HorizontalAlignment.CENTER);
        contentStyle.setWrapText(true);
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);
        contentStyle.setBorderTop(BorderStyle.THIN);
        return contentStyle;
    }

    /**
     * 下载模版
     *
     * @param param 筛选条件
     * @return 文件地址
     */
    @Override
    public String getTemplate(MkShiftArrangementParam param) {
        HSSFWorkbook workbook;
        String fileName = "值班带班安排导入模板.xls";

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook = getWorkbook(param);
            workbook.write(outputStream);

            // 上传到文件服务器
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            // 执行上传
            return fastDFSClient.getFullPath(fastDFSClient.uploadFile(inputStream, fileName));

        } catch (Exception e) {
            throw new BMSException(ExceptionEnum.EXPORT_ERROR);
        }
    }

    private HSSFWorkbook getWorkbook(MkShiftArrangementParam param) {
        LocalDate today = LocalDate.now();
        if (StringUtils.isNotBlank(param.getYear()) && StringUtils.isNotBlank(param.getMonth())) {
            today = LocalDate.of(Integer.parseInt(param.getYear()), Integer.parseInt(param.getMonth()), 1);
        }
        String titleTime = today.format(DateTimeFormatter.ofPattern("yyyy年MM月"));
        int daysInMonth = today.lengthOfMonth();
        String[] week = {"日", "一", "二", "三", "四", "五", "六"};
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet wbSheet = workbook.createSheet("值班带班安排导入模板");

        CellStyle contentStyle = createContentStyle(workbook);

        AtomicInteger rowNum = new AtomicInteger();
        // 第1行，标题设置
        HSSFRow row0 = wbSheet.createRow(rowNum.getAndIncrement());
        HSSFCell row0Cell = row0.createCell(0);
        if (param.getPlanFlag() == 0) {
            row0Cell.setCellValue(titleTime + "人员值班带班安排表（计划）");
        } else {
            row0Cell.setCellValue(titleTime + "人员值班带班安排表（实际）");
        }

        row0Cell.setCellStyle(contentStyle);
        wbSheet.addMergedRegion(new CellRangeAddress(0, 0, 0, daysInMonth));

        List<HSSFRow> rowList = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            rowList.add(wbSheet.createRow(rowNum.getAndIncrement()));
        }

        String[] headers = {"日期", "星期", "值班", "夜班", "早班", "中班", "晚班"};
        for (int i = 0; i < headers.length; i++) {
            CellBuilder.build(rowList.get(i), 0, headers[i], contentStyle);
        }

        // 填充每日数据
        for (int i = 1; i <= daysInMonth; i++) {
            LocalDate date = today.withDayOfMonth(i);
            int dayOfWeekIndex = date.getDayOfWeek().getValue() % 7;
            String weekStr = week[dayOfWeekIndex];

            CellBuilder.build(rowList.get(0), i, i, contentStyle);
            CellBuilder.build(rowList.get(1), i, weekStr, contentStyle);
            for (int j = 2; j < 7; j++) {
                CellBuilder.build(rowList.get(j), i, "", contentStyle);
            }
        }
        return workbook;
    }

    private YearMonth checkFile(Workbook workbook) {
        if (null == workbook) {
            throw new BMSException("error", "请导入正确的EXCEL模板！");
        }
        // 读取sheet内容
        Sheet sheet = workbook.getSheetAt(0);
        if (null == sheet) {
            throw new BMSException("error", "请导入正确的EXCEL模板！");
        }
        String title = CellBuilder.getCellValue(sheet.getRow(0).getCell(0));
        String regex = "\\d{4}年(0[1-9]|[1-9]|1[0-2])月";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(title);
        YearMonth yearMonth;
        if (matcher.find()) {
            String dateStr = matcher.group(0);
            try {
                yearMonth = YearMonth.parse(dateStr, DateTimeFormatter.ofPattern("yyyy年M月"));
            } catch (DateTimeParseException e) {
                throw new BMSException("error", "标题中年月格式错误，月份应在 1-12 之间");
            }
        } else {
            throw new BMSException("error", "请导入正确的EXCEL模板！");
        }
        int maxDayOfMonth = yearMonth.lengthOfMonth();
        Row dayRow = sheet.getRow(1);
        if (dayRow == null) {
            throw new BMSException("error", "请导入正确的EXCEL模板，缺少日期行");
        }

        // 校验 column[0] 的 row[1] 到 row[7]
        String[] expectedValues = {"日期", "星期", "值班", "夜班", "早班", "中班", "晚班"};
        for (int i = 0; i < expectedValues.length; i++) {
            Row row = sheet.getRow(i + 1);
            if (row == null || row.getCell(0) == null) {
                throw new BMSException("error", "请导入正确的EXCEL模板，缺少必要行");
            }
            String cellValue = CellBuilder.getCellValue(row.getCell(0)).trim();
            if (!cellValue.equals(expectedValues[i])) {
                throw new BMSException("error", "第" + (i + 2) + "行第1列内容应为'" + expectedValues[i] + "'，实际为'" + cellValue + "'");
            }
        }


        for (int day = 1; day <= maxDayOfMonth; day++) {
            Cell cell = dayRow.getCell(day);
            String cellValue = CellBuilder.getCellValue(cell);
            if (StringUtils.isBlank(cellValue)) {
                throw new BMSException("error", "第2行第" + (day + 1) + "列日期为空");
            }
            if (!cellValue.trim().matches("\\d+")) {
                throw new BMSException("error", "第2行第" + (day + 1) + "列日期应为数字");
            }
            if (Integer.parseInt(cellValue) != day) {
                throw new BMSException("error", "第2行第" + (day + 1) + "列日期应为" + day);
            }
        }
        Row weekdayRow = sheet.getRow(2);
        if (weekdayRow == null) {
            throw new BMSException("error", "请导入正确的EXCEL模板，缺少星期行");
        }

        for (int day = 1; day <= maxDayOfMonth; day++) {
            LocalDate date = yearMonth.atDay(day);
            DayOfWeek dayOfWeek = date.getDayOfWeek();
            String expectedWeekday = convertToChineseWeekday(dayOfWeek);

            Cell cell = weekdayRow.getCell(day);
            String cellValue = CellBuilder.getCellValue(cell);
            if (StringUtils.isBlank(cellValue)) {
                throw new BMSException("error", "第3行第" + day + 1 + "列星期为空");
            }
            if (!cellValue.contains(expectedWeekday)) {
                throw new BMSException("error", "第3行第" + day + 1 + "列星期应为" + expectedWeekday);
            }
        }
        return yearMonth;
    }

    private String convertToChineseWeekday(DayOfWeek dayOfWeek) {
        if (dayOfWeek == null) {
            return null;
        }
        return switch (dayOfWeek) {
            case MONDAY -> "一";
            case TUESDAY -> "二";
            case WEDNESDAY -> "三";
            case THURSDAY -> "四";
            case FRIDAY -> "五";
            case SATURDAY -> "六";
            case SUNDAY -> "日";
        };
    }

}
