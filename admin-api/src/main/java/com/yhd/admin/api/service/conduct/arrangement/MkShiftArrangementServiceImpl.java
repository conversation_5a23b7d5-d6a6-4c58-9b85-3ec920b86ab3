package com.yhd.admin.api.service.conduct.arrangement;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.conduct.arrangement.MkShiftArrangementDao;
import com.yhd.admin.api.domain.conduct.convert.arrangement.MkShiftArrangementConvert;
import com.yhd.admin.api.domain.conduct.dto.arrangement.MkShiftArrangementDTO;
import com.yhd.admin.api.domain.conduct.entity.arrangement.MkShiftArrangement;
import com.yhd.admin.api.domain.conduct.query.arrangement.MkShiftArrangementParam;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.exception.BMSException;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 值班带班安排表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29 11:30:39
 */
@Service
public class MkShiftArrangementServiceImpl extends ServiceImpl<MkShiftArrangementDao, MkShiftArrangement> implements MkShiftArrangementService {

    public final MkShiftArrangementConvert mkShiftArrangementConvert;

    public MkShiftArrangementServiceImpl(MkShiftArrangementConvert mkShiftArrangementConvert) {
        this.mkShiftArrangementConvert = mkShiftArrangementConvert;
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MkShiftArrangementDTO> queryList(MkShiftArrangementParam param) {

        LambdaQueryWrapper<MkShiftArrangement> wrapper = new LambdaQueryWrapper<>();
        // 查询条件 年、月 查询当月实际排班、计划排班
        if (StringUtils.isNotBlank(param.getYear()) || StringUtils.isNotBlank(param.getMonth())) {
            String year = param.getYear();
            String month = param.getMonth();
            LocalDate startDate;
            startDate = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 1);
            LocalDate endDate = startDate.plusMonths(1).minusDays(1);
            wrapper.ge(MkShiftArrangement::getShiftDate, startDate);
            wrapper.le(MkShiftArrangement::getShiftDate, endDate);
        }
        // 查询条件 日期 id 查询当日实际排班、计划排班
        wrapper.eq(Objects.nonNull(param.getShiftDate()), MkShiftArrangement::getShiftDate, param.getShiftDate());
        wrapper.eq(Objects.nonNull(param.getId()), MkShiftArrangement::getId, param.getId());

        if (StringUtils.isNotBlank(param.getName())) {
            wrapper.and(w -> w.eq(MkShiftArrangement::getName, param.getName())
                .or().eq(MkShiftArrangement::getPlanName, param.getName()));
        }
        return mkShiftArrangementConvert.toDTOList(baseMapper.selectList(wrapper));
    }

    /**
     * 新增或修改
     *
     * @param param 数据
     * @return 是否成功
     */
    @Override
    public Boolean addOrModify(MkShiftArrangementParam param) {
        MkShiftArrangement entity = mkShiftArrangementConvert.toEntity(param);
        if (entity.getId() == null || Objects.isNull(getById(entity.getId()))) {
            // 新增操作
            return save(entity);
        } else {
            // 更新操作
            return updateById(entity);
        }
    }

     /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @Override
    public Boolean removeBatch(MkShiftArrangementParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BMSException(ExceptionEnum.CHECK_REQUIRED);
        }
        return removeByIds(param.getIds());
    }
}
