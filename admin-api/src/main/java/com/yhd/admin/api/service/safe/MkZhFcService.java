package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.safe.dto.MkZhFcDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhFc;
import com.yhd.admin.api.domain.safe.query.MkZhFcParam;


public interface MkZhFcService extends IService<MkZhFc> {

    Boolean addOrModify(MkZhFcParam param);

    Boolean remove(MkZhFcParam param);

    MkZhFcDTO getCurrentDetail(MkZhFcParam param);

    IPage<MkZhFcDTO> pagingQuery(MkZhFcParam param);

    MkZhFcDTO getData();
}
