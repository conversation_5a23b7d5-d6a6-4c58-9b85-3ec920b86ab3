package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.opencsv.CSVReader;
import com.opencsv.CSVWriter;
import com.yhd.admin.api.configuration.FastDFSClient;
import com.yhd.admin.api.dao.sys.UserDao;
import com.yhd.admin.api.dao.sys.UserRoleDao;
import com.yhd.admin.api.domain.sys.convert.UserConvert;
import com.yhd.admin.api.domain.sys.dto.OrgDTO;
import com.yhd.admin.api.domain.sys.dto.RoleDTO;
import com.yhd.admin.api.domain.sys.entity.SysOrg;
import com.yhd.admin.api.domain.sys.entity.SysRole;
import com.yhd.admin.api.domain.sys.enums.APIRedisKeyEnum;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.common.domain.dto.UserDTO;
import com.yhd.admin.common.domain.entity.SysUser;
import com.yhd.admin.common.domain.entity.SysUserRole;
import com.yhd.admin.common.domain.enums.RedisKeyEnum;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.IdentityService;
import org.flowable.idm.api.Group;
import org.flowable.idm.api.User;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName UserServiceImpl.java @Description TODO
 * @createTime 2020年04月01日 14:50:00
 */
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserDao, SysUser> implements UserService {

    private final UserConvert convert;

    private final UserPinSrv accountService;

    private final UserRoleDao userRoleDao;

    private final DicService dicService;

    private final RoleService roleService;

    private final ValueOperations<String, UserDTO> valueOperations;

    private final RedisTemplate<String, UserDTO> redisTemplate;

    private final OrgService orgService;

    private final FastDFSClient fastDFSClient;

    @Resource
    private IdentityService identityService;

    public UserServiceImpl(UserConvert convert, UserPinSrv accountService, UserRoleDao userRoleDao,
                           DicService dicService, RedisTemplate<String, UserDTO> redisTemplate, RoleService roleService,
                           OrgService orgService, FastDFSClient fastDFSClient) {
        this.convert = convert;
        this.accountService = accountService;
        this.userRoleDao = userRoleDao;
        this.dicService = dicService;
        this.valueOperations = redisTemplate.opsForValue();
        this.redisTemplate = redisTemplate;
        this.roleService = roleService;
        this.orgService = orgService;
        this.fastDFSClient = fastDFSClient;
    }

    @Override
    public UserDTO getUserByUid(String uid) {
        if (StringUtils.isBlank(uid)) {
            throw new BMSException(ExceptionEnum.USER_NAME_ID_NULL.getCode(),
                ExceptionEnum.USER_NAME_ID_NULL.getDesc());
        }
        String key = String.format(APIRedisKeyEnum.USER.getKey(), uid);
        UserParam param = UserParam.builder().uid(uid).build();
        return Optional.ofNullable(valueOperations.get(key)).orElseGet(() -> {
            UserDTO userDTO = convert.toDTO(getById(uid));
            userDTO.setRoles(roleService.getRoleByUser(param).stream().filter(RoleDTO::getIsEnable).map(RoleDTO::getId)
                .collect(Collectors.toList()));
            valueOperations.set(key, userDTO);
            return userDTO;
        });
    }

    @Override
    public UserDTO getUserByUsername(String username) {
        if (StringUtils.isBlank(username)) {
            throw new BMSException(ExceptionEnum.USER_NAME_NULL.getCode(), ExceptionEnum.USER_NAME_NULL.getDesc());
        }
        String key = String.format(APIRedisKeyEnum.USER.getKey(), username);
        UserParam param = UserParam.builder().username(username).build();
        Supplier<UserDTO> userDTOSupplier = () -> {
            SysUser userEntity = getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, username));
            if (null != userEntity) {
                UserDTO userDTO = convert.toDTO(userEntity);
                userDTO.setRoles(roleService.getRoleByUser(param).stream().filter(RoleDTO::getIsEnable).map(RoleDTO::getId)
                    .collect(Collectors.toList()));
                valueOperations.set(key, userDTO, 1, TimeUnit.DAYS);
                return userDTO;
            }
            return null;
        };

        return Optional.ofNullable(valueOperations.get(key)).orElseGet(userDTOSupplier);
    }

    @Override
    public IPage<UserDTO> pagingQuery(UserParam queryParam) {
        IPage<SysUser> iPage = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<SysUser> userQuery = new LambdaQueryChainWrapper<>(baseMapper);

        userQuery.eq(StringUtils.isNoneBlank(queryParam.getUsername()), SysUser::getUsername, queryParam.getUsername())
            .like(StringUtils.isNotBlank(queryParam.getSfz()), SysUser::getSfz, queryParam.getSfz())
            .eq(StringUtils.isNotBlank(queryParam.getJob()), SysUser::getJob, queryParam.getJob())
            .in(queryParam.getOrgId() != null, SysUser::getOrgId,
                orgService.listChildrenOrgById(queryParam.getOrgId()).stream().map(OrgDTO::getId)
                    .collect(Collectors.toList()))
            .eq(StringUtils.isNotBlank(queryParam.getPhone()), SysUser::getPhone, queryParam.getPhone())
            .like(StringUtils.isNotBlank(queryParam.getName()), SysUser::getName, queryParam.getName())
            .like(StringUtils.isNotBlank(queryParam.getAddress()), SysUser::getAddress, queryParam.getAddress())
            .eq(SysUser::getIsAdmin, Boolean.FALSE) // 过滤超级管理员
            .orderByDesc(SysUser::getCreatedTime);
        return userQuery.page(iPage).convert(convert::toDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(UserParam userParam) {
        String uid = IdWorker.getTimeId();
        userParam.setUid(uid);

        // 职务
        if (StringUtils.isNotBlank(userParam.getJob())) {
            userParam.setJobTxt(dicService.transform("JOB_TITLE", userParam.getJob()));
        }
        // 文化程度
        if (StringUtils.isNotBlank(userParam.getEducation())) {
            userParam.setEducationTxt(dicService.transform("EDU", userParam.getEducation()));
        }

        // 工作状态
        if (StringUtils.isNotBlank(userParam.getWorkStatus())) {
            userParam.setWorkStatusTxt(dicService.transform("WORK_STATUS", userParam.getWorkStatus()));
        }

        // 用功形式
        if (StringUtils.isNotBlank(userParam.getEmploymentForm())) {
            userParam.setEmploymentFormTxt(dicService.transform("WORK_TYPE", userParam.getEmploymentForm()));
        }
        if (userParam.getOrgId() != null) {
            userParam.setOrgTxt(orgService.joinOrgName(userParam.getOrgId()));
        }

        if (Boolean.TRUE.equals(userParam.getIsEnable())) {
            // 新增 账号密码信息，
            if (StringUtils.isNotBlank(userParam.getPassword())) {
                accountService.saveOrUpdate(uid, userParam.getUsername(), userParam.getPassword());
            }
            // 新增角色
            if (!CollectionUtils.isEmpty(userParam.getRoles())) {
                userParam.getRoles().forEach(o -> {
                    SysUserRole sysUserRole = new SysUserRole();
                    sysUserRole.setRoleId(o);
                    sysUserRole.setUid(uid);
                    userRoleDao.insert(sysUserRole);
                });
            }
        }
        boolean result = save(convert.toEntity(userParam));
        // 重新分配角色，先删后增 ACT_ID_MEMBERSHIP表操作
        this.updateIdMembership(userParam);
        return result;
    }

    private void updateIdMembership(UserParam userParam) {
        User users = identityService.createUserQuery().userId(userParam.getUsername()).singleResult();
        if (users == null) {
            User user = identityService.newUser(userParam.getUsername());
            user.setFirstName(userParam.getUsername());
            user.setEmail(userParam.getUsername() + "@qq.com");
            identityService.saveUser(user);
        }
        // 存储查到的角色编码
        List<String> roleCode = new ArrayList<>();
        userParam.getRoles().forEach(e -> {
            LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysRole::getId, e);
            SysRole sysRole = roleService.getOne(queryWrapper);
            roleCode.add(sysRole.getRoleCode());
        });
        this.getNewArrayListCodes(roleCode, userParam.getUsername());
    }

    private void getNewArrayListCodes(List<String> roleCode, String username) {
        if (!CollectionUtils.isEmpty(roleCode)) {
            // 先删除再分配
            List<Group> list = identityService.createGroupQuery().groupMember(username).list();
            list.forEach(u -> {
                identityService.deleteMembership(username, u.getId());
            });
            roleCode.forEach(e -> {
                // 判断属于哪个角色编码赋予对应的groupId
                // String groupId = this.getGroupRolesCode(e);
                // 重新分配，先增后删
                this.getAssignUserGroups(username, e);
            });
        } else {
            // 删除有关联的数据
            this.deleteMemShipUses(username);
        }
    }

    private void deleteMemShipUses(String username) {
        List<Group> list = identityService.createGroupQuery().groupMember(username).list();
        list.forEach(u -> {
            identityService.deleteMembership(username, u.getId());
        });
    }

    private void getAssignUserGroups(String username, String groupId) {
        // 用户分配给用户组
        identityService.createMembership(username, groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean modify(UserParam userParam) {

        // 职务
        if (StringUtils.isNotBlank(userParam.getJob())) {
            userParam.setJobTxt(dicService.transform("JOB_TITLE", userParam.getJob()));
        }
        // 文化程度
        if (StringUtils.isNotBlank(userParam.getEducation())) {
            userParam.setEducationTxt(dicService.transform("EDU", userParam.getEducation()));
        }

        // 工作状态
        if (StringUtils.isNotBlank(userParam.getWorkStatus())) {
            userParam.setWorkStatusTxt(dicService.transform("WORK_STATUS", userParam.getWorkStatus()));
        }

        // 用功形式
        if (StringUtils.isNotBlank(userParam.getEmploymentForm())) {
            userParam.setEmploymentFormTxt(dicService.transform("WORK_TYPE", userParam.getEmploymentForm()));
        }

        if (userParam.getOrgId() != null) {
            userParam.setOrgTxt(orgService.joinOrgName(userParam.getOrgId()));
        }
        // 新增 账号密码信息，
        if (StringUtils.isNotBlank(userParam.getPassword())) {
            accountService.saveOrUpdate(userParam.getUid(), userParam.getUsername(), userParam.getPassword());
        }

        // 操作角色
        if (!CollectionUtils.isEmpty(userParam.getRoles())) {
            // 删除角色
            userRoleDao.delete(new QueryWrapper<SysUserRole>().lambda().eq(SysUserRole::getUid, userParam.getUid()));
            userParam.getRoles().forEach(o -> {
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setRoleId(o);
                sysUserRole.setUid(userParam.getUid());
                userRoleDao.insert(sysUserRole);
            });
        }
        String key = String.format(APIRedisKeyEnum.USER.getKey(), userParam.getUid());
        // 删除根据当前登录username查询的信息。
        String username_key = String.format(APIRedisKeyEnum.USER.getKey(), userParam.getUsername());
        // 删除登录时候的缓存信息。
        String accountKey = String.format(RedisKeyEnum.USER_ACCOUNT.getKey(), userParam.getUsername());

        redisTemplate.delete(Arrays.asList(key, accountKey, username_key));
        boolean result = this.updateById(convert.toEntity(userParam));
        if (userParam.getUsername() != null && !CollectionUtils.isEmpty(userParam.getRoles())) {
            // 重新分配角色，先删后增 ACT_ID_MEMBERSHIP表操作
            this.updateIdMembership(userParam);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam batchParam) {
        // uid+username;
        accountService.removeBatch(batchParam.getNames());
        userRoleDao.delete(new QueryWrapper<SysUserRole>().lambda().in(SysUserRole::getUid, batchParam.getUid()));
        List<String> keys = batchParam.getUid().stream().map(uid -> String.format(APIRedisKeyEnum.USER.getKey(), uid))
            .collect(Collectors.toList());
        List<String> username_keys = new ArrayList<>();
        batchParam.getUsernames().forEach(username -> {
            username_keys.add(String.format(RedisKeyEnum.USER_ACCOUNT.getKey(), username));
            username_keys.add(String.format(APIRedisKeyEnum.USER.getKey(), username));
        });
        redisTemplate.delete(keys);
        // 删除登录时候的缓存信息,当前登录人查询的信息。
        redisTemplate.delete(username_keys);
        boolean result = removeByIds(batchParam.getUid());
        // 删除账户同步删除ACT_ID_MEMBERSHIP对应的关系表
        this.delByActIdMemberShip(batchParam);
        return result;
    }

    private void delByActIdMemberShip(BatchParam batchParam) {
        // 删除ACT_ID_USER 有关的数据
        List<User> userList = identityService.createUserQuery().userIds(batchParam.getNames()).list();
        if (!CollectionUtils.isEmpty(userList)) {
            userList.forEach(e -> {
                // 删除对应的用户
                identityService.deleteUser(e.getId());
            });
        }
        List<Group> list = identityService.createGroupQuery().groupMembers(batchParam.getNames()).list();
        list.forEach(u -> {
            batchParam.getNames().forEach(e -> {
                identityService.deleteMembership(e, u.getId());
            });
        });
    }

    @Override
    public UserDTO obtainCurrentDetail(UserParam queryParam) {
        return convert.toDTO(getById(queryParam.getUid()));
    }

    @Override
    public Boolean checkUsernameIfNotExist(UserParam queryParam) {
        LambdaQueryChainWrapper<SysUser> userQueryChain = new LambdaQueryChainWrapper<>(baseMapper);
        userQueryChain.eq(SysUser::getUsername, queryParam.getUsername());

        if (StringUtils.isNotBlank(queryParam.getUid())) {
            userQueryChain.notIn(SysUser::getUid, List.of(queryParam.getUid()));
        }
        userQueryChain.last(" limit 1");
        return CollectionUtils.isEmpty(userQueryChain.list());
    }

    /***
     * 导出用户
     *
     * @param exportParam
     * @return
     */
    @Override
    public String exportUser(UserParam exportParam) {
        String ret_val_file_name = "";
        LambdaQueryChainWrapper<SysUser> userQueryChain = new LambdaQueryChainWrapper<>(baseMapper);
        if (CollectionUtils.isEmpty(exportParam.getSelectedRowKeys())) {
            userQueryChain
                .eq(StringUtils.isNotBlank(exportParam.getUsername()), SysUser::getUsername, exportParam.getUsername())
                .like(StringUtils.isNotBlank(exportParam.getSfz()), SysUser::getSfz, exportParam.getSfz())
                .eq(StringUtils.isNotBlank(exportParam.getJob()), SysUser::getJob, exportParam.getJob())
                .in(exportParam.getOrgId() != null, SysUser::getOrgId,
                    orgService.listChildrenOrgById(exportParam.getOrgId()).stream().map(OrgDTO::getId)
                        .collect(Collectors.toList()))
                .eq(StringUtils.isNotBlank(exportParam.getPhone()), SysUser::getPhone, exportParam.getPhone())
                .like(StringUtils.isNotBlank(exportParam.getName()), SysUser::getName, exportParam.getName())
                .like(StringUtils.isNotBlank(exportParam.getAddress()), SysUser::getAddress, exportParam.getAddress());
        } else {

            userQueryChain.in(SysUser::getUid, exportParam.getSelectedRowKeys());
        }
        userQueryChain.eq(SysUser::getIsAdmin, Boolean.FALSE) // 过滤超级管理员
            .orderByDesc(SysUser::getCreatedTime);

        // todo 写文件，csv
        String fileName = "用户信息_" + IdWorker.getTimeId() + ".csv";
        // @formatter:off
        byte[] bom = {(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
        try (
            ByteArrayOutputStream outputStream =new ByteArrayOutputStream();
            OutputStreamWriter outputStreamWriter = new OutputStreamWriter(outputStream,StandardCharsets.UTF_8);
            CSVWriter writer = new CSVWriter(outputStreamWriter)) {
            outputStream.write(bom);
            writer.writeNext(new String[]{"用户名", "姓名", "性别", "手机号", "身份证", "工作状态", "工作形式", "学历", "毕业院校","年龄", "工作年限",  "部门", "职务/工种","地址", "邮箱", "备注"});
            userQueryChain.list().forEach(user -> {
                writer.writeNext(new String[]{
                    StringUtils.trimToEmpty(user.getUsername()),
                    StringUtils.trimToEmpty(user.getName()),
                    StringUtils.trimToEmpty(user.getGender())=="M"?"男":"女",
                    StringUtils.trimToEmpty(user.getPhone()),
                    StringUtils.trimToEmpty(user.getSfz()),
                    StringUtils.trimToEmpty(user.getWorkStatusTxt()),
                    StringUtils.trimToEmpty(user.getEmploymentFormTxt()),
                    StringUtils.trimToEmpty(user.getEducationTxt()),
                    StringUtils.trimToEmpty(user.getGraduatedSchool()),
                    user.getAge()==null?"":user.getAge().toString(),
                    user.getWorkingAge()==null?"":user.getWorkingAge().toString(),
                    StringUtils.trimToEmpty(user.getOrgTxt()),
                    StringUtils.trimToEmpty(user.getJobTxt()),
                    StringUtils.trimToEmpty(user.getAddress()),
                    StringUtils.trimToEmpty(user.getEmail()),
                    StringUtils.trimToEmpty(user.getRemark()),
                });
            });
            writer.flushQuietly();
            outputStreamWriter.flush();
            outputStream.flush();
            ret_val_file_name = fastDFSClient.getFullPath(fastDFSClient.uploadFile(new ByteArrayInputStream(outputStream.toByteArray()),fileName));

        } catch (IOException e) {
            if(log.isErrorEnabled()){
                log.error("导出用户信息失败>>{},{}",e,e.getMessage());
            }
        }
        // @formatter:on
        return ret_val_file_name;
    }

    @Override
    public String generationUserTemplate() {
        String ret_val_file_name = "";
        List<RoleDTO> roleList = roleService.queryRoleList();
        List<SysOrg> orgList = orgService.list();
        // 获取所有的角色信息，写入模板。
        if (!CollectionUtils.isEmpty(roleService.queryRoleList())) {
            // 解决 MS Excel 乱码问题
            byte[] bom = {(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                 OutputStreamWriter outputStreamWriter = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
                 CSVWriter writer = new CSVWriter(outputStreamWriter)) {
                outputStream.write(bom);
                // 第一行写入系统的角色信息，角色名称，角色 ID
                String roleStr = roleList.stream().map(role -> role.getRoleName() + ":" + role.getId())
                    .collect(Collectors.joining(","));
                String orgStr =
                    orgList.stream().map(org -> org.getOrgName() + ":" + org.getId()).collect(Collectors.joining(","));
                writer.writeNext(new String[]{"系统用户角色为:[" + roleStr + "],从中选择角色对应数据字填入，为导入用户授权"});
                writer.writeNext(new String[]{"系统用部门组织架构为:[" + orgStr + "],从中选择部门对应数据字填入，为导入用户设置部门"});
                writer.writeNext(new String[]{"用户导入信息需要填写:用户名 姓名 性别 手机号 身份证 角色,从第五行可以填写，按顺序填写"});
                writer.writeNext(new String[]{"用户名(唯一，系统存在则跳过，建议姓名拼音+手机号后6位)", "姓名", "性别", "手机号", "身份证", "角色", "部门"});
                writer.flushQuietly();
                outputStreamWriter.flush();
                outputStream.flush();
                ret_val_file_name = fastDFSClient.getFullPath(
                    fastDFSClient.uploadFile(new ByteArrayInputStream(outputStream.toByteArray()), "模板.csv"));
            } catch (IOException e) {
                if (log.isErrorEnabled()) {
                    log.error("生成用户信息导入模板失败>>{},{}", e, e.getMessage());
                }
            }
        }
        return ret_val_file_name;
    }

    /**
     * 导入用户
     *
     * @param inputStream
     * @return
     */
    @Override
    public String importUser(InputStream inputStream, Boolean isGranted, String currentUserName) {
        String totalMsg = "共计解析:%1$s,导入成功：%2$s,导入失败:%3$s,%4$s";
        LocalDateTime now = LocalDateTime.now();
        Integer sum = 0;
        Integer successCount = 0;
        Integer failCount = 0;
        String failMsg = "";

        try (InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
             CSVReader csvReader = new CSVReader(inputStreamReader)) {
            List<String[]> dataList = csvReader.readAll();
            sum = dataList.size();
            for (int i = 4; i < dataList.size(); i++) {
                String[] row = dataList.get(i);
                if (row.length < 7 || StringUtils.isAnyBlank(row)) {
                    if (log.isDebugEnabled()) {
                        log.debug("{}", StringUtils.join(row));
                    }
                    continue;
                }
                // 用户名
                String username = StringUtils.trimToEmpty(row[0]);
                // 姓名
                String name = StringUtils.trimToEmpty(row[1]);
                // 性别
                String gender = StringUtils.trimToEmpty(row[2]);
                // 手机号
                String phone = StringUtils.trimToEmpty(row[3]);
                // 身份证
                String sfz = StringUtils.trimToEmpty(row[4]);
                // 角色
                String role = StringUtils.trimToEmpty(row[5]);
                // 部门
                String org = StringUtils.trimToEmpty(row[6]);
                // 添加用户，校验用户名是否唯一。
                UserParam userParam = new UserParam();
                userParam.setUsername(username);
                if (this.checkUsernameIfNotExist(userParam)) {
                    // 添加用户
                    userParam.setName(name);
                    userParam.setGender(gender);
                    userParam.setPhone(phone);
                    userParam.setSfz(sfz);
                    userParam.setRoles(List.of(Long.valueOf(role)));
                    userParam.setOrgId(Long.valueOf(org));
                    userParam.setIsEnable(isGranted);
                    // 默认密码 身份证后6位
                    userParam.setPassword(StringUtils.substring(sfz, sfz.length() - 6, sfz.length()));
                    userParam.setIsAccountNonExpired(Boolean.TRUE);
                    userParam.setIsAccountNonLocked(Boolean.TRUE);
                    userParam.setIsCredentialsNonExpired(Boolean.TRUE);
                    userParam.setRemark(currentUserName + " 通过CSV 导入用户,导入时间:" + now);
                    this.add(userParam);
                    successCount++;
                } else {
                    failCount++;
                    failMsg += username + " ";
                }
            }

        } catch (Exception ex) {
            if (log.isErrorEnabled()) {
                log.error("{}:{}", ">>>> 用户导入失败", ex.getMessage());
            }
        }
        return String.format(totalMsg, sum - 4, successCount, failCount, failMsg);
    }

    @Override
    public List<UserDTO> queryUsers(UserParam queryParam) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(queryParam.getUsername()), SysUser::getUsername, queryParam.getUsername());
        wrapper.like(StringUtils.isNotBlank(queryParam.getName()), SysUser::getName, queryParam.getName());

        List<SysUser> sysUsers = baseMapper.selectList(wrapper);

        return sysUsers.stream().map(convert::toDTO).collect(Collectors.toList());
    }
}
