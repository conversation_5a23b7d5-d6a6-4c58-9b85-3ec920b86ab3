package com.yhd.admin.api.service.sys;

import java.util.Set;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.yhd.admin.api.domain.sys.enums.APIRedisKeyEnum;

import jakarta.annotation.Resource;

/**
 * @program: admin-framework
 * @description:
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2024-10-31 14:53
 **/
@Service
public class CacheCleanUpImpl implements CacheCleanUp {

    @Resource
    private RedisTemplate redisTemplate;

    @Override
    public void userMenu() {
        String redisKey = String.format(APIRedisKeyEnum.USER_MENU.getKey(), "*", "*");
        Set<String> keys = redisTemplate.keys(redisKey);
        redisTemplate.delete(keys);
    }
}
