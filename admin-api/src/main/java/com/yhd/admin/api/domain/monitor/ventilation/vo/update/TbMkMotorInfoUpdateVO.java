package com.yhd.admin.api.domain.monitor.ventilation.vo.update;

import com.yhd.admin.api.domain.monitor.ventilation.vo.create.TbMkMotorInfoCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 电机基本信息
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkMotorInfoUpdateVO extends TbMkMotorInfoCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
