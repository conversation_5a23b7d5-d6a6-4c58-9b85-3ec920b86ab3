package com.yhd.admin.api.service.operation;

import com.yhd.admin.api.dao.operation.MkOutputWorkDao;
import com.yhd.admin.api.domain.operation.convert.MkOutputWorkConvert;
import com.yhd.admin.api.domain.operation.dto.MkOutputWorkDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputWork;
import com.yhd.admin.api.domain.operation.query.MkOutputBatchParam;
import com.yhd.admin.api.domain.operation.query.MkOutputWorkParam;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 生产录入-主要工作录入表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@Service
public class MkOutputWorkServiceImpl extends ServiceImpl<MkOutputWorkDao, MkOutputWork> implements MkOutputWorkService {

    @Resource
    private MkOutputWorkConvert convert;

    @Resource
    private MkOutputWorkService service;

    @Override
    public IPage<MkOutputWorkDTO> pagingQuery(MkOutputWorkParam queryParam) {
        Page<MkOutputWork> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkOutputWork> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        queryChain.orderByDesc(MkOutputWork::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public MkOutputWorkDTO getCurrentDetail(MkOutputWorkParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchAddAndModify(MkOutputBatchParam batchParam) {
        try {
            // 批量新增
            if (batchParam.getWorkList() != null && !batchParam.getWorkList().isEmpty()) {
                List<MkOutputWork> addEntities = batchParam.getWorkList().stream()
                    .map(convert::toEntity)
                    .collect(Collectors.toList());
                super.saveBatch(addEntities);
            }

            // 批量修改
            if (batchParam.getWorkList() != null && !batchParam.getWorkList().isEmpty()) {
                List<MkOutputWork> modifyEntities = batchParam.getWorkList().stream()
                    .map(convert::toEntity)
                    .collect(java.util.stream.Collectors.toList());
                super.updateBatchById(modifyEntities);
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

}
