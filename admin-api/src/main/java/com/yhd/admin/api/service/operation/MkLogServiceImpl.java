package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.configuration.FastDFSClient;
import com.yhd.admin.api.dao.sys.SysOpsLogDao;
import com.yhd.admin.api.domain.operation.convert.MkLogConvert;
import com.yhd.admin.api.domain.operation.dto.MkLogDTO;
import com.yhd.admin.api.domain.operation.query.MkLogParam;
import com.yhd.admin.api.domain.sys.entity.SysOpsLog;
import com.yhd.admin.api.domain.sys.enums.ExceptionEnum;
import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.UserService;
import com.yhd.admin.common.domain.dto.UserDTO;
import com.yhd.admin.common.utils.excel.CellBuilder;
import com.yhd.admin.common.utils.excel.WorkbookBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class MkLogServiceImpl extends ServiceImpl<SysOpsLogDao, SysOpsLog> implements MkLogService {

    private final MkLogConvert convert;
    private final UserService userService;
    private final FastDFSClient fastDFSClient;

    public MkLogServiceImpl(MkLogConvert convert, UserService userService, FastDFSClient fastDFSClient) {
        this.convert = convert;
        this.userService = userService;
        this.fastDFSClient = fastDFSClient;
    }

    /**
     * 分页查询
     *
     * @param param 分页查询参数
     * @return {@link IPage<MkLogDTO>}
     */
    @Override
    public IPage<MkLogDTO> pagingQuery(MkLogParam param) {
        // 1. 构建分页参数
        Page<SysOpsLog> page = new Page<>(param.getCurrent(), param.getPageSize());

        // 2. 构建查询条件
        LambdaQueryChainWrapper<SysOpsLog> wrapper = buildQueryWrapper(param);

        // 3. 执行分页查询
        Page<SysOpsLog> sysOpsLogPage = wrapper.page(page);

        // 4. 转换为DTO并过滤
        List<MkLogDTO> dtoList = convert.toDTOList(sysOpsLogPage.getRecords());
        // 填充用户信息
        dtoList = batchFilterByUserInfo(dtoList, param);

        // 5. 构建并返回分页结果
        IPage<MkLogDTO> resultPage = new Page<>(param.getCurrent(), param.getPageSize());
        resultPage.setRecords(dtoList);
        resultPage.setTotal(sysOpsLogPage.getTotal());

        return resultPage;
    }

        /**
     * 列表查询
     */
    @Override
    public List<MkLogDTO> queryList(MkLogParam param) {
        LambdaQueryChainWrapper<SysOpsLog> wrapper = buildQueryWrapper(param);

        List<SysOpsLog> sysOpsLogs = wrapper.list();

        List<MkLogDTO> dtoList = convert.toDTOList(sysOpsLogs);
        // 填充用户信息
        dtoList = batchFilterByUserInfo(dtoList, param);

        return dtoList;
    }

    /**
     * 导出文件
     *
     * @param param 筛选条件
     * @return 文件地址
     */
    @Override
    public String export(MkLogParam param) {
        // 1. 获取数据
        List<MkLogDTO> dtoList = queryList(param);

        // 2. 生成文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String fileName = "系统日志导出-" + timestamp + ".xlsx";

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 3. 读取模板文件
            File tplFile = ResourceUtils.getFile("classpath:template" + File.separator + "系统日志导出模板.xlsx");
            Workbook workbook = WorkbookBuilder.getWorkBook(tplFile);
            Sheet sheet = workbook.getSheetAt(0);

            // 4. 设置样式
            CellStyle contentStyle = createContentStyle(workbook);

            // 5. 跳过标题行，从第一行开始填充数据
            int rowNum = 1;

            for (MkLogDTO dto : dtoList) {
                Row row = sheet.createRow(rowNum++);
                CellBuilder.build(row, 0, dto.getUnitName(), contentStyle);
                CellBuilder.build(row, 1, dto.getOrgName(), contentStyle);
                CellBuilder.build(row, 2, dto.getUserAccount(), contentStyle);
                CellBuilder.build(row, 3, dto.getUserName(), contentStyle);
                CellBuilder.build(row, 4, dto.getIp(), contentStyle);
                CellBuilder.build(row, 5, dto.getCreatedTime(), contentStyle);
                CellBuilder.build(row, 6, dto.getUrl(), contentStyle);
            }

            // 6. 写入输出流
            workbook.write(outputStream);

            // 7. 上传到文件服务器
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            return fastDFSClient.getFullPath(fastDFSClient.uploadFile(inputStream, fileName));

        } catch (Exception e) {
            throw new BMSException(ExceptionEnum.EXPORT_ERROR);
        }
    }

    /**
     * 创建内容单元格样式
     */
    private CellStyle createContentStyle(Workbook workbook) {
        CellStyle contentStyle = workbook.createCellStyle();
        DataFormat format = workbook.createDataFormat();
        contentStyle.setDataFormat(format.getFormat("yyyy-mm-dd hh:mm:ss"));
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setAlignment(HorizontalAlignment.CENTER);
        contentStyle.setWrapText(true);
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);
        contentStyle.setBorderTop(BorderStyle.THIN);
        return contentStyle;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryChainWrapper<SysOpsLog> buildQueryWrapper(MkLogParam param) {
        LambdaQueryChainWrapper<SysOpsLog> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.ge(Objects.nonNull(param.getStartDate()), SysOpsLog::getCreatedTime, param.getStartDate())
            .le(Objects.nonNull(param.getEndDate()), SysOpsLog::getCreatedTime, param.getEndDate())
            .like(StringUtils.isNotBlank(param.getUserAccount()), SysOpsLog::getCreatedBy, param.getUserAccount())
            .orderByDesc(SysOpsLog::getCreatedTime);
        return wrapper;
    }

    /**
     * 批量根据用户信息过滤日志
     */
    private List<MkLogDTO> batchFilterByUserInfo(List<MkLogDTO> logDTOList, MkLogParam param) {
        Set<String> userAccounts = logDTOList.stream()
                .map(MkLogDTO::getUserAccount)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        UserParam userParam = new UserParam();
        userParam.setUserAccountList(new ArrayList<>(userAccounts));
        List<UserDTO> userDTOList = userService.queryUsers(userParam);

        // 构建用户信息映射
        Map<String, UserDTO> userMap = userDTOList.stream()
                .collect(Collectors.toMap(UserDTO::getUsername, Function.identity(), (u1, u2) -> u1));

        // 过滤并填充用户信息
        return logDTOList.stream()
                .filter(logDTO -> {
                    UserDTO userDTO = userMap.get(logDTO.getUserAccount());
                    if (userDTO == null) {
                        return false;
                    }

                    // 设置用户姓名
                    logDTO.setUserName(userDTO.getName());

                    // 处理单位和部门名称
                    String orgTxt = userDTO.getOrgTxt();
                    String unitName = null;
                    String orgName = null;

                    if (orgTxt != null) {
                        if (orgTxt.contains(">")) {
                            String[] parts = orgTxt.split(">", 2);
                            unitName = parts[0];
                            orgName = parts.length > 1 ? parts[1] : null;
                        } else {
                            unitName = orgTxt;
                        }
                    }

                    logDTO.setUnitName(unitName);
                    logDTO.setOrgName(orgName);

                    // 根据查询条件过滤
                    boolean matchUnit = StringUtils.isBlank(param.getUnitName()) || param.getUnitName().equals(unitName);
                    boolean matchDept = StringUtils.isBlank(param.getOrgName()) || param.getOrgName().equals(orgName);
                    boolean matchUser = StringUtils.isBlank(param.getUserName()) || param.getUserName().equals(userDTO.getName());

                    return matchUnit && matchDept && matchUser;
                })
                .collect(Collectors.toList());
    }
}
