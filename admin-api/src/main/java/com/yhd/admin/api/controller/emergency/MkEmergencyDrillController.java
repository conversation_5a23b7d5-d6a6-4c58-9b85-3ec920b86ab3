package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyDrillConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyDrillDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyDrillParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyDrillVO;
import com.yhd.admin.api.service.emergency.MkEmergencyDrillService;
import com.yhd.admin.common.domain.PageRespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 应急演练记录表 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/open/emergency/drill")
public class MkEmergencyDrillController {

    @Resource
    private MkEmergencyDrillService emergencyDrillService;

    @Resource
    private MkEmergencyDrillConvert convert;

    /**
     * 分页查询应急演练信息
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkEmergencyDrillVO> pagingQuery(@RequestBody MkEmergencyDrillParam queryParam) {
        log.info("分页查询应急演练信息，参数：{}", queryParam);
        IPage<MkEmergencyDrillDTO> iPage = emergencyDrillService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }


}
