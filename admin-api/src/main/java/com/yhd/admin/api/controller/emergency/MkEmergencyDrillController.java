package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkEmergencyDrillConvert;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencyDrillDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencyDrillParam;
import com.yhd.admin.api.domain.emergency.vo.MkEmergencyDrillVO;
import com.yhd.admin.api.service.emergency.MkEmergencyDrillService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 应急演练记录表 Controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/emergency/drill")
public class MkEmergencyDrillController {

    @Resource
    private MkEmergencyDrillService emergencyDrillService;

    @Resource
    private MkEmergencyDrillConvert convert;

    /**
     * 分页查询应急演练信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkEmergencyDrillVO> pagingQuery(@RequestBody MkEmergencyDrillParam queryParam) {
        log.info("分页查询应急演练信息，参数：{}", queryParam);
        IPage<MkEmergencyDrillDTO> iPage = emergencyDrillService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    /**
     * 新增应急演练信息
     * @param param 应急演练信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkEmergencyDrillParam param) {
        log.info("新增应急演练信息，参数：{}", param);
        Boolean result = emergencyDrillService.add(param);
        return RespJson.success(result);
    }

    /**
     * 修改应急演练信息
     * @param param 应急演练信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> modify(@RequestBody MkEmergencyDrillParam param) {
        log.info("修改应急演练信息，参数：{}", param);
        Boolean result = emergencyDrillService.modify(param);
        return RespJson.success(result);
    }

    /**
     * 获取应急演练详情
     * @param param 查询参数
     * @return 应急演练详情
     */
    @PostMapping(value = "/getCurrentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkEmergencyDrillVO> getCurrentDetail(@RequestBody MkEmergencyDrillParam param) {
        log.info("获取应急演练详情，参数：{}", param);
        MkEmergencyDrillDTO dto = emergencyDrillService.getCurrentDetail(param);
        return RespJson.success(convert.toVO(dto));
    }

    /**
     * 批量删除应急演练信息
     * @param param 批量删除参数
     * @return 操作结果
     */
    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody BatchParam param) {
        log.info("批量删除应急演练信息，参数：{}", param);
        Boolean result = emergencyDrillService.removeBatch(param);
        return RespJson.success(result);
    }
}
