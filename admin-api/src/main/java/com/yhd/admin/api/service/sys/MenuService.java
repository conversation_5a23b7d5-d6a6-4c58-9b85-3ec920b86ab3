package com.yhd.admin.api.service.sys;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.sys.dto.MenuDTO;
import com.yhd.admin.api.domain.sys.entity.SysMenu;
import com.yhd.admin.api.domain.sys.query.MenuParam;
import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName MenuService.java @Description TODO 菜单业务操作类
 * @createTime 2020年03月30日 10:23:00
 */
public interface MenuService extends IService<SysMenu> {

    /**
     * 根据用户查询用户对应的菜单
     *
     * <p>
     * - 首先从缓存中查询，<br>
     * -缓存没有命中，从数据库中查询。<br>
     *
     * @param param {@link UserParam}
     * @return
     */
    List<MenuDTO> queryMenuByUserWithCache(UserParam param);

    /**
     * 根据用户查询用户对应的菜单
     *
     * @param param {@link UserParam}
     * @return
     */
    List<MenuDTO> queryMenuByUser(UserParam param);

    /**
     * 根据菜单ID查询对应的子菜单递归查询，包含子子菜单。不包含自身节点
     *
     * @param menuId 菜单ID
     * @return {@link List<MenuDTO>} 子菜单
     */
    List<MenuDTO> querySubMenuById(Long menuId);

    /**
     * 根据菜单ID查询对应的子菜单递归查询，包含子子菜单。不包含自身节点
     *
     * @param menuParam 查询条件
     * @return {@link List<MenuDTO>} 子菜单
     */
    List<MenuDTO> querySubMenu(MenuParam menuParam);

    /**
     * 根据菜单ID查询对应的子菜单递归查询，包含子子菜单。包含自身节点
     *
     * @param menuId 菜单ID
     * @return {@link MenuDTO} 菜单树
     */
    MenuDTO querySubMenuIncludeSelf(Long menuId);

    /**
     * 菜单列表分页查询
     *
     * @param queryParam 菜单查询参数
     * @return IPage<MenuDTO>
     */
    IPage<MenuDTO> pagingQuery(MenuParam queryParam);

    /**
     * 根据 ID 更新编辑
     *
     * @param modifyParam 更新对象。
     * @return 是否成功
     */
    Boolean modify(MenuParam modifyParam);

    /**
     * 根据主键批量删除。
     *
     * @param batchParam 主键 List
     * @return true 成功 false 失败。
     */
    Boolean removeBatch(BatchParam batchParam);

    /**
     * 新增菜单
     *
     * @param addParam 菜单
     * @return true 成功 false 失败。
     */
    Boolean addOrUpdate(MenuParam addParam);

    /**
     * 根据 主键查询菜单详情
     *
     * @param menuId 菜单 ID
     * @return {@link MenuDTO}
     */
    MenuDTO obtainDetailById(Long menuId);

    /**
     * @param menuIds
     * @return
     */
    List<MenuDTO> findParentBySubId(String clientId, List<Long> menuIds);

    /**
     * 校验菜单的 路由是否存在。
     *
     * @param param
     * @return
     */
    Boolean validateIfNotExist(MenuParam param);

    /**
     * 获取用户权限
     *
     * @param param UserAccountParam
     * @return 权限集合
     */
    List<String> queryUserAuthority(UserParam param);

}
