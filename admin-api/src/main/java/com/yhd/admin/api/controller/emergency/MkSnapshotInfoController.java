package com.yhd.admin.api.controller.emergency;

import com.yhd.admin.api.domain.emergency.convert.MkSnapshotInfoConvert;
import com.yhd.admin.api.domain.emergency.dto.MkSnapshotInfoDTO;
import com.yhd.admin.api.domain.emergency.query.MkSnapshotInfoParam;
import com.yhd.admin.api.domain.emergency.vo.MkSnapshotInfoVO;
import com.yhd.admin.api.service.emergency.MkSnapshotInfoService;
import com.yhd.admin.common.domain.RespJson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 应急救援中心-快照信息 Controller
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/emergency/snapshot-info")
public class MkSnapshotInfoController {

    @Resource
    private MkSnapshotInfoService snapshotInfoService;

    @Resource
    private MkSnapshotInfoConvert convert;


    /**
     * 根据列表类型查询快照信息列表
     *
     * @param param 查询参数
     * @return 快照信息列表
     */
    @PostMapping(value = "/getInfosByListType", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkSnapshotInfoVO>> getInfosByListType(@RequestBody MkSnapshotInfoParam param) {
        log.info("根据列表类型查询快照信息列表，参数：{}", param);
        List<MkSnapshotInfoDTO> dtoList = snapshotInfoService.getInfosByListType(param);
        return RespJson.success(convert.toVO(dtoList));
    }


}
