package com.yhd.admin.api.controller.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.convert.MkSnapshotInfoConvert;
import com.yhd.admin.api.domain.emergency.dto.MkSnapshotInfoDTO;
import com.yhd.admin.api.domain.emergency.query.MkSnapshotInfoParam;
import com.yhd.admin.api.domain.emergency.vo.MkSnapshotInfoVO;
import com.yhd.admin.api.service.emergency.MkSnapshotInfoService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应急救援中心-快照信息 Controller
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/emergency/snapshot-info")
public class MkSnapshotInfoController {

    @Resource
    private MkSnapshotInfoService snapshotInfoService;

    @Resource
    private MkSnapshotInfoConvert convert;

    /**
     * 分页查询快照信息
     * @param queryParam 查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<MkSnapshotInfoVO> pagingQuery(@RequestBody MkSnapshotInfoParam queryParam) {
        log.info("分页查询快照信息，参数：{}", queryParam);
        IPage<MkSnapshotInfoDTO> iPage = snapshotInfoService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    /**
     * 新增快照信息
     * @param param 快照信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/add", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkSnapshotInfoParam param) {
        log.info("新增快照信息，参数：{}", param);
        Boolean result = snapshotInfoService.add(param);
        return RespJson.success(result);
    }

    /**
     * 修改快照信息
     * @param param 快照信息参数
     * @return 操作结果
     */
    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> modify(@RequestBody MkSnapshotInfoParam param) {
        log.info("修改快照信息，参数：{}", param);
        Boolean result = snapshotInfoService.modify(param);
        return RespJson.success(result);
    }

    /**
     * 获取快照信息详情
     * @param param 查询参数
     * @return 快照信息详情
     */
    @PostMapping(value = "/getCurrentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkSnapshotInfoVO> getCurrentDetail(@RequestBody MkSnapshotInfoParam param) {
        log.info("获取快照信息详情，参数：{}", param);
        MkSnapshotInfoDTO dto = snapshotInfoService.getCurrentDetail(param);
        return RespJson.success(convert.toVO(dto));
    }

    /**
     * 批量删除快照信息
     * @param param 批量删除参数
     * @return 操作结果
     */
    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody BatchParam param) {
        log.info("批量删除快照信息，参数：{}", param);
        Boolean result = snapshotInfoService.removeBatch(param);
        return RespJson.success(result);
    }

    /**
     * 根据快照ID查询快照信息列表
     * @param snapshotId 快照ID
     * @return 快照信息列表
     */
    @GetMapping(value = "/getInfosBySnapshotId", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkSnapshotInfoVO>> getInfosBySnapshotId(@RequestParam Long snapshotId) {
        log.info("根据快照ID查询快照信息列表，快照ID：{}", snapshotId);
        List<MkSnapshotInfoDTO> dtoList = snapshotInfoService.getInfosBySnapshotId(snapshotId);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据快照ID和信息类型查询快照信息列表
     * @param snapshotId 快照ID
     * @param infoType 信息类型
     * @return 快照信息列表
     */
    @GetMapping(value = "/getInfosBySnapshotIdAndType", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkSnapshotInfoVO>> getInfosBySnapshotIdAndType(@RequestParam Long snapshotId, @RequestParam String infoType) {
        log.info("根据快照ID和信息类型查询快照信息列表，快照ID：{}，信息类型：{}", snapshotId, infoType);
        List<MkSnapshotInfoDTO> dtoList = snapshotInfoService.getInfosBySnapshotIdAndType(snapshotId, infoType);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据快照ID和状态查询快照信息列表
     * @param snapshotId 快照ID
     * @param status 状态
     * @return 快照信息列表
     */
    @GetMapping(value = "/getInfosBySnapshotIdAndStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkSnapshotInfoVO>> getInfosBySnapshotIdAndStatus(@RequestParam Long snapshotId, @RequestParam String status) {
        log.info("根据快照ID和状态查询快照信息列表，快照ID：{}，状态：{}", snapshotId, status);
        List<MkSnapshotInfoDTO> dtoList = snapshotInfoService.getInfosBySnapshotIdAndStatus(snapshotId, status);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据快照ID和优先级查询快照信息列表
     * @param snapshotId 快照ID
     * @param priorityLevel 优先级
     * @return 快照信息列表
     */
    @GetMapping(value = "/getInfosBySnapshotIdAndPriority", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkSnapshotInfoVO>> getInfosBySnapshotIdAndPriority(@RequestParam Long snapshotId, @RequestParam Integer priorityLevel) {
        log.info("根据快照ID和优先级查询快照信息列表，快照ID：{}，优先级：{}", snapshotId, priorityLevel);
        List<MkSnapshotInfoDTO> dtoList = snapshotInfoService.getInfosBySnapshotIdAndPriority(snapshotId, priorityLevel);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据列表类型查询快照信息列表
     * @param param 查询参数
     * @return 快照信息列表
     */
    @PostMapping(value = "/getInfosByListType", consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkSnapshotInfoVO>> getInfosByListType(@RequestBody MkSnapshotInfoParam param) {
        log.info("根据列表类型查询快照信息列表，参数：{}", param);
        List<MkSnapshotInfoDTO> dtoList = snapshotInfoService.getInfosByListType(param);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 获取快照信息时间列表
     * @param snapshotId 快照ID
     * @return 时间列表
     */
    @GetMapping(value = "/getTimeList", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkSnapshotInfoVO>> getTimeList(@RequestParam Long snapshotId) {
        log.info("获取快照信息时间列表，快照ID：{}", snapshotId);
        List<MkSnapshotInfoDTO> dtoList = snapshotInfoService.getTimeList(snapshotId);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 获取快照信息事件列表
     * @param snapshotId 快照ID
     * @return 事件列表
     */
    @GetMapping(value = "/getEventList", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkSnapshotInfoVO>> getEventList(@RequestParam Long snapshotId) {
        log.info("获取快照信息事件列表，快照ID：{}", snapshotId);
        List<MkSnapshotInfoDTO> dtoList = snapshotInfoService.getEventList(snapshotId);
        return RespJson.success(convert.toVO(dtoList));
    }

    /**
     * 根据列表类型查询快照信息列表
     * @param snapshotId 快照ID
     * @param listType 列表类型(EVENT:事件列表,TIME:时间列表)
     * @return 快照信息列表
     */
    @GetMapping(value = "/getInfosByListType", produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkSnapshotInfoVO>> getInfosByListType(@RequestParam Long snapshotId, @RequestParam String listType) {
        log.info("根据列表类型查询快照信息列表，快照ID：{}，列表类型：{}", snapshotId, listType);
        List<MkSnapshotInfoDTO> dtoList = snapshotInfoService.getInfosByListType(snapshotId, listType);
        return RespJson.success(convert.toVO(dtoList));
    }
}
