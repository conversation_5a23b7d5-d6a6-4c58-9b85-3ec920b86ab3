package com.yhd.admin.api.domain.sys.enums;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName ExceptionEnum.java @Description TODO
 *     异常编码，定义4位编码，如果1000～1999为一组，不同模块采用不同组，不要交叉使用。
 * @createTime 2020年04月01日 15:08:00
 */
public enum ExceptionEnum {
  // 通用异常0000-0099
  UNKNOWN_EXCEPTION("0000", "系统未知异常"),
  VALID_EXCEPTION("0001", "参数格式校验异常"),
  MISS_PARAM_EXCEPTION("0002", "缺少请求参数异常"),
  STRING_INDEX_OUT_OF_BOUNDS_EXCEPTION("0003", "字符串下标越界异常"),
  NO_HANDLER_FOUND_EXCEPTION("0004", "请求路径不存在异常"),
  DUPLICATE_KEY_EXCEPTION("0005", "数据库中已存在该记录异常"),
  AUTHORIZATION_EXCEPTION("0006", "没有权限，请联系管理员授权"),
  PARAM_EMPTY_ERROR("0007", "请求参数不能为空"),
  // User 用户类 【1000-1999】
  USER_NAME_NULL("1000", "账户名为空，请检查"),
  USER_ID_NULL("1001", "用户ID为空，请检查"),
  CHECK_REQUIRED("1005", "请确认必填项！"),
  USER_NAME_ID_NULL("1002", "用户ID和账户名为空，请检查"),
  USER_PASSWD_NOT_MATCH("1003", "原始密码不匹配"),
  USER_PASSWD_NULL("1004", "重置密码为空"),
  DIC_ID_CATEGORY_NULL("9000", "字典项分类ID和分类编码都为空，请检查"),
  FILE_UPLOAD_ERROR("1006", "文件上传失败"),
  ;

  private final String code;
  private final String desc;

  ExceptionEnum(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public String getCode() {
    return code;
  }

  public String getDesc() {
    return desc;
  }
}
