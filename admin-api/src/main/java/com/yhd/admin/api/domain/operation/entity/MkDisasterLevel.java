package com.yhd.admin.api.domain.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;

/**
 * 算法模型-报警级别表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-08-01
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MkDisasterLevel extends BaseEntity implements Cloneable, Serializable {
    /**
     * Id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

	/**
	* 开始级别code
	*/
	private String startCode;

	/**
	* 开始级别name
	*/
	private String startName;

	/**
	* 结束级别code
	*/
	private String endCode;

	/**
	* 结束级别name
	*/
	private String endName;

	/**
	* 报警级别
	*/
	private String warnLevel;

	/**
	* 监测名称
	*/
	private String monitorName;
}
