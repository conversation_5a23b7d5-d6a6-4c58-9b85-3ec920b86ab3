package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.sys.entity.FileManager;

import java.util.List;


public interface FileService extends IService<FileManager> {

    /**
     * 保存提交的附件，先删除原有的全部附件，再重新保存新提交的全部附件
     *
     * @param relationId
     * @param businessType
     * @param fileType
     * @param fileList
     * @return
     */
    boolean insertFile(
        Long relationId, String businessType, String fileType, List<String> fileList);

    /**
     * @param relationId
     * @param businessType
     * @param fileType
     * @return
     */
    List<FileManager> queryFileListByRelationId(Long relationId, String businessType, String fileType);

    /**
     * 根据查询条件，查看全部附件信息
     *
     * @param relationId
     * @param businessType
     * @param fileType
     * @return
     */
    List<String> getFileList(Long relationId, String businessType, String fileType);

    /**
     * 根据查询条件，删除附件信息
     *
     * @param relationId
     * @param businessType
     * @param fileType
     * @return
     */
    boolean removeFile(Long relationId, String businessType, String fileType);

    int removeFile(Long relationId, String businessType);


    /**
     * 根据项目编号，查询该项目的施工日志的图片
     *
     * @param relationId
     * @param businessType
     * @param fileType
     * @return
     */
    List<String> queryFilePictureByRelationId(Long relationId, String businessType, String fileType);

}
