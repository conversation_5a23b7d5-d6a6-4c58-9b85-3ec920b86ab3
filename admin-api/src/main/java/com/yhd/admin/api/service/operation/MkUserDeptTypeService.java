package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkUserDeptTypeDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserDeptType;
import com.yhd.admin.api.domain.operation.query.MkUserDeptTypeParam;
import com.yhd.admin.common.domain.query.BatchParam;

import java.util.List;

/**
 * 运维配置中心-部门类型-Service
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */
public interface MkUserDeptTypeService extends IService<MkUserDeptType> {

    IPage<MkUserDeptTypeDTO> pagingQuery(MkUserDeptTypeParam queryParam);

     Boolean add(MkUserDeptTypeParam param);

    Boolean modify(MkUserDeptTypeParam param);

    Boolean removeBatch(BatchParam param);

    MkUserDeptTypeDTO getCurrentDetail(MkUserDeptTypeParam param);

    List<MkUserDeptTypeDTO> getList(MkUserDeptTypeParam param);
}
