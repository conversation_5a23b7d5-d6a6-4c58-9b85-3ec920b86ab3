package com.yhd.admin.api.domain.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;

/**
 * 生产录入-值班人员表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class MkOutputDuty extends BaseEntity implements Cloneable, Serializable {
    /**
     * Id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

	/**
	* 日期
	*/
	private Date outputDate;

	/**
	* 班次类型 0：值班领导 1：一班 2：二班 3：三班 4：四班
	*/
	private Integer shiftType;

	/**
	* 值班人员id
	*/
	private String userNo;

	/**
	* 值班人员名称
	*/
	private String userName;

}
