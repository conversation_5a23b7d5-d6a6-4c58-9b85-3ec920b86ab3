package com.yhd.admin.api.domain.monitor.ventilation.vo.create;

import lombok.Data;

import java.io.Serializable;

/**
 * 电机基本信息
 *
 * <AUTHOR>
 * @since
 */
@Data
public class TbMkMotorInfoCreateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联通风机基本信息表ID
     */
    private Long ventilationId;

    /**
     * 电机名称
     */
    private String motorName;

    /**
     * 电机编号
     */
    private String motorNumber;

    /**
     * 备注信息
     */
    private String remarks;

}
