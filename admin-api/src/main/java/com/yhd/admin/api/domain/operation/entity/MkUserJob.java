package com.yhd.admin.api.domain.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 运维配置中心-职务/工种管理-职务/工种管理表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tb_mk_user_job")
public class MkUserJob extends BaseEntity implements Serializable {
    /**
     * Id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 职务/工种编码
     */
    private String jobCode;

    /**
     * 职务/工种名称
     */
    private String jobName;

    /**
     * 单位ID
     */
    private String orgId;

    /**
     * 单位名称
     */
    private String orgName;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;
}
