package com.yhd.admin.api.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yhd.admin.api.dao.emergency.MkAreaSafetyAssessmentDao;
import com.yhd.admin.api.domain.emergency.entity.MkAreaSafetyAssessment;
import com.yhd.admin.api.domain.emergency.query.MkAreaSafetyAssessmentParam;
import com.yhd.admin.api.service.emergency.MkAreaSafetyAssessmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkAreaSafetyAssessmentServiceImpl.java
 * @Description 区域安全评估Service实现类
 * @createTime 2025年08月02日 10:00:00
 */
@Slf4j
@Service
public class MkAreaSafetyAssessmentServiceImpl implements MkAreaSafetyAssessmentService {

    private final MkAreaSafetyAssessmentDao areaSafetyAssessmentDao;

    public MkAreaSafetyAssessmentServiceImpl(MkAreaSafetyAssessmentDao areaSafetyAssessmentDao) {
        this.areaSafetyAssessmentDao = areaSafetyAssessmentDao;
    }

    @Override
    public IPage<MkAreaSafetyAssessment> pagingQuery(MkAreaSafetyAssessmentParam param) {
        log.debug("分页查询区域安全评估，参数：{}", param);

        Page<MkAreaSafetyAssessment> page = new Page<>(1, 10); // 默认分页参数
        LambdaQueryWrapper<MkAreaSafetyAssessment> queryWrapper = buildQueryWrapper(param);

        return areaSafetyAssessmentDao.selectPage(page, queryWrapper);
    }

    @Override
    public List<MkAreaSafetyAssessment> queryList(MkAreaSafetyAssessmentParam param) {
        log.debug("查询区域安全评估列表，参数：{}", param);

        LambdaQueryWrapper<MkAreaSafetyAssessment> queryWrapper = buildQueryWrapper(param);
        return areaSafetyAssessmentDao.selectList(queryWrapper);
    }

    @Override
    public MkAreaSafetyAssessment getByAreaCode(String areaCode) {
        log.debug("根据区域编码查询：{}", areaCode);

        if (!StringUtils.hasText(areaCode)) {
            return null;
        }

        LambdaQueryWrapper<MkAreaSafetyAssessment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkAreaSafetyAssessment::getAreaCode, areaCode);

        return areaSafetyAssessmentDao.selectOne(queryWrapper);
    }

    @Override
    public List<Map<String, Object>> statisticsByRiskLevel(String orgCode, String monitoringType) {
        log.debug("按风险等级统计，组织编码：{}，监测类型：{}", orgCode, monitoringType);

        LambdaQueryWrapper<MkAreaSafetyAssessment> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(orgCode)) {
            queryWrapper.eq(MkAreaSafetyAssessment::getOrgCode, orgCode);
        }
        if (StringUtils.hasText(monitoringType)) {
            queryWrapper.eq(MkAreaSafetyAssessment::getMonitoringType, monitoringType);
        }

        List<MkAreaSafetyAssessment> list = areaSafetyAssessmentDao.selectList(queryWrapper);

        // 按风险等级分组统计
        Map<String, Long> riskStats = list.stream()
            .collect(Collectors.groupingBy(
                item -> item.getRiskLevel() != null ? item.getRiskLevel() : "未知",
                Collectors.counting()));

        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<String, Long> entry : riskStats.entrySet()) {
            Map<String, Object> stat = new HashMap<>();
            stat.put("riskLevel", entry.getKey());
            stat.put("count", entry.getValue());
            result.add(stat);
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> statisticsByAreaType(String orgCode, String monitoringType) {
        log.debug("按区域类型统计，组织编码：{}，监测类型：{}", orgCode, monitoringType);

        LambdaQueryWrapper<MkAreaSafetyAssessment> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(orgCode)) {
            queryWrapper.eq(MkAreaSafetyAssessment::getOrgCode, orgCode);
        }
        if (StringUtils.hasText(monitoringType)) {
            queryWrapper.eq(MkAreaSafetyAssessment::getMonitoringType, monitoringType);
        }

        List<MkAreaSafetyAssessment> list = areaSafetyAssessmentDao.selectList(queryWrapper);

        // 按区域类型分组统计
        Map<String, Long> areaStats = list.stream()
            .collect(Collectors.groupingBy(
                item -> item.getAreaType() != null ? item.getAreaType() : "未知",
                Collectors.counting()));

        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<String, Long> entry : areaStats.entrySet()) {
            Map<String, Object> stat = new HashMap<>();
            stat.put("areaType", entry.getKey());
            stat.put("count", entry.getValue());
            result.add(stat);
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> statisticsByMonitoringType(String orgCode) {
        log.debug("按监测类型统计，组织编码：{}", orgCode);

        LambdaQueryWrapper<MkAreaSafetyAssessment> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(orgCode)) {
            queryWrapper.eq(MkAreaSafetyAssessment::getOrgCode, orgCode);
        }

        List<MkAreaSafetyAssessment> list = areaSafetyAssessmentDao.selectList(queryWrapper);

        // 按监测类型分组统计
        Map<String, Long> monitoringStats = list.stream()
            .collect(Collectors.groupingBy(
                item -> item.getMonitoringType() != null ? item.getMonitoringType() : "未知",
                Collectors.counting()));

        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<String, Long> entry : monitoringStats.entrySet()) {
            Map<String, Object> stat = new HashMap<>();
            stat.put("monitoringType", entry.getKey());
            stat.put("count", entry.getValue());
            result.add(stat);
        }

        return result;
    }

    /**
     * 构建查询条件
     *
     * @param param 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<MkAreaSafetyAssessment> buildQueryWrapper(MkAreaSafetyAssessmentParam param) {
        LambdaQueryWrapper<MkAreaSafetyAssessment> queryWrapper = new LambdaQueryWrapper<>();
        if (param == null) {
            return queryWrapper;
        }

        // 区域编码
        if (StringUtils.hasText(param.getAreaCode())) {
            queryWrapper.eq(MkAreaSafetyAssessment::getAreaCode, param.getAreaCode());
        }

        // 区域名称（模糊搜索）
        if (StringUtils.hasText(param.getAreaName())) {
            queryWrapper.like(MkAreaSafetyAssessment::getAreaName, param.getAreaName());
        }

        // 区域类型
        if (StringUtils.hasText(param.getAreaType())) {
            queryWrapper.eq(MkAreaSafetyAssessment::getAreaType, param.getAreaType());
        }

        // 评估类型
        if (StringUtils.hasText(param.getAssessmentType())) {
            queryWrapper.eq(MkAreaSafetyAssessment::getAssessmentType, param.getAssessmentType());
        }


        // 风险等级
        if (StringUtils.hasText(param.getRiskLevel())) {
            queryWrapper.eq(MkAreaSafetyAssessment::getRiskLevel, param.getRiskLevel());
        }

        // 评估结果
        if (StringUtils.hasText(param.getAssessmentResult())) {
            queryWrapper.eq(MkAreaSafetyAssessment::getAssessmentResult, param.getAssessmentResult());
        }

        // 状态
        if (param.getStatus() != null) {
            queryWrapper.eq(MkAreaSafetyAssessment::getStatus, param.getStatus());
        }

        // 组织编码
        if (StringUtils.hasText(param.getOrgCode())) {
            queryWrapper.eq(MkAreaSafetyAssessment::getOrgCode, param.getOrgCode());
        }
        // 类型
        if (StringUtils.hasText(param.getMonitoringType())) {
            queryWrapper.eq(MkAreaSafetyAssessment::getMonitoringType, param.getMonitoringType());
        }
        // 关键词搜索（区域名称或位置信息）
        if (StringUtils.hasText(param.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                .like(MkAreaSafetyAssessment::getAreaName, param.getKeyword())
                .or()
                .like(MkAreaSafetyAssessment::getLocation, param.getKeyword()));
        }

        // 按更新时间倒序
        queryWrapper.orderByDesc(MkAreaSafetyAssessment::getUpdatedTime);

        return queryWrapper;
    }
}
