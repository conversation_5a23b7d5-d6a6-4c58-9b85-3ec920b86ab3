package com.yhd.admin.api.service.emergency;

import com.yhd.admin.api.domain.emergency.query.MkSafetyDecisionParam;
import com.yhd.admin.api.domain.emergency.vo.MkSafetyDecisionVO;

/**
 * 安全决策大屏 Service
 * 
 * <AUTHOR>
 */
public interface MkSafetyDecisionService {

    /**
     * 获取安全决策大屏数据
     *
     * @param param 查询参数
     * @return 安全决策大屏数据
     */
    MkSafetyDecisionVO getSafetyDecisionData(MkSafetyDecisionParam param);
}