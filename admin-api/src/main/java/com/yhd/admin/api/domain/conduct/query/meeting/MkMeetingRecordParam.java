package com.yhd.admin.api.domain.conduct.query.meeting;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 调度会议记录(MkMeetingRecord)Param
 *
 * <AUTHOR>
 * @since 2025-07-30 15:35:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkMeetingRecordParam extends QueryParam implements Serializable {

    /** 主键列表ids */
    private List<Long> ids;
    /** 会议记录id */
    private Long id;
    /** 会议日期 */
    private LocalDate date;
    /** 会议名称 */
    private String name;
    /** 会议类型名称 */
    private String typeName;
    /** 会议类型id */
    private Long typeId;
    /** 上传日期 */
    private LocalDate uploadDate;
    /** 创建人 */
    private String creator;
    /** 会记录文件url */
    private List<String> recordFile;
    /** 签名表单文件url */
    private List<String> signUpSheet;
    /** 图片文件url */
    private List<String> pictureFile;
    /** 开始日期 */
    private LocalDate startDate;
    /** 结束日期 */
    private LocalDate endDate;
}

