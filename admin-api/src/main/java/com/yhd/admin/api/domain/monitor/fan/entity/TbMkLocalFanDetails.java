package com.yhd.admin.api.domain.monitor.fan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.admin.common.domain.entity.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 局扇风机详细监控数据
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_mk_local_fan_details")
public class TbMkLocalFanDetails extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联风机单元ID
     */
    @TableField("fan_unit_id")
    private Long fanUnitId;

    /**
     * 风机风量(m³/min)
     */
    @TableField("air_volume")
    private BigDecimal airVolume;

    /**
     * 垂直振动(mm/s)
     */
    @TableField("vertical_vibration")
    private BigDecimal verticalVibration;

    /**
     * 水平振动(mm/s)
     */
    @TableField("horizontal_vibration")
    private BigDecimal horizontalVibration;

    /**
     * 瓦斯浓度T1(%)
     */
    @TableField("gas_concentration_t1")
    private BigDecimal gasConcentrationT1;

    /**
     * 瓦斯浓度T2(%)
     */
    @TableField("gas_concentration_t2")
    private BigDecimal gasConcentrationT2;

    /**
     * 瓦斯浓度T3(%)
     */
    @TableField("gas_concentration_t3")
    private BigDecimal gasConcentrationT3;

    /**
     * I级电机电压(V)
     */
    @TableField("stage_i_motor_voltage")
    private BigDecimal stageIMotorVoltage;

    /**
     * I级电机电流(A)
     */
    @TableField("stage_i_motor_current")
    private BigDecimal stageIMotorCurrent;

    /**
     * I级前机电流(A)
     */
    @TableField("stage_i_front_motor_current")
    private BigDecimal stageIFrontMotorCurrent;

    /**
     * I级后机电流(A)
     */
    @TableField("stage_i_rear_motor_current")
    private BigDecimal stageIRearMotorCurrent;

    /**
     * 前级前轴温度(°C)
     */
    @TableField("stage_i_front_bearing_temperature")
    private BigDecimal stageIFrontBearingTemperature;

    /**
     * 前级后轴温度(°C)
     */
    @TableField("stage_i_rear_bearing_temperature")
    private BigDecimal stageIRearBearingTemperature;

    /**
     * 前级绕组温度(°C)
     */
    @TableField("stage_i_winding_temperature")
    private BigDecimal stageIWindingTemperature;

    /**
     * 后级前轴温度(°C)
     */
    @TableField("stage_ii_front_bearing_temperature")
    private BigDecimal stageIiFrontBearingTemperature;

    /**
     * 后级后轴温度(°C)
     */
    @TableField("stage_ii_rear_bearing_temperature")
    private BigDecimal stageIiRearBearingTemperature;

    /**
     * 后级绕组温度(°C)
     */
    @TableField("stage_ii_winding_temperature")
    private BigDecimal stageIiWindingTemperature;

    /**
     * 风机状态
     */
    @TableField("status")
    private String status;
}
