package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkUserJobDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserJob;
import com.yhd.admin.api.domain.operation.query.MkUserJobParam;
import com.yhd.admin.api.domain.operation.vo.MkUserJobVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 职务/工种管理表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-01-27
 */
@Mapper(componentModel = "spring")
public interface MkUserJobConvert {

    MkUserJob toEntity(MkUserJobParam param);

    MkUserJobVO toVO(MkUserJobDTO dto);

    List<MkUserJobVO> toVOList(List<MkUserJobDTO> dtos);

    MkUserJobDTO toDTO(MkUserJob entity);

    List<MkUserJobDTO> toDTOList(List<MkUserJob> entity);
} 