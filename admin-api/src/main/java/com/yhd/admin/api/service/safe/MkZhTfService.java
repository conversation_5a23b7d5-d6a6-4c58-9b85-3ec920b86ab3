package com.yhd.admin.api.service.safe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.safe.dto.MkZhTfDTO;
import com.yhd.admin.api.domain.safe.entity.MkZhTf;
import com.yhd.admin.api.domain.safe.query.MkZhTfParam;


public interface MkZhTfService extends IService<MkZhTf> {

    Boolean addOrModify(MkZhTfParam param);

    Boolean remove(MkZhTfParam param);

    MkZhTfDTO getCurrentDetail(MkZhTfParam param);

    IPage<MkZhTfDTO> pagingQuery(MkZhTfParam param);

    MkZhTfDTO getData();
}
