package com.yhd.admin.api.domain.conduct.convert.transfer;

import com.yhd.admin.api.domain.conduct.dto.transfer.MkTransferOrgDTO;
import com.yhd.admin.api.domain.conduct.entity.transfer.MkTransferOrg;
import com.yhd.admin.api.domain.conduct.query.transfer.MkTransferOrgParam;
import com.yhd.admin.api.domain.conduct.vo.transfer.MkTransferOrgVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 收发文部门表(MkTransferOrg)Convert
 *
 * <AUTHOR>
 * @since 2025-07-28 16:31:06
 */
@Mapper(componentModel = "spring")
public interface MkTransferOrgConvert {
    MkTransferOrg toEntity(MkTransferOrgParam param);

    MkTransferOrgDTO toDTO(MkTransferOrg entity);

    MkTransferOrgVO toVO(MkTransferOrgDTO dto);

    List<MkTransferOrgDTO> toDTOList(List<MkTransferOrg> entityList);

    List<MkTransferOrgVO> toVOList(List<MkTransferOrgDTO> dtoList);
}

