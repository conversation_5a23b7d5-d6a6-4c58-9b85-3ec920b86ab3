package com.yhd.admin.api.domain.emergency.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 综合决策能耗统计表
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_mk_energy_consumption_stats")
public class MkEnergyConsumptionStats {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 统计编码
     */
    private String statsCode;

    /**
     * 统计日期
     */
    private LocalDate statsDate;

    /**
     * 设备类型
     */
    private String equipmentType;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 能耗值
     */
    private BigDecimal consumptionValue;

    /**
     * 能耗占比(%)
     */
    private BigDecimal consumptionPercentage;

    /**
     * 颜色代码
     */
    private String colorCode;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 组织机构编码
     */
    private String orgCode;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}