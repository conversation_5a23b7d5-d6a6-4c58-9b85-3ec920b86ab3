package com.yhd.admin.api.domain.monitor.fan.vo.update;

import com.yhd.admin.api.domain.monitor.fan.vo.create.TbMkLocalFanSystemCreateVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 局扇控制系统
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkLocalFanSystemUpdateVO extends TbMkLocalFanSystemCreateVO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
}
