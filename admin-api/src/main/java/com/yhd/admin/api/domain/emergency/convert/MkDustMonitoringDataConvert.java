package com.yhd.admin.api.domain.emergency.convert;

import com.yhd.admin.api.domain.emergency.entity.MkDustMonitoringData;
import com.yhd.admin.api.domain.emergency.vo.MkDustMonitoringDataVO;
import org.mapstruct.Mapper;

/**
 * @program: admin-framework
 * @description:
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2025-08-06 10:22
 **/
@Mapper(componentModel = "spring")
public interface MkDustMonitoringDataConvert {
    /**
     * 实体转VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    MkDustMonitoringDataVO toVO(MkDustMonitoringData entity);
}
