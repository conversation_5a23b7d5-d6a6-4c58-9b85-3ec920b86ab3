package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkOutputWorkDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputWork;
import com.yhd.admin.api.domain.operation.query.MkOutputWorkParam;
import com.yhd.admin.api.domain.operation.vo.MkOutputWorkVO;
import org.mapstruct.Mapper;


/**
* 生产录入-主要工作录入表
*
* <AUTHOR>
* @since 1.0.0 2025-07-30
*/
@Mapper(componentModel = "spring")
public interface MkOutputWorkConvert {

    MkOutputWork toEntity(MkOutputWorkParam param);

    MkOutputWorkVO toVO(MkOutputWorkDTO dto);

    MkOutputWorkDTO toDTO(MkOutputWork entity);

}
