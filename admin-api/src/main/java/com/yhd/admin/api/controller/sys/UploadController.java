package com.yhd.admin.api.controller.sys;

import java.io.IOException;
import java.io.InputStream;
import java.security.Principal;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.yhd.admin.api.configuration.FastDFSClient;
import com.yhd.admin.common.domain.RespJson;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName UploadController.java @Description TODO
 * @createTime 2020年04月23日 15:14:00
 */
@RestController
@Slf4j
public class UploadController {

    private final FastDFSClient client;

    public UploadController(FastDFSClient client) {
        this.client = client;
    }

    @PostMapping("/upload")
    public RespJson upload(@RequestParam("file") MultipartFile file, Principal principal) {
        if (log.isInfoEnabled()) {
            log.info(">>{}", principal.getName());
        }
        try (InputStream inputStream = file.getInputStream()) {
            String[] retVal = client.uploadFile(inputStream, file.getOriginalFilename());
            if (log.isDebugEnabled()) {
                log.debug(">>{}", new Object[] {retVal});
            }
            return RespJson.success(client.getFullPath(retVal));
        } catch (IOException e) {
            log.error("上传失败{}", e.getMessage());
        }
        return RespJson.failure("");
    }
}
