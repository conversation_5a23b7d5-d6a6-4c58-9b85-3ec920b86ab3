package com.yhd.admin.api.domain.video.convert;

import com.yhd.admin.api.domain.video.dto.MkVideoDeviceDTO;
import com.yhd.admin.api.domain.video.entity.MkVideoDevice;
import com.yhd.admin.api.domain.video.query.MkVideoDeviceParam;
import com.yhd.admin.api.domain.video.vo.MkVideoDeviceVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MkVideoDeviceConvert {
  MkVideoDeviceDTO toDTO(MkVideoDevice entity);

  MkVideoDeviceVO toVO(MkVideoDeviceDTO dto);

  MkVideoDevice toEntity(MkVideoDeviceParam param);
}
