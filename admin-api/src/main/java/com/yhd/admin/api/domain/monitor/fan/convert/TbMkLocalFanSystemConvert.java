package com.yhd.admin.api.domain.monitor.fan.convert;

import com.yhd.admin.api.domain.monitor.fan.dto.TbMkLocalFanSystemDTO;
import com.yhd.admin.api.domain.monitor.fan.entity.TbMkLocalFanSystem;
import com.yhd.admin.api.domain.monitor.fan.vo.TbMkLocalFanSystemVO;
import com.yhd.admin.api.domain.monitor.fan.vo.create.TbMkLocalFanSystemCreateVO;
import com.yhd.admin.api.domain.monitor.fan.vo.update.TbMkLocalFanSystemUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 局扇控制系统
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkLocalFanSystemConvert {

    @Mapping(target = "id", ignore = true)
    TbMkLocalFanSystem convert(TbMkLocalFanSystemCreateVO createVO);

    TbMkLocalFanSystem convert(TbMkLocalFanSystemUpdateVO updateVO);

    TbMkLocalFanSystemVO convert(TbMkLocalFanSystem po);

    TbMkLocalFanSystemDTO toDTO(TbMkLocalFanSystem po);



}
