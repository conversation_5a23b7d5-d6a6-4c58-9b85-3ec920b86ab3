package com.yhd.admin.api.domain.sys.convert;

import com.yhd.admin.api.domain.sys.dto.MesNoticeDTO;
import com.yhd.admin.api.domain.sys.entity.MesNotice;
import com.yhd.admin.api.domain.sys.query.MesNoticeParam;
import com.yhd.admin.api.domain.sys.vo.MesNoticeVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MesNoticeConvert {

    MesNotice toEntity(MesNoticeParam param);

    MesNoticeDTO toDTO(MesNotice classes);

    MesNoticeVO toVO(MesNoticeDTO classesDTO);
}
