package com.yhd.admin.api.service.sys;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.sys.RoleDao;
import com.yhd.admin.api.dao.sys.RoleMenuRelDao;
import com.yhd.admin.api.domain.sys.convert.RoleConvert;
import com.yhd.admin.api.domain.sys.dto.RoleDTO;
import com.yhd.admin.api.domain.sys.entity.SysRole;
import com.yhd.admin.api.domain.sys.entity.SysRoleMenu;
import com.yhd.admin.api.domain.sys.enums.APIRedisKeyEnum;
import com.yhd.admin.api.domain.sys.query.RoleParam;
import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.IdentityService;
import org.flowable.idm.api.Group;
import org.flowable.idm.engine.impl.persistence.entity.GroupEntityImpl;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName RoleServiceImpl.java @Description TODO
 * @createTime 2020年03月30日 11:17:00
 */
@Service
public class RoleServiceImpl extends ServiceImpl<RoleDao, SysRole> implements RoleService {
    @Resource
    private IdentityService identityService;

    private final RoleConvert convert;

    private final RoleMenuRelDao roleMenuRelDao;

    private final RedisTemplate redisTemplate;

    public RoleServiceImpl(RoleConvert convert, RoleMenuRelDao roleMenuRelDao, RedisTemplate redisTemplate) {
        this.convert = convert;
        this.roleMenuRelDao = roleMenuRelDao;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public List<RoleDTO> getRoleByUser(UserParam param) {
        return convert.toDTO(baseMapper.selectRoleListByUser(param));
    }

    @Override
    public IPage<RoleDTO> pagingQuery(RoleParam queryParam) {
        IPage<SysRole> roleIPage = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<SysRole> roleQueryChain = new LambdaQueryChainWrapper<>(baseMapper)
            .like(StringUtils.isNotBlank(queryParam.getRoleName()), SysRole::getRoleName, queryParam.getRoleName())
            .eq(StringUtils.isNotBlank(queryParam.getRoleCode()), SysRole::getRoleCode, queryParam.getRoleCode())
            .eq(queryParam.getIsEnable() != null, SysRole::getIsEnable, queryParam.getIsEnable());

        return roleQueryChain.page(roleIPage).convert(convert::toDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addOrUpdate(RoleParam param) {
        SysRole role = convert.toEntity(param);
        boolean success = saveOrUpdate(role);
        // 先删除已经加入的菜单映射的关系
        roleMenuRelDao.delete(new QueryWrapper<SysRoleMenu>().lambda().eq(SysRoleMenu::getRoleId, param.getId()));
        if (!CollectionUtils.isEmpty(param.getAuthority())) {
            param.getAuthority().forEach(o -> {
                SysRoleMenu rel = new SysRoleMenu();
                rel.setMenuId(o);
                rel.setRoleId(role.getId());
                rel.setCreatedBy(param.getCreatedBy());
                roleMenuRelDao.insert(rel);
            });
        }
        this.cleanupCache();
        // 新增角色同步到flowable表
        this.addGroup(param);
        return success;
    }

    private void addGroup(RoleParam param) {
        // 先查询是否存在改组
        List<Group> groupList = identityService.createGroupQuery().groupId(param.getRoleCode()).list();
        if (CollectionUtils.isEmpty(groupList)) {
            // 为空添加组
            GroupEntityImpl g = new GroupEntityImpl();
            g.setName(param.getRoleName());
            g.setId(param.getRoleCode());
            g.setRevision(0);
            identityService.saveGroup(g);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean modify(RoleParam param) {
        this.cleanupCache();
        // 先删除已经加入的菜单映射的关系
        roleMenuRelDao.delete(new QueryWrapper<SysRoleMenu>().lambda().eq(SysRoleMenu::getRoleId, param.getId()));
        // 加入新的映射关系
        param.getAuthority().forEach(o -> {
            SysRoleMenu rel = new SysRoleMenu();
            rel.setMenuId(o);
            rel.setRoleId(param.getId());
            rel.setCreatedBy(param.getCreatedBy());
            rel.setUpdatedBy(param.getUpdatedBy());
            roleMenuRelDao.insert(rel);
        });
        this.cleanupCache();
        // 新增角色同步到flowable表
        this.modifyGroup(param);
        return updateById(convert.toEntity(param));
    }

    private void modifyGroup(RoleParam param) {
        Group group = identityService.createGroupQuery().groupId(param.getRoleCode()) // 要更新的用户组的ID
            .singleResult();
        if (group == null) {
            GroupEntityImpl g = new GroupEntityImpl();
            g.setName(param.getRoleName());
            g.setId(param.getRoleCode());
            g.setRevision(0);
            identityService.saveGroup(g);
        } else {
            group.setName(param.getRoleName());
            group.setId(param.getRoleCode());
            identityService.saveGroup(group);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatch(BatchParam batchParam) {
        this.cleanupCache();
        batchParam.getId().forEach(
            o -> roleMenuRelDao.delete(new QueryWrapper<SysRoleMenu>().lambda().eq(SysRoleMenu::getRoleId, o)));
        // 删除角色同步删除flowable ACT_ID_GROUP
        this.deleteGroup(batchParam);
        return removeByIds(batchParam.getId());
    }

    private void deleteGroup(BatchParam batchParam) {
        List<Long> ids = batchParam.getId();
        ids.forEach(e -> {
            RoleParam roleParam = new RoleParam();
            roleParam.setId(e);
            RoleDTO roleDTO = this.currentDetail(roleParam);
            identityService.deleteGroup(roleDTO.getRoleCode());
        });
    }

    @Override
    public RoleDTO currentDetail(RoleParam param) {

        RoleDTO roleDTO = convert.toDTO(getById(param.getId()));
        LambdaQueryChainWrapper<SysRoleMenu> roleRelQuery = new LambdaQueryChainWrapper<>(roleMenuRelDao);
        List<Long> authority = roleRelQuery.eq(SysRoleMenu::getRoleId, param.getId()).list().stream()
            .map(SysRoleMenu::getMenuId).collect(Collectors.toList());
        roleDTO.setAuthority(authority);
        return roleDTO;
    }

    @Override
    public List<RoleDTO> queryRoleList() {
        return convert.toDTO(list());
    }

    /**
     * 校验角色编码是否存在
     *
     * @param param
     * @return
     */
    @Override
    public Boolean validateIfNotExist(RoleParam param) {
        LambdaQueryChainWrapper<SysRole> validateChain = new LambdaQueryChainWrapper<>(baseMapper);
        validateChain.notIn(param.getId() != null, SysRole::getId, param.getId()).eq(SysRole::getRoleCode,
            param.getRoleCode());
        return CollectionUtils.isEmpty(validateChain.list());
    }

    private void cleanupCache() {
        String redisKey = String.format(APIRedisKeyEnum.USER_MENU.getKey(), "*", "*");
        Set<String> keys = redisTemplate.keys(redisKey);

        redisTemplate.delete(keys);
    }
}
