package com.yhd.admin.api.controller.sys;

import java.security.Principal;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.sys.convert.ParameterConvert;
import com.yhd.admin.api.domain.sys.dto.ParameterDTO;
import com.yhd.admin.api.domain.sys.query.ParameterParam;
import com.yhd.admin.api.domain.sys.vo.ParameterVO;
import com.yhd.admin.api.service.sys.ParameterService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName ParameterController.java @Description TODO
 * @createTime 2020年05月12日 15:32:00
 */
@RestController
@RequestMapping("/parameter")
public class ParameterController {

    private final ParameterService parameterService;

    private final ParameterConvert convert;

    public ParameterController(ParameterService parameterService, ParameterConvert convert) {
        this.parameterService = parameterService;
        this.convert = convert;
    }

    @PostMapping(value = "/pagingQuery", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<ParameterVO> pagingQuery(@RequestBody ParameterParam queryParam) {
        IPage<ParameterDTO> iPage = parameterService.pagingQuery(queryParam);
        return new PageRespJson<>(iPage.convert(convert::toVO));
    }

    @PostMapping(value = "/saveOrUpdate", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson saveOrUpdate(@RequestBody ParameterParam addParam) {
        parameterService.saveOrUpdate(addParam);
        return RespJson.success();
    }

    @PostMapping(value = "/removeBatch", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson removeBatch(@RequestBody BatchParam batchParam) {
        parameterService.removeBatch(batchParam);
        return RespJson.success();
    }

    @PostMapping(value = "/modify", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson modify(@RequestBody ParameterParam modifyParam, Principal auth2Authentication) {
        modifyParam.setUpdatedBy(auth2Authentication.getName());
        parameterService.modify(modifyParam);
        return RespJson.success();
    }

    @PostMapping(value = "/currentDetail", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson currentDetail(@RequestBody ParameterParam queryParam) {

        return RespJson.success(convert.toVO(parameterService.currentDetail(queryParam.getId())));
    }

    @PostMapping(value = "/validateIfNotExist", consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<Boolean> validateIfNotExist(@RequestBody ParameterParam queryParam) {

        return RespJson.success(parameterService.validateIfNotExist(queryParam));
    }
}
