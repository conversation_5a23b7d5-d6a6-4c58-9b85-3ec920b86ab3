package com.yhd.admin.api.domain.emergency.vo;

import lombok.Data;
import java.util.List;

/**
 * 安全决策大屏数据VO
 *
 * <AUTHOR>
 */
@Data
public class MkSafetyDecisionVO {

    /**
     * 隐患统计数据
     */
    private MkSafetyHazardStatsVO hazardStats;

    /**
     * 三违统计数据
     */
    private MkSafetyViolationStatsVO violationStats;

    /**
     * 报警统计数据
     */
    private MkSafetyAlarmStatsVO alarmStats;

    /**
     * 风险统计数据
     */
    private MkSafetyRiskStatsVO riskStats;

    /**
     * 风险管控清单
     */
    private List<MkSafetyRiskManagementVO> riskManagementList;

    /**
     * 专业分析统计数据
     */
    private List<MkSafetyProfessionalAnalysisVO> professionalAnalysisList;

    /**
     * 中央环形数据
     */
    private CentralRingData centralRingData;

    @Data
    public static class CentralRingData {
        /**
         * 接警总数
         */
        private Integer alarmTotal;

        /**
         * 三违总数
         */
        private Integer violationTotal;

        /**
         * 风险总数
         */
        private Integer riskTotal;

        /**
         * 隐患总数
         */
        private Integer hazardTotal;
    }
}