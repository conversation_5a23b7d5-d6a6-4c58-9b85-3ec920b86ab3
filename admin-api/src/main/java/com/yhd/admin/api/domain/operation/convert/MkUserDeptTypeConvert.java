package com.yhd.admin.api.domain.operation.convert;

import com.yhd.admin.api.domain.operation.dto.MkUserDeptDTO;
import com.yhd.admin.api.domain.operation.dto.MkUserDeptTypeDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserDept;
import com.yhd.admin.api.domain.operation.entity.MkUserDeptType;
import com.yhd.admin.api.domain.operation.query.MkUserDeptTypeParam;
import com.yhd.admin.api.domain.operation.vo.MkUserDeptTypeVO;
import com.yhd.admin.api.domain.operation.vo.MkUserDeptVO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * 部门类型表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */
@Mapper(componentModel = "spring")
public interface MkUserDeptTypeConvert {

    MkUserDeptType toEntity(MkUserDeptTypeParam param);

    MkUserDeptTypeVO toVO(MkUserDeptTypeDTO dto);

    MkUserDeptTypeDTO toDTO(MkUserDeptType entity);

    List<MkUserDeptTypeVO> toVOList(List<MkUserDeptTypeDTO> dtos);

    List<MkUserDeptTypeDTO> toDTOList(List<MkUserDeptType> entitys);

}
