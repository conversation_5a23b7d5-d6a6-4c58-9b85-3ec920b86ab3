package com.yhd.admin.api.domain.conduct.convert.arrangement;

import com.yhd.admin.api.domain.conduct.dto.arrangement.MkShiftArrangementRecordDTO;
import com.yhd.admin.api.domain.conduct.entity.arrangement.MkShiftArrangementRecord;
import com.yhd.admin.api.domain.conduct.query.arrangement.MkShiftArrangementRecordParam;
import com.yhd.admin.api.domain.conduct.vo.arrangement.MkShiftArrangementRecordVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 调班记录表(MkShiftArrangementRecord)Convert
 *
 * <AUTHOR>
 * @since 2025-07-29 11:30:39
 */
@Mapper(componentModel = "spring")
public interface MkShiftArrangementRecordConvert {
    MkShiftArrangementRecord toEntity(MkShiftArrangementRecordParam param);

    MkShiftArrangementRecordDTO toDTO(MkShiftArrangementRecord entity);

    MkShiftArrangementRecordVO toVO(MkShiftArrangementRecordDTO dto);

    List<MkShiftArrangementRecordDTO> toDTOList(List<MkShiftArrangementRecord> entityList);

    List<MkShiftArrangementRecordVO> toVOList(List<MkShiftArrangementRecordDTO> dtoList);
}

