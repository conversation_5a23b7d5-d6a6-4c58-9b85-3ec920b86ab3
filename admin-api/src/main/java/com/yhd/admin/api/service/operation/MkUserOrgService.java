package com.yhd.admin.api.service.operation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.operation.dto.MkUserOrgDTO;
import com.yhd.admin.api.domain.operation.entity.MkUserOrg;
import com.yhd.admin.api.domain.operation.query.MkUserDeptParam;
import com.yhd.admin.api.domain.operation.query.MkUserOrgParam;
import com.yhd.admin.common.domain.query.BatchParam;

import java.util.List;

/**
 * 运维配置中心-单位管理-Service
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-28
 */
public interface MkUserOrgService extends IService<MkUserOrg> {

    IPage<MkUserOrgDTO> pagingQuery(MkUserOrgParam queryParam);

     Boolean addOrModify(MkUserOrgParam param);

    Boolean removeBatch(BatchParam param);

    MkUserOrgDTO getCurrentDetail(MkUserOrgParam param);
    /**
     * 获取单位集合
     *
     * @param param
     * @return
     */
    List<MkUserOrgDTO> getOrgList(MkUserDeptParam param);
}
