package com.yhd.admin.api.service.stats;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.stats.MkUserDownholeStatisticDao;
import com.yhd.admin.api.domain.stats.convert.MkUserDownholeStatisticConvert;
import com.yhd.admin.api.domain.stats.dto.MkUserDownholeStatisticDTO;
import com.yhd.admin.api.domain.stats.entity.MkUserDownholeStatistic;
import com.yhd.admin.api.domain.stats.query.MkUserDownholeStatisticParam;
import com.yhd.admin.common.utils.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 人员下井统计-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2025/7/31 15:57
 */
@Service
public class MkUserDownholeStatisticServiceImpl
    extends ServiceImpl<MkUserDownholeStatisticDao, MkUserDownholeStatistic>
    implements MkUserDownholeStatisticService {

  private final MkUserDownholeStatisticConvert statisticConvert;

  public MkUserDownholeStatisticServiceImpl(MkUserDownholeStatisticConvert statisticConvert) {
    this.statisticConvert = statisticConvert;
  }

  /**
   * 获取人员下井记录列表
   *
   * @param param 参数
   * @return 人员下井记录列表
   */
  @Override
  public List<MkUserDownholeStatisticDTO> getUserDownholeRecordList(
      MkUserDownholeStatisticParam param) {
    LambdaQueryWrapper<MkUserDownholeStatistic> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MkUserDownholeStatistic::getRealName, param.getRealName());
    // 时间范围
    if (Objects.nonNull(param.getStartTime())) {
      wrapper.ge(MkUserDownholeStatistic::getDownTime, param.getStartTime());
    }
    if (Objects.nonNull(param.getEndTime())) {
      wrapper.le(MkUserDownholeStatistic::getUpTime, param.getEndTime());
    }

    // 排序，下井时间倒叙
    wrapper.orderByDesc(MkUserDownholeStatistic::getDownTime);

    List<MkUserDownholeStatistic> list = baseMapper.selectList(wrapper);

    return list.stream().map(statisticConvert::toDTO).collect(Collectors.toList());
  }

  /**
   * 根据条件查询人员下井统计分页列表
   *
   * @param param 查询条件
   * @return 人员下井统计分页列表
   */
  @Override
  public List<MkUserDownholeStatisticDTO> userCountList(MkUserDownholeStatisticParam param) {
    List<MkUserDownholeStatisticDTO> result = Lists.newArrayList();
    LambdaQueryWrapper<MkUserDownholeStatistic> wrapper = new LambdaQueryWrapper<>();
    // 时间范围
    if (Objects.nonNull(param.getStartTime())) {
      wrapper.ge(MkUserDownholeStatistic::getDownTime, param.getStartTime());
    }
    if (Objects.nonNull(param.getEndTime())) {
      wrapper.le(MkUserDownholeStatistic::getUpTime, param.getEndTime());
    }
    // 单位
    if (StringUtils.isNotBlank(param.getOrgCode())) {
      wrapper.eq(MkUserDownholeStatistic::getOrgCode, param.getOrgCode());
    }
    // 姓名，模糊查询
    if (StringUtils.isNotBlank(param.getRealName())) {
      wrapper.like(MkUserDownholeStatistic::getRealName, param.getRealName());
    }
    List<MkUserDownholeStatistic> selectList = baseMapper.selectList(wrapper);
    if (CollectionUtils.isNotEmpty(selectList)) {

      // 根据姓名进行分组
      Map<String, List<MkUserDownholeStatistic>> listMap =
          selectList.stream()
              .collect(Collectors.groupingBy(v -> v.getRealName() + "-" + v.getOrgName()));

      listMap.forEach(
          (k, v) -> {
            String[] split = k.split("-");
            MkUserDownholeStatisticDTO dto = new MkUserDownholeStatisticDTO();
            dto.setRealName(split[0]);
            dto.setOrgName(split[1]);
            // 下井次数
            dto.setDownCount(v.size());
            // 下井时长
            long sumDuration = v.stream().mapToLong(MkUserDownholeStatistic::getDuration).sum();
            dto.setDuration(sumDuration);
            dto.setDurationStr(DateUtil.formatSecondsToHMs(sumDuration));
            result.add(dto);
          });
    }

    return result;
  }
}
