package com.yhd.admin.api.service.stats;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.stats.MkUserDownholeStatisticDao;
import com.yhd.admin.api.domain.stats.convert.MkUserDownholeStatisticConvert;
import com.yhd.admin.api.domain.stats.dto.MkUserDownholeStatisticDTO;
import com.yhd.admin.api.domain.stats.entity.MkUserDownholeStatistic;
import com.yhd.admin.api.domain.stats.query.MkUserDownholeStatisticParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 人员下井统计-业务层接口实现类
 *
 * <AUTHOR>
 * @date 2025/7/31 15:57
 */
@Service
public class MkUserDownholeStatisticServiceImpl
    extends ServiceImpl<MkUserDownholeStatisticDao, MkUserDownholeStatistic>
    implements MkUserDownholeStatisticService {
  private static final Logger logger =
      LoggerFactory.getLogger(MkUserDownholeStatisticServiceImpl.class);

  private final MkUserDownholeStatisticConvert statisticConvert;

  public MkUserDownholeStatisticServiceImpl(MkUserDownholeStatisticConvert statisticConvert) {
    this.statisticConvert = statisticConvert;
  }

  /**
   * 获取人员下井记录列表
   *
   * @param param 参数
   * @return 人员下井记录列表
   */
  @Override
  public List<MkUserDownholeStatisticDTO> getUserDownholeRecordList(
      MkUserDownholeStatisticParam param) {
    return null;
  }

  /**
   * 根据条件查询人员下井统计分页列表
   *
   * @param param 查询条件
   * @return 人员下井统计分页列表
   */
  @Override
  public IPage<MkUserDownholeStatisticDTO> userCountPagingQuery(
      MkUserDownholeStatisticParam param) {
    return null;
  }
}
