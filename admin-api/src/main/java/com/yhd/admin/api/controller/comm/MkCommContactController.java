package com.yhd.admin.api.controller.comm;

import com.yhd.admin.api.domain.comm.convert.MkCommContactConvert;
import com.yhd.admin.api.service.comm.MkCommContactService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 调度通讯-控制层
 *
 * <AUTHOR>
 * @date 2025/7/25 16:58
 */
@RestController
@RequestMapping("/comm/contact")
public class MkCommContactController {
  private final MkCommContactConvert contactConvert;
  private final MkCommContactService contactService;

  public MkCommContactController(
      MkCommContactConvert contactConvert, MkCommContactService contactService) {
    this.contactConvert = contactConvert;
    this.contactService = contactService;
  }
}
