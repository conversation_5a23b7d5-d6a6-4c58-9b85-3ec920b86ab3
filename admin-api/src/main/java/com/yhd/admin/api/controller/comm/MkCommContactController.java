package com.yhd.admin.api.controller.comm;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.comm.convert.MkCommContactConvert;
import com.yhd.admin.api.domain.comm.dto.MkCommContactDTO;
import com.yhd.admin.api.domain.comm.query.MkCommContactParam;
import com.yhd.admin.api.domain.comm.vo.MkCommContactVO;
import com.yhd.admin.api.service.comm.MkCommContactService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 调度通讯-控制层
 *
 * <AUTHOR>
 * @date 2025/7/25 16:58
 */
@RestController
@RequestMapping("/comm/contact")
public class MkCommContactController {
  private final MkCommContactConvert contactConvert;
  private final MkCommContactService contactService;

  public MkCommContactController(
      MkCommContactConvert contactConvert, MkCommContactService contactService) {
    this.contactConvert = contactConvert;
    this.contactService = contactService;
  }

  @PostMapping(
      value = "/add",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> add(@RequestBody MkCommContactParam param) {
    return RespJson.success(contactService.add(param));
  }

  @PostMapping(
      value = "/modify",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> modify(@RequestBody MkCommContactParam param) {
    return RespJson.success(contactService.modify(param));
  }

  @PostMapping(
      value = "/remove",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<Boolean> remove(@RequestBody MkCommContactParam param) {
    return RespJson.success(contactService.remove(param));
  }

  @PostMapping(
      value = "/getCurrentDetail",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public RespJson<MkCommContactVO> getCurrentDetail(@RequestBody MkCommContactParam param) {
    return RespJson.success(contactConvert.toVO(contactService.getCurrentDetail(param)));
  }

  @PostMapping(
      value = "/pagingQuery",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PageRespJson<MkCommContactVO> pagingQuery(@RequestBody MkCommContactParam param) {
    IPage<MkCommContactDTO> iPage = contactService.pagingQuery(param);
    return new PageRespJson<>(iPage.convert(contactConvert::toVO));
  }
}
