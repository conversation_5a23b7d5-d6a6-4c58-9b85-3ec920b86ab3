package com.yhd.admin.api.domain.emergency.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 应急演练记录表 DTO
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkEmergencyDrillDTO extends BaseDTO implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 演练编码
     */
    private String drillCode;

    /**
     * 演练名称
     */
    private String drillName;

    /**
     * 演练时间
     */
    private LocalDateTime drillTime;

    /**
     * 演练地点
     */
    private String drillLocation;

    /**
     * 演练方式
     */
    private String drillType;

    /**
     * 负责人
     */
    private String responsiblePerson;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 演练状态：1-计划中，2-进行中，3-已完成，4-已取消
     */
    private Integer drillStatus;

    /**
     * 参与人数
     */
    private Integer participantsCount;

    /**
     * 演练时长（分钟）
     */
    private Integer drillDuration;

    /**
     * 演练目标
     */
    private String drillObjective;

    /**
     * 演练内容
     */
    private String drillContent;

    /**
     * 演练结果
     */
    private String drillResult;

    /**
     * 演练评价
     */
    private String evaluation;

    /**
     * 改进建议
     */
    private String improvementSuggestions;

    /**
     * 备注
     */
    private String remark;
}
