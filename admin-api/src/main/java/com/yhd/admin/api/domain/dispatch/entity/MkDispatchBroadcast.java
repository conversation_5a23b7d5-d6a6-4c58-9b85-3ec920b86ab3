package com.yhd.admin.api.domain.dispatch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yhd.admin.common.domain.entity.BaseEntity;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 智能调度中心-应急广播
 *
 * <AUTHOR>
 * @date 2025/7/31 09:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkDispatchBroadcast extends BaseEntity implements Serializable {

  @TableId(type = IdType.AUTO)
  private Long id;

  /** 应急广播数 */
  private Long urgencyNum;

  /** 在线数 */
  private Long onlineNum;

  /** 离线数 */
  private Long offlineNum;

  /** 广播状态 */
  private String broadcastStatus;

  /** 文字广播 */
  private String broadcastWriting;

  /** 广播列表 */
  private String broadcast;
}
