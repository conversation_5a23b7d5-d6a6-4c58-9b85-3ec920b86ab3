package com.yhd.admin.api.service.operation;

import com.yhd.admin.api.dao.operation.MkOutputDutyDao;
import com.yhd.admin.api.domain.operation.convert.MkOutputDutyConvert;
import com.yhd.admin.api.domain.operation.dto.MkOutputDutyDTO;
import com.yhd.admin.api.domain.operation.entity.MkOutputDuty;
import com.yhd.admin.api.domain.operation.query.MkOutputDutyParam;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.sys.UserService;
import com.yhd.admin.common.domain.query.BatchParam;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;

/**
 * 生产录入-值班人员表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-30
 */
@Service
public class MkOutputDutyServiceImpl extends ServiceImpl<MkOutputDutyDao, MkOutputDuty> implements MkOutputDutyService {

    @Resource
    private MkOutputDutyConvert convert;

    @Resource
    private MkOutputDutyService service;

    @Resource
    private UserService userService;

    @Override
    public IPage<MkOutputDutyDTO> pagingQuery(MkOutputDutyParam queryParam) {
        Page<MkOutputDuty> page = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<MkOutputDuty> queryChain = new LambdaQueryChainWrapper<>(baseMapper);
        queryChain.orderByDesc(MkOutputDuty::getCreatedTime);
        return queryChain.page(page).convert(convert::toDTO);
    }

    @Override
    public Boolean add(MkOutputDutyParam param) {
        if (StringUtils.isBlank(param.getLeaderUserNo()) ||
            StringUtils.isBlank(param.getOneUserNo()) ||
            StringUtils.isBlank(param.getTwoUserNo()) ||
            StringUtils.isBlank(param.getThreeUserNo()) ||
            StringUtils.isBlank(param.getFourUserNo())){
           throw new BMSException("error","值班人员不能为空");
        }
        param.setLeaderUserName(userService.getUserByUid(param.getLeaderUserNo()).getName());
        param.setOneUserName(userService.getUserByUid(param.getOneUserNo()).getName());
        param.setTwoUserName(userService.getUserByUid(param.getTwoUserNo()).getName());
        param.setThreeUserName(userService.getUserByUid(param.getThreeUserNo()).getName());
        param.setFourUserName(userService.getUserByUid(param.getFourUserNo()).getName());
        MkOutputDuty entity = convert.toEntity(param);
        return super.save(entity);
    }

    @Override
    public Boolean modify(MkOutputDutyParam param) {
        if (param.getId() == null ||
            StringUtils.isBlank(param.getLeaderUserNo()) ||
            StringUtils.isBlank(param.getOneUserNo()) ||
            StringUtils.isBlank(param.getTwoUserNo()) ||
            StringUtils.isBlank(param.getThreeUserNo()) ||
            StringUtils.isBlank(param.getFourUserNo())){
            throw new BMSException("error","id和值班人员不能为空");
        }
        param.setLeaderUserName(userService.getUserByUid(param.getLeaderUserNo()).getName());
        param.setOneUserName(userService.getUserByUid(param.getOneUserNo()).getName());
        param.setTwoUserName(userService.getUserByUid(param.getTwoUserNo()).getName());
        param.setThreeUserName(userService.getUserByUid(param.getThreeUserNo()).getName());
        param.setFourUserName(userService.getUserByUid(param.getFourUserNo()).getName());
        MkOutputDuty entity = convert.toEntity(param);
        return super.updateById(entity);
    }

    @Override
    public MkOutputDutyDTO getCurrentDetail(MkOutputDutyParam param) {
        return convert.toDTO(super.getById(param.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

}
