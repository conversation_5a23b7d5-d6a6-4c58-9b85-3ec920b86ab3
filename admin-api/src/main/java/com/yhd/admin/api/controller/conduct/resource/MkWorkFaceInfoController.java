package com.yhd.admin.api.controller.conduct.resource;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.conduct.convert.resource.MkWorkFaceInfoConvert;
import com.yhd.admin.api.domain.conduct.dto.resource.MkWorkFaceInfoDTO;
import com.yhd.admin.api.domain.conduct.query.resource.MkWorkFaceInfoParam;
import com.yhd.admin.api.domain.conduct.vo.resource.MkWorkFaceInfoVO;
import com.yhd.admin.api.service.conduct.resource.MkWorkFaceInfoService;
import com.yhd.admin.common.domain.PageRespJson;
import com.yhd.admin.common.domain.RespJson;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作面信息控制层
 *
 * <AUTHOR>
 * @since 2025-07-28 08:21:47
 */
@RestController
@RequestMapping("/conduct/workFace")
public class MkWorkFaceInfoController {

    public final MkWorkFaceInfoService mkWorkFaceInfoService;
    public final MkWorkFaceInfoConvert mkWorkFaceInfoConvert;

    public MkWorkFaceInfoController(
        MkWorkFaceInfoService mkWorkFaceInfoService,
        MkWorkFaceInfoConvert mkWorkFaceInfoConvert
    ) {
        this.mkWorkFaceInfoService = mkWorkFaceInfoService;
        this.mkWorkFaceInfoConvert = mkWorkFaceInfoConvert;
    }

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/pagingQuery",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public PageRespJson<?> pagingQuery(@RequestBody MkWorkFaceInfoParam param) {
        IPage<MkWorkFaceInfoDTO> page = mkWorkFaceInfoService.pagingQuery(param);
        return new PageRespJson<>(page.convert(mkWorkFaceInfoConvert::toVO));
    }

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    @PostMapping(
        value = "/queryList",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> queryList(@RequestBody MkWorkFaceInfoParam param) {
        return RespJson.success(mkWorkFaceInfoConvert.toVOList(mkWorkFaceInfoService.queryList(param)));
    }

    /**
     * 根据主键查询详细信息
     *
     * @param param 参数
     * @return 详细信息
     */
    @PostMapping(
        value = "/getCurrentDetails",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkWorkFaceInfoVO> getCurrentDetails(@RequestBody MkWorkFaceInfoParam param) {
        return RespJson.success(mkWorkFaceInfoConvert.toVO(mkWorkFaceInfoService.getCurrentDetails(param)));
    }

    /**
     * 新增或修改数据
     *
     * @param param 参数
     * @return 是否成功
     */
    @PostMapping(
        value = "/addOrModify",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> add(@RequestBody MkWorkFaceInfoParam param) {
        Boolean retVal = mkWorkFaceInfoService.addOrModify(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

    /**
     * 批量删除数据
     *
     * @param param 主键列表
     * @return 是否成功
     */
    @PostMapping(
    value = "/removeBatch",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<?> removeBatch(@RequestBody MkWorkFaceInfoParam param) {
        Boolean retVal = mkWorkFaceInfoService.removeBatch(param);
        return retVal ? RespJson.success() : RespJson.failure("");
    }

}

