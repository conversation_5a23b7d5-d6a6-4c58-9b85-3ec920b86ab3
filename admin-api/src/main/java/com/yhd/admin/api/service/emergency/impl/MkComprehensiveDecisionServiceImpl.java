package com.yhd.admin.api.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.admin.api.dao.emergency.*;
import com.yhd.admin.api.domain.emergency.entity.*;
import com.yhd.admin.api.domain.emergency.query.MkComprehensiveDecisionParam;
import com.yhd.admin.api.domain.emergency.vo.*;
import com.yhd.admin.api.service.emergency.MkComprehensiveDecisionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 综合决策大屏 Service 实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MkComprehensiveDecisionServiceImpl implements MkComprehensiveDecisionService {

    @Resource
    private MkProductionStatsDao productionStatsDao;

    @Resource
    private MkSensorStatsDao sensorStatsDao;

    @Resource
    private MkRiskControlStatsDao riskControlStatsDao;

    @Resource
    private MkHazardControlStatsDao hazardControlStatsDao;

    @Resource
    private MkPersonnelStatsDao personnelStatsDao;

    @Resource
    private MkIntelligentWorkfaceStatsDao intelligentWorkfaceStatsDao;

    @Resource
    private MkHydrologyMonitorStatsDao hydrologyMonitorStatsDao;

    @Resource
    private MkEquipmentStatsDao equipmentStatsDao;

    @Resource
    private MkEnergyConsumptionStatsDao energyConsumptionStatsDao;

    @Override
    public MkComprehensiveDecisionVO getDashboardData(MkComprehensiveDecisionParam param) {
        log.info("获取综合决策大屏数据，参数：{}", param);

        MkComprehensiveDecisionVO result = new MkComprehensiveDecisionVO();

        LocalDate statsDate = param.getStatsDate() != null ? param.getStatsDate() : LocalDate.now();
        String orgCode = param.getOrgCode();

        // 获取生产统计数据
        result.setProductionStats(getProductionStats(statsDate, orgCode));

        // 获取传感器统计数据
        result.setSensorStats(getSensorStats(statsDate, orgCode));

        // 获取风险管控统计数据
        result.setRiskControlStats(getRiskControlStats(statsDate, orgCode));

        // 获取隐患管控统计数据
        result.setHazardControlStats(getHazardControlStats(statsDate, orgCode));

        // 获取人员统计数据
        result.setPersonnelStats(getPersonnelStats(statsDate, orgCode));

        // 获取智能工作面统计数据
        result.setIntelligentWorkfaceStats(getIntelligentWorkfaceStats(statsDate, orgCode));

        // 获取水文监测统计数据
        result.setHydrologyMonitorStats(getHydrologyMonitorStats(statsDate, orgCode));

        // 获取设备统计数据
        result.setEquipmentStats(getEquipmentStats(statsDate, orgCode));

        // 获取能耗统计数据
        result.setEnergyConsumptionStats(getEnergyConsumptionStats(statsDate, orgCode));

        return result;
    }

    /**
     * 获取生产统计数据
     */
    private MkProductionStatsVO getProductionStats(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkProductionStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkProductionStats::getStatsDate, statsDate)
                .eq(MkProductionStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkProductionStats::getOrgCode, orgCode)
                .orderByDesc(MkProductionStats::getCreateTime)
                .last("LIMIT 1");

        MkProductionStats stats = productionStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkProductionStatsVO vo = new MkProductionStatsVO();
            vo.setStatsDate(stats.getStatsDate());
            vo.setDailyProduction(stats.getDailyProduction());
            vo.setDailySales(stats.getDailySales());
            vo.setMonthlyProduction(stats.getMonthlyProduction());
            vo.setMonthlySales(stats.getMonthlySales());
            vo.setYearlyProduction(stats.getYearlyProduction());
            vo.setYearlySales(stats.getYearlySales());
            vo.setSafetyDays(stats.getSafetyDays());
            vo.setDesignCapacity(stats.getDesignCapacity());
            vo.setMainCoalSeam(stats.getMainCoalSeam());
            return vo;
        }

        return new MkProductionStatsVO();
    }

    /**
     * 获取传感器统计数据
     */
    private MkSensorStatsVO getSensorStats(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkSensorStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkSensorStats::getStatsDate, statsDate)
                .eq(MkSensorStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkSensorStats::getOrgCode, orgCode)
                .orderByDesc(MkSensorStats::getCreateTime)
                .last("LIMIT 1");

        MkSensorStats stats = sensorStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkSensorStatsVO vo = new MkSensorStatsVO();
            vo.setCoSensorCount(stats.getCoSensorCount());
            vo.setMethaneSensorCount(stats.getMethaneSensorCount());
            vo.setTotalSensorCount(stats.getTotalSensorCount());
            return vo;
        }

        return new MkSensorStatsVO();
    }

    /**
     * 获取风险管控统计数据
     */
    private MkRiskControlStatsVO getRiskControlStats(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkRiskControlStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkRiskControlStats::getStatsDate, statsDate)
                .eq(MkRiskControlStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkRiskControlStats::getOrgCode, orgCode)
                .orderByDesc(MkRiskControlStats::getCreateTime)
                .last("LIMIT 1");

        MkRiskControlStats stats = riskControlStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkRiskControlStatsVO vo = new MkRiskControlStatsVO();
            vo.setMajorRiskCount(stats.getMajorRiskCount());
            vo.setSignificantRiskCount(stats.getSignificantRiskCount());
            vo.setGeneralRiskCount(stats.getGeneralRiskCount());
            vo.setLowRiskCount(stats.getLowRiskCount());
            return vo;
        }

        return new MkRiskControlStatsVO();
    }

    /**
     * 获取隐患管控统计数据
     */
    private MkHazardControlStatsVO getHazardControlStats(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkHazardControlStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkHazardControlStats::getStatsDate, statsDate)
                .eq(MkHazardControlStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkHazardControlStats::getOrgCode, orgCode)
                .orderByDesc(MkHazardControlStats::getCreateTime)
                .last("LIMIT 1");

        MkHazardControlStats stats = hazardControlStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkHazardControlStatsVO vo = new MkHazardControlStatsVO();
            vo.setGeneralHazardCount(stats.getGeneralHazardCount());
            vo.setMajorHazardCount(stats.getMajorHazardCount());
            return vo;
        }

        return new MkHazardControlStatsVO();
    }

    /**
     * 获取人员统计数据
     */
    private MkPersonnelStatsVO getPersonnelStats(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkPersonnelStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkPersonnelStats::getStatsDate, statsDate)
                .eq(MkPersonnelStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkPersonnelStats::getOrgCode, orgCode)
                .orderByDesc(MkPersonnelStats::getCreateTime)
                .last("LIMIT 1");

        MkPersonnelStats stats = personnelStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkPersonnelStatsVO vo = new MkPersonnelStatsVO();
            vo.setTotalEmployeeCount(stats.getTotalEmployeeCount());
            vo.setUndergroundLeaderCount(stats.getUndergroundLeaderCount());
            vo.setUndergroundWorkerCount(stats.getUndergroundWorkerCount());
            return vo;
        }

        return new MkPersonnelStatsVO();
    }

    /**
     * 获取智能工作面统计数据
     */
    private MkIntelligentWorkfaceStatsVO getIntelligentWorkfaceStats(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkIntelligentWorkfaceStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkIntelligentWorkfaceStats::getStatsDate, statsDate)
                .eq(MkIntelligentWorkfaceStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkIntelligentWorkfaceStats::getOrgCode, orgCode)
                .orderByDesc(MkIntelligentWorkfaceStats::getCreateTime)
                .last("LIMIT 1");

        MkIntelligentWorkfaceStats stats = intelligentWorkfaceStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkIntelligentWorkfaceStatsVO vo = new MkIntelligentWorkfaceStatsVO();
            vo.setMemoryCuttingRate(stats.getMemoryCuttingRate());
            vo.setStartupRate(stats.getStartupRate());
            vo.setAutoFollowingRate(stats.getAutoFollowingRate());
            return vo;
        }

        return new MkIntelligentWorkfaceStatsVO();
    }

    /**
     * 获取水文监测统计数据
     */
    private MkHydrologyMonitorStatsVO getHydrologyMonitorStats(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkHydrologyMonitorStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkHydrologyMonitorStats::getStatsDate, statsDate)
                .eq(MkHydrologyMonitorStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkHydrologyMonitorStats::getOrgCode, orgCode)
                .orderByDesc(MkHydrologyMonitorStats::getCreateTime)
                .last("LIMIT 1");

        MkHydrologyMonitorStats stats = hydrologyMonitorStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkHydrologyMonitorStatsVO vo = new MkHydrologyMonitorStatsVO();
            vo.setMiningAreaPumpFlow(stats.getMiningAreaPumpFlow());
            vo.setCentralPumpRoomFlow(stats.getCentralPumpRoomFlow());
            vo.setCoalAuxiliaryTunnelFlow(stats.getCoalAuxiliaryTunnelFlow());
            return vo;
        }

        return new MkHydrologyMonitorStatsVO();
    }

    /**
     * 获取设备统计数据
     */
    private MkEquipmentStatsVO getEquipmentStats(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkEquipmentStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkEquipmentStats::getStatsDate, statsDate)
                .eq(MkEquipmentStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkEquipmentStats::getOrgCode, orgCode)
                .orderByDesc(MkEquipmentStats::getCreateTime)
                .last("LIMIT 1");

        MkEquipmentStats stats = equipmentStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkEquipmentStatsVO vo = new MkEquipmentStatsVO();
            vo.setOnlineEquipmentCount(stats.getOnlineEquipmentCount());
            vo.setTotalEquipmentCount(stats.getTotalEquipmentCount());
            vo.setStartupRate(stats.getStartupRate());
            vo.setFaultEquipmentCount(stats.getFaultEquipmentCount());
            vo.setFaultRate(stats.getFaultRate());
            return vo;
        }

        return new MkEquipmentStatsVO();
    }

    /**
     * 获取能耗统计数据
     */
    private List<MkEnergyConsumptionStatsVO> getEnergyConsumptionStats(LocalDate statsDate, String orgCode) {
        LambdaQueryWrapper<MkEnergyConsumptionStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkEnergyConsumptionStats::getStatsDate, statsDate)
                .eq(MkEnergyConsumptionStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkEnergyConsumptionStats::getOrgCode, orgCode)
                .orderByAsc(MkEnergyConsumptionStats::getSortOrder);

        List<MkEnergyConsumptionStats> statsList = energyConsumptionStatsDao.selectList(queryWrapper);

        List<MkEnergyConsumptionStatsVO> voList = new ArrayList<>();
        for (MkEnergyConsumptionStats stats : statsList) {
            MkEnergyConsumptionStatsVO vo = new MkEnergyConsumptionStatsVO();
            vo.setEquipmentType(stats.getEquipmentType());
            vo.setEquipmentName(stats.getEquipmentName());
            vo.setConsumptionValue(stats.getConsumptionValue());
            vo.setConsumptionPercentage(stats.getConsumptionPercentage());
            vo.setColorCode(stats.getColorCode());
            vo.setSortOrder(stats.getSortOrder());
            voList.add(vo);
        }
        return voList;
    }
}