package com.yhd.admin.api.service.emergency.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.admin.api.dao.emergency.*;
import com.yhd.admin.api.domain.emergency.entity.*;
import com.yhd.admin.api.domain.emergency.query.MkComprehensiveDecisionParam;
import com.yhd.admin.api.domain.emergency.vo.*;
import com.yhd.admin.api.service.emergency.MkComprehensiveDecisionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 综合决策大屏 Service 实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MkComprehensiveDecisionServiceImpl implements MkComprehensiveDecisionService {

    @Resource
    private MkProductionStatsDao productionStatsDao;

    @Resource
    private MkSensorStatsDao sensorStatsDao;

    @Resource
    private MkRiskControlStatsDao riskControlStatsDao;

    @Resource
    private MkHazardControlStatsDao hazardControlStatsDao;

    @Resource
    private MkPersonnelStatsDao personnelStatsDao;

    @Resource
    private MkIntelligentWorkfaceStatsDao intelligentWorkfaceStatsDao;

    @Resource
    private MkHydrologyMonitorStatsDao hydrologyMonitorStatsDao;

    @Resource
    private MkEquipmentStatsDao equipmentStatsDao;

    @Resource
    private MkEnergyConsumptionStatsDao energyConsumptionStatsDao;

    @Override
    public MkComprehensiveDecisionVO getDashboardData(MkComprehensiveDecisionParam param) {
        log.info("获取综合决策大屏数据，参数：{}", param);

        MkComprehensiveDecisionVO result = new MkComprehensiveDecisionVO();

        String orgCode = param.getOrgCode();

        // 获取生产统计数据
        result.setProductionStats(getProductionStats(orgCode));

        // 获取传感器统计数据
        result.setSensorStats(getSensorStats(orgCode));

        // 获取风险管控统计数据
        result.setRiskControlStats(getRiskControlStats(orgCode));

        // 获取隐患管控统计数据
        result.setHazardControlStats(getHazardControlStats(orgCode));

        // 获取人员统计数据
        result.setPersonnelStats(getPersonnelStats(orgCode));

        // 获取智能工作面统计数据
        result.setIntelligentWorkfaceStats(getIntelligentWorkfaceStats(orgCode));

        // 获取水文监测统计数据
        result.setHydrologyMonitorStats(getHydrologyMonitorStats(orgCode));

        // 获取设备统计数据
        result.setEquipmentStats(getEquipmentStats(orgCode));

        // 获取能耗统计数据
        result.setEnergyConsumptionStats(getEnergyConsumptionStats(orgCode));

        return result;
    }

    /**
     * 获取生产统计数据
     */
    private MkProductionStatsVO getProductionStats(String orgCode) {
        LambdaQueryWrapper<MkProductionStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkProductionStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkProductionStats::getOrgCode, orgCode)
                .orderByDesc(MkProductionStats::getCreateTime)
                .last("LIMIT 1");

        MkProductionStats stats = productionStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkProductionStatsVO vo = new MkProductionStatsVO();
            vo.setStatsDate(stats.getStatsDate());
            vo.setDailyProduction(stats.getDailyProduction());
            vo.setDailySales(stats.getDailySales());
            vo.setMonthlyProduction(stats.getMonthlyProduction());
            vo.setMonthlySales(stats.getMonthlySales());
            vo.setYearlyProduction(stats.getYearlyProduction());
            vo.setYearlySales(stats.getYearlySales());
            vo.setSafetyDays(stats.getSafetyDays());
            vo.setDesignCapacity(stats.getDesignCapacity());
            vo.setMainCoalSeam(stats.getMainCoalSeam());
            return vo;
        }

        return new MkProductionStatsVO();
    }

    /**
     * 获取传感器统计数据
     */
    private MkSensorStatsVO getSensorStats(String orgCode) {
        LambdaQueryWrapper<MkSensorStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkSensorStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkSensorStats::getOrgCode, orgCode)
                .orderByDesc(MkSensorStats::getCreateTime)
                .last("LIMIT 1");

        MkSensorStats stats = sensorStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkSensorStatsVO vo = new MkSensorStatsVO();
            vo.setCoSensorCount(stats.getCoSensorCount());
            vo.setMethaneSensorCount(stats.getMethaneSensorCount());
            vo.setTotalSensorCount(stats.getTotalSensorCount());
            return vo;
        }

        return new MkSensorStatsVO();
    }

    /**
     * 获取风险管控统计数据
     */
    private MkRiskControlStatsVO getRiskControlStats(String orgCode) {
        LambdaQueryWrapper<MkRiskControlStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkRiskControlStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkRiskControlStats::getOrgCode, orgCode)
                .orderByDesc(MkRiskControlStats::getCreateTime)
                .last("LIMIT 1");

        MkRiskControlStats stats = riskControlStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkRiskControlStatsVO vo = new MkRiskControlStatsVO();
            vo.setMajorRiskCount(stats.getMajorRiskCount());
            vo.setSignificantRiskCount(stats.getSignificantRiskCount());
            vo.setGeneralRiskCount(stats.getGeneralRiskCount());
            vo.setLowRiskCount(stats.getLowRiskCount());
            return vo;
        }

        return new MkRiskControlStatsVO();
    }

    /**
     * 获取隐患管控统计数据
     */
    private MkHazardControlStatsVO getHazardControlStats(String orgCode) {
        LambdaQueryWrapper<MkHazardControlStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkHazardControlStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkHazardControlStats::getOrgCode, orgCode)
                .orderByDesc(MkHazardControlStats::getCreateTime)
                .last("LIMIT 1");

        MkHazardControlStats stats = hazardControlStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkHazardControlStatsVO vo = new MkHazardControlStatsVO();
            vo.setGeneralHazardCount(stats.getGeneralHazardCount());
            vo.setMajorHazardCount(stats.getMajorHazardCount());

            // 获取近7日隐患趋势数据
            vo.setTrendData(getHazardTrendData(orgCode));

            return vo;
        }

        return new MkHazardControlStatsVO();
    }

    /**
     * 获取近7日隐患趋势数据
     */
    private List<MkHazardTrendVO> getHazardTrendData(String orgCode) {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(6); // 获取近7天数据

        LambdaQueryWrapper<MkHazardControlStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkHazardControlStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkHazardControlStats::getOrgCode, orgCode)
                .ge(MkHazardControlStats::getStatsDate, startDate)
                .le(MkHazardControlStats::getStatsDate, endDate)
                .orderByAsc(MkHazardControlStats::getStatsDate);

        List<MkHazardControlStats> statsList = hazardControlStatsDao.selectList(queryWrapper);

        List<MkHazardTrendVO> trendList = new ArrayList<>();

        // 创建近7天的完整日期列表，确保每天都有数据
        for (int i = 0; i < 7; i++) {
            LocalDate currentDate = startDate.plusDays(i);
            MkHazardTrendVO trendVO = new MkHazardTrendVO();
            trendVO.setStatsDate(currentDate);

            // 查找当天的数据
            MkHazardControlStats dayStats = statsList.stream()
                    .filter(s -> s.getStatsDate().equals(currentDate))
                    .findFirst()
                    .orElse(null);

            if (dayStats != null) {
                trendVO.setGeneralHazardCount(
                        dayStats.getGeneralHazardCount() != null ? dayStats.getGeneralHazardCount() : 0);
                trendVO.setMajorHazardCount(
                        dayStats.getMajorHazardCount() != null ? dayStats.getMajorHazardCount() : 0);
            } else {
                // 如果当天没有数据，设置为0
                trendVO.setGeneralHazardCount(0);
                trendVO.setMajorHazardCount(0);
            }

            trendVO.setTotalHazardCount(trendVO.getGeneralHazardCount() + trendVO.getMajorHazardCount());
            trendList.add(trendVO);
        }

        return trendList;
    }

    /**
     * 获取人员统计数据
     */
    private MkPersonnelStatsVO getPersonnelStats(String orgCode) {
        LambdaQueryWrapper<MkPersonnelStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkPersonnelStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkPersonnelStats::getOrgCode, orgCode)
                .orderByDesc(MkPersonnelStats::getCreateTime)
                .last("LIMIT 1");

        MkPersonnelStats stats = personnelStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkPersonnelStatsVO vo = new MkPersonnelStatsVO();
            vo.setTotalEmployeeCount(stats.getTotalEmployeeCount());
            vo.setUndergroundLeaderCount(stats.getUndergroundLeaderCount());
            vo.setUndergroundWorkerCount(stats.getUndergroundWorkerCount());
            return vo;
        }

        return new MkPersonnelStatsVO();
    }

    /**
     * 获取智能工作面统计数据
     */
    private MkIntelligentWorkfaceStatsVO getIntelligentWorkfaceStats(String orgCode) {
        LambdaQueryWrapper<MkIntelligentWorkfaceStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkIntelligentWorkfaceStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkIntelligentWorkfaceStats::getOrgCode, orgCode)
                .orderByDesc(MkIntelligentWorkfaceStats::getCreateTime)
                .last("LIMIT 1");

        MkIntelligentWorkfaceStats stats = intelligentWorkfaceStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkIntelligentWorkfaceStatsVO vo = new MkIntelligentWorkfaceStatsVO();
            vo.setMemoryCuttingRate(stats.getMemoryCuttingRate());
            vo.setStartupRate(stats.getStartupRate());
            vo.setAutoFollowingRate(stats.getAutoFollowingRate());
            return vo;
        }

        return new MkIntelligentWorkfaceStatsVO();
    }

    /**
     * 获取水文监测统计数据
     */
    private MkHydrologyMonitorStatsVO getHydrologyMonitorStats(String orgCode) {
        LambdaQueryWrapper<MkHydrologyMonitorStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkHydrologyMonitorStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkHydrologyMonitorStats::getOrgCode, orgCode)
                .orderByDesc(MkHydrologyMonitorStats::getCreateTime)
                .last("LIMIT 1");

        MkHydrologyMonitorStats stats = hydrologyMonitorStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkHydrologyMonitorStatsVO vo = new MkHydrologyMonitorStatsVO();
            vo.setMiningAreaPumpFlow(stats.getMiningAreaPumpFlow());
            vo.setCentralPumpRoomFlow(stats.getCentralPumpRoomFlow());
            vo.setCoalAuxiliaryTunnelFlow(stats.getCoalAuxiliaryTunnelFlow());
            return vo;
        }

        return new MkHydrologyMonitorStatsVO();
    }

    /**
     * 获取设备统计数据
     */
    private MkEquipmentStatsVO getEquipmentStats(String orgCode) {
        LambdaQueryWrapper<MkEquipmentStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkEquipmentStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkEquipmentStats::getOrgCode, orgCode)
                .orderByDesc(MkEquipmentStats::getCreateTime)
                .last("LIMIT 1");

        MkEquipmentStats stats = equipmentStatsDao.selectOne(queryWrapper);

        if (stats != null) {
            MkEquipmentStatsVO vo = new MkEquipmentStatsVO();
            vo.setOnlineEquipmentCount(stats.getOnlineEquipmentCount());
            vo.setTotalEquipmentCount(stats.getTotalEquipmentCount());
            vo.setStartupRate(stats.getStartupRate());
            vo.setFaultEquipmentCount(stats.getFaultEquipmentCount());
            vo.setFaultRate(stats.getFaultRate());
            return vo;
        }

        return new MkEquipmentStatsVO();
    }

    /**
     * 获取能耗统计数据
     */
    private List<MkEnergyConsumptionStatsVO> getEnergyConsumptionStats(String orgCode) {
        LambdaQueryWrapper<MkEnergyConsumptionStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkEnergyConsumptionStats::getStatus, 1)
                .eq(StringUtils.hasText(orgCode), MkEnergyConsumptionStats::getOrgCode, orgCode)
                .orderByDesc(MkEnergyConsumptionStats::getCreateTime)
                .orderByAsc(MkEnergyConsumptionStats::getSortOrder);

        List<MkEnergyConsumptionStats> statsList = energyConsumptionStatsDao.selectList(queryWrapper);

        List<MkEnergyConsumptionStatsVO> voList = new ArrayList<>();
        for (MkEnergyConsumptionStats stats : statsList) {
            MkEnergyConsumptionStatsVO vo = new MkEnergyConsumptionStatsVO();
            vo.setEquipmentType(stats.getEquipmentType());
            vo.setEquipmentName(stats.getEquipmentName());
            vo.setConsumptionValue(stats.getConsumptionValue());
            vo.setConsumptionPercentage(stats.getConsumptionPercentage());
            vo.setColorCode(stats.getColorCode());
            vo.setSortOrder(stats.getSortOrder());
            voList.add(vo);
        }
        return voList;
    }
}