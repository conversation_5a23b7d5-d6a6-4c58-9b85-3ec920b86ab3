package com.yhd.admin.api.domain.sys.dto;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SysFileTypeDTO extends BaseDTO {
  private Long id;
  /** 类型名称 */
  private String typeName;
  /** 父级ID */
  private Long parentId;
  /** 父级名称 */
  private String parentName;
  /** 状态 */
  private Boolean status;
  /** 排序，越小越靠前 */
  private Long sortNum;

  /** 子节点 */
  private List<SysFileTypeDTO> children;
}
