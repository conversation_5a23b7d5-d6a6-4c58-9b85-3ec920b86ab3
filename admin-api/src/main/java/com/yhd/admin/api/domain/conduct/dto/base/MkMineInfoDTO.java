package com.yhd.admin.api.domain.conduct.dto.base;

import com.yhd.admin.common.domain.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 矿井基础信息表(MkMineInfo)DTO
 *
 * <AUTHOR>
 * @since 2025-07-28 09:25:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MkMineInfoDTO extends BaseDTO implements Serializable {
    /** Id */
    private Long id;
    /** 所属煤矿 */
    private String orgName;
    /** 组织机构编码 */
    private String orgCode;
    /** 名称 */
    private String name;
    /** 编码 */
    private String code;
    /** 设计产能 */
    private BigDecimal designCapacity;
    /** 通风方式名称（1压入式通风、2抽出式通风、3长压短抽式、4短压长抽式） */
    private String ventilationModeName;
    /** 通风方式代码 */
    private String ventilationModeCode;
}

