package com.yhd.admin.api.service.sys;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.api.dao.sys.TemplateTableDao;
import com.yhd.admin.api.domain.sys.convert.TemplateTableConvert;
import com.yhd.admin.api.domain.sys.dto.TemplateTableDTO;
import com.yhd.admin.api.domain.sys.entity.TemplateTable;
import com.yhd.admin.api.domain.sys.query.TemplateTableParam;
import com.yhd.admin.common.domain.query.BatchParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/3/17 15:35
 */

@Service
public class TemplateTableServiceImpl extends ServiceImpl<TemplateTableDao, TemplateTable>
    implements TemplateTableService {
    private final TemplateTableConvert tableConvert;

    public TemplateTableServiceImpl(TemplateTableConvert tableConvert) {
        this.tableConvert = tableConvert;
    }

    @Override
    public Boolean addOrModify(TemplateTableParam addParam) {
        return super.saveOrUpdate(tableConvert.transformParamToEntity(addParam));
    }

    @Override
    public Boolean removeBatch(BatchParam param) {
        return super.removeByIds(param.getId());
    }

    @Override
    public IPage<TemplateTableDTO> pagingQuery(TemplateTableParam queryParam) {
        Page<TemplateTable> tablePage = new Page<>(queryParam.getCurrent(), queryParam.getPageSize());
        LambdaQueryChainWrapper<TemplateTable> chainWrapper = new LambdaQueryChainWrapper<>(baseMapper).like(
            StringUtils.isNoneBlank(queryParam.getTableName()), TemplateTable::getTableName, queryParam.getTableName());

        return chainWrapper.page(tablePage).convert(tableConvert::transformEntityToDTO);
    }

    @Override
    public TemplateTableDTO getDetail(Long id) {
        return tableConvert.transformEntityToDTO(super.getById(id));
    }
}
