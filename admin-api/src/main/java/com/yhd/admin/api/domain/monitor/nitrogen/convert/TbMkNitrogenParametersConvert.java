package com.yhd.admin.api.domain.monitor.nitrogen.convert;

import com.yhd.admin.api.domain.monitor.nitrogen.dto.TbMkNitrogenParametersDTO;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenParameters;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.TbMkNitrogenParametersVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenParametersCreateVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.update.TbMkNitrogenParametersUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 制氮系统参数表
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkNitrogenParametersConvert {

    @Mapping(target = "id", ignore = true)
    TbMkNitrogenParameters convert(TbMkNitrogenParametersCreateVO createVO);

    TbMkNitrogenParameters convert(TbMkNitrogenParametersUpdateVO updateVO);

    TbMkNitrogenParametersVO convert(TbMkNitrogenParameters po);

    TbMkNitrogenParametersDTO toDTO(TbMkNitrogenParameters po);



}
