package com.yhd.admin.api.domain.monitor.nitrogen.convert;

import com.yhd.admin.api.domain.monitor.nitrogen.dto.TbMkNitrogenSystemMainDTO;
import com.yhd.admin.api.domain.monitor.nitrogen.entity.TbMkNitrogenSystemMain;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.TbMkNitrogenSystemMainVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.create.TbMkNitrogenSystemMainCreateVO;
import com.yhd.admin.api.domain.monitor.nitrogen.vo.update.TbMkNitrogenSystemMainUpdateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 制氮系统监控数据-主表
 *
 * <AUTHOR>
 * @since
 */
@Mapper(componentModel = "spring")
public interface TbMkNitrogenSystemMainConvert {

    @Mapping(target = "id", ignore = true)
    TbMkNitrogenSystemMain convert(TbMkNitrogenSystemMainCreateVO createVO);

    TbMkNitrogenSystemMain convert(TbMkNitrogenSystemMainUpdateVO updateVO);

    TbMkNitrogenSystemMainVO convert(TbMkNitrogenSystemMain po);

    TbMkNitrogenSystemMainDTO toDTO(TbMkNitrogenSystemMain po);



}
