package com.yhd.admin.api.domain.monitor.nitrogen.vo.page;

import com.yhd.admin.common.domain.query.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 制氮系统空压机基本信息表
 *
 * <AUTHOR>
 * @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TbMkNitrogenCompressorInfoPageVO extends QueryParam {

    private static final long serialVersionUID = 1L;



        /**
         * 所属制氮系统ID
         */
        private Long nitrogenSystemId;

        /**
         * 空压机名称
         */
        private String compressorName;

        /**
         * 空压机编号
         */
        private String compressorNumber;

        /**
         * 备注信息
         */
        private String remarks;
}
