package com.yhd.admin.api.service.conduct.trouble;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.api.domain.conduct.dto.trouble.MkHideTroubleRemindDTO;
import com.yhd.admin.api.domain.conduct.entity.trouble.MkHideTroubleRecord;
import com.yhd.admin.api.domain.conduct.entity.trouble.MkHideTroubleRemind;
import com.yhd.admin.api.domain.conduct.query.trouble.MkHideTroubleRemindParam;

import java.util.List;

/**
 * 隐患催办记录表服务接口
 *
 * <AUTHOR>
 * @since 2025-08-04 10:28:36
 */
public interface MkHideTroubleRemindService extends IService<MkHideTroubleRemind> {

    /**
     * 分页查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    IPage<MkHideTroubleRemindDTO> pagingQuery(MkHideTroubleRemindParam param);

    /**
     * 列表查询
     *
     * @param param 筛选条件
     * @return 查询结果
     */
    List<MkHideTroubleRemindDTO> queryList(MkHideTroubleRemindParam param);

    /**
     * 新增
     *
     * @param record 隐患数据
     * @return 是否成功
     */
    Boolean add(MkHideTroubleRecord record);
}
