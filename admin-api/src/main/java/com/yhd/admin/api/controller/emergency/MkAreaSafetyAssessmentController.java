package com.yhd.admin.api.controller.emergency;

import com.yhd.admin.api.domain.emergency.convert.MkAreaSafetyAssessmentConvert;
import com.yhd.admin.api.domain.emergency.entity.MkAreaSafetyAssessment;
import com.yhd.admin.api.domain.emergency.query.MkAreaSafetyAssessmentParam;
import com.yhd.admin.api.domain.emergency.vo.MkAreaSafetyAssessmentVO;
import com.yhd.admin.api.exception.BMSException;
import com.yhd.admin.api.service.emergency.MkAreaSafetyAssessmentService;
import com.yhd.admin.common.domain.RespJson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MkAreaSafetyAssessmentController.java
 * @Description 区域安全评估-灾害预警中心Controller
 * @createTime 2025年08月02日 10:00:00
 */
@RestController
@RequestMapping("/open/emergency/area-safety")
@Slf4j
public class MkAreaSafetyAssessmentController {

    private final MkAreaSafetyAssessmentService areaSafetyAssessmentService;
    private final MkAreaSafetyAssessmentConvert convert;

    public MkAreaSafetyAssessmentController(MkAreaSafetyAssessmentService areaSafetyAssessmentService,
                                            MkAreaSafetyAssessmentConvert convert) {
        this.areaSafetyAssessmentService = areaSafetyAssessmentService;
        this.convert = convert;
    }

    /**
     * 查询区域安全评估列表
     *
     * @param param 查询参数（可选，如果不传或参数为空则查询全部）
     * @return 区域安全评估列表
     */
    @PostMapping(value = "/queryList", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<MkAreaSafetyAssessmentVO>> queryList(
        @RequestBody(required = false) MkAreaSafetyAssessmentParam param) {
        log.info("查询区域安全评估列表，参数：{}", param);
        List<MkAreaSafetyAssessment> list = areaSafetyAssessmentService.queryList(param);
        List<MkAreaSafetyAssessmentVO> voList = list.stream().map(convert::toVO).collect(Collectors.toList());
        return RespJson.success(voList);
    }

    /**
     * 根据区域编码查询详情
     *
     * @param areaCode 区域编码
     * @return 区域安全评估详情
     */
    @PostMapping(value = "/getDetail", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<MkAreaSafetyAssessmentVO> getDetail(@RequestBody(required = false) MkAreaSafetyAssessmentParam param) {
        //判断非空
        if (param.getAreaCode() == null) {
            throw new BMSException("error", "类型不能为空");
        }
        MkAreaSafetyAssessment entity = areaSafetyAssessmentService.getByAreaCode(param.getAreaCode());
        if (entity == null) {
            return RespJson.failure("未找到该区域的安全评估信息");
        }
        MkAreaSafetyAssessmentVO vo = convert.toVO(entity);
        return RespJson.success(vo);
    }

    /**
     * 按风险等级统计
     *
     * @param param 查询参数，包含orgCode和monitoringType
     * @return 风险等级统计结果
     */
    @PostMapping(value = "/statisticsByRiskLevel", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<Map<String, Object>>> statisticsByRiskLevel(
        @RequestBody(required = false) MkAreaSafetyAssessmentParam param) {
        log.info("按风险等级统计，参数：{}", param);
        String orgCode = param != null ? param.getOrgCode() : null;
        String monitoringType = param != null ? param.getMonitoringType() : null;
        List<Map<String, Object>> statistics = areaSafetyAssessmentService.statisticsByRiskLevel(orgCode,
            monitoringType);
        return RespJson.success(statistics);
    }

    /**
     * 按区域类型统计
     *
     * @param param 查询参数，包含orgCode和monitoringType
     * @return 区域类型统计结果
     */
    @PostMapping(value = "/statisticsByAreaType", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<Map<String, Object>>> statisticsByAreaType(
        @RequestBody(required = false) MkAreaSafetyAssessmentParam param) {
        log.info("按区域类型统计，参数：{}", param);
        String orgCode = param != null ? param.getOrgCode() : null;
        String monitoringType = param != null ? param.getMonitoringType() : null;
        List<Map<String, Object>> statistics = areaSafetyAssessmentService.statisticsByAreaType(orgCode,
            monitoringType);
        return RespJson.success(statistics);
    }

    /**
     * 按监测类型统计
     *
     * @param param 查询参数，包含orgCode
     * @return 监测类型统计结果
     */
    @PostMapping(value = "/statisticsByMonitoringType", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public RespJson<List<Map<String, Object>>> statisticsByMonitoringType(
        @RequestBody(required = false) MkAreaSafetyAssessmentParam param) {
        log.info("按监测类型统计，参数：{}", param);
        String orgCode = param != null ? param.getOrgCode() : null;
        List<Map<String, Object>> statistics = areaSafetyAssessmentService.statisticsByMonitoringType(orgCode);
        return RespJson.success(statistics);
    }
}
