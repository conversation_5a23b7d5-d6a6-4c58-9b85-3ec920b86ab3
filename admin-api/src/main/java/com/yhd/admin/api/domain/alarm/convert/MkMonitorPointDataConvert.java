package com.yhd.admin.api.domain.alarm.convert;

import com.yhd.admin.api.domain.alarm.dto.MkMonitorPointDataDTO;
import com.yhd.admin.api.domain.alarm.entity.MkMonitorPointData;
import com.yhd.admin.api.domain.alarm.query.MkMonitorPointDataParam;
import com.yhd.admin.api.domain.alarm.vo.MkMonitorPointDataVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MkMonitorPointDataConvert {
  MkMonitorPointDataDTO toDTO(MkMonitorPointData entity);

  MkMonitorPointDataVO toVO(MkMonitorPointDataDTO dto);

  MkMonitorPointData toEntity(MkMonitorPointDataParam param);
}
