package com.yhd.admin.api.service;

import com.yhd.admin.api.domain.sys.query.UserParam;
import com.yhd.admin.api.service.sys.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/21
 */
@Slf4j
public class UserServiceTest extends ApiApplicationTests {

    @Resource
    private UserService userService;

    @Resource
    WebApplicationContext applicationContext;

    @Test
    void exportUser() {
        String filePath = userService.exportUser(new UserParam());
        log.debug("{}", filePath);
        Assertions.assertNotEquals("", filePath);
    }

    @Test
    void testGenerationUserTemplate() {
        String filePath = userService.generationUserTemplate();
        log.debug(">>>>>>>>>>>> {}", filePath);
        Assertions.assertNotEquals("", filePath);
    }

    @Test
    void testImportUser() throws FileNotFoundException {
        userService.importUser(new FileInputStream("/Users/<USER>/Downloads/CgooBWbIKCiABKnpAAADEZKEthY901.csv"),
            Boolean.TRUE, "1111");
    }

    @Test
    void testGetAllUrl() {
        RequestMappingHandlerMapping mapping =
            applicationContext.getBean("requestMappingHandlerMapping", RequestMappingHandlerMapping.class);
        List<String> urls = new ArrayList<>();
        // 获取url与类和方法的对应信息
        Map<RequestMappingInfo, HandlerMethod> map = mapping.getHandlerMethods();
        for (Map.Entry<RequestMappingInfo, HandlerMethod> m : map.entrySet()) {
            RequestMappingInfo info = m.getKey();
            HandlerMethod method = m.getValue();
            // 获取当前请求的url
            info.getPatternValues().forEach(path -> {
                log.debug("{}", path);
                urls.add(path);
            });
        }
        urls.sort(Comparator.naturalOrder());
        Assertions.assertNotEquals(0, urls.size());

    }
}
