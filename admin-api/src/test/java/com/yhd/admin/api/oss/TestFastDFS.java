package com.yhd.admin.api.oss;

import com.yhd.admin.api.ApiApplication;
import com.yhd.admin.api.configuration.FastDFSClient;
import jakarta.annotation.Resource;
import java.io.File;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = ApiApplication.class)
public class TestFastDFS {

  @Resource private FastDFSClient client;

  @Test
  void testUpLoad() {
    String[] retVal = client.uploadFile(new File("/Users/<USER>/Pictures/234_03.png"));
    log.info(">> {},{}", retVal[0], retVal[1]);
  }
}
