package com.yhd.admin.api.service.emergency;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yhd.admin.api.domain.emergency.dto.MkEmergencySensorDTO;
import com.yhd.admin.api.domain.emergency.query.MkEmergencySensorParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 应急救援监测信息表(传感器表) Service 测试类
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class MkEmergencySensorServiceTest {

    @Resource
    private MkEmergencySensorService emergencySensorService;

    /**
     * 测试分页查询
     */
    @Test
    public void testPagingQuery() {
        log.info("开始测试分页查询");
        
        MkEmergencySensorParam param = new MkEmergencySensorParam();
        param.setCurrent(1L);
        param.setPageSize(10L);
        param.setOrgCode("ORG001");
        
        IPage<MkEmergencySensorDTO> result = emergencySensorService.pagingQuery(param);
        
        log.info("分页查询结果：总数={}, 当前页数据量={}", result.getTotal(), result.getRecords().size());
        
        assert result != null;
        assert result.getRecords() != null;
    }

    /**
     * 测试新增传感器
     */
    @Test
    public void testAdd() {
        log.info("开始测试新增传感器");
        
        MkEmergencySensorParam param = new MkEmergencySensorParam();
        param.setSensorCode("TEST_SENSOR_001");
        param.setSensorName("测试传感器01");
        param.setSensorType("温度传感器");
        param.setLocation("测试位置");
        param.setLevelType("point");
        param.setLevelCode("TEST_001");
        param.setLevelName("测试监测点01");
        param.setOrgCode("ORG001");
        param.setStatus(1);
        param.setCurrentValue(new BigDecimal("25.5"));
        param.setThresholdMin(new BigDecimal("10.0"));
        param.setThresholdMax(new BigDecimal("40.0"));
        param.setUnit("℃");
        param.setDescription("测试传感器描述");
        param.setRemark("测试备注");
        
        Boolean result = emergencySensorService.add(param);
        
        log.info("新增传感器结果：{}", result);
        
        assert result != null && result;
    }

    /**
     * 测试获取传感器树形结构
     */
    @Test
    public void testGetSensorTree() {
        log.info("开始测试获取传感器树形结构");
        
        List<MkEmergencySensorDTO> result = emergencySensorService.getSensorTree("ORG001", null);
        
        log.info("传感器树形结构结果：数据量={}", result.size());
        
        // 打印树形结构
        printTree(result, 0);
        
        assert result != null;
    }

    /**
     * 测试查询异常传感器
     */
    @Test
    public void testGetAbnormalSensors() {
        log.info("开始测试查询异常传感器");
        
        List<MkEmergencySensorDTO> result = emergencySensorService.getAbnormalSensors("ORG001");
        
        log.info("异常传感器结果：数据量={}", result.size());
        
        for (MkEmergencySensorDTO dto : result) {
            log.info("异常传感器：{} - {} - 状态:{}", dto.getSensorCode(), dto.getSensorName(), dto.getStatus());
        }
        
        assert result != null;
    }

    /**
     * 测试查询超出阈值的传感器
     */
    @Test
    public void testGetOutOfRangeSensors() {
        log.info("开始测试查询超出阈值的传感器");
        
        List<MkEmergencySensorDTO> result = emergencySensorService.getOutOfRangeSensors("ORG001");
        
        log.info("超出阈值传感器结果：数据量={}", result.size());
        
        for (MkEmergencySensorDTO dto : result) {
            log.info("超出阈值传感器：{} - {} - 当前值:{} - 阈值范围:[{}, {}]", 
                    dto.getSensorCode(), dto.getSensorName(), dto.getCurrentValue(),
                    dto.getThresholdMin(), dto.getThresholdMax());
        }
        
        assert result != null;
    }

    /**
     * 测试根据传感器编码查询
     */
    @Test
    public void testGetBySensorCode() {
        log.info("开始测试根据传感器编码查询");
        
        MkEmergencySensorDTO result = emergencySensorService.getBySensorCode("SENSOR_1302_O2_001");
        
        if (result != null) {
            log.info("查询结果：{} - {} - 当前值:{}", result.getSensorCode(), result.getSensorName(), result.getCurrentValue());
        } else {
            log.info("未找到指定传感器");
        }
    }

    /**
     * 测试更新传感器当前值
     */
    @Test
    public void testUpdateCurrentValue() {
        log.info("开始测试更新传感器当前值");
        
        Boolean result = emergencySensorService.updateCurrentValue("SENSOR_1302_O2_001", new BigDecimal("19.5"));
        
        log.info("更新传感器当前值结果：{}", result);
        
        assert result != null;
    }

    /**
     * 测试统计各状态传感器数量
     */
    @Test
    public void testCountByStatus() {
        log.info("开始测试统计各状态传感器数量");
        
        Map<String, Long> result = emergencySensorService.countByStatus("ORG001");
        
        log.info("各状态传感器数量统计：{}", result);
        
        assert result != null;
    }

    /**
     * 测试统计各类型传感器数量
     */
    @Test
    public void testCountBySensorType() {
        log.info("开始测试统计各类型传感器数量");
        
        Map<String, Long> result = emergencySensorService.countBySensorType("ORG001");
        
        log.info("各类型传感器数量统计：{}", result);
        
        assert result != null;
    }

    /**
     * 测试获取传感器监控概览
     */
    @Test
    public void testGetMonitorOverview() {
        log.info("开始测试获取传感器监控概览");
        
        Map<String, Object> result = emergencySensorService.getMonitorOverview("ORG001");
        
        log.info("传感器监控概览：{}", result);
        
        assert result != null;
        assert result.containsKey("totalCount");
        assert result.containsKey("abnormalCount");
        assert result.containsKey("normalRate");
    }

    /**
     * 测试校验传感器编码唯一性
     */
    @Test
    public void testCheckSensorCodeUnique() {
        log.info("开始测试校验传感器编码唯一性");
        
        // 测试已存在的编码
        Boolean result1 = emergencySensorService.checkSensorCodeUnique("SENSOR_1302_O2_001", null);
        log.info("已存在编码校验结果：{}", result1);
        
        // 测试不存在的编码
        Boolean result2 = emergencySensorService.checkSensorCodeUnique("NOT_EXIST_SENSOR", null);
        log.info("不存在编码校验结果：{}", result2);
        
        assert result2 != null && result2;
    }

    /**
     * 打印树形结构
     */
    private void printTree(List<MkEmergencySensorDTO> nodes, int level) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }
        
        String indent = "  ".repeat(level);
        
        for (MkEmergencySensorDTO node : nodes) {
            log.info("{}├─ {} - {} ({})", indent, node.getSensorCode(), node.getSensorName(), node.getLevelType());
            
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                printTree(node.getChildren(), level + 1);
            }
        }
    }
}
