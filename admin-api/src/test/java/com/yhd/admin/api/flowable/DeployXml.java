package com.yhd.admin.api.flowable;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.yhd.admin.api.ApiApplication;

import jakarta.annotation.Resource;

/**
 * @program: admin-framework
 * @description: 部署流程
 * @author: wang<PERSON>ngman
 * @create: 2024-09-25 17:46
 **/
@SpringBootTest(classes = ApiApplication.class)
public class DeployXml {
    private static final String BPMN_FILE_SUFFIX = ".bpmn";
    @Resource
    private RepositoryService repositoryService;

    /**
     * 出库流程
     */
    @Test
    void ckDeploy() {
        // 获取相对路径的流程文件方法
        InputStream in =
            Thread.currentThread().getContextClassLoader().getResourceAsStream("processes/outbound_key.xml");
        Deployment deploy = repositoryService.createDeployment().addInputStream("出库审批" + BPMN_FILE_SUFFIX, in)
            .name("出库审批").key("outbound_key").category("出库审批").tenantId("flow").deploy();
        ProcessDefinition definition =
            repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
        repositoryService.setProcessDefinitionCategory(definition.getId(), "出库审批");
        System.out.println("部署出库成功:" + deploy.getId());
    }

    /**
     * 入库流程
     */
    @Test
    void rkDeploy() {
        // 获取相对路径的流程文件方法
        InputStream in =
            Thread.currentThread().getContextClassLoader().getResourceAsStream("processes/warehousing_key.xml");
        Deployment deploy = repositoryService.createDeployment().addInputStream("入库审批" + BPMN_FILE_SUFFIX, in)
            .name("入库审批").key("warehousing_key").category("入库审批").tenantId("flow").deploy();
        ProcessDefinition definition =
            repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
        repositoryService.setProcessDefinitionCategory(definition.getId(), "入库审批");
        System.out.println("部署入库成功:" + deploy.getId());
    }

    /**
     * 检修流程
     */
    @Test
    void xjDeploy() {
        // 获取相对路径的流程文件方法
        InputStream in =
            Thread.currentThread().getContextClassLoader().getResourceAsStream("processes/overhaul_key.xml");
        Deployment deploy = repositoryService.createDeployment().addInputStream("检修审批" + BPMN_FILE_SUFFIX, in)
            .name("检修审批").key("OVERHAUL_KEY").category("检修审批").tenantId("flow").deploy();
        ProcessDefinition definition =
            repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
        repositoryService.setProcessDefinitionCategory(definition.getId(), "检修审批");
        System.out.println("部署检修成功:" + deploy.getId());
    }

    /**
     * 挂起/激活流程
     */
    @Test
    void rkUndeploy() {
        ProcessDefinition procDef = repositoryService.createProcessDefinitionQuery()
            .deploymentId("19e8ff19-7be2-11ef-a946-8aaf02aae4f0").singleResult();
        repositoryService.suspendProcessDefinitionById(procDef.getId(), true, null);
        System.out.println("挂起成功:" + procDef.getId());
    }

    /**
     * 删除流程
     */
    @Test
    void rkUndeploy2() {
        ProcessDefinition procDef = repositoryService.createProcessDefinitionQuery()
            .deploymentId("7ad072c6-86f7-11ef-9f2d-005056c00001").singleResult();
        repositoryService.deleteDeployment("7ad072c6-86f7-11ef-9f2d-005056c00001", true);
        System.out.println("删除成功:" + procDef.getId());
    }

    @Test
    void rkUndeploy3() {
        SimpleDateFormat simpleFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 获取系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();

        // 获取当前日期并加上一天
        LocalDate localDate = LocalDate.now().plusDays(1);

        // 将LocalDate转换为ZonedDateTime，并设置时间为凌晨一点
        // 注意：atTime(int hour, int minute, int second) 方法用于设置时间
        ZonedDateTime zdt = localDate.atTime(1, 0).atZone(zoneId);

        // 将ZonedDateTime转换为Instant，然后转换为Date
        System.out.println(simpleFormatter.format(Date.from(zdt.toInstant())) + "========成功");
    }

    @Test
    void rkUndeploy5() {
        SimpleDateFormat simpleFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Date expireAtData = getExpireAtData(DayOfWeek.THURSDAY, LocalTime.of(1, 0));
        System.out.println(simpleFormatter.format(expireAtData) + "========成功");
    }

    public Date getExpireAtData(DayOfWeek targetDayOfWeek, LocalTime targetTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        LocalDate today = now.toLocalDate();
        // 如果今天就是目标星期几，并且当前时间已经过了目标时间，则目标日期应该是明天的目标星期几
        if (today.getDayOfWeek().equals(targetDayOfWeek) && now.toLocalTime().isAfter(targetTime)) {
            today = today.plusWeeks(1); // 或者使用plusDays(7)来获取下周的同一天
            today = today.with(TemporalAdjusters.next(targetDayOfWeek)); // 调整为下周的目标星期几
        } else if (!today.getDayOfWeek().equals(targetDayOfWeek)) {
            // 如果今天不是目标星期几，则直接调整到本周的目标星期几
            today = today.with(TemporalAdjusters.nextOrSame(targetDayOfWeek));
        }

        // 构建ZonedDateTime对象
        ZonedDateTime zdt = today.atTime(targetTime).atZone(zoneId);

        // 将ZonedDateTime转换为Date
        return Date.from(zdt.toInstant());
    }

    @Test
    void rkUndeploy6() {
        SimpleDateFormat simpleFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        getExpireAtData();

        System.out.println(getExpireAtData() + "========成功");
    }

    public Date getExpireAtData() {
        // 获取系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();
        // 获取当前日期并加上一天
        LocalDate localDate = LocalDate.now().plusDays(1);

        // 将LocalDate转换为ZonedDateTime，并设置时间为凌晨一点
        // 注意：atTime(int hour, int minute, int second) 方法用于设置时间
        ZonedDateTime zdt = localDate.atTime(1, 0).atZone(zoneId);
        return Date.from(zdt.toInstant());
        // // 获取系统默认时区
        // ZoneId zoneId = ZoneId.systemDefault();
        // // 获取当前时间
        // ZonedDateTime now = ZonedDateTime.now(zoneId);
        // // 获取当前日期
        // LocalDate today = now.toLocalDate();
        // // 设定目标时间为下午1点15分
        // LocalTime targetTime = LocalTime.of(17, 13);
        //
        // // 如果当前时间已经超过了目标时间，则目标日期应该是明天
        // if (now.toLocalTime().isAfter(targetTime)) {
        // today = today.plusDays(1);
        // }
        //
        // // 构建ZonedDateTime对象
        // ZonedDateTime zdt = today.atTime(targetTime).atZone(zoneId);
        //
        // // 将ZonedDateTime转换为Date
        // return Date.from(zdt.toInstant());
        // }
    }

    /**
     * 获取基于月份中特定日期和时间的到期日期
     *
     * @param targetDayOfMonth 每月的目标日期（1-31）
     * @param targetTime 目标时间
     * @return 到期时间的Date对象
     */
    public Date getExpireAtData(int targetDayOfMonth, LocalTime targetTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        LocalDate today = now.toLocalDate();

        // 创建目标月份的LocalDate对象，默认年份和月份与当前日期相同
        LocalDate targetDate = today.withDayOfMonth(targetDayOfMonth);

        // 如果目标日期小于当前日期（即已经过去），则滚动到下一个月
        if (targetDate.isBefore(today)) {
            targetDate = targetDate.plusMonths(1);
        }

        // 构建ZonedDateTime对象
        ZonedDateTime zdt = targetDate.atTime(targetTime).atZone(zoneId);

        // 将ZonedDateTime转换为Date
        return Date.from(zdt.toInstant());
    }
}
