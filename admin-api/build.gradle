plugins {
    id 'java'
    id 'war'
}
group = 'com.yhd'
version = '0.0.1-SNAPSHOT'

dependencies {
    implementation project(':admin-common')

    implementation group: 'com.squareup.okhttp3', name: 'okhttp'
    implementation group: 'com.github.houbb', name: 'pinyin'
    implementation group: 'commons-codec', name: 'commons-codec'
    implementation group: 'io.minio', name: 'minio'
    implementation group: 'com.fasterxml.jackson.core', name: 'jackson-annotations'
    implementation group: 'commons-codec', name: 'commons-codec'
    implementation group: 'org.apache.commons', name: 'commons-lang3'
    implementation group: 'org.apache.commons', name: 'commons-text'
    implementation group: 'org.apache.commons', name: 'commons-pool2'
    implementation group: 'org.mapstruct', name: 'mapstruct'
    implementation group: 'org.csource', name: 'fastdfs-client-java'
    implementation group: 'org.flowable', name: 'flowable-spring-boot-starter'

    implementation 'com.opencsv:opencsv'
    implementation 'org.redisson:redisson'
// https://mvnrepository.com/artifact/com.baomidou/dynamic-datasource-spring-boot3-starter
    implementation 'com.baomidou:dynamic-datasource-spring-boot3-starter'
    implementation 'com.github.gavlyukovskiy:p6spy-spring-boot-starter'
    implementation 'org.postgresql:postgresql'
    implementation 'com.baomidou:mybatis-plus-spring-boot3-starter'
    implementation 'com.baomidou:mybatis-plus-jsqlparser'

    // ShardingSphere-JDBC
//    implementation 'org.apache.shardingsphere:shardingsphere-jdbc-core'
//    implementation 'com.microsoft.sqlserver:mssql-jdbc'
    //Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding'

    //SpringBoot
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation "org.springframework.boot:spring-boot-starter-oauth2-resource-server"
    implementation 'org.springframework.boot:spring-boot-starter-websocket'

//    implementation 'cn.hutool:hutool-all'
    // https://mvnrepository.com/artifact/commons-io/commons-io
    implementation 'commons-io:commons-io'
    // https://mvnrepository.com/artifact/commons-codec/commons-codec
    implementation 'commons-codec:commons-codec'
    // https://mvnrepository.com/artifact/org.apache.commons/commons-collections4
    implementation 'org.apache.commons:commons-collections4'
    //
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
//    implementation 'de.codecentric:spring-boot-admin-starter-client'

    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor group: 'org.mapstruct', name: 'mapstruct-processor'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    runtimeOnly 'com.mysql:mysql-connector-j'
    providedRuntime 'org.springframework.boot:spring-boot-starter-tomcat'

    //Test
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'


    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok-mapstruct-binding'

}

