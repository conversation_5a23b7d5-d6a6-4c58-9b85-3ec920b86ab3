-- 综合决策人员统计表
DROP TABLE IF EXISTS `tb_mk_personnel_stats`;

CREATE TABLE `tb_mk_personnel_stats` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stats_code` VARCHAR(50) NOT NULL COMMENT '统计编码',
    `stats_date` DATE NOT NULL COMMENT '统计日期',
    `total_employee_count` INT DEFAULT 0 COMMENT '员工总人数',
    `underground_leader_count` INT DEFAULT 0 COMMENT '井下领导人数',
    `underground_worker_count` INT DEFAULT 0 COMMENT '井下人数',
    `org_code` VARCHAR(50) NOT NULL COMMENT '组织机构编码',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(50) NOT NULL COMMENT '创建人',
    `update_by` VARCHAR(50) NOT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code_date` (`stats_code`, `stats_date`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='综合决策人员统计表';

-- 插入示例数据
INSERT INTO `tb_mk_personnel_stats` (
    `stats_code`, 
    `stats_date`, 
    `total_employee_count`, 
    `underground_leader_count`, 
    `underground_worker_count`, 
    `org_code`, 
    `status`, 
    `create_by`, 
    `update_by`
) VALUES 
('STATS_001', '2024-01-01', 850, 15, 320, 'ORG_001', 1, 'admin', 'admin'),
('STATS_002', '2024-01-02', 852, 16, 325, 'ORG_001', 1, 'admin', 'admin'),
('STATS_003', '2024-01-03', 848, 14, 318, 'ORG_001', 1, 'admin', 'admin'),
('STATS_004', '2024-01-04', 855, 17, 330, 'ORG_001', 1, 'admin', 'admin'),
('STATS_005', '2024-01-05', 860, 18, 335, 'ORG_001', 1, 'admin', 'admin'),

-- 不同组织机构的数据
('STATS_006', '2024-01-01', 650, 12, 280, 'ORG_002', 1, 'admin', 'admin'),
('STATS_007', '2024-01-02', 655, 13, 285, 'ORG_002', 1, 'admin', 'admin'),
('STATS_008', '2024-01-03', 648, 11, 275, 'ORG_002', 1, 'admin', 'admin'),

-- 第三个组织机构
('STATS_009', '2024-01-01', 420, 8, 180, 'ORG_003', 1, 'admin', 'admin'),
('STATS_010', '2024-01-02', 425, 9, 185, 'ORG_003', 1, 'admin', 'admin'),

-- 一些历史数据
('STATS_011', '2023-12-28', 840, 14, 315, 'ORG_001', 1, 'admin', 'admin'),
('STATS_012', '2023-12-29', 845, 15, 320, 'ORG_001', 1, 'admin', 'admin'),
('STATS_013', '2023-12-30', 842, 13, 312, 'ORG_001', 1, 'admin', 'admin'),
('STATS_014', '2023-12-31', 847, 16, 318, 'ORG_001', 1, 'admin', 'admin'),

-- 一些禁用状态的数据
('STATS_015', '2024-01-06', 0, 0, 0, 'ORG_004', 0, 'admin', 'admin'),
('STATS_016', '2024-01-07', 0, 0, 0, 'ORG_004', 0, 'admin', 'admin');

-- 查询验证数据
SELECT 
    id,
    stats_code,
    stats_date,
    total_employee_count,
    underground_leader_count,
    underground_worker_count,
    org_code,
    status,
    create_time,
    update_time,
    create_by,
    update_by
FROM tb_mk_personnel_stats 
ORDER BY stats_date DESC, org_code;

-- 按组织机构统计
SELECT 
    org_code,
    COUNT(*) as record_count,
    AVG(total_employee_count) as avg_total_employees,
    AVG(underground_leader_count) as avg_underground_leaders,
    AVG(underground_worker_count) as avg_underground_workers
FROM tb_mk_personnel_stats 
WHERE status = 1
GROUP BY org_code
ORDER BY org_code; 