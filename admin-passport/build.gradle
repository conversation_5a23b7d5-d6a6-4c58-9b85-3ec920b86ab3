plugins {
    id 'java'
    id 'war'
}

group = 'com.yhd'
version = '0.0.1-SNAPSHOT'

dependencies {
    implementation project(':admin-common')

    implementation 'com.squareup.okhttp3:okhttp'
    implementation 'com.github.houbb:pinyin'
    implementation 'commons-codec:commons-codec'
    implementation 'io.minio:minio'
    implementation 'com.baomidou:mybatis-plus-spring-boot3-starter'
    implementation 'commons-codec:commons-codec'
    implementation 'org.apache.commons:commons-lang3'
    implementation 'org.apache.commons:commons-text'
    implementation 'org.apache.commons:commons-pool2'
    implementation 'org.mapstruct:mapstruct'

    // https://mvnrepository.com/artifact/com.baomidou/dynamic-datasource-spring-boot3-starter
    implementation 'com.baomidou:dynamic-datasource-spring-boot3-starter'
    implementation 'com.github.gavlyukovskiy:p6spy-spring-boot-starter'
    //SpringBoot
//    implementation 'com.fasterxml.jackson.core:jackson-annotations'
//    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation("org.springframework.session:spring-session-data-redis")
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.security:spring-security-cas'

    // ShardingSphere-JDBC
//    implementation 'org.apache.shardingsphere:shardingsphere-jdbc-core'
    //
    compileOnly 'com.sun.xml.bind:jaxb-impl'
    implementation 'com.microsoft.sqlserver:mssql-jdbc'

    //
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
//    implementation 'de.codecentric:spring-boot-admin-starter-server'
    // Spring Authorization Server
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-authorization-server'

    //thymeleaf
    implementation 'org.thymeleaf:thymeleaf-spring5'
    implementation 'org.thymeleaf.extras:thymeleaf-extras-java8time'

    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor 'org.mapstruct:mapstruct-processor'
    //Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding'

    runtimeOnly 'com.mysql:mysql-connector-j'

    providedRuntime 'org.springframework.boot:spring-boot-starter-tomcat'


    //Test
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok-mapstruct-binding'


    developmentOnly 'org.springframework.boot:spring-boot-devtools'


}
