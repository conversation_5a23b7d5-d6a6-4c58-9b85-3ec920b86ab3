body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
}

#login-bg.container-fluid {
    padding: 0;
    height: 100%;
    position: absolute;
}

.logo{
    position: absolute;
    top:24px;
    left:32px;
    z-index: 200;
    height:48px;
    width:50%;
    background-image: url(../img/logo2.png);
    background-repeat: no-repeat;
    background-size: contain;
}

/* Background image an color divs*/
.bg-img {
    min-width: 100%;
    vertical-align: top;
    padding: 0;
    margin-left: 0;
    height: 100%;
    background-color: #000512;
    display: inline-block;
    overflow: hidden;
}

.bg-color {
    margin-left: -5px;
}

.bg-img {
    background-image: url(../img/bg0.jpg);
    background-size: cover;
    background-position: bottom;
}

.login {
    display: flex;
    height: 400px;
    width: 800px;
    margin: 0 auto;
}

.titleBox {
    color: #fff;
    font-size: 24px;
    letter-spacing: 4px;
    width: 50%;
    background-image: linear-gradient(to right bottom, #56e2f3, #3993c7bb, #1b429a);
    display: flex;
    justify-content: center;
    align-items: center;
}

#login {
    padding-top: 10%;
    text-align: center;
    text-transform: uppercase;
}

:root {
    --borderColor: #21ecff;
}

.login h1 {
    margin-top: 30px;
    font-weight: bold;
    font-size: 60px;
    letter-spacing: 3px;
}

.login form {
    background: #fff;
    width: 50%;
    padding-top: 80px;
}

.login .btn {
    text-transform: uppercase;;
    font-size: 16px;
    padding: 8px;
    background-color: #4593ea;
    letter-spacing: 8px;
    color: #fff;
    font-weight: 100;
    transition: 0.5s;
    width: 80%;
    margin: 0 auto;
    border-radius: 50px;
}

.login .btn:hover {
    background-color: #58bfff;
    transition: 0.5s;
}

.form-group {
    position: relative;
    width: 80%;
    margin: 20px auto;
}

.form-icon {
    position: absolute;
    top: 4px;
    left: 8px;
    width: 26px;
    height: 26px;
}
.username-icon {
    background-image: url(../img/username.png);
    background-size: contain;
    background-position:center;
    background-repeat:no-repeat;
}
.password-icon {
    background-image: url(../img/password.png);
    background-size: contain;
    background-position:center;
    background-repeat:no-repeat;
}

.form-icon img {
    height: 100%;
}

.form-group input {
    font-size: 16px;
    font-weight: lighter;
    border: none;
    border-bottom: 1px solid #ccc;
    color: #333 !important;
    padding: 8px 16px;
    transition: 0.2s;
    padding-left: 42px;
}
#password {
    letter-spacing: 4px;
}

.form-group input:focus {
    border-bottom: 1px solid #aaa;
}

.form-group input::placeholder {
    color: #ccc; /* 设置为灰色 */
    letter-spacing: 0;
}
/* Form check styles*/

.form-check {
    padding: 0;
    text-align: left;
    width: 80%;
    margin: 0 auto;
    padding-left: 12px;
}

.form-check div {
    display: inline-block;
    padding-top: 2px;
}

.form-check label {
    vertical-align: top;
    padding-top: 2px;
    padding-left: 2px;
    color: #aaa;
    font-size: 14px;
}

.forgot-password {
    text-align: right;
    float: right;
    font-weight: bold;
}

.forgot-password a {
    color: #0086b7;
    opacity: 0.6;
}

.forgot-password a:hover {
    opacity: 1;
}

/* Switch styles */

.switch {
    position: relative;
    display: inline-block;
    width: 56px;
    height: 24px;
}

/* Hide default HTML checkbox */
.switch input {
    display: none;
}

/* The slider */
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #0091ff47;
    -webkit-transition: 0.4s;
    transition: 0.4s;
    border-radius: 30px;
}

.slider:before {
    position: absolute;
    content: '';
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: 0.4s;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #00aeff88;
}

input:focus + .slider {
    box-shadow: 0 0 1px #0ba1f8;
}

input:checked + .slider:before {
    /*-webkit-transform: translateX(30px);*/
    -ms-transform: translateX(30px);
    transform: translateX(30px);
}

/* Media queries */

@media (max-width: 500px) {
    .bg-img,
    .bg-color {
        min-width: 100%;
        height: 50%;
        margin: 0;
    }

    .forgot-password {
        text-align: right;
        float: left;
        padding: 20px 0;
    }

    #login {
        padding-top: 50px;
    }
}

/* 提示框的样式 */
.alert-box {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px;
    background-color: #2A70AB;
    color: white;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    opacity: 1;
    transition: opacity 0.5s ease-in-out;
}
/* 提示框消失时的透明度 */
.hidden {
    opacity: 0;
}

