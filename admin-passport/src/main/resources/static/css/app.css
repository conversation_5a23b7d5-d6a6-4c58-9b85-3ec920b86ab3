* {
    margin: 0;
    padding: 0;
}
body {
    margin: 0;
    padding: 0;
    background-image: url(../img/bg1.jpg);
    background-size: cover;
}
#logout-button {
    position: absolute;
    top:12px;
    right:12px;
    z-index: 100;
    color:#76D1EB;
    padding:8px 12px;
    border: 1px solid #76D1EB;
    border-radius: 4px;
    cursor: pointer;
    transition: 0.3s;
}
#logout-button:hover {
    border: 1px solid #fff;
    color: #fff;
    background: #1f96d599;
    transition: 0.3s;
}
.main {
    margin-top: 12%;
    text-align: center;
    font-size: 24px;
}
.main-border{
    border: 2px solid #81c0ffcc;
    width:70%;
    margin:0 auto;
    padding:8px;
    border-radius: 28px 0 28px 0;
    /* background-color: #409EFF; */
    position: relative;
}
/* .main-border:before{
  content:'';
  position: absolute;
  top:-30px;
  left:30px;
  background-color: inherit;
  width: 100%;
  height:100%;
  z-index: 200;
} */
.main-box {
    padding: 32px;
    /* background:#ffffff66; */
    background: linear-gradient(to bottom, #ffffff66, #2f97ff33);
    border-radius: 20px 0 20px 0;
}

.title {
    /* background-image: -webkit-linear-gradient(top, rgb(0, 229, 255), rgb(0, 153, 255));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent; */
    color: #fff;
    font-size: 28px;
    font-weight: 100;
    letter-spacing: 4px;
    margin-bottom: 24px;
}
.website-list {
    margin: 20px auto;
    width: 100%;
    /* display: grid; */
    display: flex;
    flex-wrap: wrap;
    justify-content: start;
    /* grid-template-columns: repeat(auto-fill, 300px);*/
    /* grid-gap: 30px 0;  */
}

.website-box {
    /*width: 100px;*/
    width:20%;
    height: 90px;
    font-size: 14px;
    color: #efefef;
    display: flex;
    align-items: center;
    vertical-align: middle;
    /* padding:20px; */

    position: relative;
    margin: 20px 0 30px;
    text-decoration: none;
    /*background: #ffffff33;*/
    border-radius: 15px;
    text-align: center;
}

.website-content {
    text-align: center;
    margin: 0 auto;
    text-decoration: none;
    color: #efefef;
    cursor: pointer;
}

.website-content:hover {
    /*background: #ffffff;*/
    text-decoration: none;
    color: #fff;
}

.svg {
    width: 88px;
    height: 88px;
    /*background-image: url('../img/platform_icon.png');*/
    /*background-size: contain;*/
    display:flex;
    justify-content: center;
    align-items: center;
    margin:0 auto;
    margin-bottom: 8px;
    /*margin: 20px 8px 20px 20px;*/
}
.svg img{
    width:80px;
    height:80px;
    border-radius: 10px;
    transition: 0.1s;
}
.website-content:hover .svg img{
    width:88px;
    height:88px;
    box-shadow: 0 0 16px 4px #ffffffcc;
    transition: 0.1s;
}

.website-box-text{
    height:32px;
}
