'use strict';


class LikeButton extends React.Component {
    constructor(props) {
        super(props);
        this.state = {data: undefined};
    }

    componentDidMount() {
        console.log("componentDidMount....")
        fetch('/passport/web/getAppUrl', {headers: {'Accept': 'application/json'}})
            .then(res => res.json()).then((data) => this.setState(() => ({data})))
    }

    render() {
        const {data} = this.state;
        return (<><div className="website-list">{data && data.data.map(e => (
            <React.Fragment>
                <span key={e.id} className="website-box" >
                    <a className="website-content" href={e.appUrl || undefined} target='_blank'>
                        <span className="svg">
                            <img src={e?.appIcon || '../img/app_default_icon.png'} alt=""/>
                        </span>
                        <div className="website-box-text">
                            {e.appName}
                        </div>
                    </a>
                </span>
            </React.Fragment>))}</div></>)
    }
}

const domContainer = document.querySelector('#navigation');
const root = ReactDOM.createRoot(domContainer);
root.render(<LikeButton/>);

const logoutButton = document.getElementById('logout-button');
logoutButton.onclick = async () => {
    console.log('logout...')
    await fetch('/passport/logout');

    location.reload();
}
