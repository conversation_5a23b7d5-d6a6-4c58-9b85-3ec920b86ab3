<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title>一体化管控平台</title>
    <link rel="stylesheet" type="text/css" th:href="@{/css/app.css}"></head>
<body>
<!--<div id="navigation"></div>-->
<div id="logout-button">退出登录</div>
<div class="main">
    <div class="title">泊里煤矿一体化管控平台</div>
    <div class="main-border">
        <div class="main-box">
            <div id="navigation">
            </div>
        </div>
    </div>
</div>
<!-- Note: when deploying, replace "development.js" with "production.min.js". -->
<script th:src="@{/js/react.production.min.js}"></script>
<script th:src="@{/js/react-dom.production.min.js}" ></script>
<script th:src="@{/js/babel.min.js}" ></script>
<script th:src="@{/js/app.js}" type="text/babel"></script>
<!--<script type="text/babel" data-presets="env,react" datatype="module" th:inline="javascript">-->
<!--    const htmlStr = `<a href="#"/>`;-->
<!--    console.log(`htmlStr:${htmlStr}`)-->
<!--</script>-->
</body>
</html>
