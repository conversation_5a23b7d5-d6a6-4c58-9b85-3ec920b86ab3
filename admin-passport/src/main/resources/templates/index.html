<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" th:href="@{/css/bootstrap.min.css}">

    <!-- Loding font -->
    <link th:href="@{/css/gfont.css}" rel="stylesheet">

    <!-- Custom Styles -->
    <link rel="stylesheet" type="text/css" th:href="@{/css/styles.css}">

    <title>一体化管控平台</title>
</head>
<body>
<!-- Backgrounds -->

<div id="login-bg" class="container-fluid">
    <div class="bg-img"></div>
</div>
<!--<div class="logo"></div>-->

<!-- End Backgrounds -->
<div class="container" id="login">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            <div class="login">
                <div class="titleBox">
                    <div>
                        <div>
                            泊里煤矿
                        </div>
                        <div>
                            一体化管控平台
                        </div>
                    </div>
                </div>

                <!-- Loging form -->
                <form th:action="@{/api/login}" method="post">
                    <div class="form-group">
                        <div class="form-icon username-icon"></div>
                        <input type="text" id="username" name="username" class="form-control" placeholder="请输入用户名" />
                    </div>
                    <div class="form-group">
                        <div class="form-icon password-icon"></div>
                        <input type="password" id="password" name="password" class="form-control" placeholder="请输入密码" />
                    </div>

                    <div class="form-check">
                        <!-- <label class="switch"> -->
                        <div>
                            <input type="checkbox" name="remember-me" />
                        </div>
                        <!-- <span class="slider round"></span>
                        </label> -->
                        <label class="form-check-label" for="remember-me">记住密码</label>
                    </div>

                    <br />
                    <button type="submit" class="btn btn-lg btn-block">登录</button>
                </form>
                <!-- End Loging form -->
            </div>
        </div>
    </div>
</div>
<div id="alertBox" class="alert-box" style="display: none;">
    登录失败，请重试。
</div>
</body>
<script>
    // 获取URL
    const urlParams = new URLSearchParams(window.location.search);

    // 显示提示框的函数
    function showAlert(message) {
        const alertBox = document.getElementById('alertBox');
        alertBox.textContent = message; // 动态设置提示框内容
        alertBox.style.display = 'block';

        // 3秒后自动隐藏提示框
        setTimeout(() => {
            alertBox.classList.add('hidden'); // 使用透明度淡出效果
            setTimeout(() => {
                alertBox.style.display = 'none'; // 完全隐藏
            }, 500); // 与淡出效果时间同步
        }, 3000); // 3秒后开始淡出
    }

    // 检查 failure 参数
    const failureParam = urlParams.get('failure');
    if (failureParam === 'true') {
        showAlert('登录失败，请重试');
    } else if (failureParam === 'password') {
        showAlert('用户名或密码错误');
    }

    // 删除URL中的failure参数，避免刷新页面时重复显示
    const newUrl = window.location.protocol + "//" + window.location.host + window.location.pathname;
    window.history.replaceState({}, document.title, newUrl);
</script>
</html>
