modulelist=com.baomidou.mybatisplus.extension.p6spy.MybatisPlusLogFactory,com.p6spy.engine.outage.P6OutageFactory
# ???????
logMessageFormat=com.baomidou.mybatisplus.extension.p6spy.P6SpyLogger
#????????
appender=com.baomidou.mybatisplus.extension.p6spy.StdoutLogger
# ???????? sql
#appender=com.p6spy.engine.spy.appender.Slf4JLogger
# ?? p6spy driver ??
deregisterdrivers=true
# ??JDBC URL??
useprefix=true
# ???? Log ??,????????error,info,batch,debug,statement,commit,rollback,result,resultset.
excludecategories=info,debug,result,commit,resultset
# ????
dateformat=yyyy-MM-dd HH:mm:ss
# ???????
#driverlist=org.h2.Driver
# ?????SQL??
outagedetection=true
# ?SQL???? 2 ?
outagedetectioninterval=2
