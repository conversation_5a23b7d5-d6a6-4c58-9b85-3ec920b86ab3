mode:
    type: Standalone
    repository:
        type: JDBC

dataSources:
    ds_1:
        dataSourceClassName: com.zaxxer.hikari.HikariDataSource
        driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
        jdbcUrl: *****************************************************************************
        username: SA
        password: 2z_Jhm*u
    ds_0:
        dataSourceClassName: com.zaxxer.hikari.HikariDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        jdbcUrl: *************************************************************************************************
        username: root
        password: 2z_Jhm*u

props:
    sql-show: true
    sql-simple: true
    connection-test-query: SELECT 1
    pool-name: HikariCP-DEV

# 数据分片，对于SQL，不用使用特别复杂的语法，存在不支持的情况。
rules:
    - !SHARDING
        tables:
            oauth2_authorization:
                actualDataNodes: ds_0.oauth2_authorization
            oauth2_authorization_consent:
                actualDataNodes: ds_0.oauth2_authorization_consent
            oauth2_registered_client:
                actualDataNodes: ds_0.oauth2_registered_client
            oauth_access_token:
                actualDataNodes: ds_0.oauth_access_token
            oauth_approvals:
                actualDataNodes: ds_0.oauth_approvals
            oauth_client_details:
                actualDataNodes: ds_0.oauth_client_details
            oauth_client_token:
                actualDataNodes: ds_0.oauth_client_token
            oauth_code:
                actualDataNodes: ds_0.oauth_code
            oauth_refresh_token:
                actualDataNodes: ds_0.oauth_refresh_token
            sys_log:
                actualDataNodes: ds_0.sys_log
            tb_sys_client:
                actualDataNodes: ds_0.tb_sys_client
            tb_sys_dic:
                actualDataNodes: ds_0.tb_sys_dic
            tb_sys_dic_item:
                actualDataNodes: ds_0.tb_sys_dic_item
            tb_sys_menu:
                actualDataNodes: ds_0.tb_sys_menu
            tb_sys_ops_log:
                actualDataNodes: ds_0.tb_sys_ops_log
            tb_sys_org:
                actualDataNodes: ds_0.tb_sys_org
            tb_sys_parameter:
                actualDataNodes: ds_0.tb_sys_parameter
            tb_sys_role:
                actualDataNodes: ds_0.tb_sys_role
            tb_sys_role_menu:
                actualDataNodes: ds_0.tb_sys_role_menu
            tb_sys_template_table:
                actualDataNodes: ds_0.tb_sys_template_table
            tb_sys_user:
                actualDataNodes: ds_0.tb_sys_user
            tb_sys_user_pin:
                actualDataNodes: ds_0.tb_sys_user_pin
            tb_sys_user_role:
                actualDataNodes: ds_0.tb_sys_user_role
            #ds_1
            DisplayData:
                actualDataNodes: ds_1.tb_sys_user_role
