server:
    tomcat:
        uri-encoding: UTF-8
        max-connections: 1000
        threads:
            max: 1000
            min-spare: 5
    servlet:
        context-path: /passport
spring:
    profiles:
        active: dev
    jackson:
        date-format: yyyy-MM-dd HH:mm:ss
        time-zone: GMT+8
        serialization:
            WRITE_DATES_AS_TIMESTAMPS: true
    servlet:
        multipart:
            max-file-size: 100MB
            max-request-size: 100MB
            enabled: true
    thymeleaf:
        cache: false
        mode: HTML
        encoding: UTF-8
        servlet:
            content-type: text/html
    #        prefix: classpath:static/,templates/
    session:
        redis:
            repository-type: indexed
        timeout: 3600
    main:
        allow-bean-definition-overriding: true
#mybatis
mybatis-plus:
    configuration:
        map-underscore-to-camel-case: true
        cache-enabled: false
        call-setters-on-nulls: true
        jdbc-type-for-null: 'null'
    global-config:
        banner: false
        db-config:
            table-prefix: 'tb_'
            update-strategy: ignored
    mapper-locations: classpath*:mapper/**/*Mapper.xml
