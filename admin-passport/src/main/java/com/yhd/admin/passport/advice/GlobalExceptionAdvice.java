package com.yhd.admin.passport.advice;

import java.nio.file.AccessDeniedException;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import com.yhd.admin.common.domain.RespJson;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/2/26 15:18
 */
@ControllerAdvice
@Slf4j
public class GlobalExceptionAdvice {

    @ExceptionHandler(value = AccessDeniedException.class)
    public void accessDeniedException(AccessDeniedException e) throws AccessDeniedException {
        throw e;
    }

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public ResponseEntity<RespJson> exceptionHandler(Exception e) {
        if (log.isErrorEnabled()) {
            log.error("系统异常:{}", e.getMessage());
        }
        return ResponseEntity.ok().body(RespJson.failure("系统错误"));
    }
}
