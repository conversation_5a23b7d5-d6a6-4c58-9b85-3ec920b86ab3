package com.yhd.admin.passport.biz.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.passport.biz.dao.AppDao;
import com.yhd.admin.passport.biz.domain.convert.AppConvert;
import com.yhd.admin.passport.biz.domain.dto.AppDTO;
import com.yhd.admin.passport.biz.domain.entity.SysApp;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/6
 */
@Component
public class AppSrvImpl extends ServiceImpl<AppDao, SysApp> implements AppSrv {

    @Resource
    private AppConvert convert;

    @Override
    public List<AppDTO> queryAll() {
        LambdaQueryWrapper<SysApp> queryWrapper =
            new QueryWrapper<SysApp>().lambda().eq(SysApp::getIsEnable, Boolean.TRUE).orderByAsc(SysApp::getSorted).orderByAsc(SysApp::getCreatedTime);
        return convert.toDTO(list(queryWrapper));
    }
}
