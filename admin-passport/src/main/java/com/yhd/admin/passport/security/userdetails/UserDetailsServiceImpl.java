package com.yhd.admin.passport.security.userdetails;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import com.yhd.admin.passport.biz.domain.dto.RoleDTO;
import com.yhd.admin.passport.biz.domain.dto.UserDTO;
import com.yhd.admin.passport.biz.domain.dto.UserPinDTO;
import com.yhd.admin.passport.biz.domain.query.UserParam;
import com.yhd.admin.passport.biz.service.RoleSrv;
import com.yhd.admin.passport.biz.service.UserPinSrv;
import com.yhd.admin.passport.biz.service.UserSrv;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName UserDetailsServiceImpl.java @Description TODO
 * @createTime 2020年03月20日 09:55:00
 */
@Component
@Slf4j
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserSrv userSrv;
    private final RoleSrv roleSrv;

    private final UserPinSrv userPinSrv;

    public UserDetailsServiceImpl(UserSrv userSrv, RoleSrv roleSrv, UserPinSrv userPinSrv) {
        this.userSrv = userSrv;
        this.roleSrv = roleSrv;
        this.userPinSrv = userPinSrv;
    }

    @Override
    public UserDetails loadUserByUsername(String username) {
        UserParam userParam = new UserParam();
        userParam.setUserName(username);
        // 查询账户
        UserDTO user = userSrv.getUser(userParam);
        if (Objects.isNull(user)) {
            throw new UsernameNotFoundException("User not found");
        }
        // 获取密码
        UserPinDTO userPin = userPinSrv.getUserPin(user.getUid(), user.getUsername());

        // 获取账户角色
        List<RoleDTO> roles = roleSrv.queryRole(userParam);
        return new User(user.getUsername(), userPin.getPassword(), user.getIsEnable(), user.getIsAccountNonExpired(),
            user.getIsCredentialsNonExpired(), user.getIsAccountNonLocked(),
            roles.stream().map(RoleDTO::getRoleCode).map(SimpleGrantedAuthority::new).collect(Collectors.toList()));
    }
}
