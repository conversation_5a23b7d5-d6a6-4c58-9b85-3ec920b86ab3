package com.yhd.admin.passport.biz.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@Controller
public class LoginController {
    @GetMapping(value = {"/web/login"})
    String login() {
        return "index";
    }

    @RequestMapping(value = "/loginFailure", method = {RequestMethod.GET, RequestMethod.POST})
    String loginFailure() {
        return "loginFailure";
    }
}
