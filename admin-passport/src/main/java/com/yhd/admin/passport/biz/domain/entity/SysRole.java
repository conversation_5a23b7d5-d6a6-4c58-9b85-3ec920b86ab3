package com.yhd.admin.passport.biz.domain.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SysRole implements Cloneable, Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 角色名称 */
    private String roleName;
    /** 角色编码 */
    private String roleCode;
    /** 角色状态 */
    private Boolean isEnable;
}
