package com.yhd.admin.passport.mixin;

import com.fasterxml.jackson.annotation.*;

@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE,
    isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonIgnoreProperties(ignoreUnknown = true, value = {"cause", "stackTrace", "suppressedExceptions"})
public class SessionAuthenticationExceptionMixin {
    @JsonCreator
    SessionAuthenticationExceptionMixin(@JsonProperty("error") Throwable error,
        @JsonProperty("detailMessage") String detailMessage) {}
}
