package com.yhd.admin.passport.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.session.events.SessionDeletedEvent;
import org.springframework.session.events.SessionExpiredEvent;

/**
 * <AUTHOR>
 * @date 2024/7/8
 */
@Configuration
@Slf4j
public class SessionListenerConfiguration {
    @EventListener
    public void onSessionExpired(SessionExpiredEvent expiredEvent) {
        if(log.isDebugEnabled()){
            log.debug("Session Expired {}",expiredEvent.getSession());
        }
    }

    @EventListener
    public void onSessionDeleted(SessionDeletedEvent deletedEvent) {
        if(log.isDebugEnabled()){
            log.debug("Session DeletedEvent {}",deletedEvent.getSession());
        }
    }
}
