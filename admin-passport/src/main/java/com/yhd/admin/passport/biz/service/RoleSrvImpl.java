package com.yhd.admin.passport.biz.service;

import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.passport.biz.dao.RoleDao;
import com.yhd.admin.passport.biz.domain.convert.RoleConvert;
import com.yhd.admin.passport.biz.domain.dto.RoleDTO;
import com.yhd.admin.passport.biz.domain.entity.SysRole;
import com.yhd.admin.passport.biz.domain.query.UserParam;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName RoleServiceImpl.java @Description TODO
 * @createTime 2020年03月20日 10:48:00
 */
@Service
public class RoleSrvImpl extends ServiceImpl<RoleDao, SysRole> implements RoleSrv {

    private final RoleConvert convert;

    public RoleSrvImpl(RoleConvert convert) {
        this.convert = convert;
    }

    @Override
    public List<RoleDTO> queryRole(UserParam param) {
        return convert.toDTO(baseMapper.selectRoleListByUser(param));
    }

    @Override
    public List<String> getAuthority(String clientId, List<Long> roleList) {
        if (!CollectionUtils.isEmpty(roleList)) {
            List<Long> menuIds = baseMapper.selectMenuByRole(clientId, roleList);
            if (!CollectionUtils.isEmpty(menuIds)) {
                return baseMapper.selectAuthorityById(menuIds);
            }
        }

        return Collections.emptyList();
    }
}
