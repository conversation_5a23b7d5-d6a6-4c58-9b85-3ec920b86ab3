package com.yhd.admin.passport.biz.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UserAccountEnum.java
 * @Description TODO
 * @createTime 2020年04月19日 10:08:00
 */
@Getter
public enum PassPortRedisKeyEnum {
    /**
     * KEY为用户名
     */
    USER_ACCOUNT2("PASSPORT:USER:ACCOUNT:%1$s", "账户REDIS 缓存 KEY，");

    private String key;
    private String desc;

    private PassPortRedisKeyEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
