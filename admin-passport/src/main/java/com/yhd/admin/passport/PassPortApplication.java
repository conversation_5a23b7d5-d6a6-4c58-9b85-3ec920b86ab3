package com.yhd.admin.passport;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.session.FlushMode;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisIndexedHttpSession;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@MapperScan("com.yhd.admin.passport.biz.dao")
@EnableRedisIndexedHttpSession(maxInactiveIntervalInSeconds = 43200, redisNamespace = "BL_XMC:SESSION",
    flushMode = FlushMode.IMMEDIATE)
public class PassPortApplication {

    public static void main(String[] args) {
        SpringApplication passPortApplication = new SpringApplication(PassPortApplication.class);
        passPortApplication.setBannerMode(Banner.Mode.OFF);
        passPortApplication.run(args);
    }

}
