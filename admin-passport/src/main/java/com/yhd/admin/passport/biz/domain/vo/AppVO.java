package com.yhd.admin.passport.biz.domain.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
public class AppVO implements Cloneable, Serializable {

    private Long id;
    /**
     * app 图标
     */
    private String appIcon;
    /**
     * app名称
     */
    private String appName;
    /**
     * app访问地址
     */
    private String appUrl;
    /**
     * 角色状态
     */
    private Boolean isEnable;
    /**
     * 排序
     */
    private Integer sorted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER)
    private LocalDateTime updatedTime;
}
