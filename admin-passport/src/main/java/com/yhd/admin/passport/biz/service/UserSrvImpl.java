package com.yhd.admin.passport.biz.service;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.common.domain.enums.RedisKeyEnum;
import com.yhd.admin.passport.biz.dao.UserDao;
import com.yhd.admin.passport.biz.domain.convert.UserConvert;
import com.yhd.admin.passport.biz.domain.dto.UserDTO;
import com.yhd.admin.passport.biz.domain.entity.SysUser;
import com.yhd.admin.passport.biz.domain.query.UserParam;

/**
 * <AUTHOR>
 * @version 1.0.0 @ClassName UserAccountServiceImpl.java @Description TODO
 * @createTime 2020年03月19日 20:26:00
 */
@Service
public class UserSrvImpl extends ServiceImpl<UserDao, SysUser> implements UserSrv {

    private final UserConvert userConvert;

    private final RedisTemplate<String, UserDTO> userTemplate;

    public UserSrvImpl(UserConvert userConvert, RedisTemplate<String, UserDTO> userTemplate) {
        this.userConvert = userConvert;
        this.userTemplate = userTemplate;
    }

    @Override
    public UserDTO getUser(UserParam param) {
        String accountKey = String.format(RedisKeyEnum.USER_ACCOUNT.getKey(), param.getUserName());
        return Optional.ofNullable(userTemplate.opsForValue().get(accountKey)).orElseGet(() -> {
            LambdaQueryWrapper<SysUser> accountQueryWrapper = new QueryWrapper<SysUser>().lambda()
                .eq(StringUtils.isNotBlank(param.getUserName()), SysUser::getUsername, param.getUserName());
            UserDTO account = userConvert.toDTO(getOne(accountQueryWrapper));
            userTemplate.opsForValue().set(accountKey, account, 1, TimeUnit.DAYS);
            return account;
        });
    }

    /**
     * @param param
     * @return
     */
    @Override
    public Boolean updateUserAccount(UserParam param) {
        String accountKey = String.format(RedisKeyEnum.USER_ACCOUNT.getKey(), param.getUserName());
        LambdaUpdateChainWrapper<SysUser> updateChainWrapper =
            new LambdaUpdateChainWrapper<>(baseMapper);
        updateChainWrapper
            .eq(SysUser::getUsername, param.getUserName())
            .set(SysUser::getIsAccountNonLocked, param.getIsAccountNonLocked());
        userTemplate.delete(accountKey);
        return updateChainWrapper.update();
    }
}
