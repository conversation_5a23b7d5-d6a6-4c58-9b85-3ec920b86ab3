package com.yhd.admin.passport.security;

import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.session.HttpSessionEventPublisher;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.session.FindByIndexNameSessionRepository;
import org.springframework.session.Session;
import org.springframework.session.security.SpringSessionBackedSessionRegistry;
import org.springframework.session.security.web.authentication.SpringSessionRememberMeServices;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import com.yhd.admin.passport.listener.EnhancedHttpSessionEventPublisher;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
public class DefaultSecurityConfiguration<S extends Session> {

    @Resource
    FindByIndexNameSessionRepository<S> sessionRepository;
    @Resource
    AdminInvalidSessionStrategy invalidSessionStrategy;
    @Resource
    AdminSessionInformationExpiredStrategy sessionExpiredStrategy;

    @Bean
    public PasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        final CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(List.of("*"));
        configuration.setAllowedMethods(List.of("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(List.of("*"));
        configuration.setAllowedOriginPatterns(List.of("*"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);
        final UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    @Bean
    @Order(2)
    public SecurityFilterChain defaultSecurityFilterChain(HttpSecurity http) throws Exception {
        // @formatter:off
        http.sessionManagement(session -> session
            .sessionConcurrency((concurrency) -> concurrency
                .maximumSessions(1)
                .maxSessionsPreventsLogin(false)
                .sessionRegistry(sessionRegistry())
                .expiredSessionStrategy(sessionExpiredStrategy))
            .invalidSessionUrl("/web/login")
                .sessionAuthenticationFailureHandler(new EnhancedAuthenticationFailureHandler())
                .sessionCreationPolicy(SessionCreationPolicy.NEVER))
            .rememberMe(rme -> rme.rememberMeServices(rememberMeServices()))
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(AbstractHttpConfigurer::disable)
            .authorizeHttpRequests(authorize -> authorize
                .requestMatchers(
                    new AntPathRequestMatcher("/web/login"),
                    new AntPathRequestMatcher("/api/login"),
                    new AntPathRequestMatcher("/api/open/**"),
                    new AntPathRequestMatcher("/loginFailure"),
                    new AntPathRequestMatcher("/css/**"),
                    new AntPathRequestMatcher("/js/**"),
                    new AntPathRequestMatcher("/img/**"),
                    new AntPathRequestMatcher("/font/**"))
                .permitAll()
                .anyRequest().authenticated())
            .formLogin(form -> form.loginPage("/web/login")
                .permitAll()
                .loginProcessingUrl("/api/login").permitAll()
                .successHandler(new EnhancedAuthenticationSuccessHandler())
                .failureHandler(new EnhancedAuthenticationFailureHandler())
                .passwordParameter("password").failureHandler(new PasswordFailureHandler()))
            .logout(logout -> logout.deleteCookies("SESSION", "JSESSIONID")
                .logoutUrl("/logout").permitAll()
                .logoutRequestMatcher(new AntPathRequestMatcher("/logout", "GET")));
        // @formatter:off
        return http.build();
    }

    @Bean
    public SpringSessionRememberMeServices rememberMeServices() {
        SpringSessionRememberMeServices rememberMeServices = new SpringSessionRememberMeServices();
        // optionally customize
        rememberMeServices.setAlwaysRemember(true);
        rememberMeServices.setValiditySeconds(43200);
        return rememberMeServices;
    }

    @Bean
    public SpringSessionBackedSessionRegistry<S> sessionRegistry() {
        return new SpringSessionBackedSessionRegistry<>(this.sessionRepository);
    }

    @Bean
    public HttpSessionEventPublisher httpSessionEventPublisher() {
        return new EnhancedHttpSessionEventPublisher();
    }
}
