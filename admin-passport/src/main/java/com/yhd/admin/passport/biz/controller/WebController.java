package com.yhd.admin.passport.biz.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.passport.biz.domain.convert.AppConvert;
import com.yhd.admin.passport.biz.service.AppSrv;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/9/6
 */
@Controller
@RequestMapping("/web")
public class WebController {

    @Resource
    private AppSrv appSrv;

    @Resource
    private AppConvert convert;

    @RequestMapping(value = "/getAppUrl")
    @ResponseBody
    public RespJson index() {
        return RespJson.success(convert.toVO(appSrv.queryAll()));
    }

    @RequestMapping(value = "/app", method = {RequestMethod.GET, RequestMethod.POST})
    String loginSuccess() {
        return "App";
    }
}
