package com.yhd.admin.passport.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.passport.biz.domain.dto.UserDTO;
import com.yhd.admin.passport.biz.domain.entity.SysUser;
import com.yhd.admin.passport.biz.domain.query.UserParam;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UserAccountService.java
 * @Description TODO 登录账户业务操作服务类
 * @createTime 2020年03月19日 20:25:00
 */
public interface UserSrv extends IService<SysUser> {

    /**
     * 查询用户账户
     *
     * @param param {@link UserParam}
     * @return {@link UserDTO}
     */
    UserDTO getUser(UserParam param);

    Boolean updateUserAccount(UserParam param);
}
