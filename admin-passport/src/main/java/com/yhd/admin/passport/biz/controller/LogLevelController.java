package com.yhd.admin.passport.biz.controller;

import java.util.Arrays;
import java.util.Objects;

import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yhd.admin.common.domain.RespJson;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/log/level")
public class LogLevelController {
    LoggerContext loggerContext = (LoggerContext)LoggerFactory.getILoggerFactory();

    final String[] levels = {"OFF", "FATAL", "ERROR", "WARN", "INFO", "DEBUG", "TRACE", "ALL"};

    @GetMapping("/{level}")
    public RespJson changeLevel(@PathVariable String level) {
        return RespJson.success(setLogger("root", level));
    }

    /**
     * 修改 指定包 日志级别
     *
     * @param level 日志级别
     * @return Result 返回结果 栗子: curl [baseUrl]/sys/log/level/info/[包名] curl [baseUrl]/sys/log/level/debug/[包名]
     */
    @GetMapping("/{level}/{packageName}")
    public RespJson changeLevel(@PathVariable String level, @PathVariable String packageName) {
        return RespJson.success(setLogger(packageName, level));
    }

    // --------------------------------------依赖方法------------------------------

    /**
     * 获取指定包日志级别 封装[设置日志级别+封装返回值信息]
     *
     * @param packageName 包名
     * @return String 日志级别信息
     */
    private String getLogger(String packageName) {
        return packageName + "日志等级为:" + getLevel(packageName);
    }

    /**
     * 设置指定包日志级别 封装[日志级别检测+设置日志级别+封装返回值信息]
     *
     * @param packageName 包名
     * @return String 日志级别信息
     */
    private String setLogger(String packageName, String level) {
        boolean isAllowed = isAllowed(level);
        if (isAllowed) {
            setLevel(packageName, level);
        }
        return isAllowed ? packageName + "日志等级更改为:" + level : packageName + "日志等级修改失败,可用值[ERROR,WARN,INFO,DEBUG,TRACE]";
    }

    /**
     * 获取制定包的日志级别
     *
     * @param packageName 包名
     * @return String 日志级别
     */
    private String getLevel(String packageName) {
        Logger logger = loggerContext.getLogger(packageName);
        // ArrayUtil.hasNull(logger,logger.getLevel());//依赖Hutool工具
        return hasNull(logger, logger.getLevel()) ? "" : logger.getLevel().toString();
    }

    /**
     * 设置制定包的日志级别
     *
     * @param packageName 包名
     * @param level 日志等级
     */
    private void setLevel(String packageName, String level) {
        loggerContext.getLogger(packageName).setLevel(Level.toLevel(level));
    }

    /**
     * 判断是否是合法的日志级别
     *
     * @param level 日志等级
     * @return boolean
     */
    private boolean isAllowed(String level) {
        return Arrays.asList(levels).contains(level.toUpperCase());
    }

    /**
     * 判断多个对象中是否包含空对象
     *
     * @param objects 多个对象
     * @return String 日志级别
     */
    private boolean hasNull(Object... objects) {
        if (Objects.nonNull(objects)) {
            for (Object element : objects) {
                if (null == element) {
                    return true;
                }
            }
        }
        return false;
    }
}
