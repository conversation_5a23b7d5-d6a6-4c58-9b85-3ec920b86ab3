package com.yhd.admin.passport.biz.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yhd.admin.passport.biz.domain.dto.RoleDTO;
import com.yhd.admin.passport.biz.domain.entity.SysRole;
import com.yhd.admin.passport.biz.domain.query.UserParam;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RoleService.java
 * @Description TODO 账户角色业务类
 * @createTime 2020年03月20日 10:46:00
 */
public interface RoleSrv extends IService<SysRole> {

    /**
     * 根据用户查询对应的用户角色
     *
     * @param param {@link UserParam}
     * @return {@link List< RoleDTO >}
     */
    List<RoleDTO> queryRole(UserParam param);

    /**
     * 根据角色查询授权的菜单权限。
     *
     * @param roleList 角色 ID
     * @return 菜单权限集合
     */
    List<String> getAuthority(String clientId, List<Long> roleList);
}
