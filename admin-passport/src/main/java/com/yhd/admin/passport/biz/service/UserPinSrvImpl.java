package com.yhd.admin.passport.biz.service;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yhd.admin.passport.biz.dao.UserPinDao;
import com.yhd.admin.passport.biz.domain.dto.UserPinDTO;
import com.yhd.admin.passport.biz.domain.entity.SysUserPin;

@Service
public class UserPinSrvImpl extends ServiceImpl<UserPinDao, SysUserPin> implements UserPinSrv {
    @Override
    public UserPinDTO getUserPin(String uid, String username) {
        String id = DigestUtils.md5Hex(uid + username).toUpperCase();
        SysUserPin sysUserPin = this.getById(id);
        return new UserPinDTO(sysUserPin.getUid(), sysUserPin.getPassword());
    }
}
