package com.yhd.admin.passport.biz.controller;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yhd.admin.common.domain.RespJson;
import com.yhd.admin.common.domain.vo.TimeVO;

@RestController
@RequestMapping("/api/open")
public class TimerController {

    @RequestMapping(value = "/currentTime", method = {RequestMethod.GET, RequestMethod.POST})
    public RespJson getCurrentDateTime() {
        return RespJson.success(new TimeVO(LocalDate.now(), LocalDateTime.now(), LocalTime.now(),
            LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"))));
    }
}
