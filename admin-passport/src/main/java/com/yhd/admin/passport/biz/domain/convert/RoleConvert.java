package com.yhd.admin.passport.biz.domain.convert;

import java.util.List;

import org.mapstruct.Mapper;

import com.yhd.admin.passport.biz.domain.dto.RoleDTO;
import com.yhd.admin.passport.biz.domain.entity.SysRole;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RoleConvert.java
 * @Description SysRole 转换成RoleDTO
 * @createTime 2020年03月20日 10:14:00
 */
@Mapper(componentModel = "spring")
public interface RoleConvert {

    RoleDTO toDTO(SysRole role);

    List<RoleDTO> toDTO(List<SysRole> role);

}
