package com.yhd.admin.passport.biz.domain.dto;

import java.util.Set;

import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/25
 */
@Data
public class ClientDTO {
    private String id;
    private String password;
    private Set<ClientAuthenticationMethod> clientAuthenticationMethodSet;
    private Set<AuthorizationGrantType> AuthorizationGrantType;
    private Set<String> redirectUriSet;

}
