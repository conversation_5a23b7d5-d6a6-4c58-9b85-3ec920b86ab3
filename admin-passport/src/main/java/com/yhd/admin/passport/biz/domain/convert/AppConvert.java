package com.yhd.admin.passport.biz.domain.convert;

import java.util.List;

import com.yhd.admin.passport.biz.domain.dto.AppDTO;
import com.yhd.admin.passport.biz.domain.entity.SysApp;
import com.yhd.admin.passport.biz.domain.vo.AppVO;
import org.mapstruct.Mapper;

import com.yhd.admin.passport.biz.domain.dto.RoleDTO;
import com.yhd.admin.passport.biz.domain.entity.SysRole;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RoleConvert.java
 * @Description SysRole 转换成RoleDTO
 * @createTime 2020年03月20日 10:14:00
 */
@Mapper(componentModel = "spring")
public interface AppConvert {

    AppDTO toDTO(SysApp sysApp);

    List<AppDTO> toDTO(List<SysApp> apps);

    AppVO toVO(AppDTO sysApp);

    List<AppVO> toVO(List<AppDTO> apps);

}
