package com.yhd.admin.passport.biz.domain.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;

/** <AUTHOR> */
@Data
public class SysUserRole implements Cloneable, Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 账户ID */
    private String accountId;
    /** 角色 */
    private Long roleId;
}
