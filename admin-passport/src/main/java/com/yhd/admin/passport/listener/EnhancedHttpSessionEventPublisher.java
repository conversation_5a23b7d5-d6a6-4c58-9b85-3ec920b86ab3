package com.yhd.admin.passport.listener;

import org.springframework.security.web.session.HttpSessionEventPublisher;

import jakarta.servlet.http.HttpSessionEvent;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EnhancedHttpSessionEventPublisher extends HttpSessionEventPublisher {

    @Override
    public void sessionCreated(HttpSessionEvent event) {
        if (log.isDebugEnabled()) {
            log.debug("用户上线: {}", event.getSession());
        }
        super.sessionCreated(event);
    }

    @Override
    public void sessionDestroyed(HttpSessionEvent event) {
        if (log.isDebugEnabled()) {
            log.debug("用户下线: {}", event.getSession());
        }
        super.sessionDestroyed(event);
    }

    @Override
    public void sessionIdChanged(HttpSessionEvent event, String oldSessionId) {
        super.sessionIdChanged(event, oldSessionId);
    }
}
