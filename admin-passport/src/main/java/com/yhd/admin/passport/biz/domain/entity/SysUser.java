package com.yhd.admin.passport.biz.domain.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("tb_sys_user")
public class SysUser implements Serializable, Cloneable {

    /** user id */
    @TableId(type = IdType.ASSIGN_ID)
    private String uid;

    /** 登录名称 */
    private String username;
    /** 账户未过期 */
    private Boolean isAccountNonExpired;
    /** 账户未锁定 */
    private Boolean isAccountNonLocked;
    /** 密码未过期 */
    private Boolean isCredentialsNonExpired;
    /** 账户状态 */
    private Boolean isEnable;

    /** 超级管理员 */
    private Boolean isAdmin;
}
