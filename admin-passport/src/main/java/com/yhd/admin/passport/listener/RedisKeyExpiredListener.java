package com.yhd.admin.passport.listener;

import com.yhd.admin.passport.biz.domain.query.UserParam;
import com.yhd.admin.passport.biz.service.UserSrv;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;



@Component
@Slf4j
public class RedisKeyExpiredListener extends KeyExpirationEventMessageListener {

  @Resource
  private UserSrv userSrv;
  /**
   * Creates new {@link } for {@code __keyevent@*__:expired} messages.
   *
   * @param listenerContainer must not be {@literal null}.
   */
  public RedisKeyExpiredListener(RedisMessageListenerContainer listenerContainer) {
    super(listenerContainer);
  }

  /**
   * @param message message must not be {@literal null}.
   * @param pattern pattern matching the channel (if specified) - can be {@literal null}.
   */
  @Override
  public void onMessage(Message message, byte[] pattern) {
    super.onMessage(message, pattern);
    String kry = message.toString();
    if (StringUtils.startsWith(kry, "PASSPORT:USER:PASSWD")) {
      String usernname = StringUtils.substringAfterLast(kry, ":");
      UserParam param = new UserParam();
      param.setUserName(usernname);
      param.setIsAccountNonLocked(Boolean.TRUE);
      userSrv.updateUserAccount(param);
    }
    log.debug(">>>> {}", message.toString());
  }
}
