package com.yhd.admin.passport.biz.domain.dto;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UserDTO implements Serializable, Cloneable {

    private String uid;
    /** 登录名称 */
    private String username;
    /** 账户未过期 */
    private Boolean isAccountNonExpired;
    /** 账户未锁定 */
    private Boolean isAccountNonLocked;
    /** 密码未过期 */
    private Boolean isCredentialsNonExpired;
    /** 账户状态 */
    private Boolean isEnable;

    /** 超级管理员 */
    private Boolean isAdmin;
}
