-- 安全决策大屏模块数据库表结构
-- 创建时间：2025-08-01
--
-- 注意：实体类命名规范
-- 数据库表名：tb_mk_xxx_xxx (带tb_mk前缀)
-- Java实体类名：MkXxxXxx (去掉tb前缀，驼峰命名)
--
-- 示例：
-- 表名：tb_mk_hazard_stats
-- 实体类：MkHazardStats
--

-- 1. 隐患统计表
CREATE TABLE `tb_mk_hazard_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '安全决策主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '安全决策统计编码',
    `stats_date` date NOT NULL COMMENT '安全决策统计日期',
    `unrectified_count` int DEFAULT NULL COMMENT '安全决策未整改数量',
    `rectified_unverified_count` int DEFAULT NULL COMMENT '安全决策已整改未验收数量',
    `verified_unaccepted_count` int DEFAULT NULL COMMENT '安全决策已验收收回数量',
    `verified_passed_count` int DEFAULT NULL COMMENT '安全决策已验通过数量',
    `total_count` int DEFAULT NULL COMMENT '安全决策隐患总数',
    `org_code` varchar(50) DEFAULT NULL COMMENT '安全决策组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '安全决策状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '安全决策创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '安全决策更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '安全决策创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '安全决策更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='安全决策隐患统计表';

-- 2. 三违统计表
CREATE TABLE `tb_mk_violation_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '安全决策主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '安全决策统计编码',
    `stats_date` date NOT NULL COMMENT '安全决策统计日期',
    `daily_violation_count` int DEFAULT NULL COMMENT '安全决策当日三违数量',
    `trend_data` json DEFAULT NULL COMMENT '安全决策趋势数据(近7日)',
    `violation_type_breakdown` json DEFAULT NULL COMMENT '安全决策三违类型分解',
    `org_code` varchar(50) DEFAULT NULL COMMENT '安全决策组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '安全决策状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '安全决策创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '安全决策更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '安全决策创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '安全决策更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='安全决策三违统计表';

-- 3. 报警统计表
CREATE TABLE `tb_mk_alarm_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '安全决策主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '安全决策统计编码',
    `stats_date` date NOT NULL COMMENT '安全决策统计日期',
    `yesterday_count` int DEFAULT NULL COMMENT '安全决策昨日报警数量',
    `today_count` int DEFAULT NULL COMMENT '安全决策今日报警数量',
    `growth_rate` decimal(5,2) DEFAULT NULL COMMENT '安全决策环比增长率(%)',
    `trend_data` json DEFAULT NULL COMMENT '安全决策趋势数据',
    `alarm_type_breakdown` json DEFAULT NULL COMMENT '安全决策报警类型分解',
    `org_code` varchar(50) DEFAULT NULL COMMENT '安全决策组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '安全决策状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '安全决策创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '安全决策更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '安全决策创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '安全决策更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='安全决策报警统计表';

-- 4. 风险统计表（重用之前的risk_control_stats，但增加更详细的分级）
CREATE TABLE `tb_mk_risk_detailed_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '安全决策主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '安全决策统计编码',
    `stats_date` date NOT NULL COMMENT '安全决策统计日期',
    `major_risk_count` int DEFAULT NULL COMMENT '安全决策重大风险数量',
    `significant_risk_count` int DEFAULT NULL COMMENT '安全决策较大风险数量',
    `general_risk_count` int DEFAULT NULL COMMENT '安全决策一般风险数量',
    `low_risk_count` int DEFAULT NULL COMMENT '安全决策低风险数量',
    `general_a_level_count` int DEFAULT NULL COMMENT '安全决策一般A级数量',
    `general_b_level_count` int DEFAULT NULL COMMENT '安全决策一般B级数量',
    `general_c_level_count` int DEFAULT NULL COMMENT '安全决策一般C级数量',
    `total_risk_count` int DEFAULT NULL COMMENT '安全决策风险总数',
    `org_code` varchar(50) DEFAULT NULL COMMENT '安全决策组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '安全决策状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '安全决策创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '安全决策更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '安全决策创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '安全决策更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_code` (`stats_code`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='安全决策风险详细统计表';

-- 5. 风险管控清单表
CREATE TABLE `tb_mk_risk_management_list` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '安全决策主键ID',
    `item_code` varchar(50) NOT NULL COMMENT '安全决策清单编码',
    `risk_description` text DEFAULT NULL COMMENT '安全决策风险描述',
    `risk_level` varchar(20) DEFAULT NULL COMMENT '安全决策风险等级',
    `risk_level_color` varchar(20) DEFAULT NULL COMMENT '安全决策风险等级颜色',
    `responsible_person` varchar(100) DEFAULT NULL COMMENT '安全决策责任人',
    `department` varchar(100) DEFAULT NULL COMMENT '安全决策部门',
    `control_measures` text DEFAULT NULL COMMENT '安全决策管控措施',
    `due_date` date DEFAULT NULL COMMENT '安全决策完成期限',
    `completion_status` varchar(20) DEFAULT NULL COMMENT '安全决策完成状态',
    `entry_time` datetime DEFAULT NULL COMMENT '安全决策录入时间',
    `org_code` varchar(50) DEFAULT NULL COMMENT '安全决策组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '安全决策状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '安全决策创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '安全决策更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '安全决策创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '安全决策更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_item_code` (`item_code`),
    KEY `idx_risk_level` (`risk_level`),
    KEY `idx_entry_time` (`entry_time`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='安全决策风险管控清单表';

-- 6. 专业分析统计表
CREATE TABLE `tb_mk_professional_analysis_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '安全决策主键ID',
    `stats_code` varchar(50) NOT NULL COMMENT '安全决策统计编码',
    `stats_date` date NOT NULL COMMENT '安全决策统计日期',
    `professional_type` varchar(100) DEFAULT NULL COMMENT '安全决策专业类型',
    `hazard_count` int DEFAULT NULL COMMENT '安全决策隐患数量',
    `peak_analysis_data` json DEFAULT NULL COMMENT '安全决策高峰分析数据',
    `trend_data` json DEFAULT NULL COMMENT '安全决策趋势数据',
    `org_code` varchar(50) DEFAULT NULL COMMENT '安全决策组织机构编码',
    `status` tinyint(1) DEFAULT 1 COMMENT '安全决策状态：0-禁用，1-启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '安全决策创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '安全决策更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '安全决策创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '安全决策更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stats_professional` (`stats_code`, `professional_type`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_professional_type` (`professional_type`),
    KEY `idx_org_code` (`org_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='安全决策专业分析统计表';

-- 插入示例数据

-- 隐患统计示例数据
INSERT INTO `tb_mk_hazard_stats` (`stats_code`, `stats_date`, `unrectified_count`, `rectified_unverified_count`, `verified_unaccepted_count`, `verified_passed_count`, `total_count`, `org_code`) VALUES
('HAZARD001', '2025-08-01', 0, 3, 0, 27, 30, 'LJTKYJGS');

-- 三违统计示例数据
INSERT INTO `tb_mk_violation_stats` (`stats_code`, `stats_date`, `daily_violation_count`, `trend_data`, `org_code`) VALUES
('VIOLATION001', '2025-08-01', 0, '{"05-15": 0, "05-17": 0, "05-19": 0, "05-21": 2, "05-23": 1}', 'LJTKYJGS');

-- 报警统计示例数据
INSERT INTO `tb_mk_alarm_stats` (`stats_code`, `stats_date`, `yesterday_count`, `today_count`, `growth_rate`, `trend_data`, `org_code`) VALUES
('ALARM001', '2025-08-01', 2, 3, 50.00, '{"hours": [0.2, 0.4, 0.6, 0.8, 0.6, 0.4, 0.2]}', 'LJTKYJGS');

-- 风险详细统计示例数据
INSERT INTO `tb_mk_risk_detailed_stats` (`stats_code`, `stats_date`, `major_risk_count`, `significant_risk_count`, `general_risk_count`, `low_risk_count`, `general_a_level_count`, `general_b_level_count`, `general_c_level_count`, `total_risk_count`, `org_code`) VALUES
('RISK001', '2025-08-01', 0, 0, 1, 2, 18, 12, 0, 3, 'LJTKYJGS');

-- 风险管控清单示例数据
INSERT INTO `tb_mk_risk_management_list` (`item_code`, `risk_description`, `risk_level`, `risk_level_color`, `responsible_person`, `department`, `entry_time`, `org_code`) VALUES
('RISK_ITEM001', '高空作业安全动态管控作业人', '低风险', '#45B7D1', '张三', '安全部', '2025-05-14 17:42:38', 'LJTKYJGS'),
('RISK_ITEM002', '作业前，未对接帮顶行程等', '一般风险', '#FECA57', '李四', '生产部', '2025-05-14 17:40:14', 'LJTKYJGS'),
('RISK_ITEM003', '距离工作面新的基层岗位交', '低风险', '#45B7D1', '王五', '技术部', '2025-05-14 17:38:22', 'LJTKYJGS');

-- 专业分析统计示例数据
INSERT INTO `tb_mk_professional_analysis_stats` (`stats_code`, `stats_date`, `professional_type`, `hazard_count`, `peak_analysis_data`, `org_code`) VALUES
('PROF001', '2025-08-01', '通风专业', 20, '{"peak_time": "14:00-16:00", "peak_count": 5}', 'LJTKYJGS'),
('PROF002', '2025-08-01', '机电专业', 15, '{"peak_time": "10:00-12:00", "peak_count": 3}', 'LJTKYJGS'),
('PROF003', '2025-08-01', '运输专业', 10, '{"peak_time": "08:00-10:00", "peak_count": 2}', 'LJTKYJGS');

-- 创建索引
CREATE INDEX idx_hazard_date ON tb_mk_hazard_stats(stats_date);
CREATE INDEX idx_violation_date ON tb_mk_violation_stats(stats_date);
CREATE INDEX idx_alarm_date ON tb_mk_alarm_stats(stats_date);
CREATE INDEX idx_risk_detailed_date ON tb_mk_risk_detailed_stats(stats_date);
CREATE INDEX idx_risk_mgmt_entry_time ON tb_mk_risk_management_list(entry_time);
CREATE INDEX idx_professional_date ON tb_mk_professional_analysis_stats(stats_date);
