-- 为应急救援设备信息表添加实时值相关字段
ALTER TABLE `tb_mk_emergency_device` 
ADD COLUMN `current_value` decimal(10,2) DEFAULT NULL COMMENT '当前实时值' AFTER `org_code`,
ADD COLUMN `threshold_min` decimal(10,2) DEFAULT NULL COMMENT '最小阈值' AFTER `current_value`,
ADD COLUMN `threshold_max` decimal(10,2) DEFAULT NULL COMMENT '最大阈值' AFTER `threshold_min`,
ADD COLUMN `unit` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '测量单位' AFTER `threshold_max`,
ADD COLUMN `last_update_time` datetime DEFAULT NULL COMMENT '最后更新时间' AFTER `unit`;

-- 添加索引
ALTER TABLE `tb_mk_emergency_device` 
ADD INDEX `idx_current_value` (`current_value`) COMMENT '实时值索引',
ADD INDEX `idx_last_update_time` (`last_update_time`) COMMENT '最后更新时间索引'; 