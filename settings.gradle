pluginManagement {
    resolutionStrategy {}
    repositories {
        mavenLocal()
        maven { url "https://maven.aliyun.com/nexus/content/groups/public" }
        maven { url 'https://repo.spring.io/milestone' }
        maven { url 'https://repo.spring.io/snapshot' }
        maven { url 'https://oss.sonatype.org/content/repositories/snapshots/' }
        mavenCentral()
        gradlePluginPortal()
    }
}

rootProject.name = 'admin-framework'
include 'admin-passport'
include 'admin-api'
include 'admin-common'
include 'admin-scada'
include 'admin-scheduling'
