# 应急救援监测信息表(传感器表) - API使用说明

## 🔧 获取传感器树形结构接口

### 接口信息
- **请求方式**: POST
- **接口路径**: `/emergency/sensor/getSensorTree`
- **Content-Type**: `application/json`

### 请求参数 (MkEmergencySensorTreeParam)

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orgCode | String | 否 | 组织编码 |
| parentId | Integer | 否 | 父级ID，默认为0（根目录） |
| sensorName | String | 否 | 传感器名称（模糊查询） |
| sensorType | String | 否 | 传感器类型 |
| levelType | String | 否 | 层级类型 |
| onlyLeaf | Boolean | 否 | 是否只查询叶子节点 |
| status | Integer | 否 | 传感器状态 |

### 使用场景

#### 1. 查询全部数据
```json
POST /emergency/sensor/getSensorTree
{}
```
或者不传请求体

#### 2. 查询指定组织的根节点
```json
{
  "orgCode": "ORG001"
}
```

#### 3. 查询指定节点的子树
```json
{
  "orgCode": "ORG001",
  "parentId": 2
}
```

#### 4. 根据传感器名称搜索
```json
{
  "orgCode": "ORG001",
  "sensorName": "氧气"
}
```
**说明**: 当传入`sensorName`时，系统会进行全表搜索，并构建包含搜索结果及其完整父节点路径的树形结构。

#### 5. 组合条件查询
```json
{
  "orgCode": "ORG001",
  "sensorName": "传感器",
  "sensorType": "氧气传感器",
  "levelType": "point",
  "status": 1,
  "onlyLeaf": true
}
```

#### 6. 只查询叶子节点（实际传感器设备）
```json
{
  "orgCode": "ORG001",
  "onlyLeaf": true
}
```

### 响应结果

```json
{
  "status": "200",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "sensorCode": "MINE_ROOT",
      "sensorName": "煤矿总监控",
      "sensorType": "监控中心",
      "location": "地面监控中心",
      "parentId": 0,
      "levelType": "mine",
      "levelCode": "MINE001",
      "levelName": "煤矿总监控中心",
      "levelPath": "/mine",
      "isLeaf": 0,
      "status": 1,
      "statusText": "正常",
      "levelTypeText": "矿井",
      "children": [
        {
          "id": 2,
          "sensorCode": "AREA_1302",
          "sensorName": "1302采区监控",
          "parentId": 1,
          "levelType": "area",
          "isLeaf": 0,
          "children": [...]
        }
      ]
    }
  ],
  "timestamp": 1690780800
}
```

## 🏗️ 父子级关系说明

### 层级结构
```
根节点 (parent_id = 0)
├── 1302采区 (parent_id = 根节点ID)
│   └── 1302工作面 (parent_id = 1302采区ID)
│       ├── 氧气传感器 (parent_id = 1302工作面ID)
│       └── 甲烷传感器 (parent_id = 1302工作面ID)
└── 1308采区 (parent_id = 根节点ID)
    └── 1308工作面 (parent_id = 1308采区ID)
        └── 湿度传感器 (parent_id = 1308工作面ID)
```

### 层级类型说明
- **mine**: 矿井级别（根节点）
- **area**: 采区级别
- **workface**: 工作面级别
- **tunnel**: 巷道级别
- **point**: 监测点级别（实际传感器设备）

## 🔍 查询逻辑说明

### 1. 普通层级查询
- 当不传`sensorName`时，按照`parentId`进行层级查询
- 递归构建子节点的完整树形结构

### 2. 名称搜索查询
- 当传入`sensorName`时，进行全表模糊搜索
- 自动构建包含搜索结果的完整树形结构
- 包含搜索结果节点的所有父节点路径

### 3. 条件过滤
- 支持多种条件组合过滤
- `onlyLeaf=true`时只返回叶子节点（实际传感器设备）
- 支持按状态、类型等条件过滤

## 💡 使用建议

1. **首次加载**: 不传参数或只传`orgCode`，获取完整树形结构
2. **搜索功能**: 传入`sensorName`进行模糊搜索
3. **懒加载**: 传入`parentId`按需加载子节点
4. **设备列表**: 使用`onlyLeaf=true`获取所有传感器设备
5. **状态监控**: 结合`status`参数查询异常设备

## ⚠️ 注意事项

- 接口已改为POST请求，支持复杂查询条件
- `parent_id = 0` 表示根目录
- 搜索时会自动构建完整的父节点路径
- 支持不传参数查询全部数据
