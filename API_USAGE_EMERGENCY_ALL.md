# 应急救援模块统一API文档

## 模块说明

应急救援管理系统API文档，包含应急预案、设备管理、周边信息、快照管理、专家库、物资管理、历史事件、演练记录、通讯录、传感器监测等功能模块。

## 目录

- [0. 应急救援中心总览](#0-应急救援中心总览)
- [0.5. 综合决策大屏](#05-综合决策大屏)
- [1. 应急预案分类管理](#1-应急预案分类管理)
- [2. 应急预案管理](#2-应急预案管理)
- [3. 应急设备管理](#3-应急设备管理)
- [4. 周边信息管理](#4-周边信息管理)
- [5. 矿井快照管理](#5-矿井快照管理)
- [6. 快照信息管理](#6-快照信息管理)
- [7. 专家库管理](#7-专家库管理)
- [8. 应急物资管理](#8-应急物资管理)
- [9. 应急历史事件](#9-应急历史事件)
- [10. 应急演练管理](#10-应急演练管理)
- [11. 应急通讯录](#11-应急通讯录)
- [12. 传感器监测](#12-传感器监测)

---

## 0. 应急救援中心总览

**模块路径：** `/emergency/overview`

### 0.1 获取应急救援中心总览数据

**接口地址：** `POST /emergency/overview/getOverviewData`

**请求参数：** （可选）

```json
{
    "orgCode": "LJTKYJGS",
    "statsDate": "2025-01-20",
    "summaryDate": "2025-01-20",
    "onlyUnderground": true,
    "onlyActive": true
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": {
        "leaders": [
            {
                "leaderCode": "LEADER001",
                "leaderName": "张总经理",
                "position": "总经理",
                "department": "管理层",
                "phone": "13812345678",
                "avatarUrl": null,
                "isUnderground": true,
                "isUndergroundName": "已下井",
                "entryTime": "2025-01-20 08:30:00",
                "currentLocation": "1301工作面"
            }
        ],
        "personnelStats": {
            "totalCount": 193,
            "teamStats": [
                {
                    "categoryName": "搬家公司",
                    "personnelCount": 25,
                    "percentage": "12.95%",
                    "colorCode": "#FF8C00",
                    "sortOrder": 1
                },
                {
                    "categoryName": "机电队",
                    "personnelCount": 21,
                    "percentage": "10.88%",
                    "colorCode": "#FF8C00",
                    "sortOrder": 2
                }
            ],
            "areaStats": [
                {
                    "categoryName": "1301工作面",
                    "personnelCount": 35,
                    "percentage": "18.13%",
                    "colorCode": "#FF6B6B",
                    "sortOrder": 1
                }
            ]
        },
        "safetySummary": {
            "summaryDate": "2025-01-20",
            "summaryTitle": "应急演练总结",
            "summaryContent": "今日进行了火灾应急演练...",
            "hasContent": true,
            "warningLevel": 2,
            "warningLevelName": "警告",
            "warningMessage": "发现多项问题需要整改",
            "responsiblePerson": "李主管",
            "department": "安全部"
        },
        "workfaces": [
            {
                "workfaceCode": "WF1302",
                "workfaceName": "1302工作面采空区",
                "workfaceType": "采空区",
                "coordinateX": "100.00",
                "coordinateY": "200.00",
                "coordinateZ": "-300.00",
                "safetyStatus": 1,
                "safetyStatusName": "正常",
                "isActive": true
            }
        ],
        "navigations": [
            {
                "itemCode": "NAV001",
                "itemName": "人员",
                "navIcon": "icon-person",
                "navUrl": null,
                "isDefault": false,
                "displayOrder": 1
            },
            {
                "itemCode": "NAV003",
                "itemName": "安全监测",
                "navIcon": "icon-safety",
                "navUrl": null,
                "isDefault": true,
                "displayOrder": 3
            }
        ],
        "listItems": [
            {
                "itemCode": "FB005",
                "itemName": "FB005监测点",
                "itemType": "SAFETY",
                "itemStatus": 1,
                "itemStatusName": "正常",
                "itemValue": null,
                "itemUnit": null,
                "locationInfo": "1301工作面",
                "actionUrl": null,
                "iconClass": null,
                "colorCode": null,
                "displayOrder": 1
            }
        ]
    }
}
```

### 0.2 根据导航类型获取列表项目

**接口地址：** `POST /emergency/overview/getListItemsByNavType`

**请求参数：**

```json
{
    "navType": "SAFETY",
    "orgCode": "LJTKYJGS"
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": [
        {
            "itemCode": "FB005",
            "itemName": "FB005监测点",
            "itemType": "SAFETY",
            "itemStatus": 1,
            "itemStatusName": "正常",
            "itemValue": null,
            "itemUnit": null,
            "locationInfo": "1301工作面",
            "actionUrl": null,
            "iconClass": null,
            "colorCode": null,
            "displayOrder": 1
        }
    ]
}
```

### 0.3 获取功能导航列表

**接口地址：** `POST /emergency/overview/getNavigations`

**请求参数：** （可选）

```json
{
    "orgCode": "LJTKYJGS"
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": [
        {
            "itemCode": "NAV001",
            "itemName": "人员",
            "navIcon": "icon-person",
            "navUrl": null,
            "isDefault": false,
            "displayOrder": 1
        },
        {
            "itemCode": "NAV002",
            "itemName": "车辆",
            "navIcon": "icon-vehicle",
            "navUrl": null,
            "isDefault": false,
            "displayOrder": 2
        },
        {
            "itemCode": "NAV003",
            "itemName": "安全监测",
            "navIcon": "icon-safety",
            "navUrl": null,
            "isDefault": true,
            "displayOrder": 3
        }
    ]
}
```

**导航类型说明：**

- `NAV` - 导航模块
- `PERSON` - 人员
- `VEHICLE` - 车辆
- `SAFETY` - 安全监测
- `ENVIRONMENT` - 环境监测
- `WAREHOUSE` - 应急仓库

---

## 0.5. 综合决策大屏

**模块路径：** `/emergency/decision-screen`

### 0.5.1 获取综合决策大屏数据

**接口地址：** `POST /emergency/decision-screen/getDecisionScreenData`

**请求参数：** （可选）

```json
{
    "orgCode": "LJTKYJGS",
    "statsDate": "2025-01-20",
    "summaryDate": "2025-01-20",
    "onlyUnderground": true,
    "onlyActive": true
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": {
        "leaders": [
            {
                "leaderCode": "LEADER001",
                "leaderName": "张总经理",
                "position": "总经理",
                "department": "管理层",
                "phone": "13812345678",
                "avatarUrl": null,
                "isUnderground": true,
                "isUndergroundName": "已下井",
                "entryTime": "2025-01-20 08:30:00",
                "currentLocation": "1301工作面"
            }
        ],
        "personnelStats": {
            "totalCount": 193,
            "teamStats": [
                {
                    "categoryName": "搬家公司",
                    "personnelCount": 25,
                    "percentage": "12.95%",
                    "colorCode": "#FF8C00",
                    "sortOrder": 1
                },
                {
                    "categoryName": "机电队",
                    "personnelCount": 21,
                    "percentage": "10.88%",
                    "colorCode": "#FF8C00",
                    "sortOrder": 2
                }
            ],
            "areaStats": [
                {
                    "categoryName": "1301工作面",
                    "personnelCount": 35,
                    "percentage": "18.13%",
                    "colorCode": "#FF6B6B",
                    "sortOrder": 1
                }
            ]
        },
        "safetySummary": {
            "summaryDate": "2025-01-20",
            "summaryTitle": "应急演练总结",
            "summaryContent": "今日进行了火灾应急演练...",
            "hasContent": true,
            "warningLevel": 2,
            "warningLevelName": "警告",
            "warningMessage": "发现多项问题需要整改",
            "responsiblePerson": "李主管",
            "department": "安全部"
        },
        "workfaces": [
            {
                "workfaceCode": "WF1302",
                "workfaceName": "1302工作面采空区",
                "workfaceType": "采空区",
                "coordinateX": "100.00",
                "coordinateY": "200.00",
                "coordinateZ": "-300.00",
                "safetyStatus": 1,
                "safetyStatusName": "正常",
                "isActive": true
            }
        ],
        "navigations": [
            {
                "itemCode": "NAV001",
                "itemName": "人员",
                "navIcon": "icon-person",
                "navUrl": null,
                "isDefault": false,
                "displayOrder": 1
            },
            {
                "itemCode": "NAV003",
                "itemName": "安全监测",
                "navIcon": "icon-safety",
                "navUrl": null,
                "isDefault": true,
                "displayOrder": 3
            }
        ],
        "listItems": [
            {
                "itemCode": "FB005",
                "itemName": "FB005监测点",
                "itemType": "SAFETY",
                "itemStatus": 1,
                "itemStatusName": "正常",
                "itemValue": null,
                "itemUnit": null,
                "locationInfo": "1301工作面",
                "actionUrl": null,
                "iconClass": null,
                "colorCode": null,
                "displayOrder": 1
            }
        ]
    }
}
```

---

## 1. 应急预案分类管理

**模块路径：** `/emergency/planCategory`

### 1.1 获取树形分类列表

**接口地址：** `POST /emergency/planCategory/getCategoryTree`

**请求参数：** （可选）

```json
{
    "orgCode": "LJTKYJGS",
    "status": 1
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "categoryCode": "EMERGENCY_001",
            "categoryName": "自然灾害类",
            "parentId": 0,
            "categoryLevel": 1,
            "categoryPath": "1",
            "orgCode": "LJTKYJGS",
            "sortOrder": 1,
            "categoryIcon": "icon-disaster",
            "isLeaf": false,
            "status": 1,
            "statusName": "启用",
            "children": [
                ...
            ]
        }
    ]
}
```

### 1.2 根据父级ID获取子分类列表

**接口地址：** `POST /emergency/planCategory/getCategoriesByParentId`

**请求参数：**

```json
{
    "parentId": 1,
    "orgCode": "LJTKYJGS"
}
```

### 1.3 获取所有启用的分类列表

**接口地址：** `POST /emergency/planCategory/getEnabledCategories`

**请求参数：** （可选）

```json
{
    "orgCode": "LJTKYJGS",
    "categoryLevel": 1
}
```

---

## 2. 应急预案管理

**模块路径：** `/emergency/plan`

### 2.1 根据预案类型查询预案列表

**接口地址：** `POST /emergency/plan/getPlansByType`

**请求参数：**

```json
{
    "planType": "自然灾害",
    "orgCode": "LJTKYJGS",
    "planStatus": 3
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "planCode": "PLAN_001",
            "planName": "地质灾害应急预案",
            "categoryId": 2,
            "planType": "自然灾害",
            "planLevel": "I级",
            "planStatus": 3,
            "planStatusName": "已发布",
            "version": "v1.0",
            "responsiblePerson": "张三",
            "responsibleDepartment": "安全部"
        }
    ]
}
```

### 2.2 获取需要演练的预案列表

**接口地址：** `POST /emergency/plan/getNeedsDrillPlans`

**请求参数：**

```json
{
    "orgCode": "LJTKYJGS",
    "planStatus": 3
}
```

### 2.3 根据分类ID查询预案列表

**接口地址：** `POST /emergency/plan/getPlansByCategoryId`

**请求参数：**

```json
{
    "categoryId": 1,
    "orgCode": "LJTKYJGS",
    "planStatus": 3
}
```

---

## 3. 应急设备管理

**模块路径：** `/emergency/device`

### 3.1 获取设备实时值曲线数据

**接口地址：** `POST /emergency/device/getRealtimeChart`

**请求参数：**

```json
{
    "deviceCode": "DEV001",
    "orgCode": "LJTKYJGS",
    "timeRangeType": 3,
    "limitCount": 100
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": {
        "deviceCode": "DEV001",
        "deviceName": "矿井排风机001",
        "unit": "RPM",
        "currentValue": 1200.50,
        "thresholdMin": 800.00,
        "thresholdMax": 1500.00,
        "dataPoints": [
            {
                "time": "2024-01-20T08:00:00",
                "value": 1200.50,
                "status": 1,
                "statusName": "正常"
            }
        ]
    }
}
```

**时间范围类型说明：**

- `1` - 最近1小时
- `2` - 最近6小时
- `3` - 最近24小时
- `4` - 最近7天
- `5` - 自定义（需要传startTime和endTime）

---

## 4. 周边信息管理

**模块路径：** `/emergency/surrounding`

### 4.1 查询周边信息列表

**接口地址：** `POST /emergency/surrounding/queryList`

**请求参数：** （可选，不传参数查询全部）

```json
{
    "itemType": "人员",
    "orgCode": "LJTKYJGS",
    "status": 1,
    "location": "1301工作面"
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "itemCode": "PERSON001",
            "itemName": "张三",
            "itemType": "人员",
            "location": "1301工作面",
            "status": 1,
            "statusName": "正常",
            "orgCode": "LJTKYJGS",
            "coordinateX": 120.50,
            "coordinateY": 36.20,
            "coordinateZ": 100.00,
            "contactPerson": "张三",
            "contactPhone": "13812345678"
        }
    ]
}
```

**项目类型说明：**

- `人员` - 矿井作业人员
- `分站` - 监测分站
- `视频广播` - 视频监控设备
- `电话安监` - 应急通讯设备

---

## 5. 矿井快照管理

**模块路径：** `/emergency/snapshot`

### 5.1 根据快照类型查询快照列表

**接口地址：** `POST /emergency/snapshot/getSnapshotsByType`

**请求参数：**

```json
{
    "snapshotType": "EMERGENCY",
    "mineId": 1001,
    "snapshotStatus": "ACTIVE",
    "snapshotTimeStart": "2024-01-01T00:00:00",
    "snapshotTimeEnd": "2024-12-31T23:59:59"
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "snapshotName": "应急状态快照_20240120",
            "snapshotType": "EMERGENCY",
            "mineId": 1001,
            "mineName": "龙江煤矿",
            "snapshotTime": "2024-01-20T10:30:00",
            "snapshotStatus": "ACTIVE",
            "description": "应急演练期间的矿井状态快照",
            "filePath": "/snapshots/emergency/2024/01/20/snapshot_001.json",
            "fileSize": 2048576
        }
    ]
}
```

### 5.2 获取最新快照

**接口地址：** `POST /emergency/snapshot/getLatestSnapshot`

**请求参数：** （可选）

```json
{
    "mineId": 1001,
    "snapshotType": "EMERGENCY",
    "snapshotStatus": "ACTIVE"
}
```

**快照类型说明：**

- `EMERGENCY` - 应急快照
- `ROUTINE` - 日常快照
- `MAINTENANCE` - 维护快照
- `INSPECTION` - 检查快照
- `DRILL` - 演练快照

**快照状态说明：**

- `ACTIVE` - 有效
- `INACTIVE` - 无效
- `EXPIRED` - 已过期
- `PROCESSING` - 处理中

---

## 6. 快照信息管理

**模块路径：** `/emergency/snapshot-info`

### 6.1 根据列表类型查询快照信息列表

**接口地址：** `POST /emergency/snapshot-info/getInfosByListType`

**请求参数：**

```json
{
    "snapshotId": 1,
    "listType": "EVENT",
    "infoType": "SENSOR",
    "status": "NORMAL"
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "snapshotId": 1,
            "listType": "EVENT",
            "eventId": "EVENT001",
            "eventName": "瓦斯浓度异常",
            "infoType": "SENSOR",
            "infoCategory": "安全监测",
            "infoName": "瓦斯浓度",
            "infoValue": "0.85",
            "infoUnit": "%",
            "status": "WARNING",
            "priorityLevel": 3,
            "locationInfo": "1301工作面"
        }
    ]
}
```

**列表类型说明：**

- `EVENT` - 事件列表
- `TIME` - 时间列表

**状态说明：**

- `NORMAL` - 正常
- `WARNING` - 警告
- `ALARM` - 报警

---

## 7. 专家库管理

**模块路径：** `/emergency/expert`

### 7.1 分页查询专家信息

**接口地址：** `POST /emergency/expert/pagingQuery`

**请求参数：**

```json
{
    "current": 1,
    "pageSize": 10,
    "expertName": "张",
    "specialty": "安全工程",
    "status": 1
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "expertCode": "EXPERT001",
                "expertName": "张专家",
                "specialty": "安全工程",
                "title": "高级工程师",
                "workUnit": "安全技术研究院",
                "phone": "13812345678",
                "email": "<EMAIL>",
                "status": 1,
                "statusName": "正常"
            }
        ],
        "total": 1,
        "current": 1,
        "size": 10
    }
}
```

---

## 8. 应急物资管理

**模块路径：** `/emergency/storage`

### 8.1 分页查询应急物资信息

**接口地址：** `POST /emergency/storage/pagingQuery`

**请求参数：**

```json
{
    "current": 1,
    "pageSize": 10,
    "materialName": "防毒面具",
    "storageLocation": "A区仓库",
    "status": 1
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "materialCode": "MAT001",
                "materialName": "防毒面具",
                "materialType": "防护用品",
                "specification": "标准型",
                "unit": "个",
                "quantity": 100,
                "storageLocation": "A区仓库",
                "status": 1,
                "statusName": "正常"
            }
        ],
        "total": 1,
        "current": 1,
        "size": 10
    }
}
```

---

## 9. 应急历史事件

**模块路径：** `/emergency/history`

### 9.1 分页查询应急历史事件信息

**接口地址：** `POST /emergency/history/pagingQuery`

**请求参数：**

```json
{
    "current": 1,
    "pageSize": 10,
    "eventName": "瓦斯泄漏",
    "eventLevel": "重大",
    "eventTimeStart": "2024-01-01T00:00:00",
    "eventTimeEnd": "2024-12-31T23:59:59"
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "eventCode": "EVENT001",
                "eventName": "瓦斯泄漏事件",
                "eventType": "安全事故",
                "eventLevel": "重大",
                "eventTime": "2024-01-15T14:30:00",
                "location": "1301工作面",
                "description": "工作面瓦斯浓度超标",
                "handleStatus": "已处理",
                "responsePerson": "张三"
            }
        ],
        "total": 1,
        "current": 1,
        "size": 10
    }
}
```

---

## 10. 应急演练管理

**模块路径：** `/emergency/drill`

### 10.1 分页查询应急演练信息

**接口地址：** `POST /emergency/drill/pagingQuery`

**请求参数：**

```json
{
    "current": 1,
    "pageSize": 10,
    "drillName": "火灾演练",
    "drillType": "消防演练",
    "drillTimeStart": "2024-01-01T00:00:00",
    "drillTimeEnd": "2024-12-31T23:59:59"
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "drillCode": "DRILL001",
                "drillName": "火灾应急演练",
                "drillType": "消防演练",
                "drillTime": "2024-01-20T09:00:00",
                "drillLocation": "办公区域",
                "participantCount": 50,
                "drillResult": "优秀",
                "organizer": "安全部",
                "description": "模拟办公区域火灾应急疏散"
            }
        ],
        "total": 1,
        "current": 1,
        "size": 10
    }
}
```

---

## 11. 应急通讯录

**模块路径：** `/emergency/contact`

### 11.1 分页查询应急救援通讯录信息

**接口地址：** `POST /emergency/contact/pagingQuery`

**请求参数：**

```json
{
    "current": 1,
    "pageSize": 10,
    "contactName": "张三",
    "department": "安全部",
    "contactType": "内部联系人"
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "contactCode": "CONTACT001",
                "contactName": "张三",
                "department": "安全部",
                "position": "安全主管",
                "phone": "13812345678",
                "emergencyPhone": "13987654321",
                "email": "<EMAIL>",
                "contactType": "内部联系人",
                "status": 1,
                "statusName": "正常"
            }
        ],
        "total": 1,
        "current": 1,
        "size": 10
    }
}
```

---

## 12. 传感器监测

**模块路径：** `/emergency/sensor`

### 12.1 获取传感器树形结构

**接口地址：** `POST /emergency/sensor/getSensorTree`

**请求参数：** （可选）

```json
{
    "sensorName": "瓦斯",
    "orgCode": "LJTKYJGS"
}
```

**响应示例：**

```json
{
    "code": "0000",
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "sensorCode": "SENSOR001",
            "sensorName": "瓦斯浓度传感器",
            "sensorType": "气体监测",
            "location": "1301工作面",
            "status": 1,
            "statusName": "正常",
            "currentValue": "0.35",
            "unit": "%",
            "thresholdMin": 0.00,
            "thresholdMax": 1.00,
            "children": [
            ]
        }
    ]
}
```

---

## 通用数据字典

### 状态码 (status)

- `0` - 禁用/异常
- `1` - 启用/正常
- `2` - 异常
- `3` - 离线

### 预案状态 (planStatus)

- `1` - 草稿
- `2` - 审核中
- `3` - 已发布
- `4` - 已废止

### 预案级别 (planLevel)

- `I级` - 特别重大
- `II级` - 重大
- `III级` - 较大
- `IV级` - 一般

### 优先级 (priorityLevel)

- `1` - 低
- `2` - 中
- `3` - 高
- `4` - 紧急

## 通用错误码

- `0000` - 操作成功
- `0002` - 参数错误
- `0005` - 业务逻辑错误
- `0404` - 数据不存在

## 使用建议

1. **统一认证**：所有接口都需要通过认证，建议在请求头中携带认证信息
2. **组织隔离**：建议在查询参数中传入 `orgCode` 进行数据隔离
3. **分页查询**：对于大量数据，建议使用分页查询接口
4. **时间格式**：所有时间字段统一使用 `yyyy-MM-dd HH:mm:ss` 格式
5. **状态管理**：定期检查和更新各模块的状态信息
6. **错误处理**：客户端应根据返回的错误码进行相应的错误处理

## 更新日志

- **2025-01-20**：创建统一API文档
- **2025-01-20**：整合所有emergency模块接口
- **2025-01-20**：添加数据字典和使用建议 